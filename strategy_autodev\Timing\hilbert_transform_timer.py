# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# coding: utf-8
"""
希尔伯特变换择时器
基于希尔伯特变换的短线择时策略。
使用信号处理方法，将时间域信号转换到频率域，分解为同相和正交部分。
根据信号在四个象限的分布进行交易决策。

策略原理：
- 价格上涨时，分解信号位于第一象限 (I>0, Q>0)
- 价格下跌时，分解信号位于第三象限 (I<0, Q<0)
- 进入第一象限买入，出第一象限卖出
- 进入第三象限做空，出第三象限平仓
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import warnings
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
    from .base_timer import BaseTimer, TimingConfig, TimingSignal
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class StockDataAdapter:
        def get_stock_data(self, symbol, start_date, end_date, adjust='qfq'):
            print(f"警告：无法导入StockDataAdapter，请检查adapters模块")
            return pd.DataFrame()
    
    from loguru import logger
    
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
            self._required_columns = kwargs.get('required_columns', ['close'])
            self._min_history = kwargs.get('min_history', 20)
            
        def validate_data(self, data):
            return True  # 简化验证
            
        def apply_threshold(self, strength):
            return strength
            
        def normalize_strength(self, value, vmin, vmax):
            return min(1.0, max(0.0, (value - vmin) / (vmax - vmin)))
    
    class TimingConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class TimingSignal:
        def __init__(self, timestamp, signal, strength, strategy, metadata):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata

warnings.filterwarnings('ignore')


class HilbertTransformTimer(BaseTimer):
    """
    希尔伯特变换择时策略
    基于希尔伯特变换的信号处理方法进行短线择时。
    """
    
    def __init__(self, period: int = 20, multiplier: float = 1.5):
        super().__init__(TimingConfig(lookback_period=period * 2),
                         required_columns=['close'],
                         min_history=period)
        self.period = period
        self.data_adapter = StockDataAdapter()
        
    def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取股票数据
        
        Args:
            stock_code: 股票代码 (如 '600519.XSHG')
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            包含OHLC数据的DataFrame
        """
        try:
            # 转换股票代码格式
            if '.XSHG' in stock_code:
                ts_code = stock_code.replace('.XSHG', '.SH')
            elif '.XSHE' in stock_code:
                ts_code = stock_code.replace('.XSHE', '.SZ')
            else:
                ts_code = stock_code
            
            # 使用股票数据适配器获取数据
            data = self.data_adapter.get_stock_data(
                symbol=ts_code,
                start_date=start_date,
                end_date=end_date,
                adjust='qfq'  # 前复权
            )
            
            if data.empty:
                logger.warning(f"未获取到股票 {stock_code} 的数据")
                return pd.DataFrame()
            
            # 转换列名为小写，匹配原始代码格式
            data.columns = data.columns.str.lower()
            
            return data
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_hilbert_transform(self, prices: pd.Series) -> Dict[str, np.ndarray]:
        """
        计算希尔伯特变换的各个组件
        
        Args:
            prices: 价格序列
            
        Returns:
            包含S, D, Q, I, signals的字典
        """
        T = len(prices)
        p = prices.values
        
        # 初始化数组
        S = np.zeros(T)  # 平滑价格序列
        D = np.zeros(T)  # 去趋势序列
        Q = np.zeros(T)  # 正交分量 (Quadrature)
        I = np.zeros(T)  # 同相分量 (In-phase)
        sig = np.zeros(T)  # 交易信号
        
        # 计算平滑价格序列S
        for t in range(3, T):
            S[t] = (4*p[t] + 3*p[t-1] + 2*p[t-2] + p[t-3]) / 10
        
        # 计算去趋势序列D
        for t in range(6, T):
            D[t] = (0.25*S[t] + 0.75*S[t-2] - 0.25*S[t-4] - 0.75*S[t-6])
        
        # 计算正交分量Q (希尔伯特变换)
        for t in range(6, T):
            Q[t] = (0.25*D[t] + 0.75*D[t-2] - 0.25*D[t-4] - 0.75*D[t-6])
        
        # 计算同相分量I (延迟3期的D)
        for t in range(9, T):
            I[t] = D[t-3]
        
        # 生成交易信号
        for t in range(9, T):
            if Q[t] > 0 and I[t] > 0:  # 第一象限：买入
                sig[t] = 1
            elif Q[t] < 0 and I[t] < 0:  # 第三象限：卖出
                sig[t] = -1
            else:  # 其他象限：持有
                sig[t] = 0
        
        return {
            'S': S,
            'D': D,
            'Q': Q,
            'I': I,
            'signals': sig
        }
    
    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法"""
        if not self.validate_data(data):
            raise ValueError("数据无效")
        
        # 使用 calculate_signals 方法
        signal_data = self.calculate_signals(data)
        
        if signal_data.empty:
            return TimingSignal(
                timestamp=datetime.now(),
                signal=0,
                strength=0.0,
                strategy="HilbertTransform",
                metadata={"error": "计算失败"}
            )
        
        latest = signal_data.iloc[-1]
        signal_value = int(latest.get('hilbert_signal', 0))
        signal_strength = float(latest.get('signal_strength', 0))
        
        # 归一化强度
        normalized_strength = self.normalize_strength(signal_strength, vmin=0, vmax=signal_strength + 1)
        strength = self.apply_threshold(normalized_strength)
        
        return TimingSignal(
            timestamp=datetime.now(),
            signal=signal_value,
            strength=strength,
            strategy="HilbertTransform",
            metadata={
                "phase_angle": latest.get("phase_angle", 0),
                "signal_strength": signal_strength
            }
        )
    
    def calculate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算希尔伯特变换信号
        
        Args:
            data: 包含价格数据的DataFrame
            
        Returns:
            包含希尔伯特变换指标和信号的DataFrame
        """
        if data.empty:
            return pd.DataFrame()
        
        # 使用收盘价计算希尔伯特变换
        hilbert_data = self.calculate_hilbert_transform(data['close'])
        
        # 将结果合并到原数据中
        result = data.copy()
        result['smooth_price'] = hilbert_data['S']
        result['detrend'] = hilbert_data['D']
        result['quadrature'] = hilbert_data['Q']
        result['inphase'] = hilbert_data['I']
        result['hilbert_signal'] = hilbert_data['signals']
        
        # 生成具体的买卖信号
        result['buy_signal'] = (result['hilbert_signal'] == 1).astype(int)
        result['sell_signal'] = (result['hilbert_signal'] == -1).astype(int)
        
        # 计算信号强度（基于I和Q的幅度）
        result['signal_strength'] = np.sqrt(result['quadrature']**2 + result['inphase']**2)
        
        # 计算相位角度
        result['phase_angle'] = np.arctan2(result['quadrature'], result['inphase']) * 180 / np.pi
        
        return result
    
    def analyze_signals(self, data: pd.DataFrame) -> Dict[str, Union[int, float]]:
        """
        分析信号统计信息
        
        Args:
            data: 包含信号的DataFrame
            
        Returns:
            信号分析结果字典
        """
        if data.empty or 'hilbert_signal' not in data.columns:
            return {}
        
        signals = data['hilbert_signal']
        
        # 统计各类信号数量
        buy_signals = (signals == 1).sum()
        sell_signals = (signals == -1).sum()
        hold_signals = (signals == 0).sum()
        
        # 计算信号变化次数
        signal_changes = (signals.diff() != 0).sum()
        
        # 计算平均信号强度
        avg_signal_strength = data['signal_strength'].mean() if 'signal_strength' in data.columns else 0
        
        return {
            'total_periods': len(data),
            'buy_signals': int(buy_signals),
            'sell_signals': int(sell_signals),
            'hold_signals': int(hold_signals),
            'signal_changes': int(signal_changes),
            'buy_ratio': float(buy_signals / len(data)),
            'sell_ratio': float(sell_signals / len(data)),
            'hold_ratio': float(hold_signals / len(data)),
            'avg_signal_strength': float(avg_signal_strength)
        }
    
    def plot_analysis(self, data: pd.DataFrame, stock_code: str = "", save_path: str = None):
        """
        绘制希尔伯特变换分析图表
        
        Args:
            data: 包含分析结果的DataFrame
            stock_code: 股票代码
            save_path: 图表保存路径
        """
        if data.empty:
            logger.warning("数据为空，无法绘制图表")
            return
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle(f'希尔伯特变换择时分析 - {stock_code}', fontsize=16, fontweight='bold')
        
        # 有效数据范围（去除前面的NaN值）
        valid_data = data.dropna(subset=['quadrature', 'inphase'])
        
        if valid_data.empty:
            logger.warning("没有有效的希尔伯特变换数据")
            return
        
        # 1. 价格和信号图
        ax1 = axes[0]
        ax1.plot(valid_data.index, valid_data['close'], label='收盘价', color='black', linewidth=1)
        ax1.plot(valid_data.index, valid_data['smooth_price'], label='平滑价格', color='blue', alpha=0.7)
        
        # 标记买卖信号
        buy_points = valid_data[valid_data['buy_signal'] == 1]
        sell_points = valid_data[valid_data['sell_signal'] == 1]
        
        if not buy_points.empty:
            ax1.scatter(buy_points.index, buy_points['close'], color='red', marker='^', 
                       s=50, label='买入信号', zorder=5)
        if not sell_points.empty:
            ax1.scatter(sell_points.index, sell_points['close'], color='green', marker='v', 
                       s=50, label='卖出信号', zorder=5)
        
        ax1.set_title('价格走势与交易信号')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. I-Q相位图
        ax2 = axes[1]
        scatter = ax2.scatter(valid_data['inphase'], valid_data['quadrature'], 
                             c=valid_data['hilbert_signal'], cmap='RdYlGn', 
                             alpha=0.6, s=20)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        ax2.set_xlabel('同相分量 (I)')
        ax2.set_ylabel('正交分量 (Q)')
        ax2.set_title('I-Q相位图 (颜色表示信号类型)')
        ax2.grid(True, alpha=0.3)
        
        # 添加象限标注
        ax2.text(0.02, 0.98, '第二象限', transform=ax2.transAxes, ha='left', va='top')
        ax2.text(0.98, 0.98, '第一象限\n(买入)', transform=ax2.transAxes, ha='right', va='top', 
                bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.5))
        ax2.text(0.02, 0.02, '第三象限\n(卖出)', transform=ax2.transAxes, ha='left', va='bottom',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
        ax2.text(0.98, 0.02, '第四象限', transform=ax2.transAxes, ha='right', va='bottom')
        
        # 3. 信号强度图
        ax3 = axes[2]
        ax3.plot(valid_data.index, valid_data['signal_strength'], label='信号强度', color='purple')
        ax3.set_title('信号强度变化')
        ax3.set_ylabel('强度')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易信号图
        ax4 = axes[3]
        ax4.plot(valid_data.index, valid_data['hilbert_signal'], label='希尔伯特信号', 
                color='orange', linewidth=2)
        ax4.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='买入线')
        ax4.axhline(y=-1, color='green', linestyle='--', alpha=0.5, label='卖出线')
        ax4.axhline(y=0, color='gray', linestyle='-', alpha=0.5, label='中性线')
        ax4.set_title('交易信号时序图')
        ax4.set_ylabel('信号值')
        ax4.set_xlabel('时间')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(-1.5, 1.5)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def print_analysis_results(self, analysis: Dict[str, Union[int, float]], stock_code: str = ""):
        """
        打印分析结果
        
        Args:
            analysis: 分析结果字典
            stock_code: 股票代码
        """
        if not analysis:
            logger.warning("没有分析结果可显示")
            return
        
        print(f"\n{'='*50}")
        print(f"希尔伯特变换择时分析结果 - {stock_code}")
        print(f"{'='*50}")
        print(f"总交易周期: {analysis['total_periods']}")
        print(f"买入信号: {analysis['buy_signals']} ({analysis['buy_ratio']:.2%})")
        print(f"卖出信号: {analysis['sell_signals']} ({analysis['sell_ratio']:.2%})")
        print(f"持有信号: {analysis['hold_signals']} ({analysis['hold_ratio']:.2%})")
        print(f"信号变化次数: {analysis['signal_changes']}")
        print(f"平均信号强度: {analysis['avg_signal_strength']:.4f}")
        print(f"{'='*50}\n")
    
    def run_strategy(self, stock_code: str, start_date: str, end_date: str, 
                    plot: bool = True, save_path: str = None) -> Tuple[pd.DataFrame, Dict]:
        """
        运行完整的希尔伯特变换择时策略
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            plot: 是否绘制图表
            save_path: 图表保存路径
            
        Returns:
            (分析结果DataFrame, 统计信息字典)
        """
        logger.info(f"开始运行希尔伯特变换择时策略: {stock_code}")
        
        # 获取数据
        data = self.get_stock_data(stock_code, start_date, end_date)
        if data.empty:
            logger.error("无法获取股票数据")
            return pd.DataFrame(), {}
        
        # 计算信号
        result = self.calculate_signals(data)
        
        # 分析信号
        analysis = self.analyze_signals(result)
        
        # 打印结果
        self.print_analysis_results(analysis, stock_code)
        
        # 绘制图表
        if plot:
            self.plot_analysis(result, stock_code, save_path)
        
        return result, analysis


# 使用示例和测试
if __name__ == "__main__":
    # 创建希尔伯特变换择时器
    timer = HilbertTransformTimer(smoothing_window=10)
    
    # 测试数据（如果无法获取真实数据）
    def generate_test_data(periods=200):
        """生成测试数据"""
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=periods, freq='D')
        
        # 生成带趋势的价格数据
        trend = np.linspace(100, 120, periods)
        noise = np.random.normal(0, 2, periods)
        cycle = 5 * np.sin(np.linspace(0, 4*np.pi, periods))
        
        prices = trend + noise + cycle
        
        data = pd.DataFrame({
            'open': prices * 0.99,
            'high': prices * 1.02,
            'low': prices * 0.98,
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, periods)
        }, index=dates)
        
        return data
    
    # 运行测试
    print("希尔伯特变换择时器测试")
    print("使用模拟数据进行测试...")
    
    # 生成测试数据
    test_data = generate_test_data(200)
    
    # 计算希尔伯特变换信号
    result = timer.calculate_signals(test_data)
    
    # 分析信号
    analysis = timer.analyze_signals(result)
    
    # 打印结果
    timer.print_analysis_results(analysis, "测试数据")
    
    # 绘制图表
    timer.plot_analysis(result, "测试数据")
    
    print("\n希尔伯特变换择时器测试完成！")
