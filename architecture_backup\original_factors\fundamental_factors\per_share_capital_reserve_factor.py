# -*- coding: utf-8 -*-
"""
每股资本公积因子 (Per Share Capital Reserve Factor)
因子描述基于: https://factors.directory/zh/factors/basic-surface/per-share-capital-reserve

因子逻辑:
每股资本公积是衡量公司财务健康和潜在扩张能力的重要指标。
它反映了每一股普通股所拥有的资本公积金的数额。
资本公积主要来源于资本（或股本）溢价，是投资者投入的超出法定股本的部分。

因子公式:
每股资本公积 = 资本公积 / 流通股本 (Shares Outstanding)

其中:
- 资本公积: 公司资产负债表中的资本公积项目。
- 流通股本: 公司在外的总股本。

因子解释:
较高的每股资本公积通常被市场解读为积极信号：
- 公司财务实力雄厚，有能力应对未来的不确定性。
- 公司有更大的潜力进行“高送转”（即用资本公积转增股本），这往往会刺激股价。
- 公司有更充足的资金用于再投资或业务扩张。

适用场景:
1. 价值投资：寻找财务稳健、有安全边际的公司。
2. 成长投资：筛选有高送转潜力、可能受市场追捧的公司。
3. 综合评分模型：作为财务健康维度的一个重要组成部分。

原始网页: https://factors.directory/zh/factors/basic-surface/per-share-capital-reserve

作者: AI Assistant
创建时间: 2025-01-27
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import warnings
import logging

warnings.filterwarnings('ignore')

# 动态添加项目根目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 动态导入所需模块
try:
    from adapters.base_data_adapter import BaseDataAdapter
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from factor_analyze.factor_core.base_interfaces import BaseFactorMiner
    ADAPTER_AVAILABLE = True
    logger.info("基础适配器和因子挖掘器接口加载成功。")
except ImportError as e:
    logger.warning(f"加载基础模块失败: {e}。将使用降级功能。")
    ADAPTER_AVAILABLE = False
    # 创建临时的基类以确保代码可以运行
    class BaseFactorMiner:
        def __init__(self, *args, **kwargs): pass
    class BaseDataAdapter:
        def __init__(self, *args, **kwargs): pass
    # 已删除重复定义: FundamentalDataAdapter
class PerShareCapitalReserveFactorForStocks(BaseFactorMiner):
    """
    个股的每股资本公积因子计算器。

    计算流程:
    1. 获取资产负债表中的资本公积。
    2. 获取公司的总股本数据。
    3. 计算每股资本公积并进行数据清洗。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.config = config or {}
        self._setup_logger()
        
        self.outlier_threshold = self.config.get('outlier_threshold', 3)
        
        self.fundamental_adapter = None
        if ADAPTER_AVAILABLE:
            try:
                self.fundamental_adapter = FundamentalDataAdapter()
                self.logger.info("财务数据适配器初始化成功。")
            except Exception as e:
                self.logger.warning(f"财务数据适配器初始化失败: {e}。将使用模拟数据。")
    
    def _setup_logger(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _get_financial_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取财务数据（资本公积和总股本）。"""
        if self.fundamental_adapter:
            try:
                # 获取资产负债表数据
                balance_sheet = self.fundamental_adapter.get_balance_sheet(
                    stock_code, start_date, end_date, fields='ts_code,ann_date,end_date,capital_rese'
                )
                # 获取总股本数据
                daily_basic = self.fundamental_adapter.get_daily_basic(
                    stock_code, start_date, end_date, fields='ts_code,trade_date,total_share'
                )
                
                if balance_sheet is None or balance_sheet.empty or daily_basic is None or daily_basic.empty:
                    self.logger.warning(f"未能获取到 {stock_code} 的完整财务数据。")
                    return self._generate_mock_financial_data(stock_code, start_date, end_date)
                
                # 将财报数据与日度股本数据对齐
                balance_sheet['date'] = pd.to_datetime(balance_sheet['ann_date'], format='%Y%m%d')
                daily_basic['date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
                
                # 使用merge_asof将季度的资本公积数据广播到每日
                merged_data = pd.merge_asof(
                    daily_basic.sort_values('date'),
                    balance_sheet[['date', 'capital_rese']].sort_values('date'),
                    on='date',
                    direction='backward'
                )
                return merged_data.dropna()
                    
            except Exception as e:
                self.logger.error(f"从适配器获取 {stock_code} 财务数据失败: {e}")
        
        self.logger.warning("正在生成模拟财务数据...")
        return self._generate_mock_financial_data(stock_code, start_date, end_date)
    
    def _generate_mock_financial_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟财务数据。"""
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        n_days = len(dates)
        np.random.seed(hash(stock_code) % (2**32))
        
        capital_reserve = (np.random.uniform(5e8, 50e8) * (1 + np.random.randn(n_days).cumsum() * 0.001)).clip(min=0)
        total_share = (np.random.uniform(1e8, 10e8) * (1 + np.random.randn(n_days).cumsum() * 0.0001)).clip(min=1e7)
        
        return pd.DataFrame({
            'date': dates,
            'capital_rese': capital_reserve,
            'total_share': total_share,
        })
    
    def _calculate_per_share_capital_reserve(self, financial_data: pd.DataFrame) -> pd.DataFrame:
        """计算每股资本公积。"""
        if financial_data.empty:
            return pd.DataFrame()
        
        result = financial_data.copy()
        result['capital_rese'] = pd.to_numeric(result['capital_rese'], errors='coerce')
        result['total_share'] = pd.to_numeric(result['total_share'], errors='coerce')
        
        # 计算因子值，注意A股股本单位为“股”，资本公积单位为“元”，Tushare返回的总股本是“万股”
        result['factor_value'] = result['capital_rese'] / (result['total_share'] * 10000)
        
        result.replace([np.inf, -np.inf], np.nan, inplace=True)
        result.dropna(subset=['factor_value'], inplace=True)
        
        result = self._handle_outliers(result, 'factor_value')
        return result
    
    def _handle_outliers(self, data: pd.DataFrame, column: str) -> pd.DataFrame:
        """使用Z-score处理异常值。"""
        if data.empty or column not in data.columns:
            return data
        
        mean = data[column].mean()
        std = data[column].std()
        
        if std > 0:
            data[f'{column}_is_outlier'] = np.abs(data[column] - mean) > (self.outlier_threshold * std)
            outlier_count = data[f'{column}_is_outlier'].sum()
            if outlier_count > 0:
                self.logger.warning(f"在列 '{column}' 中发现并标记了 {outlier_count} 个异常值。")
        return data
    
    def calculate_factor(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        计算单个股票的每股资本公积因子。
        
        Args:
            stock_code: 股票代码。
            start_date: 开始日期。
            end_date: 结束日期。
            
        Returns:
            包含因子值的DataFrame。
        """
        try:
            financial_data = self._get_financial_data(stock_code, start_date, end_date)
            if financial_data.empty:
                self.logger.warning(f"股票 {stock_code} 无有效财务数据。")
                return pd.DataFrame()
            
            factor_data = self._calculate_per_share_capital_reserve(financial_data)
            if factor_data.empty:
                self.logger.warning(f"股票 {stock_code} 未能计算出有效的因子值。")
                return pd.DataFrame()
            
            # 格式化输出
            factor_data['symbol'] = stock_code
            factor_data['factor_name'] = 'per_share_capital_reserve'
            
            output_columns = ['symbol', 'date', 'factor_name', 'factor_value']
            result = factor_data[output_columns].copy()
            
            self.logger.info(f"成功为股票 {stock_code} 计算了 {len(result)} 条因子记录。")
            return result
            
        except Exception as e:
            self.logger.error(f"计算股票 {stock_code} 因子时出错: {e}", exc_info=True)
            return pd.DataFrame()
    
    def mine_factors(self, stock_codes: List[str], start_date: str, end_date: str, **kwargs) -> Dict[str, pd.DataFrame]:
        """批量挖掘多个股票的因子。"""
        results = {}
        for stock_code in stock_codes:
            results[stock_code] = self.calculate_factor(stock_code, start_date, end_date)
        return results


class PerShareCapitalReserveFactorForIndices(BaseFactorMiner):
    """
    指数的每股资本公积因子。
    通过对成分股的因子值进行加权平均计算得出。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.config = config or {}
        self._setup_logger()
        self.stock_factor = PerShareCapitalReserveFactorForStocks(self.config)
        self.fundamental_adapter = self.stock_factor.fundamental_adapter
    
    def _setup_logger(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def calculate_factor(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """计算单个指数的因子值。"""
        try:
            constituents = self._get_index_constituents(index_code, end_date)
            if constituents.empty:
                self.logger.warning(f"无法获取指数 {index_code} 的成分股。")
                return pd.DataFrame()

            # 批量获取所有成分股的因子数据
            all_stock_factors = []
            for stock_code in constituents['con_code']:
                factor_df = self.stock_factor.calculate_factor(stock_code, start_date, end_date)
                if not factor_df.empty:
                    all_stock_factors.append(factor_df)

            if not all_stock_factors:
                self.logger.warning(f"指数 {index_code} 的所有成分股均无有效因子数据。")
                return pd.DataFrame()

            combined_data = pd.concat(all_stock_factors)
            merged_data = pd.merge(combined_data, constituents, left_on='symbol', right_on='con_code')
            
            # 按权重加权平均
            def weighted_avg(group):
                return np.average(group['factor_value'], weights=group['weight'])

            index_factor = merged_data.groupby('date').apply(weighted_avg).reset_index(name='factor_value')
            index_factor['symbol'] = index_code
            
            self.logger.info(f"成功为指数 {index_code} 计算了 {len(index_factor)} 条因子记录。")
            return index_factor

        except Exception as e:
            self.logger.error(f"计算指数 {index_code} 因子时出错: {e}", exc_info=True)
            return pd.DataFrame()

    def _get_index_constituents(self, index_code: str, date: str) -> pd.DataFrame:
        """获取指数成分股和权重。"""
        if self.fundamental_adapter:
            try:
                # 假设适配器有获取指数权重的方法
                weights = self.fundamental_adapter.get_index_weight(index_code=index_code, trade_date=date)
                if weights is not None and not weights.empty:
                    return weights[['con_code', 'weight']]
            except AttributeError:
                 self.logger.warning("当前适配器无 `get_index_weight` 方法。")
            except Exception as e:
                self.logger.error(f"获取指数 {index_code} 权重失败: {e}")
        
        return self._generate_mock_constituents(index_code)

    def _generate_mock_constituents(self, index_code: str) -> pd.DataFrame:
        """生成模拟成分股数据。"""
        np.random.seed(hash(index_code) % (2**32))
        mock_stocks = [f'{i:06d}.SZ' for i in np.random.randint(1, 3000, 20)] + \
                      [f'60{i:04d}.SH' for i in np.random.randint(1, 3000, 10)]
        weights = np.random.rand(len(mock_stocks))
        weights /= weights.sum()
        return pd.DataFrame({'con_code': mock_stocks, 'weight': weights})


# 统一接口
class PerShareCapitalReserveFactor(BaseFactorMiner):
    """
    每股资本公积因子的统一接口。
    根据输入代码自动区分股票和指数。
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.config = config or {}
        self.stock_factor = PerShareCapitalReserveFactorForStocks(config)
        self.index_factor = PerShareCapitalReserveFactorForIndices(config)
    
    def mine_factors(self, symbols: List[str], start_date: str, end_date: str, **kwargs) -> Dict[str, pd.DataFrame]:
        results = {}
        for symbol in symbols:
            # 简单的代码判断逻辑
            if symbol.startswith('000') or symbol.startswith('300') or symbol.startswith('600'):
                results[symbol] = self.stock_factor.calculate_factor(symbol, start_date, end_date)
            else: # 假设为指数
                results[symbol] = self.index_factor.calculate_factor(symbol, start_date, end_date)
        return results


if __name__ == "__main__":
    logger.info("=== 每股资本公积因子计算演示 ===")
    
    # 演示个股计算
    stock_calculator = PerShareCapitalReserveFactorForStocks()
    stock_result = stock_calculator.calculate_factor('000001.SZ', '2023-01-01', '2023-03-31')
    logger.info("\n--- 个股因子计算结果 (平安银行) ---")
    print(stock_result.tail())
    
    # 演示指数计算
    index_calculator = PerShareCapitalReserveFactorForIndices()
    index_result = index_calculator.calculate_factor('000300.SH', '2023-01-01', '2023-03-31')
    logger.info("\n--- 指数因子计算结果 (沪深300) ---")
    print(index_result.tail())
    
    logger.info("\n=== 演示结束 ===")
