# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析师预期EPS调整幅度因子集成测试
测试因子与factor_mining模块的集成功能

创建时间: 2025-01-27
作者: AI Assistant
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_factor_creation():
    """
    测试因子创建功能
    """
    print("=== 测试1: 因子创建 ===")
    
    try:
        from factor_analyze.expectation_factors.expected_eps_adjustment_factor import (
            create_stock_expected_eps_adjustment_factor,
            create_index_expected_eps_adjustment_factor
        )
        
        # 创建个股因子
        stock_factor = create_stock_expected_eps_adjustment_factor()
        print("✅ 个股EPS调整因子创建成功")
        print(f"   因子名称: {stock_factor.factor_name}")
        
        # 创建指数因子
        index_factor = create_index_expected_eps_adjustment_factor()
        print("✅ 指数EPS调整因子创建成功")
        print(f"   因子名称: {index_factor.factor_name}")
        
        # 获取因子信息
        stock_info = stock_factor.get_factor_info()
        print(f"   个股因子类型: {stock_info['factor_type']}")
        print(f"   计算频率: {stock_info['calculation_frequency']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 因子创建失败: {e}")
        return False


def test_factor_calculation():
    """
    测试因子计算功能
    """
    print("\n=== 测试2: 因子计算 ===")
    
    try:
        from factor_analyze.expectation_factors.expected_eps_adjustment_factor import (
            create_stock_expected_eps_adjustment_factor,
            create_index_expected_eps_adjustment_factor
        )
        
        # 测试个股因子计算
        print("测试个股因子计算...")
        stock_factor = create_stock_expected_eps_adjustment_factor()
        
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
        stock_results = stock_factor.calculate_factor(
            securities=test_stocks,
            start_date='2024-01-01',
            end_date='2024-06-30'
        )
        
        if not stock_results.empty:
            print(f"✅ 个股因子计算成功，共{len(stock_results)}条记录")
            print(f"   样本数据:")
            for col in ['security', 'date', 'eps_adjustment_ratio', 'adjustment_direction']:
                if col in stock_results.columns:
                    print(f"     {col}: {stock_results[col].iloc[0] if len(stock_results) > 0 else 'N/A'}")
        else:
            print("⚠️ 个股因子计算无结果")
        
        # 测试指数因子计算
        print("\n测试指数因子计算...")
        index_factor = create_index_expected_eps_adjustment_factor()
        
        test_indices = ['000300.SH']
        index_results = index_factor.calculate_factors(
            securities=test_indices,
            start_date='2024-01-01',
            end_date='2024-06-30'
        )
        
        if not index_results.empty:
            print(f"✅ 指数因子计算成功，共{len(index_results)}条记录")
            print(f"   样本数据:")
            for col in ['security', 'date', 'eps_adjustment_ratio', 'constituent_count']:
                if col in index_results.columns:
                    print(f"     {col}: {index_results[col].iloc[0] if len(index_results) > 0 else 'N/A'}")
        else:
            print("⚠️ 指数因子计算无结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 因子计算失败: {e}")
        return False


def test_factor_mining_integration():
    """
    测试与factor_mining模块的集成
    """
    print("\n=== 测试3: factor_mining集成 ===")
    
    try:
        # 导入factor_mining集成模块
        from factor_analyze.factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
        
        # 创建注册表
        registry = FactorAnalyzeRegistry()
        
        # 检查因子是否已注册
        available_factors = registry.list_available_factors()
        
        stock_factors = available_factors.get('stock_factors', [])
        index_factors = available_factors.get('index_factors', [])
        
        if 'expected_eps_adjustment' in stock_factors:
            print("✅ 个股EPS调整因子已成功注册到factor_mining")
        else:
            print("⚠️ 个股EPS调整因子未在factor_mining中注册")
        
        if 'expected_eps_adjustment' in index_factors:
            print("✅ 指数EPS调整因子已成功注册到factor_mining")
        else:
            print("⚠️ 指数EPS调整因子未在factor_mining中注册")
        
        # 测试通过注册表获取因子
        stock_factor_class = registry.get_stock_factor('expected_eps_adjustment')
        index_factor_class = registry.get_index_factor('expected_eps_adjustment')
        
        if stock_factor_class:
            print("✅ 成功从注册表获取个股因子类")
            
            # 创建实例并测试
            factor_instance = stock_factor_class()
            print(f"   实例化成功: {factor_instance.factor_name}")
            
        if index_factor_class:
            print("✅ 成功从注册表获取指数因子类")
            
            # 创建实例并测试
            factor_instance = index_factor_class()
            print(f"   实例化成功: {factor_instance.factor_name}")
        
        # 显示所有可用因子
        print(f"\n📊 当前可用因子:")
        print(f"   股票因子: {stock_factors}")
        print(f"   指数因子: {index_factors}")
        
        return True
        
    except Exception as e:
        print(f"❌ factor_mining集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_factor_metadata():
    """
    测试因子元数据功能
    """
    print("\n=== 测试4: 因子元数据 ===")
    
    try:
        from factor_analyze.factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
        
        registry = FactorAnalyzeRegistry()
        
        # 获取个股因子元数据
        stock_metadata = registry.get_factor_metadata('stock', 'expected_eps_adjustment')
        if stock_metadata:
            print("✅ 个股因子元数据获取成功")
            print(f"   名称: {stock_metadata.get('name')}")
            print(f"   类别: {stock_metadata.get('category')}")
            print(f"   描述: {stock_metadata.get('description')}")
            print(f"   公式: {stock_metadata.get('formula')}")
            print(f"   回看期: {stock_metadata.get('lookback_months')} 个月")
        
        # 获取指数因子元数据
        index_metadata = registry.get_factor_metadata('index', 'expected_eps_adjustment')
        if index_metadata:
            print("✅ 指数因子元数据获取成功")
            print(f"   名称: {index_metadata.get('name')}")
            print(f"   数据要求: {index_metadata.get('data_requirements')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 因子元数据测试失败: {e}")
        return False


def main():
    """
    主测试函数
    """
    print("🔧 分析师预期EPS调整幅度因子集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_factor_creation())
    test_results.append(test_factor_calculation())
    test_results.append(test_factor_mining_integration())
    test_results.append(test_factor_metadata())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print(f"   通过测试: {sum(test_results)}/{len(test_results)}")
    
    if all(test_results):
        print("🎉 所有测试通过！分析师预期EPS调整幅度因子已成功集成！")
        print("\n💡 使用建议:")
        print("   1. 因子使用模拟数据，适合测试和演示")
        print("   2. 生产环境请配置真实的分析师数据源")
        print("   3. 因子计算频率为月度，适合中长期策略")
        print("   4. 因子值正负代表分析师情绪方向变化")
    else:
        print("⚠️ 部分测试未通过，请检查错误信息")
    
    return all(test_results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
