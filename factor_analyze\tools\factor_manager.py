# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
因子库管理器 - 统一管理和维护因子
Factor Library Manager

统一管理和维护所有因子数据，提供以下功能：
- 注册和发现因子
- 加载因子数据
- 存储因子数据
- 查询因子元数据
"""

import pandas as pd
import numpy as np
import json
import pickle
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
from pathlib import Path
import warnings
import sys
import os
warnings.filterwarnings('ignore')

# 添加factor_core到sys.path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'factor_core'))
try:
    from .factor_core.base_interfaces import BaseFactorManager
    from .factor_core.common_types import FactorMetadata, FactorData
except ImportError:
    try:
        from factor_core.base_interfaces import BaseFactorManager
        from factor_core.common_types import FactorMetadata, FactorData
    except ImportError:
        # 如果都失败，使用绝对路径导入
        from factor_analyze.factor_core.base_interfaces import BaseFactorManager
        from factor_analyze.factor_core.common_types import FactorMetadata, FactorData


class FactorManager(BaseFactorManager):
    """
    因子库管理器
    
    继承自BaseFactorManager，提供更具体的因子管理实现。
    主要功能包括：
    - 数据库元数据管理
    - 文件系统数据存储
    - 因子组合管理
    - 因子性能跟踪
    """
    
    def __init__(self, database_path: str = "factor_library.db", 
                 storage_path: str = "factor_data/"):
        """
        初始化因子库管理器
        
        Args:
            database_path: SQLite数据库文件路径
            storage_path: 因子数据存储目录
        """
        self.database_path = database_path
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        self.logger = self._setup_logger()
        
        # 初始化数据库
        self._init_database()
        
        # 内存缓存
        self.factor_cache = {}
        self.performance_cache = {}
        
        self.logger.info("因子库管理器初始化完成。")
    
    def _setup_logger(self) -> logging.Logger:
        """配置日志记录器"""
        logger = logging.getLogger(f'{__name__}.FactorManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 因子元数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS manual_factors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        factor_name TEXT UNIQUE NOT NULL,
                        factor_type TEXT,
                        category TEXT,
                        description TEXT,
                        source_hypothesis TEXT,
                        created_at TIMESTAMP,
                        updated_at TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        data_path TEXT,
                        metadata TEXT
                    )
                ''')
                
                # 因子性能表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS factor_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        factor_name TEXT NOT NULL,
                        evaluation_date TIMESTAMP,
                        ic_mean REAL,
                        ic_std REAL,
                        ic_ir REAL,
                        ic_hit_rate REAL,
                        overall_score REAL,
                        sample_size INTEGER,
                        performance_data TEXT,
                        FOREIGN KEY (factor_name) REFERENCES manual_factors (factor_name)
                    )
                ''')
                
                # 因子标签表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS factor_tags (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        factor_name TEXT NOT NULL,
                        tag TEXT NOT NULL,
                        FOREIGN KEY (factor_name) REFERENCES manual_factors (factor_name)
                    )
                ''')
                
                # 因子组合表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS factor_portfolios (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        portfolio_name TEXT UNIQUE NOT NULL,
                        description TEXT,
                        manual_factors TEXT,  -- JSON格式的因子名称和权重
                        created_at TIMESTAMP,
                        performance_data TEXT
                    )
                ''')
                
                conn.commit()
                self.logger.info("数据库表结构初始化成功。")
                
        except Exception as e:
            self.logger.error(f"初始化数据库失败: {str(e)}")
    
    def save_factor(self, factor_name: str, factor_data: pd.DataFrame, 
                   metadata: Dict[str, Any]) -> bool:
        """
        保存或更新一个因子 - 实现抽象方法
        
        Args:
            factor_name: 因子唯一名称
            factor_data: 因子数据 (pd.DataFrame)
            metadata: 元数据字典，可包含以下键：
                    - factor_type: 因子类型
                    - category: 因子分类
                    - description: 详细描述
                    - source_hypothesis: 因子来源或构建假设
                    - tags: 标签列表
                    
        Returns:
            是否成功保存
        """
        # 从 metadata 中提取参数
        factor_type = metadata.get('factor_type')
        category = metadata.get('category')
        description = metadata.get('description')
        source_hypothesis = metadata.get('source_hypothesis')
        tags = metadata.get('tags')
        
        # 如果传入的是 DataFrame，转换为 Series（取第一列）
        if isinstance(factor_data, pd.DataFrame):
            if factor_data.shape[1] == 1:
                factor_series = factor_data.iloc[:, 0]
            else:
                # 如果有多列，需要合并或选择主要列
                factor_series = factor_data.iloc[:, 0]  # 默认选择第一列
        else:
            factor_series = factor_data
        
        return self._save_factor_internal(
            factor_name, factor_series, factor_type, category,
            description, source_hypothesis, metadata, tags
        )
    
    def _save_factor_internal(self, factor_name: str, factor_data: pd.Series,
                   factor_type: str = None, category: str = None,
                   description: str = None, source_hypothesis: str = None,
                   metadata: Dict[str, Any] = None, tags: List[str] = None) -> bool:
        """
        保存或更新一个因子
        
        Args:
            factor_name: 因子唯一名称
            factor_data: 因子数据 (pd.Series)
            factor_type: 因子类型 (如 'MOM', 'VOL')
            category: 因子分类 (如 'Style', 'Industry')
            description: 详细描述
            source_hypothesis: 因子来源或构建假设
            metadata: 其他元数据 (dict)
            tags: 标签列表
            
        Returns:
            是否成功保存
        """
        try:
            # 将因子数据序列化到文件
            data_path = self.storage_path / f"{factor_name}.pkl"
            with open(data_path, 'wb') as f:
                pickle.dump(factor_data, f)
            
            # 连接数据库
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 检查因子是否已存在
                cursor.execute("SELECT id FROM manual_factors WHERE factor_name = ?", (factor_name,))
                existing = cursor.fetchone()
                
                current_time = datetime.now()
                metadata_json = json.dumps(metadata or {})
                
                if existing:
                    # 更新已有因子
                    cursor.execute('''
                        UPDATE manual_factors SET
                            factor_type = ?, category = ?, description = ?,
                            source_hypothesis = ?, updated_at = ?,
                            data_path = ?, metadata = ?
                        WHERE factor_name = ?
                    ''', (factor_type, category, description, source_hypothesis,
                          current_time, str(data_path), metadata_json, factor_name))
                    self.logger.info(f"已更新因子: {factor_name}")
                else:
                    # 插入新因子
                    cursor.execute('''
                        INSERT INTO manual_factors (
                            factor_name, factor_type, category, description,
                            source_hypothesis, created_at, updated_at,
                            data_path, metadata
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (factor_name, factor_type, category, description,
                          source_hypothesis, current_time, current_time,
                          str(data_path), metadata_json))
                    self.logger.info(f"已创建新因子: {factor_name}")
                
                # 更新标签
                if tags:
                    # 先删除旧标签
                    cursor.execute("DELETE FROM factor_tags WHERE factor_name = ?", (factor_name,))
                    # 再插入新标签
                    for tag in tags:
                        cursor.execute(
                            "INSERT INTO factor_tags (factor_name, tag) VALUES (?, ?)",
                            (factor_name, tag)
                        )
                
                conn.commit()
            
            # 更新缓存
            self.factor_cache[factor_name] = factor_data
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存因子失败: {str(e)}")
            return False
    
    def load_factor(self, factor_name: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        从库中加载因子数据 - 实现抽象方法
        
        Args:
            factor_name: 因子名称
            
        Returns:
            返回 (pd.DataFrame, metadata) 元组，如果找不到则返回空DataFrame和空字典
        """
        factor_series = self._load_factor_internal(factor_name)
        
        if factor_series is None:
            return pd.DataFrame(), {}
        
        # 将 Series 转换为 DataFrame
        factor_df = pd.DataFrame({factor_name: factor_series})
        
        # 获取元数据
        metadata = self._get_factor_metadata(factor_name)
        
        return factor_df, metadata
    
    def _load_factor_internal(self, factor_name: str) -> Optional[pd.Series]:
        """
        内部方法：从库中加载因子数据
        
        Args:
            factor_name: 因子名称
            
        Returns:
            返回pd.Series格式的因子数据，如果找不到则返回None
        """
        try:
            # 首先检查内存缓存
            if factor_name in self.factor_cache:
                return self.factor_cache[factor_name]
            
            # 如果缓存中没有，从数据库查找
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT data_path FROM manual_factors WHERE factor_name = ? AND is_active = 1",
                    (factor_name,)
                )
                result = cursor.fetchone()
                
                if not result:
                    self.logger.warning(f"因子 '{factor_name}' 在数据库中不存在或未激活。")
                    return None
                
                data_path = Path(result[0])
                
                # 从文件加载数据
                if data_path.exists():
                    with open(data_path, 'rb') as f:
                        factor_data = pickle.load(f)
                    
                    # 添加到缓存
                    self.factor_cache[factor_name] = factor_data
                    return factor_data
                else:
                    self.logger.error(f"因子数据文件不存在: {data_path}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"加载因子失败: {str(e)}")
            return None
    
    def _get_factor_metadata(self, factor_name: str) -> Dict[str, Any]:
        """
        获取因子元数据
        
        Args:
            factor_name: 因子名称
            
        Returns:
            因子元数据字典
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT factor_type, category, description, source_hypothesis, 
                           created_at, updated_at, metadata
                    FROM manual_factors 
                    WHERE factor_name = ? AND is_active = 1
                ''', (factor_name,))
                
                result = cursor.fetchone()
                
                if result:
                    metadata = {
                        'factor_type': result[0],
                        'category': result[1], 
                        'description': result[2],
                        'source_hypothesis': result[3],
                        'created_at': result[4],
                        'updated_at': result[5],
                        'metadata': json.loads(result[6]) if result[6] else {}
                    }
                    
                    # 获取标签
                    cursor.execute(
                        "SELECT tag FROM factor_tags WHERE factor_name = ?",
                        (factor_name,)
                    )
                    metadata['tags'] = [tag[0] for tag in cursor.fetchall()]
                    
                    return metadata
                else:
                    return {}
                    
        except Exception as e:
            self.logger.error(f"获取因子元数据失败: {str(e)}")
            return {}
    
    def get_factor_series(self, factor_name: str) -> Optional[pd.Series]:
        """
        便捷方法：获取因子数据的 Series 格式
        
        Args:
            factor_name: 因子名称
            
        Returns:
            返回pd.Series格式的因子数据，如果找不到则返回None
        """
        return self._load_factor_internal(factor_name)
    
    def list_factors(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        列出因子 - 实现抽象方法
        
        Args:
            filters: 过滤条件字典，可包含以下键：
                    - category: 分类
                    - factor_type: 类型
                    - tags: 标签列表
                    - min_score: 最小综合评分
                    - limit: 返回结果数量限制
            
        Returns:
            返回一个包含因子元数据字典的列表
        """
        if filters is None:
            filters = {}
        
        # 调用已经实现的 search_factors 方法
        return self.search_factors(
            category=filters.get('category'),
            factor_type=filters.get('factor_type'),
            tags=filters.get('tags'),
            min_score=filters.get('min_score'),
            limit=filters.get('limit')
        )
    
    def search_factors(self, category: str = None, factor_type: str = None,
                      tags: List[str] = None, min_score: float = None,
                      limit: int = None) -> List[Dict[str, Any]]:
        """
        根据条件搜索因子
        
        Args:
            category: 分类
            factor_type: 类型
            tags: 标签列表
            min_score: 最小综合评分
            limit: 返回结果数量限制
            
        Returns:
            返回一个包含因子元数据字典的列表
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 构建基础查询语句
                query = '''
                    SELECT DISTINCT f.factor_name, f.factor_type, f.category, 
                           f.description, f.source_hypothesis, f.created_at,
                           f.metadata, fp.overall_score
                    FROM manual_factors f
                    LEFT JOIN factor_performance fp ON f.factor_name = fp.factor_name
                    WHERE f.is_active = 1
                '''
                
                params = []
                
                if category:
                    query += " AND f.category = ?"
                    params.append(category)
                
                if factor_type:
                    query += " AND f.factor_type = ?"
                    params.append(factor_type)
                
                if min_score is not None:
                    query += " AND fp.overall_score >= ?"
                    params.append(min_score)
                
                if tags:
                    placeholders = ','.join(['?' for _ in tags])
                    query += f'''
                        AND f.factor_name IN (
                            SELECT factor_name FROM factor_tags 
                            WHERE tag IN ({placeholders})
                        )
                    '''
                    params.extend(tags)
                
                query += " ORDER BY fp.overall_score DESC"
                
                if limit:
                    query += " LIMIT ?"
                    params.append(limit)
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                # 将结果格式化为字典列表
                factors = []
                for row in results:
                    factor_info = {
                        'factor_name': row[0],
                        'factor_type': row[1],
                        'category': row[2],
                        'description': row[3],
                        'source_hypothesis': row[4],
                        'created_at': row[5],
                        'metadata': json.loads(row[6]) if row[6] else {},
                        'overall_score': row[7] or 0.0
                    }
                    
                    # 获取因子的标签
                    cursor.execute(
                        "SELECT tag FROM factor_tags WHERE factor_name = ?",
                        (row[0],)
                    )
                    factor_info['tags'] = [tag[0] for tag in cursor.fetchall()]
                    
                    factors.append(factor_info)
                
                return factors
                
        except Exception as e:
            self.logger.error(f"搜索因子失败: {str(e)}")
            return []
    
    def save_factor_performance(self, factor_name: str, 
                              evaluation_result: Dict[str, Any]) -> bool:
        """
        保存因子的性能评估结果
        
        Args:
            factor_name: 因子名称
            evaluation_result: 评估结果字典
            
        Returns:
            是否成功保存
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 提取IC分析结果
                ic_analysis = evaluation_result.get('ic_analysis', {})
                
                cursor.execute('''
                    INSERT INTO factor_performance (
                        factor_name, evaluation_date, ic_mean, ic_std, ic_ir,
                        ic_hit_rate, overall_score, sample_size, performance_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    factor_name,
                    evaluation_result.get('evaluation_date', datetime.now()),
                    ic_analysis.get('ic_spearman', 0),
                    ic_analysis.get('ic_std', 0),
                    ic_analysis.get('ic_ir', 0),
                    ic_analysis.get('ic_hit_rate', 0),
                    evaluation_result.get('overall_score', 0),
                    evaluation_result.get('sample_size', 0),
                    json.dumps(evaluation_result, default=str)
                ))
                
                conn.commit()
            
            # 更新性能缓存
            self.performance_cache[factor_name] = evaluation_result
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存因子性能失败: {str(e)}")
            return False
    
    def get_factor_performance_history(self, factor_name: str) -> List[Dict[str, Any]]:
        """获取单个因子的历史性能记录"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT evaluation_date, ic_mean, ic_std, ic_ir, ic_hit_rate,
                           overall_score, sample_size, performance_data
                    FROM factor_performance 
                    WHERE factor_name = ?
                    ORDER BY evaluation_date DESC
                ''', (factor_name,))
                
                results = cursor.fetchall()
                
                history = []
                for row in results:
                    performance = {
                        'evaluation_date': row[0],
                        'ic_mean': row[1],
                        'ic_std': row[2],
                        'ic_ir': row[3],
                        'ic_hit_rate': row[4],
                        'overall_score': row[5],
                        'sample_size': row[6],
                        'full_data': json.loads(row[7]) if row[7] else {}
                    }
                    history.append(performance)
                
                return history
                
        except Exception as e:
            self.logger.error(f"获取因子性能历史记录失败: {str(e)}")
            return []
    
    def create_factor_portfolio(self, portfolio_name: str, 
                              factor_weights: Dict[str, float],
                              description: str = None) -> bool:
        """
        创建一个因子组合
        
        Args:
            portfolio_name: 组合名称
            factor_weights: 因子及其权重的字典
            description: 组合描述
            
        Returns:
            是否成功创建
        """
        try:
            # 检查所有因子是否存在
            for factor_name in factor_weights.keys():
                factor_data = self.load_factor(factor_name)
                if factor_data is None:
                    self.logger.warning(f"创建组合失败，因为因子 '{factor_name}' 不存在。")
                    return False
            
            # 对权重进行归一化
            total_weight = sum(abs(w) for w in factor_weights.values())
            if total_weight > 0:
                normalized_weights = {
                    factor: weight / total_weight 
                    for factor, weight in factor_weights.items()
                }
            else:
                normalized_weights = factor_weights
            
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO factor_portfolios (
                        portfolio_name, description, manual_factors, created_at
                    ) VALUES (?, ?, ?, ?)
                ''', (
                    portfolio_name,
                    description,
                    json.dumps(normalized_weights),
                    datetime.now()
                ))
                
                conn.commit()
            
            self.logger.info(f"已创建或更新因子组合: {portfolio_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建因子组合失败: {str(e)}")
            return False
    
    def get_factor_portfolio(self, portfolio_name: str) -> Optional[Dict[str, Any]]:
        """获取一个因子组合的详细信息"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT description, manual_factors, created_at, performance_data
                    FROM factor_portfolios 
                    WHERE portfolio_name = ?
                ''', (portfolio_name,))
                
                result = cursor.fetchone()
                
                if result:
                    return {
                        'portfolio_name': portfolio_name,
                        'description': result[0],
                        'manual_factors': json.loads(result[1]),
                        'created_at': result[2],
                        'performance_data': json.loads(result[3]) if result[3] else {}
                    }
                
                return None
                
        except Exception as e:
            self.logger.error(f"获取因子组合失败: {str(e)}")
            return None
    
    def calculate_portfolio_factor(self, portfolio_name: str) -> Optional[pd.Series]:
        """计算一个因子组合的合成因子值"""
        try:
            portfolio = self.get_factor_portfolio(portfolio_name)
            if not portfolio:
                return None
            
            factor_weights = portfolio['manual_factors']
            portfolio_factor = None
            
            for factor_name, weight in factor_weights.items():
                factor_data = self.load_factor(factor_name)
                if factor_data is not None:
                    if portfolio_factor is None:
                        portfolio_factor = weight * factor_data
                    else:
                        # 进行数据对齐和填充
                        aligned_data = pd.concat([portfolio_factor, factor_data], axis=1).fillna(0)
                        portfolio_factor = aligned_data.iloc[:, 0] + weight * aligned_data.iloc[:, 1]
            
            if portfolio_factor is not None:
                portfolio_factor.name = f"portfolio_{portfolio_name}"
            
            return portfolio_factor
            
        except Exception as e:
            self.logger.error(f"计算组合因子值失败: {str(e)}")
            return None
    
    def get_library_statistics(self) -> Dict[str, Any]:
        """获取因子库的统计信息"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 因子总数
                cursor.execute("SELECT COUNT(*) FROM manual_factors WHERE is_active = 1")
                total_factors = cursor.fetchone()[0]
                
                # 按分类统计
                cursor.execute('''
                    SELECT category, COUNT(*) 
                    FROM manual_factors 
                    WHERE is_active = 1 
                    GROUP BY category
                ''')
                category_stats = dict(cursor.fetchall())
                
                # 按类型统计
                cursor.execute('''
                    SELECT factor_type, COUNT(*) 
                    FROM manual_factors 
                    WHERE is_active = 1 
                    GROUP BY factor_type
                ''')
                type_stats = dict(cursor.fetchall())
                
                # 性能统计
                cursor.execute('''
                    SELECT AVG(overall_score), MAX(overall_score), 
                           COUNT(DISTINCT factor_name)
                    FROM factor_performance
                ''')
                perf_result = cursor.fetchone()
                
                # 组合数量
                cursor.execute("SELECT COUNT(*) FROM factor_portfolios")
                portfolio_count = cursor.fetchone()[0]
                
                return {
                    'total_factors': total_factors,
                    'category_distribution': category_stats,
                    'type_distribution': type_stats,
                    'performance_stats': {
                        'avg_score': perf_result[0] or 0,
                        'max_score': perf_result[1] or 0,
                        'evaluated_factors': perf_result[2] or 0
                    },
                    'portfolio_count': portfolio_count,
                    'cache_size': len(self.factor_cache)
                }
                
        except Exception as e:
            self.logger.error(f"获取因子库统计信息失败: {str(e)}")
            return {}
    
    def cleanup_factors(self, min_score: float = 0.1, 
                       days_old: int = 30) -> int:
        """清理低质量或过时的因子"""
        try:
            cleaned_count = 0
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # 查找需要清理的因子
                cursor.execute('''
                    SELECT DISTINCT f.factor_name, f.data_path
                    FROM manual_factors f
                    LEFT JOIN factor_performance fp ON f.factor_name = fp.factor_name
                    WHERE f.is_active = 1 AND (
                        fp.overall_score < ? OR 
                        f.created_at < ? AND fp.factor_name IS NULL
                    )
                ''', (min_score, cutoff_date))
                
                factors_to_clean = cursor.fetchall()
                
                for factor_name, data_path in factors_to_clean:
                    # 在数据库中标记为不活跃
                    cursor.execute(
                        "UPDATE manual_factors SET is_active = 0 WHERE factor_name = ?",
                        (factor_name,)
                    )
                    
                    # 删除对应的物理文件
                    if data_path and Path(data_path).exists():
                        Path(data_path).unlink()
                    
                    # 从缓存中移除
                    self.factor_cache.pop(factor_name, None)
                    
                    cleaned_count += 1
                
                conn.commit()
            
            self.logger.info(f"清理完成，共清理了 {cleaned_count} 个因子。")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理因子失败: {str(e)}")
            return 0
    
    def export_factor_library(self, output_path: str, 
                            include_data: bool = False) -> bool:
        """导出因子库为JSON文件"""
        try:
            # 获取所有因子元数据
            factors = self.search_factors()
            
            export_data = {
                'metadata': {
                    'export_date': datetime.now().isoformat(),
                    'total_factors': len(factors),
                    'library_stats': self.get_library_statistics()
                },
                'manual_factors': factors
            }
            
            # 如果需要，包含因子数据本身
            if include_data:
                factor_data = {}
                for factor in factors:
                    factor_name = factor['factor_name']
                    data = self.load_factor(factor_name)
                    if data is not None:
                        factor_data[factor_name] = data.to_dict()
                
                export_data['factor_data'] = factor_data
            
            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"因子库已成功导出到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出因子库失败: {str(e)}")
            return False
    
    def import_factor_library(self, import_path: str, 
                            overwrite: bool = False) -> bool:
        """从JSON文件导入因子库"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            factors = import_data.get('manual_factors', [])
            factor_data = import_data.get('factor_data', {})
            
            imported_count = 0
            
            for factor in factors:
                factor_name = factor['factor_name']
                
                # 如果设置为False，则跳过已存在的因子
                if not overwrite:
                    existing = self.load_factor(factor_name)
                    if existing is not None:
                        self.logger.info(f"因子已存在，跳过导入: {factor_name}")
                        continue
                
                # 加载因子数据
                if factor_name in factor_data:
                    data = pd.Series(factor_data[factor_name])
                    
                    # 保存因子
                    success = self.save_factor(
                        factor_name=factor_name,
                        factor_data=data,
                        factor_type=factor.get('factor_type'),
                        category=factor.get('category'),
                        description=factor.get('description'),
                        source_hypothesis=factor.get('source_hypothesis'),
                        metadata=factor.get('metadata'),
                        tags=factor.get('tags')
                    )
                    
                    if success:
                        imported_count += 1
            
            self.logger.info(f"成功导入 {imported_count} 个新因子。")
            return True
            
        except Exception as e:
            self.logger.error(f"导入因子库失败: {str(e)}")
            return False
