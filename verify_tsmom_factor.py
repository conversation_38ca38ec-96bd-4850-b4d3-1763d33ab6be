#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间序列动量因子验证脚本
简化版本，用于快速验证因子功能。
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

print("时间序列动量因子验证")
print("=" * 40)

try:
    # 测试1: 导入因子
    print("1. 导入因子模块...")
    from factor_analyze.fundamental_factors.time_series_momentum_factor import (
        StockTimeSeriesMomentumFactor,
        IndexTimeSeriesMomentumFactor
    )
    print("   ✓ 导入成功")
    
    # 测试2: 实例化因子
    print("2. 实例化因子...")
    stock_factor = StockTimeSeriesMomentumFactor()
    index_factor = IndexTimeSeriesMomentumFactor()
    print("   ✓ 实例化成功")
    
    # 测试3: 获取因子信息
    print("3. 获取因子信息...")
    print(f"   个股因子名称: {stock_factor.get_factor_names()}")
    print(f"   指数因子名称: {index_factor.get_factor_names()}")
    print(f"   个股因子描述: {stock_factor.get_factor_description()}")
    
    # 测试4: 计算个股因子（小样本）
    print("4. 计算个股因子（小样本）...")
    stock_results = stock_factor.calculate_factor(
        securities=['000001.SZ'],
        start_date='2024-01-01',
        end_date='2024-01-10'
    )
    print(f"   ✓ 个股因子计算完成，结果形态: {stock_results.shape}")
    if not stock_results.empty:
        print(f"   包含列: {list(stock_results.columns)}")
        print(f"   前几行数据:")
        print(stock_results.head(3).to_string())
    
    # 测试5: 计算指数因子（小样本）
    print("5. 计算指数因子（小样本）...")
    index_results = index_factor.calculate_factor(
        index_code='000300.SH',
        start_date='2024-01-01',
        end_date='2024-01-10'
    )
    print(f"   ✓ 指数因子计算完成，结果形态: {index_results.shape}")
    if not index_results.empty:
        print(f"   包含列: {list(index_results.columns)}")
        print(f"   前几行数据:")
        print(index_results.head(3).to_string())
    
    # 测试6: factor_mining集成
    print("6. 测试factor_mining集成...")
    try:
        from factor_analyze.factor_mining.time_series_momentum_integration import (
            calculate_time_series_momentum_factor
        )
        print("   ✓ factor_mining集成模块导入成功")
        
        # 测试统一接口
        unified_results = calculate_time_series_momentum_factor(
            target_type='stock',
            target_codes=['000001.SZ'],
            start_date='2024-01-01',
            end_date='2024-01-05'
        )
        print(f"   ✓ 统一接口调用成功，结果形态: {unified_results.shape}")
        
    except Exception as e:
        print(f"   ✗ factor_mining集成测试失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 时间序列动量因子验证完成")
    print("\n主要功能:")
    print("✓ 支持个股和指数的时间序列动量因子计算")
    print("✓ 基于网页描述实现TSMOM算法")
    print("✓ 使用模拟数据适配器（mock_开头）")
    print("✓ 可被factor_mining模块调用")
    print("✓ 遵循项目标准接口规范")
    print("\n原始网页: https://factors.directory/zh/factors/momentum/time-series-momentum")
    
except Exception as e:
    print(f"✗ 验证失败: {e}")
    import traceback
    traceback.print_exc()
