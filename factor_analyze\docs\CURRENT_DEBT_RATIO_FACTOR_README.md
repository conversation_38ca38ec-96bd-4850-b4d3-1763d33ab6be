# 短期偿债压力比率因子 (Current Debt Ratio Factor)

## 概述

短期偿债压力比率因子是基于 [Factors Directory](https://factors.directory/zh/factors/basic-surface/current-debt-ratio) 网页描述实现的基础面因子，用于衡量企业对短期融资的依赖程度和短期财务风险。

## 因子定义

### 公式
```
短期偿债压力比率 = 短期负债 / 总负债
Current Debt Ratio = Current Liabilities / Total Liabilities
```

### 指标说明
- **短期负债 (Current Liabilities)**: 企业在最近一个财务报告期末需在一年内（或一个营业周期内）偿还的债务总额，主要包括短期借款、应付账款、应付票据等
- **总负债 (Total Liabilities)**: 企业在最近一个财务报告期末的全部负债总额，包括流动负债和非流动负债

## 因子解释

该比率量化了企业利用短期负债进行经营活动的程度：

- **较高的比率**: 可能意味着企业更依赖短期融资，在经济波动或信贷紧缩时面临更大的偿债压力
- **较低的比率**: 表明企业负债结构相对稳健，长期负债占比较高
- **投资意义**: 该指标是评估企业短期财务风险和偿债能力的重要指标之一

## 适用标的

### 1. 个股 (CurrentDebtRatioFactorForStocks)
- 适用于所有上市公司
- 用于评估企业财务稳健性
- 在跨行业比较时需要谨慎，不同行业的合理范围可能不同

### 2. 指数 (CurrentDebtRatioFactorForIndices)
- 行业指数：可聚合计算行业平均水平
- 市场指数：可聚合计算市场整体水平
- 通过成分股加权聚合得到指数层面的因子值

## 实现特点

### 数据来源
- **真实数据**: 优先使用Tushare Pro API获取上市公司资产负债表数据
- **模拟数据**: 当真实数据不可用时，自动生成模拟财务数据进行测试

### 核心功能
1. **数据获取**: 从财务报表中提取流动负债和总负债数据
2. **因子计算**: 计算短期偿债压力比率
3. **数据清洗**: 处理异常值和缺失值
4. **指数聚合**: 对指数成分股进行加权聚合

### 质量控制
- 异常值过滤：比率值限制在0-1之间
- 极端值处理：使用1%-99%分位数截断
- 数据完整性检查：确保关键财务数据的有效性

## 使用方法

### 基本用法

```python
from factor_analyze.fundamental_factors.current_debt_ratio_factor import calculate_current_debt_ratio_factor

# 计算个股因子
stock_results = calculate_current_debt_ratio_factor(
    instruments=['000001.SZ', '000002.SZ', '600000.SH'],
    start_date='2023-01-01',
    end_date='2024-12-31',
    target_type='stock'
)

# 计算指数因子
index_results = calculate_current_debt_ratio_factor(
    instruments=['000300.SH', '000905.SH'],
    start_date='2023-01-01',
    end_date='2024-12-31',
    target_type='index'
)
```

### 高级配置

```python
config = {
    'lookback_quarters': 8,           # 回看季度数
    'min_quarters': 4,                # 最小季度数
    'outlier_threshold': 3,           # 异常值阈值
    'min_ratio': 0.0,                 # 最小比率阈值
    'max_ratio': 1.0,                 # 最大比率阈值
    'calculation_method': 'latest',   # 计算方法：latest, average
    'aggregation_method': 'weighted', # 聚合方法：weighted, equal
    'quality_filters': {
        'min_market_cap': 1e8,        # 最小市值过滤
        'exclude_st': True,           # 排除ST股票
    }
}

results = calculate_current_debt_ratio_factor(
    instruments=['000001.SZ'],
    start_date='2023-01-01',
    end_date='2024-12-31',
    target_type='stock',
    config=config
)
```

## 输出格式

返回的DataFrame包含以下列：

### 个股因子
- `symbol`: 股票代码
- `date`: 财务数据日期
- `current_debt_ratio`: 短期偿债压力比率

### 指数因子
- `symbol`: 指数代码
- `date`: 计算日期
- `current_debt_ratio`: 指数短期偿债压力比率
- `constituent_count`: 成分股数量
- `coverage_ratio`: 成分股覆盖率

## 因子应用

### 量化策略中的应用
1. **风险管理**: 筛选财务稳健的股票，避免短期偿债压力过大的公司
2. **行业比较**: 比较不同行业的短期偿债压力水平
3. **时间序列分析**: 跟踪企业负债结构的变化趋势
4. **多因子模型**: 作为财务质量因子的重要组成部分

### 投资决策参考
- **价值投资**: 偏好短期偿债压力较低的稳健企业
- **风险评估**: 评估企业在经济下行周期的抗风险能力
- **行业轮动**: 基于行业整体短期偿债压力进行配置调整

## 注意事项

1. **行业差异**: 不同行业的商业模式和资金需求不同，该指标的合理范围存在差异
2. **经济周期**: 在不同经济周期下，该指标的重要性和解释意义可能发生变化
3. **数据质量**: 依赖财务报表数据的准确性和及时性
4. **极端值**: 某些特殊情况下可能出现异常值，需要结合其他指标综合判断

## 技术实现

### 文件结构
- `current_debt_ratio_factor.py`: 主要实现文件
- `CURRENT_DEBT_RATIO_FACTOR_README.md`: 说明文档

### 依赖项
- pandas: 数据处理
- numpy: 数值计算
- tushare: 数据获取（可选）
- adapters.base_data_adapter: 数据适配器
- factor_core.base_interfaces: 基础接口

### 集成方式
该因子遵循项目的标准因子接口，可以被 `factor_mining` 模块直接调用和集成。

## 更新日志

- 2025-01-27: 初始版本实现
  - 支持个股和指数两种标的类型
  - 集成真实数据和模拟数据
  - 完整的数据清洗和异常值处理
  - 符合项目标准接口规范 