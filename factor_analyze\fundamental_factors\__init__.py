# -*- coding: utf-8 -*-
"""
Fundamental Factors Module
基本面因子模块

This module contains fundamental analysis factors including:
- Operating cash flow factors
- Profitability factors
- Valuation factors
- Growth factors
- Quality factors
"""

# 导入主要的因子类
try:
    from .operating_cash_flow_to_market_value_factor import (
        StockOperatingCashFlowToMarketValueFactor,
        IndexOperatingCashFlowToMarketValueFactor,
        OperatingCashFlowToMarketValueFactor
    )
except ImportError as e:
    print(f"[WARNING] 无法导入现金流因子: {e}")
    StockOperatingCashFlowToMarketValueFactor = None
    IndexOperatingCashFlowToMarketValueFactor = None
    OperatingCashFlowToMarketValueFactor = None

__all__ = [
    'StockOperatingCashFlowToMarketValueFactor',
    'IndexOperatingCashFlowToMarketValueFactor',
    'OperatingCashFlowToMarketValueFactor'
] 