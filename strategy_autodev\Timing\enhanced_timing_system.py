# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强型择时系统 - Research与Timing模块协作
=========================================

本脚本整合了 Research 模块的多种分析工具，为 Timing 模块提供一个增强的、多维度的择时决策系统。

核心功能:
1. 技术指标信号 - 运用多种传统技术指标（RSI, MACD等）。
2. 趋势动量分析 - 结合 Research 模块分析价格趋势和动量。
3. SVM择时模型 - 利用支持向量机进行择时预测。
4. 市场宽度分析 - 评估整体市场情绪和健康度。

系统特点:
- 模块化设计，易于扩展。
- 可配置的信号权重。
- 自动化的数据获取和处理。
- 集成化的结果展示和性能评估。

项目名: Timing Enhancement System
创建日期: 2025-01-29
版本: 1.0.0
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
import warnings
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入Timing模块组件
from strategy_autodev.Timing.timing_indicators import TimingIndicators, TimingSignals, MarketRegimeDetector

# 导入基础定时器
try:
    from .base_timer import BaseTimer
    from .timing_core import TimingConfig, TimingSignal
except ImportError:
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
    
    class TimingConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class TimingSignal:
        def __init__(self, timestamp, signal, strength, strategy, metadata):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata

# 导入Research模块增强组件
RESEARCH_AVAILABLE = False
try:
    # 尝试直接导入特定模块，避免从__init__.py导入
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'research'))
    
    # 逐个尝试导入
    try:
        import trend_momentum_analyzer
        TrendMomentumAnalyzer = trend_momentum_analyzer.TrendMomentumAnalyzer
    except ImportError:
        TrendMomentumAnalyzer = None
        
    try:
        import market_breadth_analyzer
        MarketBreadthAnalyzer = market_breadth_analyzer.MarketBreadthAnalyzer
        MarketBreadthConfig = market_breadth_analyzer.MarketBreadthConfig
    except ImportError:
        MarketBreadthAnalyzer = None
        MarketBreadthConfig = None
        
    try:
        from research.timing_analysis import svm_timing_analyzer
        SVMTimingAnalyzer = svm_timing_analyzer.SVMTimingAnalyzer
        SVMTimingConfig = svm_timing_analyzer.SVMTimingConfig
    except ImportError:
        SVMTimingAnalyzer = None
        SVMTimingConfig = None
    
    # 检查是否至少有一个模块可用
    if any([TrendMomentumAnalyzer, MarketBreadthAnalyzer, SVMTimingAnalyzer]):
        RESEARCH_AVAILABLE = True
        print("Research模块部分功能已加载")
    else:
        print("警告：无法导入Research模块，将使用基础择时功能")
        
except Exception as e:
    RESEARCH_AVAILABLE = False
    print(f"警告：导入Research模块时出错：{e}")
    print("将使用基础择时功能")

# 导入数据适配器
try:
    from adapters.stock_data_adapter import StockDataAdapter
except ImportError:
    from adapters import StockDataAdapter

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


@dataclass
class EnhancedTimingConfig:
    """增强择时系统配置"""
    # 基础择时参数
    use_technical_signals: bool = True
    use_trend_momentum: bool = True
    use_market_breadth: bool = True
    use_svm_timing: bool = True
    
    # 信号权重配置
    signal_weights: Dict[str, float] = None
    
    # 趋势动量参数
    trend_normalization: str = 'compound'
    trend_ma_window: int = 5
    
    # 市场宽度参数
    breadth_bias_periods: List[int] = None
    breadth_positive_threshold: float = 0.0
    
    # SVM参数
    svm_kernel: str = 'rbf'
    svm_return_threshold: float = 0.02
    
    # 综合信号参数
    signal_threshold: float = 0.6
    risk_adjustment: bool = True
    
    def __post_init__(self):
        if self.signal_weights is None:
            self.signal_weights = {
                'technical': 0.25,
                'trend': 0.25,
                'breadth': 0.25,
                'svm': 0.25
            }
        if self.breadth_bias_periods is None:
            self.breadth_bias_periods = [20, 60]


@dataclass
class EnhancedTimingResult:
    """增强择时分析结果"""
    timestamp: datetime
    symbol: str
    
    # 各类信号
    technical_signals: Dict[str, pd.Series]
    trend_signal: pd.Series
    market_breadth_signal: pd.Series
    svm_signal: pd.Series
    
    # 综合信号
    composite_signal: pd.Series
    signal_strength: pd.Series
    
    # 分析结果
    trend_analysis: Optional[Dict] = None
    breadth_analysis: Optional[Dict] = None
    svm_analysis: Optional[Dict] = None
    
    # 择时建议
    current_position: str = "neutral"
    recommendations: List[str] = None
    risk_level: str = "medium"
    
    # 性能指标
    performance_metrics: Dict[str, float] = None


class EnhancedTimingSystem(BaseTimer):
    """
    增强择时系统
    
    整合Research模块的先进分析工具，提供多维度智能择时功能。
    """
    
    def __init__(self, config: Optional[EnhancedTimingConfig] = None):
        """
        初始化增强择时系统
        
        Args:
            config: 系统配置
        """
        self.enhanced_config = config or EnhancedTimingConfig()
        
        # 初始化基类
        super().__init__(
            TimingConfig(
                lookback_period=60,
                signal_threshold=self.enhanced_config.signal_threshold
            ),
            required_columns=['open', 'high', 'low', 'close', 'volume'],
            min_history=60
        )
        
        # 初始化基础组件
        self.timing_indicators = TimingIndicators()
        self.timing_signals = TimingSignals()
        self.regime_detector = MarketRegimeDetector()
        
        # 初始化Research增强组件
        if RESEARCH_AVAILABLE:
            self.trend_analyzer = TrendMomentumAnalyzer() if TrendMomentumAnalyzer else None
            self.breadth_analyzer = MarketBreadthAnalyzer() if MarketBreadthAnalyzer else None
            self.svm_analyzer = None  # 按需初始化
        else:
            self.trend_analyzer = None
            self.breadth_analyzer = None
            self.svm_analyzer = None
        
        # 数据适配器
        self.data_adapter = StockDataAdapter()
        
        # 分析历史
        self.analysis_history: List[EnhancedTimingResult] = []
        
        print("增强择时系统初始化完成。")
    
    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法：基于最新数据计算信号"""
        if not self.validate_data(data):
            raise ValueError("数据验证失败")
        
        # 使用最近60天数据进行分析
        window_data = data.tail(60)
        
        # 1. 技术指标信号
        technical_signals = self._generate_technical_signals(window_data)
        
        # 2. 趋势信号
        trend_signal, _ = self._generate_trend_signal(window_data)
        
        # 3. 市场宽度信号
        breadth_signal, _ = self._generate_breadth_signal(window_data, "")
        
        # 4. SVM信号
        svm_signal, _ = self._generate_svm_signal(window_data)
        
        # 5. 信号融合
        composite_signal, signal_strength = self._fuse_signals(
            technical_signals, trend_signal, breadth_signal, svm_signal
        )
        
        # 获取最新信号
        latest_signal = int(composite_signal.iloc[-1]) if len(composite_signal) > 0 else 0
        latest_strength = float(signal_strength.iloc[-1]) if len(signal_strength) > 0 else 0.5
        
        # 应用阈值
        strength = self.apply_threshold(latest_strength)
        
        return TimingSignal(
            timestamp=datetime.now(),
            signal=latest_signal,
            strength=strength,
            strategy="EnhancedTimingSystem",
            metadata={
                'technical_count': len(technical_signals),
                'trend_available': len(trend_signal) > 0,
                'breadth_available': len(breadth_signal) > 0,
                'svm_available': len(svm_signal) > 0
            }
        )
    
    def analyze(self, symbol: str, start_date: str, end_date: str,
                data: Optional[pd.DataFrame] = None) -> EnhancedTimingResult:
        """
        执行增强择时分析
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            data: 可选的数据输入
            
        Returns:
            增强择时分析结果
        """
        print(f"\n🚀 开始增强择时分析: {symbol}")
        print("=" * 60)
        
        # 获取数据
        if data is None:
            print("📊 获取股票数据...")
            data = self.data_adapter.get_stock_data(symbol, start_date, end_date)
            if data.empty:
                raise ValueError("无法获取股票数据")
        
        # 1. 技术指标信号
        technical_signals = self._generate_technical_signals(data)
        
        # 2. 趋势动量信号
        trend_signal, trend_analysis = self._generate_trend_signal(data)
        
        # 3. 市场宽度信号（需要市场数据）
        breadth_signal, breadth_analysis = self._generate_breadth_signal(data, symbol)
        
        # 4. SVM机器学习信号
        svm_signal, svm_analysis = self._generate_svm_signal(data)
        
        # 5. 综合信号融合
        composite_signal, signal_strength = self._fuse_signals(
            technical_signals, trend_signal, breadth_signal, svm_signal
        )
        
        # 6. 生成择时建议
        current_position, recommendations = self._generate_recommendations(
            composite_signal, signal_strength, trend_analysis, breadth_analysis
        )
        
        # 7. 计算性能指标
        performance_metrics = self._calculate_performance_metrics(
            composite_signal, data
        )
        
        # 创建结果对象
        result = EnhancedTimingResult(
            timestamp=datetime.now(),
            symbol=symbol,
            technical_signals=technical_signals,
            trend_signal=trend_signal,
            market_breadth_signal=breadth_signal,
            svm_signal=svm_signal,
            composite_signal=composite_signal,
            signal_strength=signal_strength,
            trend_analysis=trend_analysis,
            breadth_analysis=breadth_analysis,
            svm_analysis=svm_analysis,
            current_position=current_position,
            recommendations=recommendations,
            risk_level=self._assess_risk_level(signal_strength, breadth_analysis),
            performance_metrics=performance_metrics
        )
        
        # 保存到历史
        self.analysis_history.append(result)
        
        # 打印分析摘要
        self._print_analysis_summary(result)
        
        return result
    
    def _generate_technical_signals(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成技术指标信号"""
        if not self.enhanced_config.use_technical_signals:
            return {}
        
        print("\n📈 计算技术指标信号...")
        
        signals = {}
        
        # RSI信号
        signals['rsi'] = self.timing_signals.rsi_signals(data['close'])
        
        # MACD信号
        signals['macd'] = self.timing_signals.macd_signals(data['close'])
        
        # 布林带信号
        signals['bollinger'] = self.timing_signals.bollinger_signals(data['close'])
        
        # 随机指标信号
        if all(col in data.columns for col in ['high', 'low', 'close']):
            signals['stochastic'] = self.timing_signals.stochastic_signals(
                data['high'], data['low'], data['close']
            )
        
        print(f"共生成 {len(signals)} 个技术指标信号。")
        
        return signals
    
    def _generate_trend_signal(self, data: pd.DataFrame) -> Tuple[pd.Series, Dict]:
        """生成趋势动量信号"""
        if not self.enhanced_config.use_trend_momentum or not RESEARCH_AVAILABLE or not self.trend_analyzer:
            return pd.Series(0, index=data.index), {}
        
        print("\n📊 分析趋势动量...")
        
        # 使用趋势动量分析器
        analysis_result = self.trend_analyzer.analyze(
            data['close'],
            normalization_method=self.enhanced_config.trend_normalization,
            ma_window=self.enhanced_config.trend_ma_window
        )
        
        # 基于趋势得分生成信号
        trend_score = analysis_result.analysis_summary['ultimate_score']
        trend_direction = analysis_result.analysis_summary['trend_direction']
        
        # 生成滚动趋势信号
        window = 60
        trend_signals = pd.Series(0, index=data.index)
        
        for i in range(window, len(data)):
            window_data = data['close'].iloc[i-window:i]
            try:
                window_result = self.trend_analyzer.analyze(window_data)
                score = window_result.analysis_summary['ultimate_score']
                
                # 基于得分生成信号
                if score > 0.5 and window_result.analysis_summary['trend_direction'] == 'up':
                    trend_signals.iloc[i] = 1
                elif score > 0.5 and window_result.analysis_summary['trend_direction'] == 'down':
                    trend_signals.iloc[i] = -1
            except:
                continue
        
        trend_analysis = {
            'trend_score': trend_score,
            'trend_direction': trend_direction,
            'trend_strength': analysis_result.analysis_summary['trend_strength'],
            'volatility': analysis_result.analysis_summary['volatility']
        }
        
        print(f"趋势分析完成 - 方向: {trend_direction}, 强度: {trend_score:.3f}")
        
        return trend_signals, trend_analysis
    
    def _generate_breadth_signal(self, data: pd.DataFrame, symbol: str) -> Tuple[pd.Series, Dict]:
        """生成市场宽度信号"""
        if not self.enhanced_config.use_market_breadth or not RESEARCH_AVAILABLE or not self.breadth_analyzer:
            return pd.Series(0, index=data.index), {}
        
        print("\n🌍 分析市场宽度...")
        
        # 这里简化处理，实际应用需要获取市场数据
        # 使用个股的技术指标作为市场宽度的代理
        bias_20 = self.breadth_analyzer.calculate_bias(data, 20)
        rsi = self.breadth_analyzer.calculate_rsi(data, 14)
        
        # 生成宽度信号
        breadth_signals = pd.Series(0, index=data.index)
        
        # 基于bias和RSI生成信号
        breadth_signals[(bias_20 > 5) & (rsi > 70)] = -1  # 超买
        breadth_signals[(bias_20 < -5) & (rsi < 30)] = 1   # 超卖
        
        breadth_analysis = {
            'current_bias': bias_20.iloc[-1] if len(bias_20) > 0 else 0,
            'current_rsi': rsi.iloc[-1] if len(rsi) > 0 else 50,
            'market_state': 'overbought' if rsi.iloc[-1] > 70 else 'oversold' if rsi.iloc[-1] < 30 else 'neutral'
        }
        
        print(f"市场宽度分析完成 - 状态: {breadth_analysis['market_state']}")
        
        return breadth_signals, breadth_analysis
    
    def _generate_svm_signal(self, data: pd.DataFrame) -> Tuple[pd.Series, Dict]:
        """生成SVM机器学习信号"""
        if not self.enhanced_config.use_svm_timing or not RESEARCH_AVAILABLE or not SVMTimingAnalyzer:
            return pd.Series(0, index=data.index), {}
        
        print("\n🤖 训练SVM择时模型...")
        
        # 初始化SVM分析器
        if self.svm_analyzer is None:
            svm_config = SVMTimingConfig(
                kernel=self.enhanced_config.svm_kernel,
                return_threshold=self.enhanced_config.svm_return_threshold,
                classification_method='binary',
                train_test_split_ratio=0.8
            )
            self.svm_analyzer = SVMTimingAnalyzer(svm_config)
        
        try:
            # 执行SVM分析
            svm_result = self.svm_analyzer.analyze(data)
            
            svm_analysis = {
                'model_score': svm_result.test_score,
                'current_signal': int(svm_result.timing_signals.iloc[-1]) if len(svm_result.timing_signals) > 0 else 0,
                'signal_confidence': float(svm_result.signal_strength.iloc[-1]) if len(svm_result.signal_strength) > 0 else 0.5
            }
            
            print(f"SVM模型训练完成 - 测试得分: {svm_result.test_score:.3f}")
            
            return svm_result.timing_signals, svm_analysis
            
        except Exception as e:
            print(f"⚠️ SVM分析失败: {e}")
            return pd.Series(0, index=data.index), {}
    
    def _fuse_signals(self, technical_signals: Dict[str, pd.Series],
                     trend_signal: pd.Series,
                     breadth_signal: pd.Series,
                     svm_signal: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """融合多种信号"""
        print("\n🔄 融合多维度信号...")
        
        # 准备信号数据
        all_signals = []
        weights = []
        
        # 技术指标信号
        if technical_signals:
            tech_consensus = self.timing_signals.multiple_signals_consensus(technical_signals)
            all_signals.append(tech_consensus)
            weights.append(self.enhanced_config.signal_weights['technical'])
        
        # 趋势信号
        if len(trend_signal) > 0:
            all_signals.append(trend_signal)
            weights.append(self.enhanced_config.signal_weights['trend'])
        
        # 市场宽度信号
        if len(breadth_signal) > 0:
            all_signals.append(breadth_signal)
            weights.append(self.enhanced_config.signal_weights['breadth'])
        
        # SVM信号
        if len(svm_signal) > 0:
            all_signals.append(svm_signal)
            weights.append(self.enhanced_config.signal_weights['svm'])
        
        if not all_signals:
            return pd.Series(0, index=trend_signal.index), pd.Series(0.5, index=trend_signal.index)
        
        # 对齐所有信号
        aligned_signals = pd.DataFrame(all_signals).T.fillna(0)
        
        # 加权平均
        weights_array = np.array(weights) / sum(weights)
        composite_score = aligned_signals.dot(weights_array)
        
        # 生成最终信号
        composite_signal = pd.Series(0, index=composite_score.index)
        composite_signal[composite_score > self.enhanced_config.signal_threshold] = 1
        composite_signal[composite_score < -self.enhanced_config.signal_threshold] = -1
        
        # 信号强度
        signal_strength = composite_score.abs()
        
        print(f"信号融合完成 - 当前信号: {int(composite_signal.iloc[-1])}")
        
        return composite_signal, signal_strength
    
    def _generate_recommendations(self, composite_signal: pd.Series,
                                signal_strength: pd.Series,
                                trend_analysis: Dict,
                                breadth_analysis: Dict) -> Tuple[str, List[str]]:
        """生成择时建议"""
        recommendations = []
        
        # 当前信号
        current_signal = int(composite_signal.iloc[-1]) if len(composite_signal) > 0 else 0
        current_strength = float(signal_strength.iloc[-1]) if len(signal_strength) > 0 else 0.5
        
        # 确定持仓建议
        if current_signal == 1:
            current_position = "买入" if current_strength > 0.7 else "逐步建仓"
        elif current_signal == -1:
            current_position = "卖出" if current_strength > 0.7 else "减仓"
        else:
            current_position = "观望"
        
        # 基于趋势的建议
        if trend_analysis:
            if trend_analysis['trend_direction'] == 'up' and trend_analysis['trend_strength'] > 0.6:
                recommendations.append("趋势向上明确，可积极操作。")
            elif trend_analysis['trend_direction'] == 'down' and trend_analysis['trend_strength'] > 0.6:
                recommendations.append("趋势向下明显，建议控制风险。")
        
        # 基于市场宽度的建议
        if breadth_analysis:
            if breadth_analysis['market_state'] == 'overbought':
                recommendations.append("市场超买，注意回调风险。")
            elif breadth_analysis['market_state'] == 'oversold':
                recommendations.append("市场超卖，关注反弹机会。")
        
        # 基于信号强度的建议
        if current_strength > 0.8:
            recommendations.append(f"信号强度高({current_strength:.2f})，可提高仓位比例。")
        elif current_strength < 0.5:
            recommendations.append(f"信号强度低({current_strength:.2f})，建议谨慎操作。")
        
        return current_position, recommendations
    
    def _assess_risk_level(self, signal_strength: pd.Series, breadth_analysis: Dict) -> str:
        """评估风险等级"""
        current_strength = float(signal_strength.iloc[-1]) if len(signal_strength) > 0 else 0.5
        
        # 基于信号强度和市场状态评估风险
        if breadth_analysis and breadth_analysis.get('market_state') == 'overbought':
            return "高"
        elif current_strength < 0.4:
            return "中高"
        elif current_strength > 0.7:
            return "低"
        else:
            return "中"
    
    def _calculate_performance_metrics(self, signals: pd.Series, data: pd.DataFrame) -> Dict[str, float]:
        """计算性能指标"""
        if len(signals) < 2:
            return {}
        
        # 计算收益率
        returns = data['close'].pct_change()
        
        # 策略收益
        strategy_returns = returns * signals.shift(1)
        
        # 性能指标
        metrics = {
            'total_return': (1 + strategy_returns).prod() - 1,
            'annual_return': (1 + strategy_returns.mean()) ** 252 - 1,
            'volatility': strategy_returns.std() * np.sqrt(252),
            'sharpe_ratio': strategy_returns.mean() / strategy_returns.std() * np.sqrt(252) if strategy_returns.std() > 0 else 0,
            'max_drawdown': (strategy_returns.cumsum() - strategy_returns.cumsum().cummax()).min(),
            'win_rate': (strategy_returns > 0).sum() / (strategy_returns != 0).sum() if (strategy_returns != 0).sum() > 0 else 0
        }
        
        return metrics
    
    def _print_analysis_summary(self, result: EnhancedTimingResult):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("📊 增强择时分析摘要")
        print("="*60)
        print(f"股票代码: {result.symbol}")
        print(f"分析时间: {result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\n当前择时建议: 【{result.current_position}】")
        print(f"风险等级: {result.risk_level}")
        
        if result.trend_analysis:
            print(f"\n趋势分析:")
            print(f"  - 方向: {result.trend_analysis['trend_direction']}")
            print(f"  - 强度: {result.trend_analysis['trend_strength']:.3f}")
        
        if result.breadth_analysis:
            print(f"\n市场宽度:")
            print(f"  - 状态: {result.breadth_analysis['market_state']}")
            print(f"  - RSI: {result.breadth_analysis['current_rsi']:.2f}")
        
        if result.svm_analysis:
            print(f"\nSVM模型:")
            print(f"  - 信号: {result.svm_analysis['current_signal']}")
            print(f"  - 置信度: {result.svm_analysis['signal_confidence']:.3f}")
        
        if result.recommendations:
            print(f"\n具体建议:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"  {i}. {rec}")
        
        if result.performance_metrics:
            print(f"\n历史表现:")
            print(f"  - 总收益: {result.performance_metrics.get('total_return', 0)*100:.2f}%")
            print(f"  - 夏普比率: {result.performance_metrics.get('sharpe_ratio', 0):.3f}")
            print(f"  - 胜率: {result.performance_metrics.get('win_rate', 0)*100:.1f}%")
        
        print("="*60)
    
    def plot_analysis(self, result: EnhancedTimingResult, data: pd.DataFrame,
                     save_path: Optional[str] = None):
        """绘制增强择时分析图表"""
        fig, axes = plt.subplots(4, 1, figsize=(15, 12), sharex=True)
        
        # 1. 价格和信号
        ax1 = axes[0]
        ax1.plot(data.index, data['close'], 'k-', linewidth=1, label='收盘价')
        
        # 标记买卖信号
        buy_signals = result.composite_signal == 1
        sell_signals = result.composite_signal == -1
        
        ax1.scatter(data.index[buy_signals], data['close'][buy_signals],
                   color='green', marker='^', s=100, label='买入信号', zorder=5)
        ax1.scatter(data.index[sell_signals], data['close'][sell_signals],
                   color='red', marker='v', s=100, label='卖出信号', zorder=5)
        
        ax1.set_ylabel('价格')
        ax1.legend(loc='best')
        ax1.set_title(f'{result.symbol} - 增强择时分析')
        ax1.grid(True, alpha=0.3)
        
        # 2. 信号强度
        ax2 = axes[1]
        ax2.fill_between(data.index, 0, result.signal_strength, 
                        where=result.composite_signal==1, color='green', alpha=0.3, label='买入强度')
        ax2.fill_between(data.index, 0, result.signal_strength, 
                        where=result.composite_signal==-1, color='red', alpha=0.3, label='卖出强度')
        ax2.axhline(y=self.config.signal_threshold, color='gray', linestyle='--', label='信号阈值')
        ax2.set_ylabel('信号强度')
        ax2.legend(loc='best')
        ax2.grid(True, alpha=0.3)
        
        # 3. 技术指标
        ax3 = axes[2]
        if 'rsi' in result.technical_signals:
            rsi = self.timing_indicators.rsi(data['close'])
            ax3.plot(data.index, rsi, 'b-', label='RSI')
            ax3.axhline(y=70, color='r', linestyle='--', alpha=0.5)
            ax3.axhline(y=30, color='g', linestyle='--', alpha=0.5)
        ax3.set_ylabel('RSI')
        ax3.legend(loc='best')
        ax3.grid(True, alpha=0.3)
        
        # 4. 累计收益
        ax4 = axes[3]
        returns = data['close'].pct_change()
        strategy_returns = returns * result.composite_signal.shift(1)
        cumulative_returns = (1 + strategy_returns).cumprod() - 1
        benchmark_returns = (1 + returns).cumprod() - 1
        
        ax4.plot(data.index, cumulative_returns * 100, 'b-', label='策略收益')
        ax4.plot(data.index, benchmark_returns * 100, 'gray', label='买入持有')
        ax4.set_ylabel('累计收益率(%)')
        ax4.set_xlabel('日期')
        ax4.legend(loc='best')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"\n📊 图表已保存至: {save_path}")
        
        plt.show()


# 便捷函数
def create_enhanced_timing_system(
    use_trend: bool = True,
    use_breadth: bool = True,
    use_svm: bool = True,
    signal_weights: Optional[Dict[str, float]] = None
) -> EnhancedTimingSystem:
    """
    创建增强择时系统
    
    Args:
        use_trend: 是否使用趋势动量分析
        use_breadth: 是否使用市场宽度分析
        use_svm: 是否使用SVM机器学习
        signal_weights: 信号权重配置
        
    Returns:
        增强择时系统实例
    """
    config = EnhancedTimingConfig(
        use_trend_momentum=use_trend,
        use_market_breadth=use_breadth,
        use_svm_timing=use_svm,
        signal_weights=signal_weights
    )
    
    return EnhancedTimingSystem(config)


def demo_enhanced_timing(symbol: str = "000001.SZ",
                        start_date: str = "2023-01-01",
                        end_date: str = "2024-01-01"):
    """
    演示增强择时系统
    
    Args:
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    """
    print("\n" + "="*70)
    print(f"🎬 开始演示增强择时系统 - {symbol}")
    print("="*70)
    
    try:
        # 创建系统
        system = create_enhanced_timing_system(use_svm=False) # 演示中默认关闭SVM
        
        # 获取数据并分析
        data = system.data_adapter.get_stock_data(symbol, start_date, end_date)
        if data.empty:
            print(f"错误：无法获取 {symbol} 的数据。")
            return
            
        result = system.analyze(symbol, start_date, end_date, data=data)
        
        # 绘制图表
        system.plot_analysis(result, data)
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    demo_enhanced_timing()
