# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试动量因子功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from factor_analyze.fundamental_factors.momentum_based_on_ranking_factor import (
        StockMomentumRankingFactor, 
        IndexMomentumRankingFactor
    )
    print("成功导入动量因子")
except Exception as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_stock_factor():
    """测试个股动量因子"""
    print("\n=== 测试个股动量因子 ===")
    
    try:
        # 初始化因子
        factor = StockMomentumRankingFactor()
        print("个股因子初始化成功")
        
        # 测试计算
        result = factor.calculate_factor(
            securities=['000001.SZ', '000002.SZ'],
            start_date='2024-01-01',
            end_date='2024-01-31'
        )
        
        if not result.empty:
            print(f"因子计算成功，结果行数: {len(result)}")
            print(f"  列名: {list(result.columns)}")
            if 'momentum_ranking_factor' in result.columns:
                print(f"  因子值范围: [{result['momentum_ranking_factor'].min():.4f}, {result['momentum_ranking_factor'].max():.4f}]")
        else:
            print("因子计算结果为空")
            
        # 测试mine_factors接口
        mine_result = factor.mine_factors(['000001.SZ', '000002.SZ'])
        print(f"mine_factors接口测试成功，返回类型: {type(mine_result)}")
        
        return True
        
    except Exception as e:
        print(f"个股因子测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_factor():
    """测试指数动量因子"""
    print("\n=== 测试指数动量因子 ===")
    
    try:
        # 初始化因子
        factor = IndexMomentumRankingFactor()
        print("指数因子初始化成功")
        
        # 测试计算
        result = factor.calculate_factor(
            index_codes=['000300.SH'],
            start_date='2024-01-01',
            end_date='2024-01-31'
        )
        
        if not result.empty:
            print(f"指数因子计算成功，结果行数: {len(result)}")
            print(f"  列名: {list(result.columns)}")
            if 'momentum_ranking_factor' in result.columns:
                print(f"  因子值范围: [{result['momentum_ranking_factor'].min():.4f}, {result['momentum_ranking_factor'].max():.4f}]")
        else:
            print("指数因子计算结果为空")
            
        # 测试mine_factors接口
        mine_result = factor.mine_factors(['000300.SH'])
        print(f"mine_factors接口测试成功，返回类型: {type(mine_result)}")
        
        return True
        
    except Exception as e:
        print(f"指数因子测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试动量因子...")
    
    success_count = 0
    total_tests = 2
    
    # 测试个股因子
    if test_stock_factor():
        success_count += 1
    
    # 测试指数因子
    if test_index_factor():
        success_count += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"成功: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("所有测试通过了")
        return 0
    else:
        print("部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
