# -*- coding: utf-8 -*-
"""
çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­ä½¿ç”¨ç¤ºä¾?
Current Debt Ratio Factor Usage Example

æ¼”ç¤ºå¦‚ä½•ä½¿ç”¨çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­è¿›è¡Œé‡åŒ–åˆ†æ?
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# æ·»åŠ é¡¹ç›®è·¯å¾„
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from factor_analyze.fundamental_factors.current_debt_ratio_factor import calculate_current_debt_ratio_factor


def demo_basic_usage():
    """åŸºæœ¬ä½¿ç”¨ç¤ºä¾‹"""
    print("=" * 60)
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?- åŸºæœ¬ä½¿ç”¨ç¤ºä¾‹")
    print("=" * 60)
    
    # å®šä¹‰æµ‹è¯•è‚¡ç¥¨å’Œæ—¶é—´èŒƒå›?
    stock_list = [
        '000001.SZ',  # å¹³å®‰é“¶è¡Œ
        '000002.SZ',  # ä¸‡ç§‘A
        '600000.SH',  # æµ¦å‘é“¶è¡Œ
        '600036.SH',  # æ‹›å•†é“¶è¡Œ
        '600519.SH',  # è´µå·èŒ…å°
        '000858.SZ',  # äº”ç²®æ¶?
    ]
    
    start_date = '2023-01-01'
    end_date = '2024-12-31'
    
    print(f"æµ‹è¯•è‚¡ç¥¨: {stock_list}")
    print(f"æ—¶é—´èŒƒå›´: {start_date} è‡?{end_date}")
    print()
    
    # è®¡ç®—ä¸ªè‚¡å› å­
    results = calculate_current_debt_ratio_factor(
        instruments=stock_list,
        start_date=start_date,
        end_date=end_date,
        target_type='stock'
    )
    
    if not results.empty:
        print("âœ?å› å­è®¡ç®—æˆåŠŸ!")
        print(f"æ•°æ®å½¢çŠ¶: {results.shape}")
        print("\nå› å­æ•°æ®:")
        print(results.to_string(index=False))
        
        print(f"\nå› å­å€¼ç»Ÿè®?")
        print(results['current_debt_ratio'].describe())
        
        # æ’åºæ˜¾ç¤º
        print(f"\næŒ‰çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡æ’åº?(ä»ä½åˆ°é«˜):")
        sorted_results = results.sort_values('current_debt_ratio')
        for _, row in sorted_results.iterrows():
            ratio = row['current_debt_ratio']
            symbol = row['symbol']
            risk_level = "ä½é£é™? if ratio < 0.3 else "ä¸­é£é™? if ratio < 0.6 else "é«˜é£é™?
            print(f"  {symbol}: {ratio:.4f} ({risk_level})")
    else:
        print("âœ?å› å­è®¡ç®—å¤±è´¥")


def demo_index_usage():
    """æŒ‡æ•°å› å­ä½¿ç”¨ç¤ºä¾‹"""
    print("\n" + "=" * 60)
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?- æŒ‡æ•°ä½¿ç”¨ç¤ºä¾‹")
    print("=" * 60)
    
    # å®šä¹‰æµ‹è¯•æŒ‡æ•°
    index_list = [
        '000300.SH',  # æ²ªæ·±300
        '000905.SH',  # ä¸­è¯500
        '000852.SH',  # ä¸­è¯1000
    ]
    
    start_date = '2023-01-01'
    end_date = '2024-12-31'
    
    print(f"æµ‹è¯•æŒ‡æ•°: {index_list}")
    print(f"æ—¶é—´èŒƒå›´: {start_date} è‡?{end_date}")
    print()
    
    # è®¡ç®—æŒ‡æ•°å› å­
    results = calculate_current_debt_ratio_factor(
        instruments=index_list,
        start_date=start_date,
        end_date=end_date,
        target_type='index'
    )
    
    if not results.empty:
        print("âœ?æŒ‡æ•°å› å­è®¡ç®—æˆåŠŸ!")
        print(f"æ•°æ®å½¢çŠ¶: {results.shape}")
        print("\næŒ‡æ•°å› å­æ•°æ®:")
        print(results.to_string(index=False))
        
        # åˆ†ææŒ‡æ•°å·®å¼‚
        print(f"\næŒ‡æ•°çŸ­æœŸå¿å€ºå‹åŠ›æ¯”è¾?")
        for _, row in results.iterrows():
            symbol = row['symbol']
            ratio = row['current_debt_ratio']
            coverage = row['coverage_ratio']
            count = row['constituent_count']
            
            index_name = {
                '000300.SH': 'æ²ªæ·±300',
                '000905.SH': 'ä¸­è¯500', 
                '000852.SH': 'ä¸­è¯1000'
            }.get(symbol, symbol)
            
            print(f"  {index_name}: {ratio:.4f} (æˆåˆ†è‚? {count}, è¦†ç›–ç? {coverage:.2%})")
    else:
        print("âœ?æŒ‡æ•°å› å­è®¡ç®—å¤±è´¥")


def demo_advanced_config():
    """é«˜çº§é…ç½®ç¤ºä¾‹"""
    print("\n" + "=" * 60)
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?- é«˜çº§é…ç½®ç¤ºä¾‹")
    print("=" * 60)
    
    # è‡ªå®šä¹‰é…ç½?
    custom_config = {
        'lookback_quarters': 12,          # æ›´é•¿çš„å›çœ‹æœŸ
        'min_quarters': 6,                # æ›´é«˜çš„æ•°æ®è¦æ±?
        'outlier_threshold': 2.5,         # æ›´ä¸¥æ ¼çš„å¼‚å¸¸å€¼å¤„ç?
        'min_ratio': 0.1,                 # æœ€å°æ¯”ç‡é˜ˆå€?
        'max_ratio': 0.9,                 # æœ€å¤§æ¯”ç‡é˜ˆå€?
        'calculation_method': 'average',   # ä½¿ç”¨å¹³å‡å€¼æ–¹æ³?
        'standardize': True,              # å¯ç”¨æ ‡å‡†åŒ?
        'quality_filters': {
            'min_market_cap': 5e8,        # æœ€å°å¸‚å€?0äº?
            'exclude_st': True,           # æ’é™¤STè‚¡ç¥¨
            'min_trading_days': 220,      # æœ€å°äº¤æ˜“å¤©æ•?
            'max_suspension_ratio': 0.1   # æœ€å¤§åœç‰Œæ¯”ä¾?0%
        }
    }
    
    stock_list = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    
    print("è‡ªå®šä¹‰é…ç½®å‚æ•?")
    for key, value in custom_config.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    print()
    
    # ä½¿ç”¨è‡ªå®šä¹‰é…ç½®è®¡ç®?
    results = calculate_current_debt_ratio_factor(
        instruments=stock_list,
        start_date='2023-01-01',
        end_date='2024-12-31',
        target_type='stock',
        config=custom_config
    )
    
    if not results.empty:
        print("âœ?é«˜çº§é…ç½®å› å­è®¡ç®—æˆåŠŸ!")
        print("\nç»“æœæ•°æ®:")
        print(results.to_string(index=False))
        
        # å¦‚æœå¯ç”¨äº†æ ‡å‡†åŒ–ï¼Œæ˜¾ç¤ºæ ‡å‡†åŒ–ç»“æœ
        if 'current_debt_ratio_std' in results.columns:
            print(f"\næ ‡å‡†åŒ–å› å­å€?")
            print(results[['symbol', 'current_debt_ratio_std']].to_string(index=False))
    else:
        print("âœ?é«˜çº§é…ç½®å› å­è®¡ç®—å¤±è´¥")


def demo_factor_analysis():
    """å› å­åˆ†æç¤ºä¾‹"""
    print("\n" + "=" * 60)
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?- åˆ†æç¤ºä¾‹")
    print("=" * 60)
    
    # è®¡ç®—æ›´å¤šè‚¡ç¥¨çš„å› å­å€?
    stock_list = [
        # é“¶è¡Œè‚?
        '000001.SZ', '600000.SH', '600036.SH', '601166.SH',
        # åœ°äº§è‚? 
        '000002.SZ', '000858.SZ', '001979.SZ',
        # æ¶ˆè´¹è‚?
        '600519.SH', '000858.SZ', '002304.SZ',
        # ç§‘æŠ€è‚?
        '000063.SZ', '002415.SZ', '300059.SZ'
    ]
    
    results = calculate_current_debt_ratio_factor(
        instruments=stock_list,
        start_date='2023-01-01',
        end_date='2024-12-31',
        target_type='stock'
    )
    
    if not results.empty:
        print("âœ?å¤šè‚¡ç¥¨å› å­è®¡ç®—æˆåŠ?")
        
        # ç»Ÿè®¡åˆ†æ
        ratios = results['current_debt_ratio']
        
        print(f"\næè¿°æ€§ç»Ÿè®?")
        print(f"  æ ·æœ¬æ•°é‡: {len(ratios)}")
        print(f"  å‡å€? {ratios.mean():.4f}")
        print(f"  ä¸­ä½æ•? {ratios.median():.4f}")
        print(f"  æ ‡å‡†å·? {ratios.std():.4f}")
        print(f"  æœ€å°å€? {ratios.min():.4f}")
        print(f"  æœ€å¤§å€? {ratios.max():.4f}")
        
        # åˆ†ä½æ•°åˆ†æ?
        print(f"\nåˆ†ä½æ•°åˆ†æ?")
        for q in [0.25, 0.5, 0.75, 0.9]:
            value = ratios.quantile(q)
            print(f"  {q*100:2.0f}%åˆ†ä½æ•? {value:.4f}")
        
        # é£é™©åˆ†ç±»
        print(f"\né£é™©åˆ†ç±»:")
        low_risk = (ratios < 0.3).sum()
        medium_risk = ((ratios >= 0.3) & (ratios < 0.6)).sum()
        high_risk = (ratios >= 0.6).sum()
        
        total = len(ratios)
        print(f"  ä½é£é™?(<0.3): {low_risk} ({low_risk/total:.1%})")
        print(f"  ä¸­é£é™?(0.3-0.6): {medium_risk} ({medium_risk/total:.1%})")
        print(f"  é«˜é£é™?(>=0.6): {high_risk} ({high_risk/total:.1%})")
        
        # æ˜¾ç¤ºæç«¯å€?
        print(f"\næç«¯å€¼è¯†åˆ?")
        sorted_results = results.sort_values('current_debt_ratio')
        
        print("  çŸ­æœŸå¿å€ºå‹åŠ›æœ€ä½çš„3åªè‚¡ç¥?")
        for i, (_, row) in enumerate(sorted_results.head(3).iterrows()):
            print(f"    {i+1}. {row['symbol']}: {row['current_debt_ratio']:.4f}")
        
        print("  çŸ­æœŸå¿å€ºå‹åŠ›æœ€é«˜çš„3åªè‚¡ç¥?")
        for i, (_, row) in enumerate(sorted_results.tail(3).iterrows()):
            print(f"    {i+1}. {row['symbol']}: {row['current_debt_ratio']:.4f}")
    else:
        print("âœ?å¤šè‚¡ç¥¨å› å­è®¡ç®—å¤±è´?)


def demo_investment_application():
    """æŠ•èµ„åº”ç”¨ç¤ºä¾‹"""
    print("\n" + "=" * 60)
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?- æŠ•èµ„åº”ç”¨ç¤ºä¾‹")
    print("=" * 60)
    
    # æ¨¡æ‹Ÿä¸€ä¸ªè‚¡ç¥¨æ± 
    stock_pool = [
        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
        '000858.SZ', '002304.SZ', '000063.SZ', '002415.SZ', '300059.SZ'
    ]
    
    results = calculate_current_debt_ratio_factor(
        instruments=stock_pool,
        start_date='2023-01-01',
        end_date='2024-12-31',
        target_type='stock'
    )
    
    if not results.empty:
        print("âœ?è‚¡ç¥¨æ± å› å­è®¡ç®—æˆåŠ?")
        
        # æŠ•èµ„ç­–ç•¥1: ä½é£é™©ç­›é€?
        print(f"\nç­–ç•¥1: ä½é£é™©è‚¡ç¥¨ç­›é€?(çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç?< 0.4)")
        low_risk_stocks = results[results['current_debt_ratio'] < 0.4]
        
        if not low_risk_stocks.empty:
            print(f"  ç­›é€‰å‡º {len(low_risk_stocks)} åªä½é£é™©è‚¡ç¥¨:")
            for _, row in low_risk_stocks.iterrows():
                print(f"    {row['symbol']}: {row['current_debt_ratio']:.4f}")
        else:
            print("  æœªæ‰¾åˆ°ç¬¦åˆæ¡ä»¶çš„ä½é£é™©è‚¡ç¥?)
        
        # æŠ•èµ„ç­–ç•¥2: åˆ†å±‚ç»„åˆ
        print(f"\nç­–ç•¥2: åˆ†å±‚ç»„åˆæ„å»º")
        sorted_results = results.sort_values('current_debt_ratio')
        
        # å°†è‚¡ç¥¨åˆ†ä¸ºä¸‰å±?
        n = len(sorted_results)
        tier1 = sorted_results.iloc[:n//3]  # ä½é£é™©å±‚
        tier2 = sorted_results.iloc[n//3:2*n//3]  # ä¸­é£é™©å±‚
        tier3 = sorted_results.iloc[2*n//3:]  # é«˜é£é™©å±‚
        
        print(f"  ä½é£é™©å±‚ ({len(tier1)}å?: æƒé‡50%")
        for _, row in tier1.iterrows():
            print(f"    {row['symbol']}: {row['current_debt_ratio']:.4f}")
        
        print(f"  ä¸­é£é™©å±‚ ({len(tier2)}å?: æƒé‡30%")
        for _, row in tier2.iterrows():
            print(f"    {row['symbol']}: {row['current_debt_ratio']:.4f}")
        
        print(f"  é«˜é£é™©å±‚ ({len(tier3)}å?: æƒé‡20%")
        for _, row in tier3.iterrows():
            print(f"    {row['symbol']}: {row['current_debt_ratio']:.4f}")
        
        # æŠ•èµ„ç­–ç•¥3: é£é™©é¢„è­¦
        print(f"\nç­–ç•¥3: é£é™©é¢„è­¦ç³»ç»Ÿ")
        high_risk_threshold = 0.7
        warning_stocks = results[results['current_debt_ratio'] > high_risk_threshold]
        
        if not warning_stocks.empty:
            print(f"  âš ï¸  é«˜é£é™©é¢„è­?(çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç?> {high_risk_threshold}):")
            for _, row in warning_stocks.iterrows():
                print(f"    {row['symbol']}: {row['current_debt_ratio']:.4f} - å»ºè®®å‡ä»“æˆ–å›é?)
        else:
            print(f"  âœ?æ— é«˜é£é™©é¢„è­¦è‚¡ç¥¨")
    else:
        print("âœ?è‚¡ç¥¨æ± å› å­è®¡ç®—å¤±è´?)


if __name__ == "__main__":
    try:
        # è¿è¡Œæ‰€æœ‰ç¤ºä¾?
        demo_basic_usage()
        demo_index_usage()
        demo_advanced_config()
        demo_factor_analysis()
        demo_investment_application()
        
        print("\n" + "=" * 60)
        print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­ç¤ºä¾‹è¿è¡Œå®Œæˆ?")
        print("=" * 60)
        
    except Exception as e:
        print(f"è¿è¡Œç¤ºä¾‹æ—¶å‘ç”Ÿé”™è¯? {e}")
        import traceback
        traceback.print_exc()
