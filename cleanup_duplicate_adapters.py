#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清理重复的数据适配器定义
删除重复定义，统一使用标准实现
"""

import os
import re
from pathlib import Path

def find_duplicate_definitions():
    """查找重复的数据适配器定义"""
    
    # 要查找的适配器类名
    adapter_classes = [
        'StockDataAdapter',
        'FundamentalDataAdapter', 
        'MarketDataAdapter',
        'FactorDataAdapter'
    ]
    
    # 标准实现路径（保留）
    standard_paths = [
        'adapters/stock_data_adapter.py',
        'adapters/fundamental_data_adapter.py',
        'adapters/market_data_adapter.py',
        'adapters/factor_data_adapter.py'
    ]
    
    duplicates = {}
    
    for adapter_class in adapter_classes:
        duplicates[adapter_class] = []
        
        # 搜索所有Python文件
        for root, dirs, files in os.walk('.'):
            # 跳过某些目录
            if any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache', 'venv', 'env']):
                continue
                
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    # 跳过标准实现
                    if file_path in standard_paths:
                        continue
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 查找类定义
                        pattern = rf'class\s+{adapter_class}\s*[:\(]'
                        if re.search(pattern, content):
                            duplicates[adapter_class].append(file_path)
                            
                    except Exception as e:
                        print(f"读取文件 {file_path} 时出错: {e}")
    
    return duplicates

def analyze_duplicates(duplicates):
    """分析重复定义"""
    
    print("=" * 80)
    print("重复数据适配器定义分析")
    print("=" * 80)
    
    total_duplicates = 0
    
    for adapter_class, files in duplicates.items():
        if files:
            print(f"\n📋 {adapter_class}:")
            print(f"   发现 {len(files)} 个重复定义:")
            
            for file_path in files:
                # 判断文件类型
                if 'architecture_backup' in file_path:
                    file_type = "🗑️ 备份文件"
                elif 'factor_analyze' in file_path:
                    file_type = "⚠️ 因子模块"
                elif 'strategy_autodev' in file_path:
                    file_type = "⚠️ 策略模块"
                else:
                    file_type = "❓ 其他"
                
                print(f"     {file_type}: {file_path}")
                total_duplicates += 1
    
    print(f"\n📊 总计: {total_duplicates} 个重复定义")
    return total_duplicates

def cleanup_architecture_backup():
    """清理architecture_backup中的重复定义"""
    
    backup_dir = Path('architecture_backup')
    if not backup_dir.exists():
        print("architecture_backup 目录不存在")
        return 0
    
    cleaned_count = 0
    
    # 要清理的适配器类
    adapter_classes = [
        'StockDataAdapter',
        'FundamentalDataAdapter', 
        'MarketDataAdapter',
        'FactorDataAdapter'
    ]
    
    for root, dirs, files in os.walk(backup_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    modified = False
                    
                    # 删除重复的类定义
                    for adapter_class in adapter_classes:
                        # 匹配完整的类定义（包括docstring和简单实现）
                        pattern = rf'class\s+{adapter_class}\s*[:\(].*?(?=\nclass|\n\S|\Z)'
                        matches = re.findall(pattern, content, re.DOTALL)
                        
                        for match in matches:
                            # 检查是否是简单定义（只有pass或简单方法）
                            if 'pass' in match or len(match.strip()) < 200:
                                content = content.replace(match, f'# 已删除重复定义: {adapter_class}')
                                modified = True
                                print(f"   删除 {file_path} 中的 {adapter_class}")
                    
                    if modified:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        cleaned_count += 1
                        
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {e}")
    
    return cleaned_count

def create_migration_guide():
    """创建迁移指南"""
    
    guide = """
# 数据适配器统一迁移指南

## 问题描述
项目中存在多个重复的数据适配器定义，导致导入混乱和警告。

## 解决方案
统一使用 `adapters/` 目录下的标准实现。

## 迁移步骤

### 1. 更新导入语句
将以下导入语句：
```python
# 旧方式（各种重复定义）
class StockDataAdapter:
    pass
class FundamentalDataAdapter:
    pass
```

替换为：
```python
# 新方式（统一导入）
from adapters.stock_data_adapter import StockDataAdapter
from adapters.fundamental_data_adapter import FundamentalDataAdapter
from adapters.market_data_adapter import MarketDataAdapter
from adapters.factor_data_adapter import FactorDataAdapter
```

### 2. 使用统一导入模块
或者使用我们提供的统一导入模块：
```python
from unified_adapter_import import (
    StockDataAdapter,
    FundamentalDataAdapter,
    MarketDataAdapter,
    FactorDataAdapter
)
```

### 3. 处理导入失败的情况
如果标准适配器不可用，统一导入模块会自动提供Mock版本。

## 标准实现位置
- `adapters/stock_data_adapter.py` - 股票数据适配器
- `adapters/fundamental_data_adapter.py` - 基本面数据适配器
- `adapters/market_data_adapter.py` - 市场数据适配器
- `adapters/factor_data_adapter.py` - 因子数据适配器

## 注意事项
1. 标准实现继承自 `BaseDataAdapter`
2. 提供完整的数据获取和处理功能
3. 支持Tushare Pro接口
4. 包含错误处理和日志记录
"""
    
    with open('adapter_migration_guide.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 迁移指南已创建: adapter_migration_guide.md")

def main():
    """主函数"""
    
    print("🔍 开始分析重复定义...")
    
    # 查找重复定义
    duplicates = find_duplicate_definitions()
    
    # 分析重复定义
    total_duplicates = analyze_duplicates(duplicates)
    
    if total_duplicates == 0:
        print("\n🎉 没有发现重复定义！")
        return
    
    print(f"\n🧹 开始清理重复定义...")
    
    # 清理architecture_backup
    cleaned_count = cleanup_architecture_backup()
    print(f"✅ 清理了 {cleaned_count} 个备份文件中的重复定义")
    
    # 创建迁移指南
    create_migration_guide()
    
    print("\n" + "=" * 80)
    print("清理完成！")
    print("=" * 80)
    print("建议:")
    print("1. 使用 unified_adapter_import.py 进行统一导入")
    print("2. 逐步将其他地方的重复定义替换为标准实现")
    print("3. 参考 adapter_migration_guide.md 进行迁移")
    print("4. 测试确保功能正常")

if __name__ == "__main__":
    main() 