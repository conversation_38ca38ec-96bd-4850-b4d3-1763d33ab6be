# -*- coding: utf-8 -*-
"""
集成回测框架
用于测试和验证新创建的策略和因子

主要功能：
1. 统一的回测接口
2. 性能评估指标
3. 参数优化
4. 结果可视化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# 导入数据服务
from data_pipeline.real_market_data_connector import RealMarketDataConnector

# 导入策略和因子
from strategy_autodev.ml_strategies.pinn_stock_selection_model import PINNStockSelector, PINNConfig
from factor_analyze.tools.adwm_factor_weighting_system import ADWMFactorWeightingSystem, ADWMConfig
from factor_analyze.quality_factors.free_cash_flow_quality_factor import FreeCashFlowQualityFactor, CashCowStrategy
from risk_management.panic_index_monitoring_system import MarketPanicIndexSystem, PanicIndexConfig


@dataclass
class BacktestConfig:
    """回测配置"""
    start_date: str
    end_date: str
    initial_capital: float = 1000000
    commission_rate: float = 0.0003
    slippage_rate: float = 0.0001
    rebalance_frequency: str = 'monthly'  # daily, weekly, monthly
    position_limit: int = 30
    benchmark: str = 'CSI300'
    risk_free_rate: float = 0.03


@dataclass
class BacktestResult:
    """回测结果"""
    returns: pd.Series
    positions: pd.DataFrame
    trades: pd.DataFrame
    metrics: Dict[str, float]
    factor_data: Optional[pd.DataFrame] = None


class IntegratedBacktestFramework:
    """集成回测框架"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.data_connector = RealMarketDataConnector()
        
        # 存储结果
        self.results = {}
        
    def run_pinn_backtest(self, pinn_config: Optional[PINNConfig] = None) -> BacktestResult:
        """运行PINN策略回测"""
        self.logger.info("开始PINN策略回测...")
        
        # 创建PINN选股器
        pinn_selector = PINNStockSelector(pinn_config or PINNConfig(epochs=50))
        
        # 获取数据
        market_data = self._prepare_market_data()
        
        # 训练模型（使用历史数据的80%）
        train_end_idx = int(len(market_data) * 0.8)
        train_data = market_data.iloc[:train_end_idx]
        test_data = market_data.iloc[train_end_idx:]
        
        # 这里需要实际的训练数据准备，简化处理
        self.logger.info("训练PINN模型...")
        # TODO: val_data 未定义，需要提供验证集
        # pinn_selector.train(train_data, val_data)
        
        # 回测
        results = self._run_strategy_backtest(
            strategy_name="PINN",
            data=test_data,
            signal_generator=lambda data: self._generate_pinn_signals(pinn_selector, data)
        )
        
        return results
    
    def run_adwm_backtest(self, 
                         factor_names: List[str],
                         adwm_config: Optional[ADWMConfig] = None) -> BacktestResult:
        """运行ADWM因子加权策略回测"""
        self.logger.info("开始ADWM策略回测...")
        
        # 创建ADWM系统
        adwm_system = ADWMFactorWeightingSystem(factor_names, adwm_config or ADWMConfig())
        
        # 获取因子数据
        factor_data = self._prepare_factor_data(factor_names)
        returns = self._calculate_returns()
        
        # 训练ADWM模型
        train_end_idx = int(len(factor_data) * 0.8)
        train_factor_data = factor_data.iloc[:train_end_idx]
        train_returns = returns.iloc[:train_end_idx]
        
        self.logger.info("训练ADWM模型...")
        adwm_system.train(train_factor_data, train_returns)
        
        # 回测
        test_factor_data = factor_data.iloc[train_end_idx:]
        
        results = self._run_strategy_backtest(
            strategy_name="ADWM",
            data=test_factor_data,
            signal_generator=lambda data: adwm_system.create_combined_signal(data)
        )
        
        results.factor_data = test_factor_data
        return results
    
    def run_fcf_backtest(self) -> BacktestResult:
        """运行自由现金流策略回测"""
        self.logger.info("开始自由现金流策略回测...")
        
        # 创建因子和策略
        fcf_factor = FreeCashFlowQualityFactor()
        cash_cow_strategy = CashCowStrategy(fcf_factor)
        
        # 获取财务数据和市场数据
        financial_data = self._prepare_financial_data()
        market_data = self._prepare_market_data()
        
        # 回测
        results = self._run_strategy_backtest(
            strategy_name="FCF",
            data=market_data,
            signal_generator=lambda data: self._generate_fcf_signals(
                cash_cow_strategy, financial_data, market_data
            )
        )
        
        return results
    
    def run_panic_adjusted_backtest(self, base_strategy: str = "ADWM") -> BacktestResult:
        """运行恐慌指数调整的策略回测"""
        self.logger.info(f"开始恐慌指数调整的{base_strategy}策略回测...")
        
        # 创建恐慌指数系统
        panic_system = MarketPanicIndexSystem()
        
        # 获取基础策略信号
        if base_strategy == "ADWM":
            base_results = self.run_adwm_backtest(['momentum', 'value', 'quality'])
        else:
            base_results = self.run_fcf_backtest()
        
        # 获取恐慌指数数据
        panic_data = self._calculate_panic_index(panic_system)
        
        # 调整仓位
        adjusted_positions = self._adjust_positions_by_panic(
            base_results.positions,
            panic_data
        )
        
        # 重新计算收益
        adjusted_returns = self._calculate_returns_from_positions(adjusted_positions)
        
        # 创建新的结果
        adjusted_results = BacktestResult(
            returns=adjusted_returns,
            positions=adjusted_positions,
            trades=base_results.trades,
            metrics=self._calculate_metrics(adjusted_returns)
        )
        
        return adjusted_results
    
    def _prepare_market_data(self) -> pd.DataFrame:
        """准备市场数据"""
        # 获取股票池
        symbols = self._get_stock_universe()
        
        # 获取价格数据
        data_dict = self.data_connector.get_multiple_stocks_data(
            symbols,
            self.config.start_date,
            self.config.end_date,
            frequency='1d'
        )
        
        # 如果没有真实数据，生成模拟数据
        return self._generate_mock_market_data(symbols)
    
    def _prepare_factor_data(self, factor_names: List[str]) -> pd.DataFrame:
        """准备因子数据"""
        # 这里简化处理，生成模拟因子数据
        dates = pd.date_range(self.config.start_date, self.config.end_date, freq='D')
        
        factor_data = {}
        for factor in factor_names:
            factor_data[factor] = np.random.randn(len(dates))
        
        return pd.DataFrame(factor_data, index=dates)
    
    def _prepare_financial_data(self) -> pd.DataFrame:
        """准备财务数据"""
        # 简化处理，生成模拟财务数据
        dates = pd.date_range(self.config.start_date, self.config.end_date, freq='Q')
        
        financial_metrics = [
            'cash_flow_from_operations',
            'capital_expenditure',
            'net_income',
            'total_assets',
            'revenue',
            'total_debt'
        ]
        
        data = {}
        for metric in financial_metrics:
            data[metric] = np.random.uniform(1e6, 1e8, len(dates))
        
        return pd.DataFrame(data, index=dates)
    
    def _run_strategy_backtest(self,
                             strategy_name: str,
                             data: pd.DataFrame,
                             signal_generator: Callable) -> BacktestResult:
        """运行策略回测的通用框架"""
        # 初始化
        capital = self.config.initial_capital
        positions = pd.DataFrame()
        trades = []
        returns = []
        
        # 按照rebalance频率进行回测
        rebalance_dates = self._get_rebalance_dates(data.index)
        
        for i, date in enumerate(rebalance_dates[:-1]):
            # 生成信号
            current_data = data[data.index <= date]
            signals = signal_generator(current_data)
            
            # 选股
            selected_stocks = self._select_stocks_from_signals(signals)
            
            # 计算权重
            weights = self._calculate_weights(selected_stocks, signals)
            
            # 执行交易
            new_positions, trade_records = self._execute_trades(
                date, selected_stocks, weights, capital
            )
            
            positions = pd.concat([positions, new_positions])
            trades.extend(trade_records)
            
            # 计算期间收益率
            next_date = rebalance_dates[i + 1]
            period_return = self._calculate_period_return(
                new_positions, date, next_date
            )
            
            returns.append(period_return)
            capital *= (1 + period_return)
        
        # 整理结果
        returns_series = pd.Series(returns, index=rebalance_dates[:-1])
        trades_df = pd.DataFrame(trades)
        
        # 计算评估指标
        metrics = self._calculate_metrics(returns_series)
        
        result = BacktestResult(
            returns=returns_series,
            positions=positions,
            trades=trades_df,
            metrics=metrics
        )
        
        # 存储结果
        self.results[strategy_name] = result
        
        return result
    
    def _calculate_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算回测评估指标"""
        # 年化收益率
        days = (returns.index[-1] - returns.index[0]).days
        annual_return = (1 + returns.sum()) ** (365 / days) - 1
        
        # 夏普比率
        # 注意：这里的年化因子 np.sqrt(252) 是基于日度收益率的假设，
        # 如果回测频率不是每日，这里的准确性会下降
        if returns.std() > 0:
            sharpe_ratio = (annual_return - self.config.risk_free_rate) / (returns.std() * np.sqrt(252))
        else:
            sharpe_ratio = 0
        
        # 最大回撤
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns > 0).mean()
        
        # Calmar比率
        if max_drawdown < 0:
            calmar_ratio = annual_return / abs(max_drawdown)
        else:
            calmar_ratio = np.inf
        
        return {
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'calmar_ratio': calmar_ratio,
            'volatility': returns.std() * np.sqrt(252),
            'total_return': returns.sum()
        }
    
    def compare_strategies(self) -> pd.DataFrame:
        """比较不同策略的表现"""
        if not self.results:
            self.logger.warning("没有回测结果可比较")
            return pd.DataFrame()
        
        comparison = {}
        for strategy_name, result in self.results.items():
            comparison[strategy_name] = result.metrics
        
        return pd.DataFrame(comparison).T
    
    def plot_results(self, save_path: Optional[str] = None):
        """绘制回测结果图表"""
        if not self.results:
            self.logger.warning("没有回测结果可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 累积收益曲线
        ax = axes[0, 0]
        for strategy_name, result in self.results.items():
            cumulative_returns = (1 + result.returns).cumprod()
            ax.plot(cumulative_returns.index, cumulative_returns.values, 
                   label=strategy_name)
        ax.set_title('累积收益曲线')
        ax.set_xlabel('日期')
        ax.set_ylabel('累积收益')
        ax.legend()
        ax.grid(True)
        
        # 2. 回撤曲线
        ax = axes[0, 1]
        for strategy_name, result in self.results.items():
            cumulative_returns = (1 + result.returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            ax.fill_between(drawdown.index, drawdown.values, 0, 
                          alpha=0.3, label=strategy_name)
        ax.set_title('回撤曲线')
        ax.set_xlabel('日期')
        ax.set_ylabel('回撤')
        ax.legend()
        ax.grid(True)
        
        # 3. 月度收益热力图
        ax = axes[1, 0]
        if len(self.results) > 0:
            # 取第一个策略的月度收益
            first_strategy = list(self.results.keys())[0]
            returns = self.results[first_strategy].returns
            
            # 转换为月度收益
            monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
            
            # 创建热力图数据
            heatmap_data = monthly_returns.to_frame('return')
            heatmap_data['year'] = heatmap_data.index.year
            heatmap_data['month'] = heatmap_data.index.month
            pivot_table = heatmap_data.pivot(index='year', columns='month', values='return')
            
            sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdGn', 
                       center=0, ax=ax)
            ax.set_title(f'{first_strategy} 月度收益热力图')
        
        # 4. 策略指标对比
        ax = axes[1, 1]
        comparison = self.compare_strategies()
        if not comparison.empty:
            metrics_to_plot = ['annual_return', 'sharpe_ratio', 'max_drawdown']
            comparison[metrics_to_plot].plot(kind='bar', ax=ax)
            ax.set_title('策略指标对比')
            ax.set_ylabel('指标值')
            ax.legend(metrics_to_plot)
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def optimize_parameters(self, 
                          strategy_name: str,
                          param_grid: Dict[str, List[Any]]) -> Dict[str, Any]:
        """参数优化"""
        self.logger.info(f"开始{strategy_name}策略参数优化...")
        
        best_params = None
        best_sharpe = -np.inf
        
        # 网格搜索
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        
        from itertools import product
        
        for params in product(*param_values):
            param_dict = dict(zip(param_names, params))
            
            self.logger.info(f"测试参数: {param_dict}")
            
            # 根据策略类型运行回测
            if strategy_name == "PINN":
                config = PINNConfig(**param_dict)
                result = self.run_pinn_backtest(config)
            elif strategy_name == "ADWM":
                config = ADWMConfig(**param_dict)
                result = self.run_adwm_backtest(['momentum', 'value', 'quality'], config)
            else:
                continue
            
            # 评估结果
            sharpe = result.metrics['sharpe_ratio']
            
            if sharpe > best_sharpe:
                best_sharpe = sharpe
                best_params = param_dict
                self.logger.info(f"找到更好的参数 Sharpe={sharpe:.3f}")
        
        self.logger.info(f"最优参数: {best_params}, Sharpe={best_sharpe:.3f}")
        
        return best_params
    
    def save_results(self, output_dir: str = "strategy_autodev/backtest/results"):
        """保存回测结果"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 保存每个策略的结果
        for strategy_name, result in self.results.items():
            strategy_path = output_path / strategy_name
            strategy_path.mkdir(exist_ok=True)
            
            # 保存收益数据
            result.returns.to_csv(strategy_path / "returns.csv")
            
            # 保存持仓数据
            if not result.positions.empty:
                result.positions.to_csv(strategy_path / "positions.csv")
            
            # 保存交易记录
            if not result.trades.empty:
                result.trades.to_csv(strategy_path / "trades.csv")
            
            # 保存指标
            with open(strategy_path / "metrics.json", 'w') as f:
                json.dump(result.metrics, f, indent=4)
        
        # 保存策略对比
        comparison = self.compare_strategies()
        if not comparison.empty:
            comparison.to_csv(output_path / "strategy_comparison.csv")
        
        # 保存图表
        self.plot_results(str(output_path / "backtest_results.png"))
        
        self.logger.info(f"回测结果已保存至: {output_path}")
    
    # 辅助方法
    def _get_stock_universe(self) -> List[str]:
        """获取股票池"""
        # 这里简化处理，返回一些示例股票
        return [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ',
            '600000.SH', '600036.SH', '600276.SH', '600519.SH'
        ]
    
    def _get_rebalance_dates(self, dates: pd.DatetimeIndex) -> List[pd.Timestamp]:
        """获取再平衡日期"""
        if self.config.rebalance_frequency == 'daily':
            return dates.tolist()
        elif self.config.rebalance_frequency == 'weekly':
            return dates[dates.weekday == 0].tolist()  # 每周一
        elif self.config.rebalance_frequency == 'monthly':
            return dates[dates.is_month_start].tolist()
        else:
            return dates.tolist()
    
    def _generate_mock_market_data(self, symbols: List[str]) -> pd.DataFrame:
        """生成模拟市场数据"""
        dates = pd.date_range(self.config.start_date, self.config.end_date, freq='D')
        
        data_list = []
        for symbol in symbols:
            n = len(dates)
            base_price = 100.0
            returns = np.random.randn(n) * 0.02
            prices = base_price * np.exp(np.cumsum(returns))
            
            symbol_data = pd.DataFrame({
                'open': prices * (1 + np.random.randn(n) * 0.001),
                'high': prices * (1 + np.abs(np.random.randn(n)) * 0.005),
                'low': prices * (1 - np.abs(np.random.randn(n)) * 0.005),
                'close': prices,
                'volume': np.random.uniform(1e6, 1e7, n)
            }, index=dates)
            
            data_list.append(symbol_data)
        
        return pd.concat(data_list, keys=symbols, names=['symbol', 'date'])
    
    def _calculate_returns(self) -> pd.Series:
        """计算收益"""
        market_data = self._prepare_market_data()
        
        # 计算平均收益率（简化处理）
        if 'close' in market_data.columns:
            returns = market_data['close'].pct_change()
            return returns.groupby(level='date').mean()
        
        # 如果没有数据，返回模拟收益率
        dates = pd.date_range(self.config.start_date, self.config.end_date, freq='D')
        return pd.Series(np.random.randn(len(dates)) * 0.01, index=dates)
    
    def _generate_pinn_signals(self, selector, data):
        """生成PINN信号（简化版）"""
        # 这里应该调用实际的PINN预测
        # 简化处理，返回随机信号
        n_stocks = len(self._get_stock_universe())
        return pd.Series(np.random.randn(n_stocks), index=self._get_stock_universe())
    
    def _generate_fcf_signals(self, strategy, financial_data, market_data):
        """生成自由现金流信号（简化版）"""
        # 简化处理
        n_stocks = len(self._get_stock_universe())
        return pd.Series(np.random.uniform(0, 1, n_stocks), index=self._get_stock_universe())
    
    def _select_stocks_from_signals(self, signals):
        """从信号中选择股票"""
        # 选择信号最强的前N只股票
        return signals.nlargest(self.config.position_limit).index.tolist()
    
    def _calculate_weights(self, stocks, signals):
        """计算股票权重"""
        # 简单等权
        n = len(stocks)
        if n > 0:
            return {stock: 1.0 / n for stock in stocks}
        return {}
    
    def _execute_trades(self, date, stocks, weights, capital):
        """执行交易"""
        positions = pd.DataFrame({
            'symbol': stocks,
            'weight': [weights.get(s, 0) for s in stocks],
            'value': [capital * weights.get(s, 0) for s in stocks]
        })
        positions['date'] = date
        
        trades = []
        for stock in stocks:
            trades.append({
                'date': date,
                'symbol': stock,
                'action': 'buy',
                'weight': weights.get(stock, 0),
                'value': capital * weights.get(stock, 0)
            })
        
        return positions, trades
    
    def _calculate_period_return(self, positions, start_date, end_date):
        """计算期间收益率"""
        # 简化处理，生成随机收益，实际应根据持仓和价格变动计算
        return np.random.randn() * 0.02
    
    def _calculate_panic_index(self, panic_system):
        """计算恐慌指数"""
        # 简化处理
        dates = pd.date_range(self.config.start_date, self.config.end_date, freq='D')
        return pd.Series(np.random.uniform(0, 100, len(dates)), index=dates)
    
    def _adjust_positions_by_panic(self, positions, panic_data):
        """根据恐慌指数调整仓位"""
        # 简化处理
        return positions
    
    def _calculate_returns_from_positions(self, positions):
        """从持仓计算收益"""
        # 简化处理，实际应根据持仓价值变化计算
        dates = positions['date'].unique() if 'date' in positions else []
        return pd.Series(np.random.randn(len(dates)) * 0.01, index=dates)


# 运行回测的便捷函数
def run_comprehensive_backtest():
    """运行综合回测"""
    # 创建回测配置
    config = BacktestConfig(
        start_date='2024-01-01',
        end_date='2024-12-31',
        initial_capital=1000000,
        rebalance_frequency='monthly'
    )
    
    # 创建回测框架
    framework = IntegratedBacktestFramework(config)
    
    # 运行各种策略回测
    print("运行PINN策略回测...")
    pinn_result = framework.run_pinn_backtest()
    
    print("运行ADWM策略回测...")
    adwm_result = framework.run_adwm_backtest(['momentum', 'value', 'quality', 'size'])
    
    print("运行自由现金流策略回测...")
    fcf_result = framework.run_fcf_backtest()
    
    print("运行恐慌调整策略回测...")
    panic_result = framework.run_panic_adjusted_backtest()
    
    # 比较结果
    print("\n策略对比结果:")
    comparison = framework.compare_strategies()
    print(comparison)
    
    # 绘制图表
    framework.plot_results()
    
    # 保存结果
    framework.save_results()
    
    return framework


if __name__ == "__main__":
    # 运行综合回测
    framework = run_comprehensive_backtest() 
