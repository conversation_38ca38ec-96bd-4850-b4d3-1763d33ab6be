# -*- coding: utf-8 -*-
"""
Doc2Vec情感分析策略
基于Hands-On-Machine-Learning-for-Algorithmic-Trading Chapter15的doc2vec技术
实现基于文档嵌入的情感分析驱动量化交易策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings
import logging
from pathlib import Path
import pickle
import json

# Doc2Vec相关库
try:
    from gensim.models.doc2vec import Doc2Vec, TaggedDocument
    from gensim.utils import simple_preprocess
    from gensim.parsing.preprocessing import STOPWORDS
    from sklearn.linear_model import LogisticRegression
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.preprocessing import StandardScaler
    import nltk
    DOC2VEC_AVAILABLE = True
except ImportError:
    DOC2VEC_AVAILABLE = False
    warnings.warn("Doc2Vec库不可用，策略将使用简化模式")

    # 创建简化的TaggedDocument类
    class TaggedDocument:
        def __init__(self, words, tags):
            self.words = words
            self.tags = tags

    # 创建简化的Doc2Vec类
    class Doc2Vec:
        def __init__(self, *args, **kwargs):
            pass

        def build_vocab(self, documents):
            pass

        def train(self, documents, total_examples, epochs):
            pass

        def infer_vector(self, words):
            return np.random.randn(100)  # 返回随机向量

    # 创建简化的其他类
    def simple_preprocess(text):
        return text.lower().split()

    STOPWORDS = set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])

    # 导入sklearn组件
    try:
        from sklearn.linear_model import LogisticRegression
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, classification_report
        from sklearn.preprocessing import StandardScaler
    except ImportError:
        pass

# 数据管道集成
try:
    from data_adapter import get_data_adapter
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    DATA_ADAPTER_AVAILABLE = False
    warnings.warn("数据适配器不可用，将使用模拟数据")

# 策略基类
try:
    from ..base_ml_strategy import BaseMLStrategy, BaseMLConfig
    from ...core.interfaces import StrategyCategory
except ImportError:
    try:
        # 尝试从核心模块导入
        from strategy_autodev.core.base_strategy import BaseStrategy as BaseMLStrategy
        from strategy_autodev.core.interfaces import StrategyCategory
        class BaseMLConfig:
            def __init__(self):
                pass
    except ImportError:
        # 兼容性导入
        warnings.warn("基础策略类不可用，使用简化实现")
    
    class BaseMLStrategy:
        def __init__(self, config=None):
            self.config = config or {}
        
        def generate_signals(self, data):
            return {}
        
        def calculate_positions(self, signals, data):
            return {}
    
    class BaseMLConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class StrategyCategory:
        ML = "ML"
        NLP = "NLP"


class Doc2VecSentimentConfig(BaseMLConfig):
    """Doc2Vec情感分析策略配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Doc2Vec模型参数
        self.vector_size = kwargs.get('vector_size', 100)
        self.window = kwargs.get('window', 5)
        self.min_count = kwargs.get('min_count', 5)
        self.workers = kwargs.get('workers', 4)
        self.dm = kwargs.get('dm', 1)  # 1 for PV-DM, 0 for PV-DBOW
        self.epochs = kwargs.get('epochs', 20)
        self.alpha = kwargs.get('alpha', 0.025)
        self.min_alpha = kwargs.get('min_alpha', 0.00025)
        
        # 情感分析参数
        self.sentiment_classes = kwargs.get('sentiment_classes', ['negative', 'neutral', 'positive'])
        self.sentiment_threshold = kwargs.get('sentiment_threshold', 0.6)
        self.confidence_threshold = kwargs.get('confidence_threshold', 0.7)
        
        # 文档处理参数
        self.max_vocab_size = kwargs.get('max_vocab_size', 50000)
        self.min_doc_length = kwargs.get('min_doc_length', 10)
        self.max_doc_length = kwargs.get('max_doc_length', 500)
        
        # 策略参数
        self.lookback_days = kwargs.get('lookback_days', 20)
        self.position_size = kwargs.get('position_size', 0.1)
        self.rebalance_freq = kwargs.get('rebalance_freq', 5)
        self.sentiment_weight = kwargs.get('sentiment_weight', 0.8)
        self.momentum_weight = kwargs.get('momentum_weight', 0.2)


class Doc2VecSentimentStrategy(BaseMLStrategy):
    """
    Doc2Vec情感分析策略
    
    基于Chapter15的doc2vec技术实现：
    1. 财经新闻和研报的文档向量化
    2. 基于文档嵌入的情感分类
    3. 多时间尺度的情感趋势分析
    4. 情感驱动的交易信号生成
    """
    
    def __init__(self, config: Union[Doc2VecSentimentConfig, dict] = None):
        if isinstance(config, dict):
            config = Doc2VecSentimentConfig(**config)
        elif config is None:
            config = Doc2VecSentimentConfig()
            
        super().__init__(config)
        
        self.strategy_name = "Doc2Vec情感分析策略"
        self.category = StrategyCategory.ML
        
        # 初始化组件
        self.logger = logging.getLogger(__name__)
        self.doc2vec_model = None
        self.sentiment_classifier = None
        self.scaler = StandardScaler()
        self.document_vectors = {}
        self.sentiment_history = {}
        
        # 情感标签映射
        self.sentiment_mapping = {
            'negative': -1,
            'neutral': 0,
            'positive': 1
        }
        
        # 金融情感词典
        self.financial_sentiment_dict = {
            'positive': [
                'profit', 'growth', 'increase', 'gain', 'rise', 'bull', 'upgrade',
                'outperform', 'beat', 'strong', 'robust', 'solid', 'momentum',
                'breakthrough', 'success', 'opportunity', 'optimistic', 'confident'
            ],
            'negative': [
                'loss', 'decline', 'decrease', 'fall', 'bear', 'downgrade',
                'underperform', 'miss', 'weak', 'volatile', 'risk', 'concern',
                'crisis', 'failure', 'threat', 'pessimistic', 'uncertain', 'worry'
            ],
            'neutral': [
                'maintain', 'hold', 'stable', 'unchanged', 'neutral', 'flat',
                'steady', 'consistent', 'regular', 'normal', 'standard'
            ]
        }
        
        # 数据适配器
        if DATA_ADAPTER_AVAILABLE:
            try:
                self.data_adapter = get_data_adapter()
            except Exception as e:
                self.logger.warning(f"数据适配器初始化失败: {e}")
                self.data_adapter = None
        else:
            self.data_adapter = None
            
        self._initialize_doc2vec_components()
    
    def _initialize_doc2vec_components(self):
        """初始化Doc2Vec组件"""
        if not DOC2VEC_AVAILABLE:
            self.logger.warning("Doc2Vec库不可用，使用简化分析")
            return
            
        try:
            # 下载必要的NLTK数据
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            
            # 初始化情感分类器
            self.sentiment_classifier = RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=10
            )
                
            self.logger.info("Doc2Vec组件初始化成功")
            
        except Exception as e:
            self.logger.error(f"Doc2Vec组件初始化失败: {e}")
    
    def preprocess_document(self, text: str, doc_id: str = None) -> TaggedDocument:
        """文档预处理并创建TaggedDocument"""
        if not isinstance(text, str) or len(text) < self.config.min_doc_length:
            return None
            
        # 使用gensim的简单预处理
        tokens = simple_preprocess(text, deacc=True, min_len=2, max_len=15)
        
        # 过滤停用词
        tokens = [token for token in tokens if token not in STOPWORDS]
        
        # 限制文档长度
        if len(tokens) > self.config.max_doc_length:
            tokens = tokens[:self.config.max_doc_length]
        
        if not tokens:
            return None
            
        # 创建TaggedDocument
        tag = doc_id if doc_id else f"doc_{hash(text) % 10000}"
        return TaggedDocument(words=tokens, tags=[tag])
    
    def create_sentiment_labels(self, documents: List[str]) -> List[str]:
        """基于关键词创建情感标签（用于训练）"""
        labels = []
        
        for doc in documents:
            if not isinstance(doc, str):
                labels.append('neutral')
                continue
                
            doc_lower = doc.lower()
            
            # 计算情感词汇出现次数
            pos_count = sum(1 for word in self.financial_sentiment_dict['positive'] 
                           if word in doc_lower)
            neg_count = sum(1 for word in self.financial_sentiment_dict['negative'] 
                           if word in doc_lower)
            neu_count = sum(1 for word in self.financial_sentiment_dict['neutral'] 
                           if word in doc_lower)
            
            # 确定情感标签
            if pos_count > neg_count and pos_count > neu_count:
                labels.append('positive')
            elif neg_count > pos_count and neg_count > neu_count:
                labels.append('negative')
            else:
                labels.append('neutral')
        
        return labels
    
    def train_doc2vec_model(self, documents: List[str], doc_ids: List[str] = None) -> bool:
        """训练Doc2Vec模型"""
        if not DOC2VEC_AVAILABLE:
            self.logger.warning("Doc2Vec不可用，跳过模型训练")
            return False
            
        try:
            # 预处理文档
            tagged_docs = []
            valid_indices = []
            
            for i, doc in enumerate(documents):
                doc_id = doc_ids[i] if doc_ids and i < len(doc_ids) else f"doc_{i}"
                tagged_doc = self.preprocess_document(doc, doc_id)
                if tagged_doc:
                    tagged_docs.append(tagged_doc)
                    valid_indices.append(i)
            
            if len(tagged_docs) < 10:
                self.logger.warning("文档数量不足，无法训练有效模型")
                return False
            
            # 训练Doc2Vec模型
            self.doc2vec_model = Doc2Vec(
                documents=tagged_docs,
                vector_size=self.config.vector_size,
                window=self.config.window,
                min_count=self.config.min_count,
                workers=self.config.workers,
                dm=self.config.dm,
                epochs=self.config.epochs,
                alpha=self.config.alpha,
                min_alpha=self.config.min_alpha,
                seed=42
            )
            
            self.logger.info(f"Doc2Vec模型训练完成，词汇量: {len(self.doc2vec_model.wv)}")
            
            # 训练情感分类器
            return self._train_sentiment_classifier(documents, valid_indices)
            
        except Exception as e:
            self.logger.error(f"Doc2Vec模型训练失败: {e}")
            return False
    
    def _train_sentiment_classifier(self, documents: List[str], valid_indices: List[int]) -> bool:
        """训练情感分类器"""
        try:
            # 获取有效文档
            valid_docs = [documents[i] for i in valid_indices]
            
            # 创建情感标签
            sentiment_labels = self.create_sentiment_labels(valid_docs)
            
            # 获取文档向量
            doc_vectors = []
            for i, doc in enumerate(valid_docs):
                doc_id = f"doc_{valid_indices[i]}"
                if doc_id in self.doc2vec_model.dv:
                    doc_vectors.append(self.doc2vec_model.dv[doc_id])
                else:
                    # 推断向量
                    tokens = simple_preprocess(doc)
                    vector = self.doc2vec_model.infer_vector(tokens)
                    doc_vectors.append(vector)
            
            if len(doc_vectors) != len(sentiment_labels):
                self.logger.error("文档向量和标签数量不匹配")
                return False
            
            # 标准化特征
            X = self.scaler.fit_transform(doc_vectors)
            y = sentiment_labels
            
            # 分割训练和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练分类器
            self.sentiment_classifier.fit(X_train, y_train)
            
            # 评估模型
            y_pred = self.sentiment_classifier.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            self.logger.info(f"情感分类器训练完成，准确率: {accuracy:.3f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"情感分类器训练失败: {e}")
            return False
    
    def get_document_vector(self, text: str) -> np.ndarray:
        """获取文档向量"""
        if not self.doc2vec_model:
            return np.zeros(self.config.vector_size)
            
        try:
            tokens = simple_preprocess(text)
            if not tokens:
                return np.zeros(self.config.vector_size)
            
            # 推断文档向量
            vector = self.doc2vec_model.infer_vector(tokens)
            return vector
            
        except Exception as e:
            self.logger.error(f"获取文档向量失败: {e}")
            return np.zeros(self.config.vector_size)
    
    def predict_sentiment(self, text: str) -> Dict[str, Any]:
        """预测文档情感"""
        if not self.doc2vec_model or not self.sentiment_classifier:
            return {'sentiment': 'neutral', 'confidence': 0.0, 'score': 0.0}
            
        try:
            # 获取文档向量
            doc_vector = self.get_document_vector(text)
            if not np.any(doc_vector):
                return {'sentiment': 'neutral', 'confidence': 0.0, 'score': 0.0}
            
            # 标准化
            X = self.scaler.transform([doc_vector])
            
            # 预测情感
            sentiment_pred = self.sentiment_classifier.predict(X)[0]
            sentiment_proba = self.sentiment_classifier.predict_proba(X)[0]
            
            # 获取置信度
            confidence = max(sentiment_proba)
            
            # 转换为数值分数
            score = self.sentiment_mapping.get(sentiment_pred, 0)
            
            return {
                'sentiment': sentiment_pred,
                'confidence': float(confidence),
                'score': float(score),
                'probabilities': {
                    class_name: float(prob) 
                    for class_name, prob in zip(self.sentiment_classifier.classes_, sentiment_proba)
                }
            }
            
        except Exception as e:
            self.logger.error(f"情感预测失败: {e}")
            return {'sentiment': 'neutral', 'confidence': 0.0, 'score': 0.0}
    
    def analyze_sentiment_trend(self, texts: List[str], dates: List[datetime]) -> Dict[str, Any]:
        """分析情感趋势"""
        if not texts or not dates or len(texts) != len(dates):
            return {}
            
        sentiment_scores = []
        confidence_scores = []
        
        for text in texts:
            result = self.predict_sentiment(text)
            sentiment_scores.append(result['score'])
            confidence_scores.append(result['confidence'])
        
        # 创建时间序列
        sentiment_series = pd.Series(sentiment_scores, index=dates)
        confidence_series = pd.Series(confidence_scores, index=dates)
        
        # 计算趋势指标
        trend_analysis = {
            'mean_sentiment': float(sentiment_series.mean()),
            'sentiment_std': float(sentiment_series.std()),
            'mean_confidence': float(confidence_series.mean()),
            'trend_slope': self._calculate_trend_slope(sentiment_series),
            'recent_sentiment': float(sentiment_series.tail(5).mean()),
            'sentiment_momentum': self._calculate_momentum(sentiment_series)
        }
        
        return trend_analysis
    
    def _calculate_trend_slope(self, series: pd.Series) -> float:
        """计算趋势斜率"""
        if len(series) < 2:
            return 0.0
            
        x = np.arange(len(series))
        y = series.values
        
        # 线性回归计算斜率
        slope = np.polyfit(x, y, 1)[0]
        return float(slope)
    
    def _calculate_momentum(self, series: pd.Series, window: int = 5) -> float:
        """计算情感动量"""
        if len(series) < window * 2:
            return 0.0
            
        recent_mean = series.tail(window).mean()
        previous_mean = series.iloc[-window*2:-window].mean()
        
        momentum = recent_mean - previous_mean
        return float(momentum)
    
    def get_financial_news_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """获取财经新闻数据"""
        if not self.data_adapter:
            # 返回模拟数据
            self.logger.warning("使用模拟财经新闻数据")
            dates = pd.date_range(start_date, end_date, freq='D')
            sentiments = np.random.choice(['positive', 'negative', 'neutral'], len(dates))
            return pd.DataFrame({
                'date': dates,
                'title': [f'Financial News {i}' for i in range(len(dates))],
                'content': [f'Sample {sentiments[i]} financial news content {i}' for i in range(len(dates))],
                'symbol': ['AAPL'] * len(dates),
                'source': ['financial_times'] * len(dates)
            })
        
        try:
            # 使用真实数据适配器
            news_data = self.data_adapter.get_news_data(
                start_date=start_date,
                end_date=end_date,
                source='financial_news'
            )
            return news_data
            
        except Exception as e:
            self.logger.error(f"获取财经新闻数据失败: {e}")
            return pd.DataFrame()
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成交易信号"""
        if data.empty:
            return {}
        
        try:
            # 获取日期范围
            start_date = (data.index.min() - timedelta(days=self.config.lookback_days)).strftime('%Y-%m-%d')
            end_date = data.index.max().strftime('%Y-%m-%d')
            
            # 获取新闻数据
            news_data = self.get_financial_news_data(start_date, end_date)
            
            if news_data.empty:
                self.logger.warning("无新闻数据，返回空信号")
                return {}
            
            # 训练Doc2Vec模型
            documents = news_data['content'].tolist()
            doc_ids = [f"news_{i}" for i in range(len(documents))]
            
            if not self.train_doc2vec_model(documents, doc_ids):
                return {}
            
            # 生成信号
            signals = {}
            
            for symbol in data.columns:
                if symbol in news_data['symbol'].values:
                    symbol_news = news_data[news_data['symbol'] == symbol]
                    
                    # 按日期排序
                    symbol_news = symbol_news.sort_values('date')
                    
                    # 分析情感趋势
                    texts = symbol_news['content'].tolist()
                    dates = pd.to_datetime(symbol_news['date']).tolist()
                    
                    trend_analysis = self.analyze_sentiment_trend(texts, dates)
                    
                    if trend_analysis:
                        # 计算综合信号强度
                        sentiment_signal = trend_analysis['recent_sentiment']
                        momentum_signal = trend_analysis['sentiment_momentum']
                        confidence = trend_analysis['mean_confidence']
                        
                        # 加权组合信号
                        combined_signal = (
                            self.config.sentiment_weight * sentiment_signal +
                            self.config.momentum_weight * momentum_signal
                        )
                        
                        # 生成交易信号
                        if (combined_signal > self.config.sentiment_threshold and 
                            confidence > self.config.confidence_threshold):
                            signal = 'BUY'
                        elif (combined_signal < -self.config.sentiment_threshold and 
                              confidence > self.config.confidence_threshold):
                            signal = 'SELL'
                        else:
                            signal = 'HOLD'
                        
                        signals[symbol] = {
                            'signal': signal,
                            'sentiment_score': sentiment_signal,
                            'momentum_score': momentum_signal,
                            'combined_score': combined_signal,
                            'confidence': confidence,
                            'news_count': len(symbol_news),
                            'trend_analysis': trend_analysis
                        }
            
            return signals
            
        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return {}
    
    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        if not self.doc2vec_model or not self.sentiment_classifier:
            return False
            
        try:
            model_data = {
                'doc2vec_model': self.doc2vec_model,
                'sentiment_classifier': self.sentiment_classifier,
                'scaler': self.scaler,
                'sentiment_history': self.sentiment_history,
                'config': self.config.__dict__
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.doc2vec_model = model_data['doc2vec_model']
            self.sentiment_classifier = model_data['sentiment_classifier']
            self.scaler = model_data['scaler']
            self.sentiment_history = model_data['sentiment_history']
            
            self.logger.info(f"模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False


def create_doc2vec_sentiment_strategy(config: Dict[str, Any] = None) -> Doc2VecSentimentStrategy:
    """创建Doc2Vec情感分析策略实例"""
    return Doc2VecSentimentStrategy(config)


# 策略注册
def register_strategy():
    """注册策略到统一管理系统"""
    try:
        from ...core.unified_system import get_unified_system
        
        system = get_unified_system()
        
        success = system.register_strategy(
            name="Doc2VecSentimentStrategy",
            strategy_class=Doc2VecSentimentStrategy,
            category=StrategyCategory.ML,
            description="基于Doc2Vec的情感分析策略",
            tags=["NLP", "Doc2Vec", "情感分析", "文档嵌入"],
            risk_level="MEDIUM",
            data_requirements=["financial_news", "research_reports"]
        )
        
        if success:
            print("✓ Doc2Vec情感分析策略注册成功")
        else:
            print("✗ Doc2Vec情感分析策略注册失败")
            
        return success
        
    except Exception as e:
        print(f"✗ 策略注册失败: {e}")
        return False


if __name__ == "__main__":
    # 测试策略
    config = Doc2VecSentimentConfig(
        vector_size=100,
        window=5,
        min_count=3,
        epochs=20
    )
    
    strategy = Doc2VecSentimentStrategy(config)
    print(f"策略创建成功: {strategy.strategy_name}")
    
    # 注册策略
    register_strategy()