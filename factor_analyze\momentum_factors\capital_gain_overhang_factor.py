# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CGO因子
# 资本损益悬置（Capital Gain Overhang）因子
"""

import sys
import os
import pandas as pd
import numpy as np
import concurrent.futures
import multiprocessing
from functools import partial
from abc import ABC, abstractmethod
from dateutil.parser import parse
from tqdm import tqdm
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 添加项目根目录到sys.path
try:
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
except (NameError, ImportError):
    # 如果无法获取__file__
    project_root = os.path.abspath(os.path.join(os.getcwd(), '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

# 依赖
try:
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    from adapters.index_data_adapter import IndexDataAdapter
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from factor_analyze.base_factor import BaseFactor
    # import alphalens as al  # 替换为本地实现
    import factor_analyze.local_alternatives.alphalens_mock as al
except ImportError:
    print("依赖库adapters, alphalens未找到，将使用模拟模式")
    BaseFactor = object # 定义基类
    al = None # Alphalens不可用

    # 使用模拟适配器
    class StockDataAdapter:
        def get_daily(self, ts_code, start_date, end_date): return pd.DataFrame()
    class MarketDataAdapter:
        def get_turnover_data(self, stock_list, start_date, end_date): return None
    class IndexDataAdapter:
        def get_index_member(self, index_code): return pd.DataFrame()
    from adapters import FundamentalDataAdapter


class CapitalGainOverhangFactor(BaseFactor):
    """
    CGO因子
    资本损益悬置（Capital Gain Overhang）因子
    """
    
    def __init__(self, config=None):
        """
        初始化CGO因子
        """
        super().__init__(config)
        self.stock_adapter = StockDataAdapter()
        self.market_adapter = MarketDataAdapter()
        self.index_adapter = IndexDataAdapter()
        self.fundamental_adapter = FundamentalDataAdapter()
        
        pd.set_option('display.max_columns', None)
        pd.set_option('display.max_rows', 100)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 50)

    def _get_factor_name(self) -> str:
        """获取因子名称"""
        return "capital_gain_overhang"
    
    def _get_factor_description(self) -> str:
        """获取因子描述"""
        return "资本损益悬置因子"
    
    def _get_factor_category(self) -> str:
        """获取因子分类"""
        return "momentum"
    
    def calculate_factor(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算因子
        这是BaseFactor的抽象方法实现
        """
        stock_list = data['ts_code'].unique().tolist()
        start_date = data['trade_date'].min().strftime('%Y-%m-%d')
        end_date = data['trade_date'].max().strftime('%Y-%m-%d')
        return self.get_cgo_factor(stock_list, start_date, end_date)

    def get_price_data(self, stock_list, start_date, end_date, fields=['close', 'volume', 'money']):
        """获取价格数据"""
        try:
            all_data = {}
            for stock in stock_list:
                df = self.stock_adapter.get_daily(
                    ts_code=stock,
                    start_date=start_date.replace('-', ''),
                    end_date=end_date.replace('-', '')
                )
                if df is not None and not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                    df = df.sort_values('trade_date').set_index('trade_date')
                    if 'amount' in df.columns:
                        df['money'] = df['amount'] * 1000  # Tushare单位是千元
                    elif 'vol' in df.columns and 'close' in df.columns:
                        df['money'] = df['vol'] * 100 * df['close']
                    df = df.rename(columns={'vol': 'volume'})
                    available_fields = [f for f in fields if f in df.columns]
                    if available_fields:
                        all_data[stock] = df[available_fields]
            
            if all_data:
                result = {field: pd.DataFrame({stock: data[field] for stock, data in all_data.items() if field in data}) for field in fields}
                return {k: v for k, v in result.items() if not v.empty}
            return None
        except Exception as e:
            print(f"获取价格数据失败: {e}")
            return None

    def get_turnover_data(self, stock_list, start_date, end_date):
        """获取换手率数据"""
        try:
            return self.market_adapter.get_turnover_data(stock_list, start_date, end_date)
        except Exception as e:
            print(f"获取换手率数据失败: {e}")
            return None

    def get_index_stocks(self, index_code, date=None):
        """获取指数成分股"""
        try:
            df = self.index_adapter.get_index_member(index_code)
            if df is not None and not df.empty and 'con_code' in df.columns:
                return df['con_code'].tolist()
            return []
        except Exception as e:
            print(f"获取指数成分股失败: {e}")
            return []

    def calculate_reference_price(self, price_data, turnover_data, window=100):
        """计算参考价格(Reference Price)"""
        if not isinstance(turnover_data, pd.Series):
            raise ValueError(f"turnover_data应为pandas Series，实际为: {type(turnover_data)}")
        
        avg_price = price_data['close'].copy()
        turnover = turnover_data.copy()
        if turnover.max() > 1.0: turnover = turnover / 100.0
        turnover = turnover.clip(0.0001, 0.99)
        
        rp_series = pd.Series(index=price_data.index, dtype=float)
        
        for i in range(window, len(price_data)):
            hist_prices = avg_price.iloc[i-window:i]
            hist_turnover = turnover.iloc[i-window:i]
            
            # 计算持有期权重 w_k = turnover_k * Product_{j=k+1 to N} (1 - turnover_j)
            one_minus_turnover = 1 - hist_turnover
            # 从右到左累乘，得到每个时间点之后所有(1-turnover)的乘积
            survival_prob = np.cumprod(one_minus_turnover.iloc[::-1])[::-1].shift(-1).fillna(1.0)
            
            weights = hist_turnover * survival_prob
            
            weights_sum = weights.sum()
            if weights_sum > 1e-8 and not hist_prices.empty:
                rp_series.iloc[i] = np.average(hist_prices.values, weights=weights.values)
            elif not hist_prices.empty:
                rp_series.iloc[i] = hist_prices.iloc[-1] # Fallback
        
        return rp_series

    def calculate_cgo(self, price_data, reference_price):
        """计算资本损益悬置(Capital Gain Overhang)"""
        prev_close = price_data['close'].shift(1)
        return (prev_close - reference_price) / prev_close

    def calculate_single_stock_cgo(self, stock, extended_start, end_date, start_date, window):
        """计算单只股票的CGO因子（并行计算单元）"""
        try:
            price_data_dict = self.get_price_data([stock], extended_start, end_date, ['close', 'volume', 'money'])
            if not price_data_dict or 'close' not in price_data_dict or price_data_dict['close'].empty: return stock, None
            
            stock_price_data = pd.DataFrame({field: df[stock] for field, df in price_data_dict.items()})
            if stock_price_data.empty: return stock, None

            turnover_series = self.market_adapter.get_turnover_data([stock], extended_start, end_date)
            # 使用更合理的方式填充缺失的换手率
            if turnover_series is None or turnover_series.empty:
                turnover_series = pd.Series(5.0, index=stock_price_data.index)
            
            turnover_series = turnover_series.reindex(stock_price_data.index, method='ffill').fillna(5.0)

            rp = self.calculate_reference_price(stock_price_data, turnover_series, window)
            cgo = self.calculate_cgo(stock_price_data, rp)
            return stock, cgo.loc[start_date:end_date]
        except Exception as e:
            print(f"计算 {stock} CGO因子失败: {e}")
            return stock, None

    def get_cgo_factor(self, stock_list, start_date, end_date, window=100, n_jobs=-1):
        """获取CGO因子（支持并行计算）"""
        extended_start = (pd.to_datetime(start_date) - pd.Timedelta(days=window*2.5)).strftime('%Y-%m-%d')
        
        if n_jobs == -1: n_jobs = multiprocessing.cpu_count()
        
        cgo_results = {}
        calc_func = partial(self.calculate_single_stock_cgo, extended_start=extended_start, end_date=end_date, start_date=start_date, window=window)

        if n_jobs > 1 and len(stock_list) > 1:
            with concurrent.futures.ProcessPoolExecutor(max_workers=n_jobs) as executor:
                futures = {executor.submit(calc_func, stock): stock for stock in stock_list}
                for future in tqdm(concurrent.futures.as_completed(futures), total=len(stock_list), desc="计算CGO因子"):
                    stock_code, cgo_series = future.result()
                    if cgo_series is not None and not cgo_series.empty:
                        cgo_results[stock_code] = cgo_series
        else:
            for stock in tqdm(stock_list, desc="计算CGO因子"):
                stock_code, cgo_series = calc_func(stock)
                if cgo_series is not None and not cgo_series.empty:
                    cgo_results[stock_code] = cgo_series
        
        return pd.DataFrame(cgo_results) if cgo_results else pd.DataFrame()

# 示例
if __name__ == "__main__":
    start_date = '2022-01-01'
    end_date = '2023-12-31'
    
    cgo_calculator = CapitalGainOverhangFactor()
    
    stock_list = cgo_calculator.get_index_stocks('000300.SH')
    if not stock_list:
        print("未获取到指数成分股，将使用测试列表...")
        stock_list = ['000001.SZ', '600519.SH'] # 使用测试列表
    
    if stock_list:
        test_stocks = stock_list[:10]
        cgo_data = cgo_calculator.get_cgo_factor(test_stocks, start_date, end_date)
        if not cgo_data.empty:
            print(f"\nCGO因子计算结果: {cgo_data.shape}")
            print(cgo_data.head())
        else:
            print("CGO因子计算失败")
    else:
        print("未获取到股票列表")
