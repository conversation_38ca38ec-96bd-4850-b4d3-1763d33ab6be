# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波动率的波动率(Volatility of Volatility, VVol)因子
该因子衡量资产波动率本身的变化程度或不稳定性。高VVol通常意味着市场不确定性增加或进入高波动状态。
可用于股票、期货、指数和ETF等多种资产类别。
本VOVFactor实现依赖于`adapters`模块获取数据，如果适配器不可用，将无法获取实时数据。
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from loguru import logger

# 将项目根目录添加到sys.path
# 当前文件位于: project_root/factor_analyze/volatility_factors/
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# 延迟导入BaseFactor
try:
    from factor_analyze.factor_core.base_interfaces import BaseFactorMiner as BaseFactor
except ImportError:
    # 若无法导入，定义一个基础类以确保代码可运行
    class BaseFactor:
        def __init__(self, *args, **kwargs):
            pass
        def calculate_factor(self, data: pd.DataFrame) -> pd.DataFrame:
            raise NotImplementedError

# 尝试导入数据适配器
try:
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.index_data_adapter import IndexDataAdapter
    from adapters.futures_data_adapter import FuturesDataAdapter
    from adapters.fund_data_adapter import FundDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    DATA_PIPELINE_AVAILABLE = True
    logger.info("数据管道适配器加载成功。")
except ImportError as e:
    logger.warning(f"加载数据适配器失败: {e}。将无法获取实时数据。")
    DATA_PIPELINE_AVAILABLE = False
    # 定义模拟适配器类以供备用
    from adapters import StockDataAdapter
    class IndexDataAdapter: pass
    class FuturesDataAdapter: pass
    class FundDataAdapter: pass
    from adapters import MarketDataAdapter


class VolatilityOfVolatilityFactor(BaseFactor):
    """
    波动率的波动率因子计算器
    
    该因子通过计算波动率的波动率（VVol）来衡量风险和不确定性。
    针对不同资产类别，使用了不同的参数配置：
    - 股票 (stock): 波动率窗口5天，VVol窗口5天。
    - 期货 (futures): 波动率窗口10天，VVol窗口10天。
    - 指数 (index): 波动率窗口20天，VVol窗口20天。
    - ETF (etf): 波动率窗口5天，VVol窗口5天。
    
    通过`adapters`模块获取不同资产的数据，并计算三个核心因子。
    """
    
    def __init__(self, output_path: str = None):
        """
        初始化VOV因子计算器
        
        Args:
            output_path: 结果输出路径。如果为None，则保存在模块目录下的'vvol_output'文件夹中。
        """
        super().__init__()
        if output_path is None:
            self.output_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'vvol_output'))
        else:
            self.output_path = output_path
            
        os.makedirs(self.output_path, exist_ok=True)
        self._init_adapters()
        self._init_asset_configs()
    
    def _get_factor_name(self) -> str:
        """返回因子名称"""
        return "volatility_of_volatility"
    
    def _get_factor_description(self) -> str:
        """返回因子描述"""
        return "衡量波动率自身的波动性"
    
    def _get_factor_category(self) -> str:
        """返回因子类别"""
        return "volatility"

    def _init_adapters(self):
        """初始化数据适配器"""
        if not DATA_PIPELINE_AVAILABLE:
            self.stock_adapter, self.index_adapter, self.futures_adapter, self.fund_adapter, self.market_adapter = None, None, None, None, None
            logger.warning("数据管道不可用，所有适配器将为None。")
            return
            
        try:
            self.stock_adapter = StockDataAdapter()
            self.index_adapter = IndexDataAdapter()
            self.futures_adapter = FuturesDataAdapter()
            self.fund_adapter = FundDataAdapter()
            self.market_adapter = MarketDataAdapter()
            logger.info("所有数据适配器初始化成功。")
        except Exception as e:
            logger.error(f"初始化适配器时出错: {e}")
            raise
    
    def _init_asset_configs(self):
        """初始化不同资产的计算配置"""
        self.asset_configs = {
            'stock': {'vol_window': 5, 'vvol_window': 5, 'history_days': 20, 'min_periods': 3},
            'futures': {'vol_window': 10, 'vvol_window': 10, 'history_days': 30, 'min_periods': 5},
            'index': {'vol_window': 20, 'vvol_window': 20, 'history_days': 60, 'min_periods': 10},
            'etf': {'vol_window': 5, 'vvol_window': 5, 'history_days': 20, 'min_periods': 3}
        }
    
    def get_previous_trading_days(self, end_date: str, days: int, exchange: str = 'SSE') -> List[str]:
        """获取过去N个交易日列表"""
        if not self.market_adapter:
            logger.warning("MarketDataAdapter不可用，将生成模拟交易日。")
            trade_dates = []
            current_date = datetime.strptime(end_date, '%Y-%m-%d')
            while len(trade_dates) < days:
                if current_date.weekday() < 5:
                    trade_dates.append(current_date.strftime('%Y-%m-%d'))
                current_date -= timedelta(days=1)
            return sorted(trade_dates)

        try:
            end_date_str = end_date.replace('-', '')
            start_date_str = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=days * 2 + 30)).strftime('%Y%m%d')
            
            trade_cal = self.market_adapter.get_trade_cal(exchange=exchange, start_date=start_date_str, end_date=end_date_str, is_open='1')
            if trade_cal is None or trade_cal.empty:
                logger.warning(f"未能从交易日历获取数据: {exchange} {start_date_str}-{end_date_str}")
                return []
            
            trade_dates = sorted([d.strftime('%Y-%m-%d') for d in pd.to_datetime(trade_cal['cal_date'], format='%Y%m%d')])
            return trade_dates[-days:]
        except Exception as e:
            logger.error(f"获取交易日历时出错: {e}")
            return []

    def get_data_by_asset_type(self, asset_type: str, trade_date: str, instruments: Optional[List[str]] = None) -> pd.DataFrame:
        """根据资产类型获取数据"""
        adapter_map = {
            'stock': self.stock_adapter.get_daily if self.stock_adapter else None,
            'futures': self.futures_adapter.get_fut_daily if self.futures_adapter else None,
            'index': self.index_adapter.get_index_daily if self.index_adapter else None,
            'etf': self.fund_adapter.get_fund_daily if self.fund_adapter else None,
        }
        if asset_type not in adapter_map or adapter_map[asset_type] is None:
            logger.error(f"不支持的资产类型或适配器未初始化: {asset_type}")
            return pd.DataFrame()
        
        try:
            data = adapter_map[asset_type](ts_code=','.join(instruments) if instruments else None, trade_date=trade_date.replace('-', ''))
            return data if data is not None else pd.DataFrame()
        except Exception as e:
            logger.error(f"获取{asset_type}数据时出错: {e}")
            return pd.DataFrame()

    def calculate_vvol_factors(self, asset_type: str, date: str, instruments: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        计算指定资产和日期的VVol相关因子
        
        Args:
            asset_type: 资产类型 ('stock', 'futures', 'index', 'etf')
            date: 计算日期 (YYYY-MM-DD)
            instruments: 标的代码列表，为None则计算全市场
            
        Returns:
            包含VVol因子的DataFrame或在失败时返回None
            - fac1: VVol与成交额的相关性
            - fac2: 高VVol状态下成交额均值与总成交额均值的比率
            - fac3: 高VVol状态下成交量均值与总成交量均值的比率
        """
        if asset_type not in self.asset_configs:
            raise ValueError(f"不支持的资产类型: {asset_type}")
        
        config = self.asset_configs[asset_type]
        logger.info(f"开始计算 {asset_type} 在 {date} 的VVol因子...")
        
        all_data = self._collect_historical_data(asset_type, date, config, instruments)
        if all_data is None or all_data.empty:
            logger.warning(f"未能收集到 {asset_type} 的历史数据。")
            return None
        
        all_data = self._calculate_technical_indicators(all_data, config)
        
        current_instruments = all_data[all_data['trade_date'] == pd.to_datetime(date)]['instrument'].unique()
        if not current_instruments.any():
            logger.warning(f"在 {date} 没有找到任何有效的标的。")
            return None
        
        factors_result = self._calculate_three_factors(all_data, list(current_instruments))
        if factors_result is None or factors_result.empty:
            logger.warning("因子计算失败或无结果。")
            return None
        
        factors_result = self._add_metadata(factors_result, date, asset_type)
        logger.info(f"成功计算 {len(factors_result)} 个标的。")
        return factors_result

    def _collect_historical_data(self, asset_type: str, date: str, config: Dict, instruments: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """收集计算所需的历史数据"""
        trading_days = self.get_previous_trading_days(date, config['history_days'])
        if not trading_days:
            logger.warning(f"无法获取 {date} 之前的交易日历。")
            return None

        all_data = []
        for day in trading_days:
            daily_data = self.get_data_by_asset_type(asset_type, day, instruments)
            if daily_data is not None and not daily_data.empty:
                daily_data['instrument'] = daily_data['ts_code']
                all_data.append(daily_data)
        
        if not all_data: return None
        all_data_df = pd.concat(all_data, ignore_index=True)
        all_data_df['trade_date'] = pd.to_datetime(all_data_df['trade_date'], format='%Y%m%d')
        return all_data_df.sort_values(by=['instrument', 'trade_date'])

    def _calculate_technical_indicators(self, data: pd.DataFrame, config: Dict) -> pd.DataFrame:
        """计算基础技术指标 (ret, Vol, VVol)"""
        grouped = data.groupby("instrument")
        data["ret"] = grouped["close"].pct_change()
        data["Vol"] = grouped["ret"].transform(lambda x: x.rolling(config['vol_window'], min_periods=config['min_periods']).std())
        data["VVol"] = grouped["Vol"].transform(lambda x: x.rolling(config['vvol_window'], min_periods=config['min_periods']).std())
        return data

    def _calculate_three_factors(self, data: pd.DataFrame, current_instruments: List[str]) -> Optional[pd.DataFrame]:
        """计算三个核心因子"""
        fac1 = data.groupby("instrument").apply(lambda x: x["VVol"].corr(x["amount"])).reset_index(name="fac1")
        
        grouped = data.groupby("instrument")
        data["Mean_VVol"] = grouped["VVol"].transform("mean")
        high_vvol_data = data[data["VVol"] > data["Mean_VVol"]]
        
        if high_vvol_data.empty:
            logger.warning("没有高VVol状态的数据，无法计算因子2和3。")
            return None
        
        fac2 = (high_vvol_data.groupby("instrument")["amount"].mean() / grouped["amount"].mean()).reset_index(name="fac2")
        fac3 = (high_vvol_data.groupby("instrument")["vol"].mean() / grouped["vol"].mean()).reset_index(name="fac3")

        result = pd.merge(fac1, fac2, on='instrument', how='left')
        result = pd.merge(result, fac3, on='instrument', how='left')
        return result[result['instrument'].isin(current_instruments)]

    def _add_metadata(self, data: pd.DataFrame, date: str, asset_type: str) -> pd.DataFrame:
        """为结果添加元数据"""
        data["date"] = pd.to_datetime(date)
        data["asset_type"] = asset_type
        return data

    def calculate_ambiguity_aversion(self, factors_df: pd.DataFrame) -> pd.DataFrame:
        """计算模糊厌恶度指标"""
        if factors_df is None or factors_df.empty:
            return pd.DataFrame()
        result = factors_df.copy()
        result['vvol_score'] = (result['fac1'] - result['fac1'].mean()) / result['fac1'].std()
        result['ambiguity_aversion'] = (0.4 * result['vvol_score'].fillna(0) + 0.3 * result['fac2'].fillna(0) + 0.3 * result['fac3'].fillna(0))
        result['ambiguity_level'] = pd.cut(result['ambiguity_aversion'], bins=[-np.inf, -1, -0.5, 0.5, 1, np.inf], labels=['很低', '较低', '中等', '较高', '很高'])
        return result

    def save_results(self, results_df: pd.DataFrame, asset_type: str, date: str) -> str:
        """保存计算结果到CSV文件"""
        date_str = date.replace('-', '')
        filename = f"vvol_{asset_type}_{date_str}.csv"
        filepath = os.path.join(self.output_path, filename)
        results_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        return filepath

    def run_batch(self, asset_type: str, start_date: str, end_date: str, instruments: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """批量计算指定时间段内的因子"""
        if not self.market_adapter:
            logger.error("MarketDataAdapter不可用，无法执行批量计算。")
            return {}

        trade_dates = self.get_previous_trading_days(end_date, (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days)
        if not trade_dates:
            logger.error("无法获取批量计算期间的交易日。")
            return {}
        
        results = {}
        logger.info(f"开始对 {asset_type} 进行批量计算，共 {len(trade_dates)} 个交易日...")
        for date in trade_dates:
            try:
                factors_df = self.calculate_vvol_factors(asset_type, date, instruments)
                if factors_df is not None and not factors_df.empty:
                    final_results = self.calculate_ambiguity_aversion(factors_df)
                    results[date] = final_results
                    filepath = self.save_results(final_results, asset_type, date)
                    logger.info(f"  {date}: 计算成功，结果已保存至 {filepath}")
                else:
                    logger.warning(f"  {date}: 计算无结果。")
            except Exception as e:
                logger.error(f"  {date}: 计算失败 - {e}")
        
        logger.info(f"批量计算完成。成功 {len(results)}/{len(trade_dates)} 天。")
        return results


if __name__ == "__main__":
    calculator = VolatilityOfVolatilityFactor()
    date = '2024-01-15'
    
    logger.info(f"--- 开始单日股票VVol因子计算演示 ({date}) ---")
    stock_result = calculator.calculate_vvol_factors('stock', date)
    if stock_result is not None and not stock_result.empty:
        final_result = calculator.calculate_ambiguity_aversion(stock_result)
        logger.info("模糊厌恶度指标计算结果示例:")
        print(final_result.head())
        filepath = calculator.save_results(final_result, 'stock', date)
        logger.info(f"结果已保存到: {filepath}")
    else:
        logger.warning(f"在 {date} 未能计算出股票的VVol因子。")

    logger.info(f"\n--- 开始ETF VVol因子批量计算演示 (2024-01-01 to 2024-01-05) ---")
    etf_results_batch = calculator.run_batch('etf', '2024-01-01', '2024-01-05')
    if etf_results_batch:
        logger.info(f"批量计算完成，获取了 {len(etf_results_batch)} 天的结果。")
        print(etf_results_batch['2024-01-05'].head())
    else:
        logger.warning("ETF批量计算未产生任何结果。")
