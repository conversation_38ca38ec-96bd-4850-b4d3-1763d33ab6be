# -*- coding: utf-8 -*-\n# coding: utf-8
"""
<PERSON><PERSON><PERSON><PERSON> (一目均衡图) 择时策略
基于日本一目均衡图技术分析指标的择时信号生成器。

一目均衡图包含五条线：
1. 转换线(Tenkan-sen): 9日最高价和最低价的平均值
2. 基准线(Kijun-sen): 26日最高价和最低价的平均值
3. 先行带A (Senkou Span A): (转换线 + 基准线)/2，向前移动26天
4. 先行带B (Senkou Span B): 52日最高价和最低价的平均值，向前移动26天
5. 迟行线(Chikou Span): 当前收盘价向后移动26天

择时信号：
- 买入信号：价格突破云带上方，转换线上穿基准线
- 卖出信号：价格跌破云带下方，转换线下穿基准线
- 云带颜色：先行带A > 先行带B 为看涨云带，反之为看跌云带
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import warnings
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
    from Utils.timing_config import strategy_autodev.TimingConfig
    from Utils.timing_signal import strategy_autodev.TimingSignal
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class StockDataAdapter:
        def get_stock_data(self, symbol, start_date, end_date, adjust='qfq'):
            print(f"警告：无法导入StockDataAdapter，请检查adapters模块")
            return pd.DataFrame()
    
    from loguru import logger

    class TimingConfig:
        def __init__(self, lookback_period: int):
            self.lookback_period = lookback_period

    class TimingSignal:
        def __init__(self, timestamp: datetime, signal: int, strength: float, strategy: str, metadata: Dict):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata
    
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
            self._required_columns = kwargs.get('required_columns', ['close'])
            self._min_history = kwargs.get('min_history', 20)
            
        def validate_data(self, data):
            return True  # 简化验证
            
        def apply_threshold(self, strength):
            return strength
            
        def normalize_strength(self, value, vmin, vmax):
            return min(1.0, max(0.0, (value - vmin) / (vmax - vmin)))

warnings.filterwarnings('ignore')


class IchimokuTimer(BaseTimer):
    """
    Ichimoku Kinko Hyo (一目均衡图) 择时策略。
    
    实现完整的一目均衡图技术分析指标计算和择时信号生成。
    """
    
    def __init__(self, 
                 tenkan_period: int = 9,
                 kijun_period: int = 26, 
                 senkou_period: int = 52,
                 displacement: int = 26):
        super().__init__(TimingConfig(lookback_period=max(tenkan_period, kijun_period, senkou_period, displacement)),
                         required_columns=['high', 'low', 'close'],
                         min_history=senkou_period + displacement)
        self.tenkan_period = tenkan_period
        self.kijun_period = kijun_period
        self.senkou_period = senkou_period
        self.displacement = displacement
        self.data_adapter = StockDataAdapter()
        
        logger.info(f"Ichimoku择时器初始化完成 - 参数: T{tenkan_period}/K{kijun_period}/S{senkou_period}/D{displacement}")
    
    def calculate_ichimoku_lines(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算Ichimoku一目均衡图的所有线条
        
        Args:
            data: 包含high, low, close列的DataFrame
            
        Returns:
            包含所有Ichimoku线条的DataFrame
        """
        if not self.validate_data(data):
            raise ValueError("数据校验失败：必须包含 high, low, close 且长度足够")
        
        result = data.copy()
        n = len(data)
        
        # 1. 转换线(Tenkan-sen): 9日最高价和最低价的平均值
        result['tenkan_sen'] = np.nan
        for i in range(self.tenkan_period - 1, n):
            period_high = data['high'].iloc[i - self.tenkan_period + 1:i + 1].max()
            period_low = data['low'].iloc[i - self.tenkan_period + 1:i + 1].min()
            result.iloc[i, result.columns.get_loc('tenkan_sen')] = (period_high + period_low) / 2.0
        
        # 2. 基准线(Kijun-sen): 26日最高价和最低价的平均值
        result['kijun_sen'] = np.nan
        for i in range(self.kijun_period - 1, n):
            period_high = data['high'].iloc[i - self.kijun_period + 1:i + 1].max()
            period_low = data['low'].iloc[i - self.kijun_period + 1:i + 1].min()
            result.iloc[i, result.columns.get_loc('kijun_sen')] = (period_high + period_low) / 2.0
        
        # 3. 先行带A (Senkou Span A): (转换线 + 基准线)/2，向前移动26天
        result['senkou_A'] = np.nan
        for i in range(self.senkou_period - 1, n - self.displacement):
            if not pd.isna(result.iloc[i]['tenkan_sen']) and not pd.isna(result.iloc[i]['kijun_sen']):
                senkou_a_value = (result.iloc[i]['tenkan_sen'] + result.iloc[i]['kijun_sen']) / 2.0
                if i + self.displacement < n:
                    result.iloc[i + self.displacement, result.columns.get_loc('senkou_A')] = senkou_a_value
        
        # 4. 先行带B (Senkou Span B): 52日最高价和最低价的平均值，向前移动26天
        result['senkou_B'] = np.nan
        for i in range(self.senkou_period - 1, n - self.displacement):
            period_high = data['high'].iloc[i - self.senkou_period + 1:i + 1].max()
            period_low = data['low'].iloc[i - self.senkou_period + 1:i + 1].min()
            senkou_b_value = (period_high + period_low) / 2.0
            if i + self.displacement < n:
                result.iloc[i + self.displacement, result.columns.get_loc('senkou_B')] = senkou_b_value
        
        # 5. 迟行线(Chikou Span): 当前收盘价向后移动26天
        result['chikou'] = np.nan
        for i in range(self.displacement, n):
            result.iloc[i - self.displacement, result.columns.get_loc('chikou')] = data.iloc[i]['close']
        
        return result
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        基于Ichimoku指标生成择时信号
        
        Args:
            data: 包含Ichimoku线条的DataFrame
            
        Returns:
            包含信号的DataFrame
        """
        # 生成信号前再次校验
        self.validate_data(data)
        result = data.copy()
        
        # 初始化信号列
        result['signal'] = 0  # 0: 无信号, 1: 买入, -1: 卖出
        result['signal_strength'] = 'none'  # none, weak, medium, strong
        result['cloud_color'] = 'neutral'  # bullish, bearish, neutral
        result['price_vs_cloud'] = 'in_cloud'  # above_cloud, below_cloud, in_cloud
        
        for i in range(1, len(result)):
            current = result.iloc[i]
            previous = result.iloc[i-1]
            
            # 判断云带颜色
            if not pd.isna(current['senkou_A']) and not pd.isna(current['senkou_B']):
                if current['senkou_A'] > current['senkou_B']:
                    result.iloc[i, result.columns.get_loc('cloud_color')] = 'bullish'
                elif current['senkou_A'] < current['senkou_B']:
                    result.iloc[i, result.columns.get_loc('cloud_color')] = 'bearish'
            
            # 判断价格相对云带位置
            if not pd.isna(current['senkou_A']) and not pd.isna(current['senkou_B']):
                cloud_top = max(current['senkou_A'], current['senkou_B'])
                cloud_bottom = min(current['senkou_A'], current['senkou_B'])
                
                if current['close'] > cloud_top:
                    result.iloc[i, result.columns.get_loc('price_vs_cloud')] = 'above_cloud'
                elif current['close'] < cloud_bottom:
                    result.iloc[i, result.columns.get_loc('price_vs_cloud')] = 'below_cloud'
                else:
                    result.iloc[i, result.columns.get_loc('price_vs_cloud')] = 'in_cloud'
            
            # 生成交易信号
            if (not pd.isna(current['tenkan_sen']) and not pd.isna(current['kijun_sen']) and
                not pd.isna(previous['tenkan_sen']) and not pd.isna(previous['kijun_sen'])):
                
                # 转换线上穿基准线 (金叉)
                if (current['tenkan_sen'] > current['kijun_sen'] and 
                    previous['tenkan_sen'] <= previous['kijun_sen']):
                    
                    signal_strength = 'weak'
                    
                    # 强信号条件：价格在云带上方且云带为看涨
                    if (result.iloc[i]['price_vs_cloud'] == 'above_cloud' and 
                        result.iloc[i]['cloud_color'] == 'bullish'):
                        signal_strength = 'strong'
                    # 中等信号条件：价格在云带上方或云带为看涨
                    elif (result.iloc[i]['price_vs_cloud'] == 'above_cloud' or 
                          result.iloc[i]['cloud_color'] == 'bullish'):
                        signal_strength = 'medium'
                    
                    result.iloc[i, result.columns.get_loc('signal')] = 1
                    result.iloc[i, result.columns.get_loc('signal_strength')] = signal_strength
                
                # 转换线下穿基准线 (死叉)
                elif (current['tenkan_sen'] < current['kijun_sen'] and 
                      previous['tenkan_sen'] >= previous['kijun_sen']):
                    
                    signal_strength = 'weak'
                    
                    # 强信号条件：价格在云带下方且云带为看跌
                    if (result.iloc[i]['price_vs_cloud'] == 'below_cloud' and 
                        result.iloc[i]['cloud_color'] == 'bearish'):
                        signal_strength = 'strong'
                    # 中等信号条件：价格在云带下方或云带为看跌
                    elif (result.iloc[i]['price_vs_cloud'] == 'below_cloud' or 
                          result.iloc[i]['cloud_color'] == 'bearish'):
                        signal_strength = 'medium'
                    
                    result.iloc[i, result.columns.get_loc('signal')] = -1
                    result.iloc[i, result.columns.get_loc('signal_strength')] = signal_strength
        
        return result
    
    def analyze_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析择时信号的统计信息
        
        Args:
            data: 包含信号的DataFrame
            
        Returns:
            信号分析结果字典
        """
        buy_signals = data[data['signal'] == 1]
        sell_signals = data[data['signal'] == -1]
        
        analysis = {
            'total_buy_signals': len(buy_signals),
            'total_sell_signals': len(sell_signals),
            'buy_signal_details': [],
            'sell_signal_details': [],
            'signal_strength_distribution': data['signal_strength'].value_counts().to_dict(),
            'cloud_color_distribution': data['cloud_color'].value_counts().to_dict(),
            'price_vs_cloud_distribution': data['price_vs_cloud'].value_counts().to_dict()
        }
        
        # 买入信号详情
        for idx, row in buy_signals.iterrows():
            analysis['buy_signal_details'].append({
                'date': idx,
                'price': row['close'],
                'signal_strength': row['signal_strength'],
                'tenkan_sen': row['tenkan_sen'],
                'kijun_sen': row['kijun_sen'],
                'cloud_color': row['cloud_color'],
                'price_vs_cloud': row['price_vs_cloud']
            })
        
        # 卖出信号详情
        for idx, row in sell_signals.iterrows():
            analysis['sell_signal_details'].append({
                'date': idx,
                'price': row['close'],
                'signal_strength': row['signal_strength'],
                'tenkan_sen': row['tenkan_sen'],
                'kijun_sen': row['kijun_sen'],
                'cloud_color': row['cloud_color'],
                'price_vs_cloud': row['price_vs_cloud']
            })
        
        return analysis
    
    def plot_ichimoku_chart(self, data: pd.DataFrame, title: str = "Ichimoku Kinko Hyo Chart"):
        """
        绘制Ichimoku一目均衡图
        
        Args:
            data: 包含Ichimoku线条和信号的DataFrame
            title: 图表标题
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[3, 1])
        
        # 主图：价格和Ichimoku线条
        ax1.plot(data.index, data['close'], 'k-', linewidth=2, label='收盘价')
        ax1.plot(data.index, data['tenkan_sen'], 'r-', linewidth=1, label='转换线(9)')
        ax1.plot(data.index, data['kijun_sen'], 'b-', linewidth=1, label='基准线(26)')
        ax1.plot(data.index, data['senkou_A'], 'g-', linewidth=1, label='先行带A')
        ax1.plot(data.index, data['senkou_B'], 'm-', linewidth=1, label='先行带B')
        ax1.plot(data.index, data['chikou'], 'orange', linewidth=1, label='迟行线')
        
        # 填充云带
        ax1.fill_between(data.index, data['senkou_A'], data['senkou_B'], 
                        alpha=0.3, color='lightblue', label='云带')
        
        # 标记买卖信号
        buy_signals = data[data['signal'] == 1]
        sell_signals = data[data['signal'] == -1]
        
        if not buy_signals.empty:
            ax1.scatter(buy_signals.index, buy_signals['close'], 
                       color='red', marker='^', s=100, label='买入信号', zorder=5)
        
        if not sell_signals.empty:
            ax1.scatter(sell_signals.index, sell_signals['close'], 
                       color='green', marker='v', s=100, label='卖出信号', zorder=5)
        
        ax1.set_title(title)
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 副图：信号强度
        signal_colors = {'strong': 'red', 'medium': 'orange', 'weak': 'yellow', 'none': 'gray'}
        for strength, color in signal_colors.items():
            mask = data['signal_strength'] == strength
            if mask.any():
                ax2.scatter(data.index[mask], [1]*mask.sum(), 
                           color=color, alpha=0.6, label=f'{strength}信号')
        
        ax2.set_ylabel('信号强度')
        ax2.set_xlabel('日期')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def run_strategy(self, symbol: str, start_date: str, end_date: str, 
                    plot_chart: bool = True) -> Tuple[pd.DataFrame, Dict]:
        """
        运行完整的Ichimoku择时策略
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            plot_chart: 是否绘制图表
            
        Returns:
            (包含信号的DataFrame, 分析结果字典)
        """
        logger.info(f"开始运行Ichimoku择时策略 - {symbol} ({start_date} 到 {end_date})")
        
        try:
            # 获取数据
            data = self.data_adapter.get_stock_data(symbol, start_date, end_date)
            if data.empty:
                logger.error(f"无法获取{symbol}的数据")
                return pd.DataFrame(), {}
            
            # 计算Ichimoku指标
            ichimoku_data = self.calculate_ichimoku_lines(data)
            
            # 生成信号
            signal_data = self.generate_signals(ichimoku_data)
            
            # 分析信号
            analysis = self.analyze_signals(signal_data)
            
            # 打印分析结果
            self.print_analysis(analysis, symbol)
            
            # 绘制图表
            if plot_chart:
                self.plot_ichimoku_chart(signal_data, f"{symbol} Ichimoku Kinko Hyo 择时分析")
            
            logger.info(f"Ichimoku择时策略运行完成 - {symbol}")
            return signal_data, analysis
            
        except Exception as e:
            logger.error(f"运行Ichimoku择时策略时出错: {e}")
            return pd.DataFrame(), {}
    
    def print_analysis(self, analysis: Dict, symbol: str):
        """
        打印信号分析结果
        
        Args:
            analysis: 信号分析结果
            symbol: 股票代码
        """
        print("\n" + "="*60)
        print(f"Ichimoku Kinko Hyo 择时信号分析 - {symbol}")
        print("="*60)
        print(f"总买入信号数: {analysis.get('total_buy_signals', 0)}")
        print(f"总卖出信号数: {analysis.get('total_sell_signals', 0)}")
        
        print("\n信号强度分布:")
        print("-"*30)
        for strength, count in analysis.get('signal_strength_distribution', {}).items():
            print(f"{strength}: {count}")
        
        print("\n云带颜色分布:")
        print("-"*30)
        for color, count in analysis.get('cloud_color_distribution', {}).items():
            print(f"{color}: {count}")
        
        print("\n价格相对云带位置分布:")
        print("-"*30)
        for position, count in analysis.get('price_vs_cloud_distribution', {}).items():
            print(f"{position}: {count}")
        
        print("\n最近5个买入信号:")
        print("-"*40)
        for signal in analysis.get('buy_signal_details', [])[-5:]:
            date_str = signal['date'].strftime('%Y-%m-%d') if hasattr(signal['date'], 'strftime') else str(signal['date'])
            strength_mark = {"strong": "【强】", "medium": "【中】", "weak": "【弱】"}.get(signal['signal_strength'], "")
            print(f"{strength_mark}买入信号 @ {date_str} | 价格: {signal['price']:.2f} | "
                  f"转换线: {signal['tenkan_sen']:.2f} | 基准线: {signal['kijun_sen']:.2f}")
        
        print("\n最近5个卖出信号:")
        print("-"*40)
        for signal in analysis.get('sell_signal_details', [])[-5:]:
            date_str = signal['date'].strftime('%Y-%m-%d') if hasattr(signal['date'], 'strftime') else str(signal['date'])
            strength_mark = {"strong": "【强】", "medium": "【中】", "weak": "【弱】"}.get(signal['signal_strength'], "")
            print(f"{strength_mark}卖出信号 @ {date_str} | 价格: {signal['price']:.2f} | "
                  f"转换线: {signal['tenkan_sen']:.2f} | 基准线: {signal['kijun_sen']:.2f}")
        
        print("\n" + "="*60)
        print("Ichimoku择时策略说明:")
        print("- 转换线上穿基准线 = 买入信号")
        print("- 转换线下穿基准线 = 卖出信号")
        print("- 价格在云带上方且云带看涨 = 强信号")
        print("- 价格在云带下方且云带看跌 = 强信号")
        print("="*60)

    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法：基于最新数据计算信号"""
        ichimoku_data = self.calculate_ichimoku_lines(data.iloc[-self._min_history:] if self._min_history else data)
        signal_data = self.generate_signals(ichimoku_data)
        latest = signal_data.iloc[-1]
        signal_value = latest['signal']
        strength_map = {'none': 0.0, 'weak': 0.3, 'medium': 0.6, 'strong': 1.0}
        strength = self.apply_threshold(strength_map.get(latest['signal_strength'], 0.0))
        return TimingSignal(
            timestamp=datetime.now(),
            signal=signal_value,
            strength=strength,
            strategy="Ichimoku",
            metadata={
                'cloud_color': latest['cloud_color'],
                'price_vs_cloud': latest['price_vs_cloud']
            }
        )


if __name__ == "__main__":
    # 测试示例
    print("=== Ichimoku Kinko Hyo 择时策略测试 ===")
    
    # 创建测试数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    
    # 生成模拟股价数据
    base_price = 100
    returns = np.random.normal(0.001, 0.02, 200)
    prices = base_price * np.cumprod(1 + returns)
    
    # 生成高低价
    highs = prices * (1 + np.abs(np.random.normal(0, 0.01, 200)))
    lows = prices * (1 - np.abs(np.random.normal(0, 0.01, 200)))
    
    test_data = pd.DataFrame({
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 200)
    }, index=dates)
    
    # 测试Ichimoku择时器
    try:
        ichimoku_timer = IchimokuTimer()
        
        # 计算Ichimoku指标
        ichimoku_data = ichimoku_timer.calculate_ichimoku_lines(test_data)
        print(f"-> Ichimoku指标计算完成，数据长度: {len(ichimoku_data)}")
        
        # 生成信号
        signal_data = ichimoku_timer.generate_signals(ichimoku_data)
        print(f"-> 择时信号生成完成")
        
        # 分析信号
        analysis = ichimoku_timer.analyze_signals(signal_data)
        print(f"-> 买入信号数: {analysis.get('total_buy_signals', 0)}")
        print(f"-> 卖出信号数: {analysis.get('total_sell_signals', 0)}")
        
        # 显示最新的指标值
        latest = signal_data.iloc[-1]
        print(f"-> 最新转换线: {latest['tenkan_sen']:.2f}")
        print(f"-> 最新基准线: {latest['kijun_sen']:.2f}")
        print(f"-> 最新云带状态: {latest['cloud_color']}")
        print(f"-> 价格相对云带: {latest['price_vs_cloud']}")
        
    except Exception as e:
        print(f"Ichimoku择时策略测试失败: {e}")
    
    print("\n-> Ichimoku择时策略测试完成。")
    print("\n使用说明:")
    print("1. 对于实际股票数据，请使用 IchimokuTimer.run_strategy() 方法")
    print("2. 示例: ichimoku_timer.run_strategy('000300.XSHG', '2023-01-01', '2023-12-31')")
    print("3. 确保adapters目录下有正确的StockDataAdapter实现")
    print("4. 支持个股和指数择时，适用于中长期趋势判断")
