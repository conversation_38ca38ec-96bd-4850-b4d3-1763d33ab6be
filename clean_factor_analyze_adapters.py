#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量清理 factor_analyze 目录下的重复适配器定义
"""

import os
import re
from pathlib import Path

def clean_factor_analyze_file(file_path):
    """清理单个 factor_analyze 文件中的重复适配器定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换重复的适配器定义
        patterns_to_replace = [
            # StockDataAdapter 定义 (模拟版本)
            (r'class StockDataAdapter:\s*\n\s*def get_daily\(self, \*args, \*\*kwargs\):\s*\n\s*logger\.warning\("Using mock StockDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)', 
             'from adapters import StockDataAdapter'),
            
            # MarketDataAdapter 定义 (模拟版本)
            (r'class MarketDataAdapter:\s*\n\s*def get_market_data\(self, \*args, \*\*kwargs\):\s*\n\s*logger\.warning\("Using mock MarketDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)',
             'from adapters import MarketDataAdapter'),
            
            # FundamentalDataAdapter 定义 (模拟版本)
            (r'class FundamentalDataAdapter:\s*\n\s*def get_fundamental_data\(self, \*args, \*\*kwargs\):\s*\n\s*logger\.warning\("Using mock FundamentalDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)',
             'from adapters import FundamentalDataAdapter'),
            
            # FactorDataAdapter 定义 (模拟版本)
            (r'class FactorDataAdapter:\s*\n\s*def get_factor_data\(self, \*args, \*\*kwargs\):\s*\n\s*logger\.warning\("Using mock FactorDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)',
             'from adapters import FactorDataAdapter'),
            
            # 简单的 pass 定义
            (r'class StockDataAdapter: pass', 'from adapters import StockDataAdapter'),
            (r'class MarketDataAdapter: pass', 'from adapters import MarketDataAdapter'),
            (r'class FundamentalDataAdapter: pass', 'from adapters import FundamentalDataAdapter'),
            (r'class FactorDataAdapter: pass', 'from adapters import FactorDataAdapter'),
            
            # 其他简单的模拟定义
            (r'class StockDataAdapter:\s*\n\s*pass', 'from adapters import StockDataAdapter'),
            (r'class MarketDataAdapter:\s*\n\s*pass', 'from adapters import MarketDataAdapter'),
            (r'class FundamentalDataAdapter:\s*\n\s*pass', 'from adapters import FundamentalDataAdapter'),
            (r'class FactorDataAdapter:\s*\n\s*pass', 'from adapters import FactorDataAdapter'),
        ]
        
        modified = False
        for pattern, replacement in patterns_to_replace:
            if re.search(pattern, content, re.MULTILINE):
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                modified = True
        
        if modified:
            # 备份原文件
            backup_path = str(file_path) + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 写回修改后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已清理: {file_path}")
            return True
        else:
            print(f"⏭️ 无需清理: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    factor_analyze_dir = Path("factor_analyze")
    
    if not factor_analyze_dir.exists():
        print(f"❌ 目录不存在: {factor_analyze_dir}")
        return
    
    print(f"🔍 开始清理 {factor_analyze_dir} 目录下的重复适配器定义...")
    
    cleaned_count = 0
    total_count = 0
    
    # 递归搜索所有 Python 文件
    for py_file in factor_analyze_dir.rglob("*.py"):
        if py_file.name.endswith('.py'):
            total_count += 1
            if clean_factor_analyze_file(py_file):
                cleaned_count += 1
    
    print(f"\n📊 清理完成:")
    print(f"  总文件数: {total_count}")
    print(f"  已清理: {cleaned_count}")
    print(f"  无需清理: {total_count - cleaned_count}")

if __name__ == "__main__":
    main() 