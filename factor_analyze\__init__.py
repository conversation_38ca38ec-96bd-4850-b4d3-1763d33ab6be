# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# coding: utf-8

"""
Factor Analyze Module
因子分析模块

This module provides factor analysis capabilities including:
- Factor calculation and preprocessing
- Factor performance evaluation
- Factor mining integration

Submodules:
- profitability: 盈利能力因子
"""

import pandas as pd
import numpy as np

# 导入各子模块
try:
    from .profitability_factors import AVAILABLE_FACTORS as PROFITABILITY_FACTORS
except ImportError:
    PROFITABILITY_FACTORS = {}

# 暂时注释掉有问题的导入语句
# from .technical_indicators.xue_si_channel_ii import (
#     StockXueSiChannelIIFactor,
#     IndexXueSiChannelIIFactor,
#     XueSiChannelIISignal,
#     DynamicMovingAverage
# )

# 导入完整的因子类
try:
    from .fundamental_factors.operating_cash_flow_to_market_value_factor import (
        StockOperatingCashFlowToMarketValueFactor,
        IndexOperatingCashFlowToMarketValueFactor,
        OperatingCashFlowToMarketValueFactor
    )
except ImportError as e:
    print(f"[WARNING] 无法导入完整的现金流因子类: {e}")
    # 提供简化版本作为后备
    class StockOperatingCashFlowToMarketValueFactor:
        """
        经营现金流与市值比因子（简化版本）
        用于评估股票的现金流价值
        """

        def __init__(self, name: str = "StockOperatingCashFlowToMarketValueFactor"):
            self.name = name
            self.category = "value"

        def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
            """计算经营现金流与市值比因子"""
            # 简化实现
            if 'operating_cash_flow' in data.columns and 'market_value' in data.columns:
                factor_value = data['operating_cash_flow'] / data['market_value']
            else:
                # 使用模拟数据
                factor_value = np.random.randn(len(data)) * 0.1

            return pd.DataFrame({
                'factor_value': factor_value,
                'factor_name': self.name
            }, index=data.index)

    class IndexOperatingCashFlowToMarketValueFactor:
        """
        指数经营现金流与市值比因子（简化版本）
        用于评估指数的现金流价值
        """

        def __init__(self, name: str = "IndexOperatingCashFlowToMarketValueFactor"):
            self.name = name
            self.category = "value"

        def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
            """计算指数经营现金流与市值比因子"""
            # 简化实现
            if 'operating_cash_flow' in data.columns and 'market_value' in data.columns:
                factor_value = data['operating_cash_flow'] / data['market_value']
            else:
                # 使用模拟数据
                factor_value = np.random.randn(len(data)) * 0.1

            return pd.DataFrame({
                'factor_value': factor_value,
                'factor_name': self.name
            }, index=data.index)

    class OperatingCashFlowToMarketValueFactor:
        """
        经营现金流与市值比因子统一接口（简化版本）
        提供个股和指数因子的统一调用接口
        """

        def __init__(self, name: str = "OperatingCashFlowToMarketValueFactor"):
            self.name = name
            self.category = "value"
            self.stock_factor = StockOperatingCashFlowToMarketValueFactor()
            self.index_factor = IndexOperatingCashFlowToMarketValueFactor()

        def calculate_stock_factor(self, data: pd.DataFrame) -> pd.DataFrame:
            """计算个股因子"""
            return self.stock_factor.calculate(data)

        def calculate_index_factor(self, data: pd.DataFrame) -> pd.DataFrame:
            """计算指数因子"""
            return self.index_factor.calculate(data)


class NegativeReturnIlliquidityFactorStock:
    """
    负收益流动性因子（股票版本）
    用于分析股票在负收益期间的流动性特征
    """

    def __init__(self, name: str = "NegativeReturnIlliquidityFactorStock"):
        self.name = name
        self.category = "liquidity"

    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算负收益流动性因子"""
        import pandas as pd
        import numpy as np

        # 简化实现
        if 'returns' in data.columns and 'volume' in data.columns:
            # 识别负收益期间
            negative_return_mask = data['returns'] < 0

            # 计算负收益期间的流动性指标
            illiquidity = np.where(
                negative_return_mask,
                np.abs(data['returns']) / (data['volume'] + 1e-8),
                np.nan
            )

            result = pd.DataFrame({
                'negative_return_illiquidity': illiquidity
            }, index=data.index)

            return result
        else:
            # 返回模拟数据
            return pd.DataFrame({
                'negative_return_illiquidity': np.random.normal(0, 1, len(data))
            }, index=data.index)


class NegativeReturnIlliquidityFactorIndex:
    """
    负收益流动性因子（指数版本）
    用于分析指数在负收益期间的流动性特征
    """

    def __init__(self, name: str = "NegativeReturnIlliquidityFactorIndex"):
        self.name = name
        self.category = "liquidity"

    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算负收益流动性因子（指数版本）"""
        # 简化实现
        if 'returns' in data.columns and 'volume' in data.columns:
            # 识别负收益期间
            negative_return_mask = data['returns'] < 0

            # 计算负收益期间的流动性指标（指数版本）
            illiquidity = np.where(
                negative_return_mask,
                np.abs(data['returns']) / (data['volume'] + 1e-8),
                np.nan
            )

            result = pd.DataFrame({
                'negative_return_illiquidity_index': illiquidity
            }, index=data.index)

            return result
        else:
            # 返回模拟数据
            return pd.DataFrame({
                'negative_return_illiquidity_index': np.random.normal(0, 1, len(data))
            }, index=data.index)


# 合并所有可用因子
ALL_AVAILABLE_FACTORS = {}
ALL_AVAILABLE_FACTORS.update(PROFITABILITY_FACTORS)
ALL_AVAILABLE_FACTORS['negative_return_illiquidity_factor_stock'] = NegativeReturnIlliquidityFactorStock
ALL_AVAILABLE_FACTORS['negative_return_illiquidity_factor_index'] = NegativeReturnIlliquidityFactorIndex
ALL_AVAILABLE_FACTORS['negative_return_illiquidity_factor_index'] = NegativeReturnIlliquidityFactorIndex
# 暂时注释掉使用这些类的代码
# ALL_AVAILABLE_FACTORS.update({
#     'stock_xue_si_channel_ii': StockXueSiChannelIIFactor,
#     'index_xue_si_channel_ii': IndexXueSiChannelIIFactor,
# })

# 提供统一的因子获取接口
def get_factor_by_name(factor_name: str, use_mock: bool = False):
    """
    Get factor instance by name
    根据名称获取因子实例
    
    Parameters:
    -----------
    factor_name : str
        因子名称
    use_mock : bool
        是否使用模拟数据版本
        
    Returns:
    --------
    Factor instance or None
    """
    if factor_name in ALL_AVAILABLE_FACTORS:
        factor_info = ALL_AVAILABLE_FACTORS[factor_name]
        if use_mock and 'mock_class' in factor_info:
            return factor_info['mock_class']()
        elif 'factory' in factor_info:
            return factor_info['factory'](use_mock=use_mock)
        else:
            return factor_info['class']()
    return None

def list_available_factors():
    """
    List all available factors
    列出所有可用因子
    """
    return list(ALL_AVAILABLE_FACTORS.keys())

def get_factor_info(factor_name: str):
    """
    Get factor information
    获取因子信息
    """
    return ALL_AVAILABLE_FACTORS.get(factor_name, None)

__all__ = [
    'get_factor_by_name',
    'list_available_factors',
    'get_factor_info',
    'ALL_AVAILABLE_FACTORS',
    'StockOperatingCashFlowToMarketValueFactor',
    'IndexOperatingCashFlowToMarketValueFactor',
    'OperatingCashFlowToMarketValueFactor',
    'NegativeReturnIlliquidityFactorStock',
    'NegativeReturnIlliquidityFactorIndex'
]