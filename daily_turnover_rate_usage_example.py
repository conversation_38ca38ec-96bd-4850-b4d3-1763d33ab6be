# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日均换手率因子使用示例：演示如何通过factor_mining模块调用日均换手率因子。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_factor_mining_usage():
    """通过factor_mining模块使用日均换手率因子的示例"""
    print("=== 日均换手率因子使用示例 ===")
    print("通过factor_mining模块调用日均换手率因子\n")
    
    try:
        # 方法1: 使用factor_mining集成注册器
        print("方法1: 使用FactorAnalyzeRegistry")
        from factor_analyze.factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
        
        registry = FactorAnalyzeRegistry()
        
        # 查看可用因子
        available_factors = registry.list_available_factors()
        print(f"可用股票因子: {len(available_factors['stock_factors'])}个")
        print(f"可用指数因子: {len(available_factors['index_factors'])}个")
        
        # 检查日均换手率因子是否可用
        if 'daily_turnover_rate' in available_factors['stock_factors']:
            print("✅ 日均换手率因子(股票)已注册")
            
            # 获取因子类
            stock_factor_class = registry.get_stock_factor('daily_turnover_rate')
            print(f"股票因子类: {stock_factor_class.__name__}")
            
            # 获取因子元数据
            metadata = registry.get_factor_metadata('stock', 'daily_turnover_rate')
            if metadata:
                print(f"因子名称: {metadata['name']}")
                print(f"因子类别: {metadata['category']}")
                print(f"计算周期: {metadata['calculation_period']}")
                print(f"参考链接: {metadata.get('reference_url', 'N/A')}")
        
        if 'daily_turnover_rate' in available_factors['index_factors']:
            print("✅ 日均换手率因子(指数)已注册")
        
        print("\n" + "="*50)
        
        # 方法2: 使用专用集成模块
        print("方法2: 使用专用集成模块")
        from factor_analyze.factor_mining.daily_turnover_rate_integration import (
            DailyTurnoverRateIntegration,
            calculate_daily_turnover_rate_factor
        )
        
        # 创建集成实例
        integration = DailyTurnoverRateIntegration()
        
        # 获取因子信息
        factor_info = integration.get_factor_info()
        print(f"因子名称: {factor_info.get('factor_name', 'N/A')}")
        print(f"因子描述: {factor_info.get('description', 'N/A')}")
        
        # 示例参数
        test_stocks = ['000001.SZ', '000002.SZ']
        test_indices = ['000300.SH', '000905.SH']
        start_date = '2024-01-01'
        end_date = '2024-01-10'
        
        print(f"\n测试参数:")
        print(f"股票代码: {test_stocks}")
        print(f"指数代码: {test_indices}")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 计算股票因子
        print("\n计算股票日均换手率因子...")
        stock_result = integration.calculate_stock_factor(
            stock_codes=test_stocks,
            start_date=start_date,
            end_date=end_date
        )
        
        if not stock_result.empty:
            print(f"✅ 股票因子计算成功，共 {len(stock_result)} 条记录")
            print("前5条记录:")
            print(stock_result.head())
        else:
            print("❌ 股票因子计算结果为空（可能使用了模拟数据）")
        
        # 计算指数因子
        print("\n计算指数日均换手率因子...")
        index_result = integration.calculate_index_factor(
            index_codes=test_indices,
            start_date=start_date,
            end_date=end_date
        )
        
        if not index_result.empty:
            print(f"✅ 指数因子计算成功，共 {len(index_result)} 条记录")
            print("前5条记录:")
            print(index_result.head())
        else:
            print("❌ 指数因子计算结果为空（可能使用了模拟数据）")
        
        # 使用统一接口
        print("\n使用统一接口计算因子...")
        unified_result = calculate_daily_turnover_rate_factor(
            securities=test_stocks + test_indices,
            start_date=start_date,
            end_date=end_date,
            security_type='auto'  # 自动识别证券类型
        )
        
        if not unified_result.empty:
            print(f"✅ 统一接口计算成功，共 {len(unified_result)} 条记录")
            print("前5条记录:")
            print(unified_result.head())
        else:
            print("❌ 统一接口计算结果为空（可能使用了模拟数据）")
        
        print("\n" + "="*50)
        
        # 方法3: 直接使用因子类
        print("方法3: 直接使用因子类")
        from factor_analyze.liquidity_factors.daily_turnover_rate_factor import (
            DailyTurnoverRateFactorStock,
            DailyTurnoverRateFactorIndex,
            DailyTurnoverRateFactor
        )
        
        # 使用统一因子接口
        unified_factor = DailyTurnoverRateFactor()
        
        # 获取因子定义
        definition = unified_factor.get_factor_definition()
        print(f"因子定义: {definition['name']}")
        print(f"计算公式: {definition['formula']}")
        print(f"适用标的: {definition['applicable_targets']}")
        
        # 计算因子（使用模拟数据）
        print("\n使用统一因子接口计算...")
        result = unified_factor.calculate_factor(
            securities=test_stocks[:1],  # 只测试一个股票
            start_date=start_date,
            end_date=end_date
        )
        
        if not result.empty:
            print(f"✅ 统一因子接口计算成功，共 {len(result)} 条记录")
            print("结果示例:")
            print(result.head())
        else:
            print("❌ 统一因子接口计算结果为空")
        
        print("\n=== 使用示例完成 ===")
        return True
        
    except Exception as e:
        print(f"\n❌ 使用示例执行失败: {e}")
        import traceback
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

def show_integration_summary():
    """显示集成总结"""
    print("\n" + "="*60)
    print("日均换手率因子集成总结")
    print("="*60)
    
    print("\n✅ 已完成的工作:")
    print("  1. 创建了日均换手率因子实现 (factor_analyze/daily_turnover_rate_factor.py)")
    print("  2. 编写了详细的因子说明文档 (README_daily_turnover_rate_factor.md)")
    print("  3. 创建了完整的测试套件 (test_daily_turnover_rate_factor.py)")
    print("  4. 集成到factor_analyze模块 (__init__.py)")
    print("  5. 注册到factor_mining模块 (factor_analyze_integration.py)")
    print("  6. 创建了专用集成模块 (daily_turnover_rate_integration.py)")
    print("  7. 通过了所有集成测试")
    
    print("\n✅ 因子特性:")
    print("  - 支持个股和指数两种类型")
    print("  - 提供统一的调用接口")
    print("  - 支持真实数据和模拟数据")
    print("  - 集成到factor_mining标准流程")
    print("  - 包含完整的错误处理和日志记录")
    
    print("\n✅ 调用方式:")
    print("  1. 通过factor_mining注册器: FactorAnalyzeRegistry")
    print("  2. 通过专用集成模块: DailyTurnoverRateIntegration")
    print("  3. 直接使用因子类: DailyTurnoverRateFactor")
    
    print("\n✅ 数据来源:")
    print("  - 主要: Tushare Pro daily_basic接口")
    print("  - 备用: 模拟数据生成")
    print("  - 适配器: MarketDataAdapter, IndexDataAdapter")
    
    print("\n✅ 原始参考:")
    print("  - 网页链接: https://factors.directory/zh/factors/tech/turnover-rate")
    print("  - 因子类别: 技术因子")
    print("  - 计算周期: 日频")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    # 运行使用示例
    success = example_factor_mining_usage()
    
    # 显示集成总结
    show_integration_summary()
    
    if success:
        print("\n🎉 日均换手率因子已成功集成并可通过factor_mining模块调用。")
        sys.exit(0)
    else:
        print("\n❌ 使用示例执行失败")
        sys.exit(1)
