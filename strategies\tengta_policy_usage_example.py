# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滕泰政策研报集成使用示例
展示如何使用滕泰政策因子、研究框架和风控模块
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
project_root = Path(__file__).parent.parent  # 获取项目根目录
sys.path.append(str(project_root))

def example_factor_usage():
    """因子使用示例"""
    print("=== 滕泰政策因子使用示例 ===")
    
    try:
        from factor_analyze.macro_factors.tengta_policy_factors import (
            MonetaryPolicyLiquidityFactor,
            InterestRateImpactFactor
        )
        
        # 创建流动性因子
        liquidity_factor = MonetaryPolicyLiquidityFactor()
        liquidity_values = liquidity_factor.calculate()
        
        print(f"📊 货币政策流动性因子:")
        print(f"因子值范围: {liquidity_values.min():.3f} - {liquidity_values.max():.3f}")
        print(f"最新因子值: {liquidity_values.iloc[-1]:.3f}")
        
        # 创建利率影响因子
        rate_factor = InterestRateImpactFactor()
        rate_values = rate_factor.calculate()
        
        print(f"\n📈 利率政策影响因子:")
        print(f"因子值范围: {rate_values.min():.3f} - {rate_values.max():.3f}")
        print(f"最新因子值: {rate_values.iloc[-1]:.3f}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 因子模块导入失败: {e}")
        return False

def example_research_usage():
    """研究框架使用示例"""
    print("\n=== 滕泰政策研究框架使用示例 ===")
    
    try:
        from research.macro_research.policy_analysis.tengta_policy_framework import TengtaPolicyAnalysisFramework
        
        # 创建分析框架
        framework = TengtaPolicyAnalysisFramework()
        
        # 进行政策分析（使用滕泰研报的实际数据）
        analysis_result = framework.analyze_monetary_policy_effectiveness(
            m1_growth=4.6,      # 当前M1增速
            m2_growth=8.3,      # 当前M2增速
            interest_rate=3.0   # 当前利率水平
        )
        
        print(f"📊 政策有效性分析:")
        print(f"整体政策有效性: {analysis_result.policy_effectiveness['overall']:.2f}")
        print(f"流动性充裕程度: {analysis_result.policy_effectiveness['liquidity_adequacy']:.2f}")
        print(f"消费支撑能力: {analysis_result.policy_effectiveness['consumption_support']:.2f}")
        
        print(f"\n💡 政策建议摘要 (前2条):")
        for i, rec in enumerate(analysis_result.recommendations[:2]):
            print(f"  {i+1}. {rec}")
        
        # 计算降息影响
        debt_relief = framework.calculate_debt_relief_impact(1.0)  # 降息1%
        print(f"\n💰 降息1%的经济影响:")
        print(f"总减负效果: {debt_relief['total_relief']:.1f}万亿元")
        
        return True
        
    except ImportError as e:
        print(f"❌ 研究框架导入失败: {e}")
        return False

def example_risk_management_usage():
    """风控模块使用示例"""
    print("\n=== 滕泰政策风控模块使用示例 ===")
    
    try:
        from risk_management.specialized.tengta_policy_risk import TengtaPolicyRiskManager
        
        # 创建风险管理器
        risk_manager = TengtaPolicyRiskManager()
        
        # 模拟市场数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        market_returns = pd.Series(np.random.normal(0.001, 0.02, len(dates)), index=dates)
        
        # 进行风险评估
        risk_assessments = risk_manager.comprehensive_risk_assessment(
            m1_growth=4.6,              # 当前M1增速
            current_rate=3.0,           # 当前利率
            market_returns=market_returns
        )
        
        # 生成风险报告
        risk_report = risk_manager.generate_risk_report(risk_assessments)
        
        print(f"🛡️ 风险评估结果:")
        print(f"整体风险水平: {risk_report['overall_risk_level']}")
        
        print(f"\n📊 分项风险:")
        for risk_type, level in risk_report['assessment_summary'].items():
            print(f"  {risk_type}: {level}")
            
        print(f"\n⚠️ 主要风险 (第1个):")
        if risk_report['key_risk_factors']:
            print(f"  {risk_report['key_risk_factors'][0]}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 风控模块导入失败: {e}")
        return False

def example_data_pipeline_usage():
    """数据管道使用示例"""
    print("\n=== 滕泰政策数据管道使用示例 ===")
    
    try:
        from data_pipeline.tengta_policy_adapter import TengtaPolicyDataAdapter
        
        # 创建数据适配器
        adapter = TengtaPolicyDataAdapter()
        
        # 获取当前宏观指标
        current_indicators = adapter.get_current_indicators()
        print(f"📊 当前宏观指标:")
        for key, value in list(current_indicators.items())[:3]:
            print(f"  {key}: {value}")
        
        # 评估政策条件
        policy_conditions = adapter.evaluate_policy_conditions()
        print(f"\n🎯 政策条件评估:")
        for key, value in policy_conditions.items():
            print(f"  {key}: {value}")
        
        # 获取货币数据样本
        monetary_data = adapter.fetch_monetary_data("2023-01-01", "2023-03-31")
        print(f"\n📈 货币数据样本 (前3期):")
        print(monetary_data.head(3))
        
        return True
        
    except ImportError as e:
        print(f"❌ 数据管道模块导入失败: {e}")
        return False

def main():
    """主函数 - 运行所有使用示例"""
    print("🚀 滕泰政策研报集成系统使用示例\n")
    
    success_count = 0
    total_examples = 4
    
    # 运行各模块示例
    if example_factor_usage():
        success_count += 1
        
    if example_research_usage():
        success_count += 1
        
    if example_risk_management_usage():
        success_count += 1
        
    if example_data_pipeline_usage():
        success_count += 1
    
    print(f"\n📊 示例运行结果:")
    print(f"成功运行: {success_count}/{total_examples} 个模块")
    
    if success_count == total_examples:
        print("🎉 滕泰政策研报集成系统运行完好！")
    elif success_count > 0:
        print("⚠️ 部分模块运行成功，请检查失败模块的依赖")
    else:
        print("❌ 所有模块都未成功运行，请检查环境配置")
    
    print("\n📚 后续步骤:")
    print("1. 接入真实的宏观数据源替换模拟数据")
    print("2. 根据实际市场情况调优因子参数")  
    print("3. 集成到现有的投资策略框架")
    print("4. 建立定期的政策分析和风险评估流程")


if __name__ == "__main__":
    main()
