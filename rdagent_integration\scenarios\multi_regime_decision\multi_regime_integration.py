# -*- coding: utf-8 -*-
"""
多制度联合决策系统集成
Multi-Regime Decision System Integration

将多制度联合决策系统集成到RDAgent框架中，提供统一的接口和管理功能
"""

import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)

class MultiRegimeIntegration:
    """多制度联合决策系统集成类"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MultiRegimeIntegration")
        self.decision_system = None
        self.monitoring_system = None
        self.transition_detector = None
        self.is_initialized = False
        
        # 初始化系统
        self._initialize_systems()
    
    def _initialize_systems(self):
        """初始化各个子系统"""
        try:
            # 尝试导入多制度决策系统
            from strategy_autodev.regime_analysis.multi_regime_decision_system import (
                MultiRegimeDecisionSystem, RegimeType, RegimeState
            )
            
            # 初始化决策系统
            self.decision_system = MultiRegimeDecisionSystem()
            self.logger.info("多制度决策系统初始化成功")
            
            # 尝试初始化监控系统
            try:
                from strategy_autodev.regime_analysis.real_time_monitoring_system import (
                    RealTimeMonitoringSystem, MonitoringConfig, AlertLevel
                )
                
                # 修复配置参数问题 - 移除不支持的参数
                config = MonitoringConfig(
                    # 移除不支持的参数，使用默认值
                )
                self.monitoring_system = RealTimeMonitoringSystem(config)
                self.logger.info("实时监控系统初始化成功")
                
            except Exception as e:
                self.logger.warning(f"监控系统初始化失败: {e}")
            
            # 尝试初始化转换检测器
            try:
                from strategy_autodev.regime_analysis.regime_transition_detector import (
                    RegimeTransitionDetector, TransitionDetectionConfig
                )

                config = TransitionDetectionConfig()
                self.transition_detector = RegimeTransitionDetector(config)
                self.logger.info("制度转换检测器初始化成功")

            except Exception as e:
                self.logger.warning(f"转换检测器初始化失败: {e}")
                # 设置为None，但不影响整体初始化
                self.transition_detector = None
            
            self.is_initialized = True
            self.logger.info("多制度联合决策系统集成初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            self.is_initialized = False
    
    def generate_demo_data(self, days: int = 200) -> pd.DataFrame:
        """生成演示数据"""
        try:
            dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                                periods=days, freq='D')
            
            # 生成模拟市场数据
            np.random.seed(42)
            returns = np.random.normal(0.001, 0.02, days)
            prices = 100 * np.exp(np.cumsum(returns))
            
            volumes = np.random.lognormal(10, 0.5, days)
            
            data = pd.DataFrame({
                'close': prices,
                'volume': volumes,
                'high': prices * (1 + np.abs(np.random.normal(0, 0.01, days))),
                'low': prices * (1 - np.abs(np.random.normal(0, 0.01, days))),
                'open': prices * (1 + np.random.normal(0, 0.005, days))
            }, index=dates)

            self.logger.info(f"生成了 {days} 天的演示数据")
            return data
            
        except Exception as e:
            self.logger.error(f"生成演示数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_regimes(self, data: pd.DataFrame) -> Dict[str, Any]:
        """执行制度分析"""
        if not self.is_initialized or self.decision_system is None:
            return {"error": "系统未初始化"}
        
        try:
            # 执行制度分析
            results = self.decision_system.analyze_all_regimes(data)
            
            # 生成图表
            try:
                from strategy_autodev.regime_analysis.regime_plot_generator import get_regime_plot_generator
                plot_generator = get_regime_plot_generator()
                
                # 转换结果为可序列化格式
                serializable_results = {}
                for key, value in results.items():
                    if hasattr(value, 'to_dict'):
                        serializable_results[str(key)] = value.to_dict()
                    else:
                        serializable_results[str(key)] = str(value)
                
                # 生成制度分析图表
                plot_path = plot_generator.generate_regime_analysis_plot(serializable_results, data)
                if plot_path:
                    self.logger.info(f"制度分析图表已生成: {plot_path}")
                
            except Exception as plot_error:
                self.logger.warning(f"图表生成失败: {plot_error}")
            
            self.logger.info("制度分析完成")
            return results
            
        except Exception as e:
            self.logger.error(f"制度分析失败: {e}")
            return {"error": str(e)}
    
    def _ensure_transition_detector(self):
        """确保转换检测器可用（延迟初始化）"""
        if self.transition_detector is None:
            try:
                from strategy_autodev.regime_analysis.regime_transition_detector import (
                    RegimeTransitionDetector, TransitionDetectionConfig
                )

                config = TransitionDetectionConfig()
                self.transition_detector = RegimeTransitionDetector(config)
                self.logger.info("转换检测器延迟初始化成功")
                return True

            except Exception as e:
                self.logger.error(f"转换检测器延迟初始化失败: {e}")
                return False
        return True

    def detect_transitions(self, data: pd.DataFrame) -> Dict[str, Any]:
        """检测制度转换"""
        # 尝试确保转换检测器可用
        if not self._ensure_transition_detector():
            return {"error": "转换检测器不可用"}
        
        try:
            # 检测制度转换
            transitions = self.transition_detector.detect_transitions(data)
            
            # 生成图表
            try:
                from strategy_autodev.regime_analysis.regime_plot_generator import get_regime_plot_generator
                plot_generator = get_regime_plot_generator()
                
                # 生成转换检测图表
                plot_path = plot_generator.generate_transition_detection_plot(transitions)
                if plot_path:
                    self.logger.info(f"转换检测图表已生成: {plot_path}")
                
            except Exception as plot_error:
                self.logger.warning(f"图表生成失败: {plot_error}")
            
            self.logger.info("制度转换检测完成")
            return transitions
            
        except Exception as e:
            self.logger.error(f"制度转换检测失败: {e}")
            return {"error": str(e)}
    
    def start_monitoring(self, duration: int = 30) -> Dict[str, Any]:
        """启动实时监控"""
        if not self.monitoring_system:
            return {"error": "监控系统不可用"}
        
        try:
            # 启动监控
            self.monitoring_system.start_monitoring()
            
            self.logger.info(f"启动 {duration} 秒的实时监控")
            return {"status": "monitoring_started", "duration": duration}
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            return {"error": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "is_initialized": self.is_initialized,
            "decision_system_available": self.decision_system is not None,
            "monitoring_system_available": self.monitoring_system is not None,
            "transition_detector_available": self.transition_detector is not None,
            "timestamp": datetime.now().isoformat()
        }

    def run_complete_demo(self) -> Dict[str, Any]:
        """运行完整演示"""
        try:
            self.logger.info("开始运行完整的多制度联合决策系统演示")

            # 1. 生成演示数据
            data = self.generate_demo_data(200)
            if data.empty:
                return {"error": "无法生成演示数据"}

            # 2. 执行制度分析
            regime_results = self.analyze_regimes(data)

            # 3. 检测制度转换
            transition_results = self.detect_transitions(data)

            # 4. 启动监控
            monitoring_results = self.start_monitoring(30)

            # 5. 汇总结果
            results = {
                "demo_completed": True,
                "data_points": len(data),
                "regime_analysis": regime_results,
                "transition_detection": transition_results,
                "monitoring": monitoring_results,
                "system_status": self.get_system_status(),
                "timestamp": datetime.now().isoformat()
            }

            # 6. 生成完整演示图表
            try:
                from strategy_autodev.regime_analysis.regime_plot_generator import get_regime_plot_generator
                plot_generator = get_regime_plot_generator()
                
                plot_path = plot_generator.generate_complete_demo_plot(results)
                if plot_path:
                    self.logger.info(f"完整演示图表已生成: {plot_path}")
                
            except Exception as plot_error:
                self.logger.warning(f"完整演示图表生成失败: {plot_error}")

            self.logger.info("完整演示运行完成")
            return results

        except Exception as e:
            self.logger.error(f"演示运行失败: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

# 全局实例
_global_integration = None

def get_multi_regime_integration() -> MultiRegimeIntegration:
    """获取全局多制度集成实例"""
    global _global_integration
    if _global_integration is None:
        _global_integration = MultiRegimeIntegration()
    return _global_integration
