# -*- coding: utf-8 -*-
"""
CNN时间序列研究
CNN Time Series Research

基于Chapter19的CNN时间序列方法研究
探索卷积神经网络在金融时间序列预测中的应用
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    warnings.warn("PyTorch不可用，CNN研究功能受限")

# 可视化
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    warnings.warn("绘图库不可用")

logger = logging.getLogger(__name__)

class CNNModel(nn.Module):
    """
    PyTorch CNN模型
    """
    
    def __init__(self, input_size: int, config: Dict[str, Any]):
        super(CNNModel, self).__init__()
        
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.batch_norm_layers = nn.ModuleList()
        
        # 卷积层
        conv_layers = config.get('conv_layers', [{'filters': 32, 'kernel_size': 3}])
        in_channels = input_size
        
        for conv_config in conv_layers:
            out_channels = conv_config['filters']
            kernel_size = conv_config['kernel_size']
            
            self.conv_layers.append(nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2))
            self.pool_layers.append(nn.MaxPool1d(2))
            self.batch_norm_layers.append(nn.BatchNorm1d(out_channels))
            
            in_channels = out_channels
        
        # 计算展平后的特征数量
        self.flatten_size = self._calculate_flatten_size(config)
        
        # 全连接层
        dense_layers = config.get('dense_layers', [50])
        dropout_rate = config.get('dropout_rate', 0.3)
        
        self.fc_layers = nn.ModuleList()
        self.dropout_layers = nn.ModuleList()
        
        prev_size = self.flatten_size
        for units in dense_layers:
            self.fc_layers.append(nn.Linear(prev_size, units))
            self.dropout_layers.append(nn.Dropout(dropout_rate))
            prev_size = units
        
        # 输出层
        self.output_layer = nn.Linear(prev_size, 1)
        self.sigmoid = nn.Sigmoid()
        
    def _calculate_flatten_size(self, config: Dict[str, Any]) -> int:
        """
        计算展平后的特征数量
        """
        # 简化计算，假设序列长度为24
        sequence_length = 24
        conv_layers = config.get('conv_layers', [{'filters': 32, 'kernel_size': 3}])
        
        for _ in conv_layers:
            sequence_length = sequence_length // 2  # MaxPool1d(2)
        
        last_filters = conv_layers[-1]['filters'] if conv_layers else 32
        return sequence_length * last_filters
    
    def forward(self, x):
        # 卷积层
        for conv, pool, bn in zip(self.conv_layers, self.pool_layers, self.batch_norm_layers):
            x = torch.relu(conv(x))
            x = pool(x)
            x = bn(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        for fc, dropout in zip(self.fc_layers, self.dropout_layers):
            x = torch.relu(fc(x))
            x = dropout(x)
        
        # 输出层
        x = self.output_layer(x)
        x = self.sigmoid(x)
        
        return x

class CNNTimeSeriesResearch:
    """
    CNN时间序列研究类
    
    研究内容：
    1. CNN架构对时间序列预测的影响
    2. 不同卷积核大小和滤波器数量的效果
    3. 多尺度特征提取
    4. 时间序列数据的预处理方法
    5. 特征工程对CNN性能的影响
    """
    
    def __init__(self):
        self.research_results = {}
        self.experiment_history = []
        self.best_models = {}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"CNN时间序列研究初始化完成，使用设备: {self.device}")
    
    def research_cnn_architectures(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        研究不同CNN架构的效果
        
        Args:
            data: 时间序列数据
            
        Returns:
            研究结果
        """
        if not PYTORCH_AVAILABLE:
            logger.error("PyTorch不可用，无法进行CNN架构研究")
            return {}
        
        logger.info("开始CNN架构研究")
        
        # 定义不同的架构配置
        architectures = {
            'simple_cnn': {
                'conv_layers': [{'filters': 32, 'kernel_size': 3}],
                'dense_layers': [50]
            },
            'deep_cnn': {
                'conv_layers': [
                    {'filters': 32, 'kernel_size': 3},
                    {'filters': 64, 'kernel_size': 3}
                ],
                'dense_layers': [100, 50]
            },
            'wide_cnn': {
                'conv_layers': [{'filters': 128, 'kernel_size': 3}],
                'dense_layers': [100]
            },
            'multi_scale_cnn': {
                'conv_layers': [
                    {'filters': 32, 'kernel_size': 3},
                    {'filters': 32, 'kernel_size': 5},
                    {'filters': 32, 'kernel_size': 7}
                ],
                'dense_layers': [100]
            }
        }
        
        results = {}
        
        # 准备数据
        X, y = self._prepare_research_data(data)
        
        if len(X) == 0:
            logger.error("研究数据准备失败")
            return {}
        
        # 测试每种架构
        for arch_name, config in architectures.items():
            logger.info(f"测试架构: {arch_name}")
            
            try:
                # 构建模型
                model = self._build_research_model(X.shape[-1], config)
                
                # 训练和评估
                metrics = self._train_and_evaluate_model(model, X, y, arch_name)
                
                results[arch_name] = {
                    'config': config,
                    'metrics': metrics,
                    'model_params': sum(p.numel() for p in model.parameters())
                }
                
                logger.info(f"{arch_name} 完成: 准确率={metrics.get('accuracy', 0):.3f}")
                
            except Exception as e:
                logger.error(f"架构 {arch_name} 测试失败: {e}")
                results[arch_name] = {'error': str(e)}
        
        # 保存研究结果
        self.research_results['cnn_architectures'] = results
        
        # 分析最佳架构
        best_arch = self._analyze_best_architecture(results)
        
        logger.info(f"CNN架构研究完成，最佳架构: {best_arch}")
        
        return results
    
    def research_hyperparameters(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        研究超参数对CNN性能的影响
        
        Args:
            data: 时间序列数据
            
        Returns:
            超参数研究结果
        """
        logger.info("开始CNN超参数研究")
        
        # 定义超参数搜索空间
        param_grid = {
            'learning_rate': [0.001, 0.01, 0.1],
            'batch_size': [16, 32, 64],
            'sequence_length': [12, 24, 48],
            'filters': [16, 32, 64],
            'kernel_size': [3, 5, 7],
            'dropout_rate': [0.1, 0.3, 0.5]
        }
        
        results = {}
        
        # 准备数据
        base_X, base_y = self._prepare_research_data(data)
        
        # 测试每个超参数
        for param_name, param_values in param_grid.items():
            logger.info(f"测试超参数: {param_name}")
            
            param_results = []
            
            for value in param_values:
                try:
                    # 根据参数调整数据和模型
                    if param_name == 'sequence_length':
                        X, y = self._prepare_research_data(data, sequence_length=value)
                    else:
                        X, y = base_X, base_y
                    
                    if len(X) == 0:
                        continue
                    
                    # 构建模型
                    model_config = self._get_base_model_config()
                    
                    if param_name in ['filters', 'kernel_size', 'dropout_rate']:
                        if param_name == 'filters':
                            model_config['conv_layers'][0]['filters'] = value
                        elif param_name == 'kernel_size':
                            model_config['conv_layers'][0]['kernel_size'] = value
                        elif param_name == 'dropout_rate':
                            model_config['dropout_rate'] = value
                    
                    model = self._build_research_model(X.shape[-1], model_config)
                    
                    # 训练参数
                    train_params = {
                        'learning_rate': 0.001,
                        'batch_size': 32,
                        'epochs': 20
                    }
                    
                    if param_name in ['learning_rate', 'batch_size']:
                        train_params[param_name] = value
                    
                    # 训练和评估
                    metrics = self._train_and_evaluate_model(
                        model, X, y, f"{param_name}_{value}", **train_params
                    )
                    
                    param_results.append({
                        'value': value,
                        'metrics': metrics
                    })
                    
                    logger.info(f"{param_name}={value}: 准确率={metrics.get('accuracy', 0):.3f}")
                    
                except Exception as e:
                    logger.error(f"超参数 {param_name}={value} 测试失败: {e}")
            
            results[param_name] = param_results
        
        # 保存研究结果
        self.research_results['hyperparameters'] = results
        
        logger.info("CNN超参数研究完成")
        
        return results
    
    def research_feature_engineering(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        研究特征工程对CNN性能的影响
        
        Args:
            data: 原始时间序列数据
            
        Returns:
            特征工程研究结果
        """
        logger.info("开始特征工程研究")
        
        # 定义不同的特征组合
        feature_sets = {
            'price_only': ['close'],
            'ohlc': ['open', 'high', 'low', 'close'],
            'price_volume': ['open', 'high', 'low', 'close', 'volume'],
            'technical_indicators': ['close', 'ma_5', 'ma_20', 'rsi', 'macd'],
            'returns': ['returns', 'log_returns'],
            'all_features': ['open', 'high', 'low', 'close', 'volume', 'returns', 'ma_5', 'ma_20', 'rsi', 'macd']
        }
        
        results = {}
        
        for feature_name, features in feature_sets.items():
            logger.info(f"测试特征集: {feature_name}")
            
            try:
                # 准备特征数据
                feature_data = self._prepare_feature_data(data, features)
                
                if feature_data is None or len(feature_data) == 0:
                    logger.warning(f"特征集 {feature_name} 数据为空")
                    continue
                
                # 准备序列数据
                X, y = self._prepare_research_data(feature_data)
                
                if len(X) == 0:
                    continue
                
                # 构建和训练模型
                model_config = self._get_base_model_config()
                model = self._build_research_model(X.shape[-1], model_config)
                
                metrics = self._train_and_evaluate_model(model, X, y, feature_name)
                
                results[feature_name] = {
                    'features': features,
                    'feature_count': len(features),
                    'metrics': metrics
                }
                
                logger.info(f"{feature_name} 完成: 准确率={metrics.get('accuracy', 0):.3f}")
                
            except Exception as e:
                logger.error(f"特征集 {feature_name} 测试失败: {e}")
                results[feature_name] = {'error': str(e)}
        
        # 保存研究结果
        self.research_results['feature_engineering'] = results
        
        logger.info("特征工程研究完成")
        
        return results
    
    def _prepare_research_data(self, data: pd.DataFrame, sequence_length: int = 24) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备研究用的序列数据
        """
        try:
            # 计算技术指标
            df = data.copy()
            
            # 基础特征
            if 'close' in df.columns:
                df['returns'] = df['close'].pct_change()
                df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
                
                # 移动平均
                df['ma_5'] = df['close'].rolling(window=5).mean()
                df['ma_20'] = df['close'].rolling(window=20).mean()
                
                # RSI
                df['rsi'] = self._calculate_rsi(df['close'])
                
                # MACD
                df['macd'], _ = self._calculate_macd(df['close'])
            
            # 目标变量（价格方向）
            df['target'] = (df['close'].shift(-1) > df['close']).astype(int)
            
            # 删除NaN
            df = df.dropna()
            
            if len(df) < sequence_length + 1:
                return np.array([]), np.array([])
            
            # 选择特征列
            feature_cols = [col for col in df.columns if col not in ['target']]
            
            # 标准化
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(df[feature_cols])
            
            # 创建序列
            X, y = [], []
            
            for i in range(len(scaled_features) - sequence_length):
                X.append(scaled_features[i:(i + sequence_length)])
                y.append(df['target'].iloc[i + sequence_length])
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            logger.error(f"研究数据准备失败: {e}")
            return np.array([]), np.array([])
    
    def _prepare_feature_data(self, data: pd.DataFrame, features: List[str]) -> Optional[pd.DataFrame]:
        """
        准备特定特征的数据
        """
        try:
            df = data.copy()
            
            # 计算衍生特征
            if 'returns' in features and 'close' in df.columns:
                df['returns'] = df['close'].pct_change()
            
            if 'log_returns' in features and 'close' in df.columns:
                df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            
            if 'ma_5' in features and 'close' in df.columns:
                df['ma_5'] = df['close'].rolling(window=5).mean()
            
            if 'ma_20' in features and 'close' in df.columns:
                df['ma_20'] = df['close'].rolling(window=20).mean()
            
            if 'rsi' in features and 'close' in df.columns:
                df['rsi'] = self._calculate_rsi(df['close'])
            
            if 'macd' in features and 'close' in df.columns:
                df['macd'], _ = self._calculate_macd(df['close'])
            
            # 选择需要的特征
            available_features = [f for f in features if f in df.columns]
            
            if len(available_features) == 0:
                return None
            
            return df[available_features]
            
        except Exception as e:
            logger.error(f"特征数据准备失败: {e}")
            return None
    
    def _build_research_model(self, input_size: int, config: Dict[str, Any]) -> CNNModel:
        """
        构建研究用的CNN模型
        """
        model = CNNModel(input_size, config)
        model.to(self.device)
        return model
    
    def _train_and_evaluate_model(self, model: CNNModel, X: np.ndarray, y: np.ndarray, 
                                 name: str, **train_params) -> Dict[str, float]:
        """
        训练和评估模型
        """
        try:
            # 默认训练参数
            learning_rate = train_params.get('learning_rate', 0.001)
            batch_size = train_params.get('batch_size', 32)
            epochs = train_params.get('epochs', 20)
            
            # 转换数据格式
            X = torch.FloatTensor(X).transpose(1, 2).to(self.device)  # (batch, features, sequence)
            y = torch.FloatTensor(y).to(self.device)
            
            # 分割数据
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # 优化器和损失函数
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            criterion = nn.BCELoss()
            
            # 训练模型
            model.train()
            best_val_loss = float('inf')
            patience = 5
            patience_counter = 0
            
            for epoch in range(epochs):
                train_loss = 0.0
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch_X).squeeze()
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # 验证
                model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = model(batch_X).squeeze()
                        loss = criterion(outputs, batch_y)
                        val_loss += loss.item()
                
                val_loss /= len(val_loader)
                
                # 早停
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        break
                
                model.train()
            
            # 评估模型
            model.eval()
            with torch.no_grad():
                y_pred_proba = model(X_val).squeeze().cpu().numpy()
                y_pred = (y_pred_proba > 0.5).astype(int)
                y_val_np = y_val.cpu().numpy()
            
            metrics = {
                'accuracy': accuracy_score(y_val_np, y_pred),
                'precision': precision_score(y_val_np, y_pred, zero_division=0),
                'recall': recall_score(y_val_np, y_pred, zero_division=0),
                'f1': f1_score(y_val_np, y_pred, zero_division=0),
                'auc': roc_auc_score(y_val_np, y_pred_proba),
                'final_val_loss': best_val_loss
            }
            
            # 记录实验
            self.experiment_history.append({
                'name': name,
                'timestamp': datetime.now(),
                'metrics': metrics,
                'model_params': sum(p.numel() for p in model.parameters())
            })
            
            return metrics
            
        except Exception as e:
            logger.error(f"模型训练评估失败: {e}")
            return {}
    
    def _get_base_model_config(self) -> Dict[str, Any]:
        """
        获取基础模型配置
        """
        return {
            'conv_layers': [{'filters': 32, 'kernel_size': 3}],
            'dense_layers': [50],
            'dropout_rate': 0.3
        }
    
    def _analyze_best_architecture(self, results: Dict[str, Any]) -> str:
        """
        分析最佳架构
        """
        best_arch = None
        best_score = 0
        
        for arch_name, result in results.items():
            if 'metrics' in result:
                accuracy = result['metrics'].get('accuracy', 0)
                if accuracy > best_score:
                    best_score = accuracy
                    best_arch = arch_name
        
        return best_arch or 'unknown'
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """
        计算MACD指标
        """
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def generate_research_report(self) -> str:
        """
        生成研究报告
        """
        report = []
        report.append("# CNN时间序列研究报告")
        report.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("\n## 研究概述")
        report.append("本研究探索了卷积神经网络在金融时间序列预测中的应用，")
        report.append("包括架构设计、超参数优化和特征工程的影响。")
        
        # 架构研究结果
        if 'cnn_architectures' in self.research_results:
            report.append("\n## CNN架构研究")
            arch_results = self.research_results['cnn_architectures']
            
            for arch_name, result in arch_results.items():
                if 'metrics' in result:
                    metrics = result['metrics']
                    report.append(f"\n### {arch_name}")
                    report.append(f"- 准确率: {metrics.get('accuracy', 0):.3f}")
                    report.append(f"- AUC: {metrics.get('auc', 0):.3f}")
                    report.append(f"- 模型参数: {result.get('model_params', 0)}")
        
        # 超参数研究结果
        if 'hyperparameters' in self.research_results:
            report.append("\n## 超参数研究")
            param_results = self.research_results['hyperparameters']
            
            for param_name, results in param_results.items():
                if results:
                    best_result = max(results, key=lambda x: x['metrics'].get('accuracy', 0))
                    report.append(f"\n### {param_name}")
                    report.append(f"- 最佳值: {best_result['value']}")
                    report.append(f"- 最佳准确率: {best_result['metrics'].get('accuracy', 0):.3f}")
        
        # 特征工程研究结果
        if 'feature_engineering' in self.research_results:
            report.append("\n## 特征工程研究")
            feature_results = self.research_results['feature_engineering']
            
            for feature_name, result in feature_results.items():
                if 'metrics' in result:
                    metrics = result['metrics']
                    report.append(f"\n### {feature_name}")
                    report.append(f"- 特征数量: {result.get('feature_count', 0)}")
                    report.append(f"- 准确率: {metrics.get('accuracy', 0):.3f}")
                    report.append(f"- AUC: {metrics.get('auc', 0):.3f}")
        
        # 实验历史
        if self.experiment_history:
            report.append("\n## 实验历史")
            report.append(f"总实验次数: {len(self.experiment_history)}")
            
            best_experiment = max(self.experiment_history, 
                                key=lambda x: x['metrics'].get('accuracy', 0))
            report.append(f"\n最佳实验: {best_experiment['name']}")
            report.append(f"最佳准确率: {best_experiment['metrics'].get('accuracy', 0):.3f}")
        
        report.append("\n## 结论")
        report.append("CNN在时间序列预测中显示出良好的潜力，")
        report.append("适当的架构设计和特征工程可以显著提升预测性能。")
        
        return "\n".join(report)
    
    def save_research_results(self, filepath: str) -> bool:
        """
        保存研究结果
        """
        try:
            import json
            
            # 准备保存数据
            save_data = {
                'research_results': self.research_results,
                'experiment_count': len(self.experiment_history),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"研究结果已保存到: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"保存研究结果失败: {e}")
            return False

# 研究执行函数
def run_cnn_research(data: pd.DataFrame) -> CNNTimeSeriesResearch:
    """
    运行完整的CNN研究
    
    Args:
        data: 时间序列数据
        
    Returns:
        研究对象
    """
    research = CNNTimeSeriesResearch()
    
    logger.info("开始CNN时间序列研究")
    
    # 1. 架构研究
    research.research_cnn_architectures(data)
    
    # 2. 超参数研究
    research.research_hyperparameters(data)
    
    # 3. 特征工程研究
    research.research_feature_engineering(data)
    
    logger.info("CNN研究完成")
    
    return research

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 示例：生成测试数据并运行研究
    np.random.seed(42)
    dates = pd.date_range(start='2020-01-01', periods=500, freq='D')
    
    # 生成模拟股价数据
    returns = np.random.normal(0.001, 0.02, 500)
    prices = [100]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    test_data = pd.DataFrame({
        'date': dates,
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.lognormal(10, 0.5, 500)
    })
    test_data.set_index('date', inplace=True)
    
    # 运行研究
    research = run_cnn_research(test_data)
    
    # 生成报告
    report = research.generate_research_report()
    print(report)