# -*- coding: utf-8 -*-
"""
均值回归因子 (Mean Reversion Factor)
Mean Reversion Factor Implementation

基于"Hands-On Machine Learning for Algorithmic Trading" Chapter 05
实现均值回归因子，适配自Zipline的MeanReversion因子

参考文献:
- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, & <PERSON>, S. (1993). Returns to buying winners and selling losers
- Lo, A. W., & MacKinlay, A. C. (1990). When are contrarian profits due to stock market overreaction?
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
from datetime import datetime, timedelta
import warnings

# 导入数据适配器
try:
    from strategy_autodev.data_adapter import DataAdapterManager, BaseDataAdapter
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from ...data_adapter import DataAdapterManager, BaseDataAdapter
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        logging.warning("数据适配器不可用，使用模拟数据")

# 导入基础策略类
try:
    from strategy_autodev.core.base_strategy import BaseStrategy
    BASE_STRATEGY_AVAILABLE = True
except ImportError:
    try:
        from strategy_autodev.base_strategy import BaseStrategy
        BASE_STRATEGY_AVAILABLE = True
    except ImportError:
        try:
            from ...core.base_strategy import BaseStrategy
            BASE_STRATEGY_AVAILABLE = True
        except ImportError:
            BASE_STRATEGY_AVAILABLE = False
            logging.warning("基础策略类不可用，使用简化实现")

logger = logging.getLogger(__name__)

# 如果基础策略类不可用，定义简化版本
if not BASE_STRATEGY_AVAILABLE:
    class BaseStrategy:
        def __init__(self, **kwargs):
            self.params = kwargs
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def initialize(self):
            pass
        
        def handle_data(self, context, data):
            pass

class MeanReversionFactor(BaseStrategy):
    """
    均值回归因子
    
    基于价格相对于移动平均线的偏离程度计算均值回归信号
    适配自Zipline的MeanReversion因子，使用data_adapter获取真实数据
    """
    
    def __init__(self,
                 lookback_period: int = 20,
                 zscore_window: int = 252,
                 entry_threshold: float = 2.0,
                 exit_threshold: float = 0.5,
                 max_position_size: float = 0.1,
                 rebalance_frequency: str = 'daily',
                 data_adapter_name: str = 'default',
                 **kwargs):
        """
        初始化均值回归因子
        
        Args:
            lookback_period: 移动平均线周期
            zscore_window: Z-score计算窗口
            entry_threshold: 入场阈值（Z-score绝对值）
            exit_threshold: 出场阈值（Z-score绝对值）
            max_position_size: 最大仓位大小
            rebalance_frequency: 再平衡频率
            data_adapter_name: 数据适配器名称
        """
        super().__init__(**kwargs)
        
        self.lookback_period = lookback_period
        self.zscore_window = zscore_window
        self.entry_threshold = entry_threshold
        self.exit_threshold = exit_threshold
        self.max_position_size = max_position_size
        self.rebalance_frequency = rebalance_frequency
        self.data_adapter_name = data_adapter_name
        
        self.logger = logging.getLogger(f"{__name__}.MeanReversionFactor")
        
        # 初始化数据适配器
        if DATA_ADAPTER_AVAILABLE:
            self.data_manager = DataAdapterManager()
            self.data_adapter = self.data_manager.get_adapter(data_adapter_name)
        else:
            self.data_manager = None
            self.data_adapter = None
            self.logger.warning("数据适配器不可用，将使用模拟数据")
        
        # 存储因子数据
        self.factor_data = {}
        self.price_data = {}
        self.signal_data = {}
        self.position_data = {}
        
        # 性能指标
        self.performance_metrics = {}
        
        self.logger.info(f"均值回归因子初始化完成，回看期: {lookback_period}")
    
    def fetch_market_data(self, 
                         symbols: List[str],
                         start_date: str,
                         end_date: str,
                         fields: List[str] = None) -> Dict[str, pd.DataFrame]:
        """
        获取市场数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 数据字段
            
        Returns:
            市场数据字典
        """
        try:
            if fields is None:
                fields = ['open', 'high', 'low', 'close', 'volume']
            
            market_data = {}
            
            if self.data_adapter:
                # 使用真实数据适配器
                for symbol in symbols:
                    try:
                        data = self.data_adapter.fetch_data(
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date,
                            fields=fields
                        )
                        
                        if data is not None and not data.empty:
                            market_data[symbol] = data
                        else:
                            self.logger.warning(f"无法获取{symbol}的数据，使用模拟数据")
                            market_data[symbol] = self._generate_mock_data(
                                start_date, end_date, symbol)
                    
                    except Exception as e:
                        self.logger.error(f"获取{symbol}数据失败: {e}，使用模拟数据")
                        market_data[symbol] = self._generate_mock_data(
                            start_date, end_date, symbol)
            else:
                # 使用模拟数据
                for symbol in symbols:
                    market_data[symbol] = self._generate_mock_data(
                        start_date, end_date, symbol)
            
            self.logger.info(f"成功获取{len(market_data)}只股票的数据")
            return market_data
            
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return {}
    
    def _generate_mock_data(self, 
                           start_date: str, 
                           end_date: str, 
                           symbol: str) -> pd.DataFrame:
        """
        生成模拟数据
        """
        try:
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            n_days = len(dates)
            
            # 生成随机价格数据
            np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
            
            # 基础价格
            base_price = 100 + (hash(symbol) % 100)
            
            # 生成价格序列（几何布朗运动）
            returns = np.random.normal(0.0005, 0.02, n_days)
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            prices = np.array(prices)
            
            # 生成OHLC数据
            high_noise = np.random.uniform(0.005, 0.02, n_days)
            low_noise = np.random.uniform(-0.02, -0.005, n_days)
            
            data = pd.DataFrame({
                'open': prices * (1 + np.random.uniform(-0.01, 0.01, n_days)),
                'high': prices * (1 + high_noise),
                'low': prices * (1 + low_noise),
                'close': prices,
                'volume': np.random.randint(100000, 1000000, n_days)
            }, index=dates)
            
            return data
            
        except Exception as e:
            self.logger.error(f"生成模拟数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_mean_reversion_factor(self, 
                                      price_data: pd.DataFrame,
                                      symbol: str) -> pd.Series:
        """
        计算均值回归因子
        
        Args:
            price_data: 价格数据
            symbol: 股票代码
            
        Returns:
            均值回归因子序列
        """
        try:
            if price_data.empty or 'close' not in price_data.columns:
                return pd.Series()
            
            close_prices = price_data['close']
            
            # 1. 计算移动平均线
            moving_avg = close_prices.rolling(window=self.lookback_period).mean()
            
            # 2. 计算价格偏离度
            price_deviation = (close_prices - moving_avg) / moving_avg
            
            # 3. 计算滚动标准差
            rolling_std = price_deviation.rolling(window=self.zscore_window).std()
            
            # 4. 计算Z-score
            zscore = price_deviation / rolling_std
            
            # 5. 计算均值回归因子（负Z-score，因为我们预期价格回归）
            mean_reversion_factor = -zscore
            
            # 6. 标准化因子值
            factor_mean = mean_reversion_factor.rolling(window=self.zscore_window).mean()
            factor_std = mean_reversion_factor.rolling(window=self.zscore_window).std()
            
            standardized_factor = (mean_reversion_factor - factor_mean) / factor_std
            
            # 存储中间结果
            self.factor_data[symbol] = {
                'moving_avg': moving_avg,
                'price_deviation': price_deviation,
                'zscore': zscore,
                'raw_factor': mean_reversion_factor,
                'standardized_factor': standardized_factor
            }
            
            self.logger.debug(f"计算{symbol}均值回归因子完成")
            return standardized_factor.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算{symbol}均值回归因子失败: {e}")
            return pd.Series()
    
    def generate_trading_signals(self, 
                               factor_values: pd.Series,
                               symbol: str) -> pd.Series:
        """
        生成交易信号
        
        Args:
            factor_values: 因子值序列
            symbol: 股票代码
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        try:
            if factor_values.empty:
                return pd.Series()
            
            signals = pd.Series(0, index=factor_values.index)
            
            # 当前持仓状态
            current_position = 0
            
            for i, (date, factor_value) in enumerate(factor_values.items()):
                if pd.isna(factor_value):
                    signals[date] = 0
                    continue
                
                # 入场信号
                if abs(factor_value) >= self.entry_threshold:
                    if factor_value > 0 and current_position <= 0:
                        # 买入信号
                        signals[date] = 1
                        current_position = 1
                    elif factor_value < 0 and current_position >= 0:
                        # 卖出信号
                        signals[date] = -1
                        current_position = -1
                    else:
                        signals[date] = 0
                
                # 出场信号
                elif abs(factor_value) <= self.exit_threshold:
                    if current_position != 0:
                        # 平仓信号
                        signals[date] = -current_position
                        current_position = 0
                    else:
                        signals[date] = 0
                
                else:
                    # 持有信号
                    signals[date] = 0
            
            # 存储信号数据
            self.signal_data[symbol] = signals
            
            self.logger.debug(f"生成{symbol}交易信号完成")
            return signals
            
        except Exception as e:
            self.logger.error(f"生成{symbol}交易信号失败: {e}")
            return pd.Series()
    
    def calculate_position_sizes(self, 
                               signals: pd.Series,
                               factor_values: pd.Series,
                               symbol: str) -> pd.Series:
        """
        计算仓位大小
        
        Args:
            signals: 交易信号
            factor_values: 因子值
            symbol: 股票代码
            
        Returns:
            仓位大小序列
        """
        try:
            if signals.empty or factor_values.empty:
                return pd.Series()
            
            positions = pd.Series(0.0, index=signals.index)
            current_position = 0.0
            
            for date in signals.index:
                signal = signals[date]
                factor_value = factor_values[date]
                
                if signal != 0 and not pd.isna(factor_value):
                    # 基于因子强度调整仓位大小
                    factor_strength = min(abs(factor_value) / self.entry_threshold, 2.0)
                    position_size = self.max_position_size * factor_strength
                    
                    if signal > 0:
                        # 买入
                        current_position = position_size
                    elif signal < 0:
                        # 卖出
                        current_position = -position_size
                    else:
                        # 平仓
                        current_position = 0.0
                
                positions[date] = current_position
            
            # 前向填充仓位
            positions = positions.fillna(method='ffill').fillna(0)
            
            # 存储仓位数据
            self.position_data[symbol] = positions
            
            self.logger.debug(f"计算{symbol}仓位大小完成")
            return positions
            
        except Exception as e:
            self.logger.error(f"计算{symbol}仓位大小失败: {e}")
            return pd.Series()
    
    def run_factor_analysis(self, 
                          symbols: List[str],
                          start_date: str,
                          end_date: str) -> Dict[str, Any]:
        """
        运行因子分析
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子分析结果
        """
        try:
            # 获取市场数据
            market_data = self.fetch_market_data(symbols, start_date, end_date)
            
            if not market_data:
                return {'error': '无法获取市场数据'}
            
            factor_results = {}
            
            for symbol in symbols:
                if symbol not in market_data:
                    continue
                
                price_data = market_data[symbol]
                
                # 计算因子值
                factor_values = self.calculate_mean_reversion_factor(price_data, symbol)
                
                if factor_values.empty:
                    continue
                
                # 生成交易信号
                signals = self.generate_trading_signals(factor_values, symbol)
                
                # 计算仓位大小
                positions = self.calculate_position_sizes(signals, factor_values, symbol)
                
                # 计算收益率
                returns = self._calculate_strategy_returns(price_data, positions)
                
                # 计算性能指标
                performance = self._calculate_performance_metrics(returns, symbol)
                
                factor_results[symbol] = {
                    'factor_values': factor_values,
                    'signals': signals,
                    'positions': positions,
                    'returns': returns,
                    'performance': performance
                }
            
            # 计算组合层面的结果
            portfolio_results = self._calculate_portfolio_results(factor_results)
            
            analysis_result = {
                'individual_results': factor_results,
                'portfolio_results': portfolio_results,
                'analysis_period': {'start': start_date, 'end': end_date},
                'parameters': {
                    'lookback_period': self.lookback_period,
                    'zscore_window': self.zscore_window,
                    'entry_threshold': self.entry_threshold,
                    'exit_threshold': self.exit_threshold,
                    'max_position_size': self.max_position_size
                },
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"因子分析完成，分析了{len(factor_results)}只股票")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"因子分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_strategy_returns(self, 
                                  price_data: pd.DataFrame,
                                  positions: pd.Series) -> pd.Series:
        """
        计算策略收益率
        """
        try:
            if price_data.empty or positions.empty:
                return pd.Series()
            
            # 计算价格变化率
            close_prices = price_data['close']
            price_returns = close_prices.pct_change().fillna(0)
            
            # 对齐数据
            aligned_positions = positions.reindex(price_returns.index, method='ffill').fillna(0)
            
            # 计算策略收益率（滞后一期的仓位）
            lagged_positions = aligned_positions.shift(1).fillna(0)
            strategy_returns = lagged_positions * price_returns
            
            return strategy_returns.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算策略收益率失败: {e}")
            return pd.Series()
    
    def _calculate_performance_metrics(self, 
                                     returns: pd.Series,
                                     symbol: str) -> Dict[str, float]:
        """
        计算性能指标
        """
        try:
            if returns.empty:
                return {}
            
            # 基础指标
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1
            annual_vol = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
            
            # 最大回撤
            cumulative_returns = (1 + returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # 胜率
            win_rate = (returns > 0).mean()
            
            # 信息比率
            excess_returns = returns - returns.mean()
            information_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            
            # 偏度和峰度
            skewness = returns.skew()
            kurtosis = returns.kurtosis()
            
            # VaR和CVaR
            var_95 = np.percentile(returns, 5)
            cvar_95 = returns[returns <= var_95].mean()
            
            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'annual_volatility': annual_vol,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'information_ratio': information_ratio,
                'skewness': skewness,
                'kurtosis': kurtosis,
                'var_95': var_95,
                'cvar_95': cvar_95,
                'num_trades': (returns != 0).sum(),
                'avg_trade_return': returns[returns != 0].mean() if (returns != 0).any() else 0
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {}
    
    def _calculate_portfolio_results(self, 
                                   factor_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算组合层面的结果
        """
        try:
            if not factor_results:
                return {}
            
            # 收集所有股票的收益率
            all_returns = {}
            for symbol, results in factor_results.items():
                if 'returns' in results and not results['returns'].empty:
                    all_returns[symbol] = results['returns']
            
            if not all_returns:
                return {}
            
            # 创建收益率矩阵
            returns_df = pd.DataFrame(all_returns)
            
            # 等权重组合收益率
            portfolio_returns = returns_df.mean(axis=1)
            
            # 计算组合性能指标
            portfolio_performance = self._calculate_performance_metrics(
                portfolio_returns, 'portfolio')
            
            # 因子有效性分析
            factor_effectiveness = self._analyze_factor_effectiveness(factor_results)
            
            # 相关性分析
            correlation_analysis = self._analyze_correlations(returns_df)
            
            portfolio_results = {
                'portfolio_returns': portfolio_returns,
                'portfolio_performance': portfolio_performance,
                'factor_effectiveness': factor_effectiveness,
                'correlation_analysis': correlation_analysis,
                'num_stocks': len(all_returns)
            }
            
            return portfolio_results
            
        except Exception as e:
            self.logger.error(f"计算组合结果失败: {e}")
            return {}
    
    def _analyze_factor_effectiveness(self, 
                                    factor_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析因子有效性
        """
        try:
            effectiveness_metrics = {
                'avg_sharpe_ratio': 0,
                'avg_information_ratio': 0,
                'avg_win_rate': 0,
                'positive_sharpe_ratio_pct': 0,
                'factor_stability': 0
            }
            
            sharpe_ratios = []
            information_ratios = []
            win_rates = []
            
            for symbol, results in factor_results.items():
                performance = results.get('performance', {})
                
                if performance:
                    sharpe_ratios.append(performance.get('sharpe_ratio', 0))
                    information_ratios.append(performance.get('information_ratio', 0))
                    win_rates.append(performance.get('win_rate', 0))
            
            if sharpe_ratios:
                effectiveness_metrics['avg_sharpe_ratio'] = np.mean(sharpe_ratios)
                effectiveness_metrics['avg_information_ratio'] = np.mean(information_ratios)
                effectiveness_metrics['avg_win_rate'] = np.mean(win_rates)
                effectiveness_metrics['positive_sharpe_ratio_pct'] = np.mean(np.array(sharpe_ratios) > 0)
                effectiveness_metrics['factor_stability'] = 1 - np.std(sharpe_ratios) / (abs(np.mean(sharpe_ratios)) + 1e-6)
            
            return effectiveness_metrics
            
        except Exception as e:
            self.logger.error(f"分析因子有效性失败: {e}")
            return {}
    
    def _analyze_correlations(self, returns_df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析相关性
        """
        try:
            if returns_df.empty:
                return {}
            
            # 计算相关性矩阵
            correlation_matrix = returns_df.corr()
            
            # 平均相关性
            avg_correlation = correlation_matrix.values[np.triu_indices_from(
                correlation_matrix.values, k=1)].mean()
            
            # 最大和最小相关性
            upper_triangle = correlation_matrix.values[np.triu_indices_from(
                correlation_matrix.values, k=1)]
            max_correlation = upper_triangle.max()
            min_correlation = upper_triangle.min()
            
            correlation_analysis = {
                'avg_correlation': avg_correlation,
                'max_correlation': max_correlation,
                'min_correlation': min_correlation,
                'correlation_matrix': correlation_matrix.to_dict()
            }
            
            return correlation_analysis
            
        except Exception as e:
            self.logger.error(f"分析相关性失败: {e}")
            return {}
    
    def generate_report(self) -> str:
        """
        生成因子分析报告
        """
        try:
            if not hasattr(self, 'analysis_result') or not self.analysis_result:
                return "没有分析结果可供报告"
            
            result = self.analysis_result
            
            report = f"""
=== 均值回归因子分析报告 ===

分析时间: {result.get('analysis_timestamp', 'Unknown')}
分析期间: {result.get('analysis_period', {}).get('start', 'Unknown')} 至 {result.get('analysis_period', {}).get('end', 'Unknown')}

=== 参数设置 ===
回看期: {self.lookback_period}
Z-score窗口: {self.zscore_window}
入场阈值: {self.entry_threshold}
出场阈值: {self.exit_threshold}
最大仓位: {self.max_position_size:.1%}

=== 组合表现 ===
"""
            
            portfolio_performance = result.get('portfolio_results', {}).get('portfolio_performance', {})
            if portfolio_performance:
                report += f"""
年化收益率: {portfolio_performance.get('annual_return', 0):.2%}
年化波动率: {portfolio_performance.get('annual_volatility', 0):.2%}
夏普比率: {portfolio_performance.get('sharpe_ratio', 0):.3f}
最大回撤: {portfolio_performance.get('max_drawdown', 0):.2%}
胜率: {portfolio_performance.get('win_rate', 0):.1%}
信息比率: {portfolio_performance.get('information_ratio', 0):.3f}
"""
            
            # 因子有效性
            factor_effectiveness = result.get('portfolio_results', {}).get('factor_effectiveness', {})
            if factor_effectiveness:
                report += f"""

=== 因子有效性 ===
平均夏普比率: {factor_effectiveness.get('avg_sharpe_ratio', 0):.3f}
平均信息比率: {factor_effectiveness.get('avg_information_ratio', 0):.3f}
平均胜率: {factor_effectiveness.get('avg_win_rate', 0):.1%}
正夏普比率占比: {factor_effectiveness.get('positive_sharpe_ratio_pct', 0):.1%}
因子稳定性: {factor_effectiveness.get('factor_stability', 0):.3f}
"""
            
            # 个股表现摘要
            individual_results = result.get('individual_results', {})
            if individual_results:
                report += "\n=== 个股表现摘要 ===\n"
                report += "股票代码\t年化收益\t夏普比率\t最大回撤\t胜率\n"
                report += "-" * 60 + "\n"
                
                for symbol, stock_result in individual_results.items():
                    perf = stock_result.get('performance', {})
                    report += f"{symbol}\t{perf.get('annual_return', 0):.2%}\t"
                    report += f"{perf.get('sharpe_ratio', 0):.3f}\t"
                    report += f"{perf.get('max_drawdown', 0):.2%}\t"
                    report += f"{perf.get('win_rate', 0):.1%}\n"
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return f"报告生成失败: {e}"


if __name__ == "__main__":
    # 测试代码
    factor = MeanReversionFactor(
        lookback_period=20,
        zscore_window=252,
        entry_threshold=2.0,
        exit_threshold=0.5
    )
    
    # 测试股票列表
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    # 运行因子分析
    print("=== 运行均值回归因子分析 ===")
    result = factor.run_factor_analysis(
        symbols=test_symbols,
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    
    if 'error' not in result:
        factor.analysis_result = result
        
        # 生成报告
        print("\n=== 生成分析报告 ===")
        report = factor.generate_report()
        print(report)
        
        # 显示组合收益率统计
        portfolio_returns = result.get('portfolio_results', {}).get('portfolio_returns')
        if portfolio_returns is not None and not portfolio_returns.empty:
            print(f"\n=== 组合收益率统计 ===")
            print(f"总交易日: {len(portfolio_returns)}")
            print(f"平均日收益率: {portfolio_returns.mean():.4f}")
            print(f"收益率标准差: {portfolio_returns.std():.4f}")
            print(f"最大单日收益: {portfolio_returns.max():.4f}")
            print(f"最大单日亏损: {portfolio_returns.min():.4f}")
    else:
        print(f"因子分析失败: {result['error']}")