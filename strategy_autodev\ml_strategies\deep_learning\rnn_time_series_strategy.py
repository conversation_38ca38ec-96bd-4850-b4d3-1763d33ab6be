# -*- coding: utf-8 -*-
"""
RNN时间序列策略
基于《Hands-On Machine Learning for Algorithmic Trading》Chapter 18

实现了多种RNN架构用于金融时间序列预测：
- 单变量时间序列回归
- 多变量时间序列预测
- LSTM和GRU架构
- 堆叠LSTM模型
现已迁移至PyTorch框架
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架 - PyTorch
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    import torch.nn.functional as F
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    warnings.warn("PyTorch不可用，RNN策略将无法正常工作")

# 数据处理
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error

# 导入基础策略类
try:
    from .deep_learning_base import DeepLearningBaseStrategy
except ImportError:
    try:
        from deep_learning_base import DeepLearningBaseStrategy
    except ImportError:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from deep_learning_base import DeepLearningBaseStrategy

# 导入数据管道
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from data_pipeline.data_adapter_compatibility import get_data_pipeline
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        warnings.warn("数据适配器不可用，将使用模拟数据")

logger = logging.getLogger(__name__)

class RNNModel(nn.Module):
    """
    PyTorch RNN模型
    支持LSTM和GRU架构
    """
    
    def __init__(self, input_size: int, hidden_sizes: List[int], 
                 rnn_type: str = 'LSTM', dropout_rate: float = 0.2,
                 prediction_steps: int = 1, num_layers: int = 1):
        super(RNNModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.rnn_type = rnn_type.upper()
        self.dropout_rate = dropout_rate
        self.prediction_steps = prediction_steps
        self.num_layers = num_layers
        
        # 构建RNN层
        self.rnn_layers = nn.ModuleList()
        
        # 第一层RNN
        if self.rnn_type == 'LSTM':
            self.rnn_layers.append(
                nn.LSTM(input_size, hidden_sizes[0], batch_first=True, 
                        dropout=dropout_rate if len(hidden_sizes) > 1 else 0)
            )
        elif self.rnn_type == 'GRU':
            self.rnn_layers.append(
                nn.GRU(input_size, hidden_sizes[0], batch_first=True,
                       dropout=dropout_rate if len(hidden_sizes) > 1 else 0)
            )
        
        # 额外的RNN层
        for i in range(1, len(hidden_sizes)):
            if self.rnn_type == 'LSTM':
                self.rnn_layers.append(
                    nn.LSTM(hidden_sizes[i-1], hidden_sizes[i], batch_first=True,
                            dropout=dropout_rate if i < len(hidden_sizes) - 1 else 0)
                )
            elif self.rnn_type == 'GRU':
                self.rnn_layers.append(
                    nn.GRU(hidden_sizes[i-1], hidden_sizes[i], batch_first=True,
                           dropout=dropout_rate if i < len(hidden_sizes) - 1 else 0)
                )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 输出层
        self.output_layer = nn.Linear(hidden_sizes[-1], prediction_steps)
    
    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_size)
        
        # 通过RNN层
        for rnn_layer in self.rnn_layers:
            x, _ = rnn_layer(x)
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]  # (batch_size, hidden_size)
        
        # Dropout
        x = self.dropout(x)
        
        # 输出层
        x = self.output_layer(x)
        
        return x

class RNNTimeSeriesStrategy(DeepLearningBaseStrategy):
    """
    RNN时间序列策略 - PyTorch版本
    
    基于循环神经网络的时间序列预测策略，支持：
    - LSTM/GRU架构选择
    - 单变量和多变量时间序列
    - 堆叠RNN层
    - 特征嵌入
    - 多步预测
    """
    
    def __init__(self, name: str = "RNN_TimeSeries", **kwargs):
        # RNN特定参数
        self.rnn_type = kwargs.get('rnn_type', 'LSTM')  # LSTM, GRU
        self.sequence_length = kwargs.get('sequence_length', 20)
        self.num_features = kwargs.get('num_features', 1)
        self.num_layers = kwargs.get('num_layers', 2)
        self.units = kwargs.get('units', [50, 25])
        self.return_sequences = kwargs.get('return_sequences', True)
        self.stateful = kwargs.get('stateful', False)
        
        # 预测参数
        self.prediction_steps = kwargs.get('prediction_steps', 1)
        self.multivariate = kwargs.get('multivariate', False)
        
        # 训练参数
        self.patience = kwargs.get('patience', 10)
        self.monitor = kwargs.get('monitor', 'val_loss')
        self.optimizer_name = kwargs.get('optimizer', 'rmsprop')
        
        # 特征工程参数
        self.use_technical_indicators = kwargs.get('use_technical_indicators', True)
        self.use_embeddings = kwargs.get('use_embeddings', False)
        self.embedding_features = kwargs.get('embedding_features', [])
        
        # PyTorch特定参数
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        super().__init__(name, **kwargs)
        
        # 数据缩放器
        self.feature_scaler = MinMaxScaler()
        self.target_scaler = MinMaxScaler()
        
        # 模型组件
        self.model = None
        self.training_history = None
        
        logger.info(f"RNN时间序列策略 {name} 初始化完成")
        logger.info(f"配置: RNN类型={self.rnn_type}, 序列长度={self.sequence_length}, 层数={self.num_layers}, 设备={self.device}")
    
    def _prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备特征数据（实现抽象方法）
        """
        X, y = self.prepare_sequences(data)
        return X, y
    
    def _build_model(self) -> Optional[nn.Module]:
        """
        构建模型（实现抽象方法）
        """
        # 这个方法会在fit时调用，此时我们需要知道input_shape
        # 暂时返回None，在train方法中实际构建
        return None
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型（实现抽象方法）
        """
        try:
            # 构建模型
            input_size = X.shape[2]  # 特征数量
            self.model = RNNModel(
                input_size=input_size,
                hidden_sizes=self.units,
                rnn_type=self.rnn_type,
                dropout_rate=self.dropout_rate,
                prediction_steps=self.prediction_steps,
                num_layers=len(self.units)
            ).to(self.device)
            
            # 转换数据为PyTorch张量
            X_tensor = torch.FloatTensor(X).to(self.device)
            y_tensor = torch.FloatTensor(y).to(self.device)
            
            # 分割训练和验证数据
            split_idx = int(len(X) * (1 - self.validation_split))
            X_train, X_val = X_tensor[:split_idx], X_tensor[split_idx:]
            y_train, y_val = y_tensor[:split_idx], y_tensor[split_idx:]
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
            
            # 设置优化器和损失函数
            if self.optimizer_name.lower() == 'rmsprop':
                optimizer = optim.RMSprop(self.model.parameters(), lr=self.learning_rate)
            else:
                optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            
            criterion = nn.MSELoss()
            
            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(self.epochs):
                # 训练阶段
                self.model.train()
                train_loss = 0.0
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    
                    # 处理不同的预测步数
                    if self.prediction_steps == 1:
                        loss = criterion(outputs.squeeze(), batch_y)
                    else:
                        loss = criterion(outputs, batch_y)
                    
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # 验证阶段
                self.model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = self.model(batch_X)
                        
                        if self.prediction_steps == 1:
                            loss = criterion(outputs.squeeze(), batch_y)
                        else:
                            loss = criterion(outputs, batch_y)
                        
                        val_loss += loss.item()
                
                train_loss /= len(train_loader)
                val_loss /= len(val_loader)
                
                train_losses.append(train_loss)
                val_losses.append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= self.patience:
                        logger.info(f"早停在epoch {epoch+1}")
                        break
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 恢复最佳模型
            if hasattr(self, 'best_model_state'):
                self.model.load_state_dict(self.best_model_state)
            
            # 计算训练指标
            self.model.eval()
            with torch.no_grad():
                train_pred = self.model(X_train)
                if self.prediction_steps == 1:
                    train_mse = F.mse_loss(train_pred.squeeze(), y_train).item()
                    train_mae = F.l1_loss(train_pred.squeeze(), y_train).item()
                else:
                    train_mse = F.mse_loss(train_pred, y_train).item()
                    train_mae = F.l1_loss(train_pred, y_train).item()
            
            return {
                'success': True,
                'history': {
                    'loss': train_losses,
                    'val_loss': val_losses
                },
                'train_samples': len(X),
                'train_mse': train_mse,
                'train_mae': train_mae,
                'epochs_trained': len(train_losses),
                'final_loss': train_losses[-1],
                'final_val_loss': val_losses[-1],
                'model_params': sum(p.numel() for p in self.model.parameters())
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        模型预测（实现抽象方法）
        """
        if not self.is_trained or self.model is None:
            return np.array([])
        
        try:
            self.model.eval()
            X_tensor = torch.FloatTensor(X).to(self.device)
            
            with torch.no_grad():
                predictions = self.model(X_tensor)
                predictions = predictions.cpu().numpy()
                
                # 处理不同的预测步数
                if self.prediction_steps == 1:
                    predictions = predictions.flatten()
                else:
                    predictions = predictions[:, 0]  # 取第一步预测
            
            return predictions
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.array([])
    
    def prepare_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备RNN序列数据
        
        Args:
            data: 时间序列数据
            
        Returns:
            X: 序列特征 (samples, timesteps, features)
            y: 目标变量 (samples, prediction_steps)
        """
        try:
            # 特征工程
            if self.use_technical_indicators:
                features_df = self._calculate_technical_indicators(data)
            else:
                features_df = data.copy()
            
            # 选择特征列
            if self.multivariate:
                feature_columns = [col for col in features_df.columns 
                                 if col not in ['target', 'returns'] and not col.endswith('_target')]
            else:
                feature_columns = ['close']  # 单变量使用收盘价
            
            # 计算目标变量
            if self.target_column == 'returns':
                features_df['target'] = features_df['close'].pct_change(self.prediction_steps).shift(-self.prediction_steps)
            elif self.target_column == 'price':
                features_df['target'] = features_df['close'].shift(-self.prediction_steps)
            else:
                features_df['target'] = features_df[self.target_column]
            
            # 删除NaN值
            features_df = features_df.dropna()
            
            if len(features_df) < self.sequence_length + self.prediction_steps:
                logger.warning(f"数据长度不足: {len(features_df)} < {self.sequence_length + self.prediction_steps}")
                return np.array([]), np.array([])
            
            # 准备特征和目标数据
            feature_data = features_df[feature_columns].values
            target_data = features_df['target'].values
            
            # 数据标准化
            feature_data = self.feature_scaler.fit_transform(feature_data)
            target_data = self.target_scaler.fit_transform(target_data.reshape(-1, 1)).flatten()
            
            # 创建序列
            X, y = self._create_sequences(feature_data, target_data)
            
            logger.info(f"序列数据准备完成: X shape={X.shape}, y shape={y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"序列数据准备失败: {e}")
            return np.array([]), np.array([])
    
    def _create_sequences(self, features: np.ndarray, targets: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建RNN输入序列
        """
        X, y = [], []
        
        for i in range(len(features) - self.sequence_length - self.prediction_steps + 1):
            # 输入序列
            X.append(features[i:(i + self.sequence_length)])
            
            # 目标序列
            if self.prediction_steps == 1:
                y.append(targets[i + self.sequence_length])
            else:
                y.append(targets[i + self.sequence_length:i + self.sequence_length + self.prediction_steps])
        
        return np.array(X), np.array(y)
    
    def train(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练RNN模型
        
        Args:
            data: 训练数据
            
        Returns:
            训练结果
        """
        try:
            logger.info("开始训练RNN模型")
            
            if not PYTORCH_AVAILABLE:
                logger.error("PyTorch不可用，无法训练模型")
                return {'success': False, 'error': 'PyTorch不可用'}
            
            # 准备序列数据
            X, y = self.prepare_sequences(data)
            
            if len(X) == 0:
                return {'success': False, 'error': '数据准备失败'}
            
            # 训练模型
            result = self._train_model(X, y)
            
            if result['success']:
                self.training_history = result['history']
                self.is_trained = True
                logger.info(f"RNN模型训练完成: MSE={result['train_mse']:.6f}, MAE={result['train_mae']:.6f}")
            else:
                logger.error(f"模型训练失败: {result.get('error', '未知错误')}")
            
            return result
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def predict(self, data: pd.DataFrame) -> pd.Series:
        """
        使用训练好的模型进行预测
        
        Args:
            data: 预测数据
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            logger.warning("模型未训练，返回零预测")
            return pd.Series(0, index=data.index)
        
        try:
            # 准备预测数据
            X, _ = self.prepare_sequences(data)
            
            if len(X) == 0:
                logger.warning("预测数据准备失败")
                return pd.Series(0, index=data.index)
            
            # 模型预测
            predictions = self._predict_model(X)
            
            # 反标准化
            predictions = self.target_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
            
            # 创建预测序列
            pred_index = data.index[self.sequence_length:self.sequence_length + len(predictions)]
            pred_series = pd.Series(predictions, index=pred_index)
            
            # 扩展到完整索引
            result = pd.Series(0.0, index=data.index)
            result.loc[pred_series.index] = pred_series
            
            logger.info(f"RNN预测完成: {len(predictions)} 个预测值")
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return pd.Series(0, index=data.index)
    
    def get_model_summary(self) -> Dict[str, Any]:
        """
        获取模型摘要
        """
        summary = {
            'strategy_name': self.name,
            'model_type': f'RNN_Time_Series_PyTorch_{self.rnn_type}',
            'is_trained': self.is_trained,
            'framework': 'PyTorch',
            'device': str(self.device),
            'parameters': {
                'rnn_type': self.rnn_type,
                'sequence_length': self.sequence_length,
                'hidden_units': self.units,
                'prediction_steps': self.prediction_steps,
                'multivariate': self.multivariate,
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size
            }
        }
        
        if self.model is not None:
            summary['model_params'] = sum(p.numel() for p in self.model.parameters())
        
        if hasattr(self, 'training_history') and self.training_history:
            summary['training_history'] = {
                'epochs': len(self.training_history['loss']),
                'final_loss': self.training_history['loss'][-1],
                'final_val_loss': self.training_history['val_loss'][-1]
            }
        
        return summary
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        """
        if self.model is None:
            logger.error("没有训练好的模型可保存")
            return False
        
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'model_config': {
                    'input_size': self.model.input_size,
                    'hidden_sizes': self.model.hidden_sizes,
                    'rnn_type': self.model.rnn_type,
                    'dropout_rate': self.model.dropout_rate,
                    'prediction_steps': self.model.prediction_steps,
                    'num_layers': self.model.num_layers
                },
                'feature_scaler': self.feature_scaler,
                'target_scaler': self.target_scaler,
                'strategy_config': {
                    'sequence_length': self.sequence_length,
                    'multivariate': self.multivariate,
                    'target_column': self.target_column
                }
            }, filepath)
            logger.info(f"模型已保存到: {filepath}")
            return True
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        """
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            
            # 重建模型
            config = checkpoint['model_config']
            self.model = RNNModel(
                input_size=config['input_size'],
                hidden_sizes=config['hidden_sizes'],
                rnn_type=config['rnn_type'],
                dropout_rate=config['dropout_rate'],
                prediction_steps=config['prediction_steps'],
                num_layers=config['num_layers']
            ).to(self.device)
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 加载缩放器
            self.feature_scaler = checkpoint['feature_scaler']
            self.target_scaler = checkpoint['target_scaler']
            
            # 加载策略配置
            strategy_config = checkpoint.get('strategy_config', {})
            self.sequence_length = strategy_config.get('sequence_length', self.sequence_length)
            self.multivariate = strategy_config.get('multivariate', self.multivariate)
            self.target_column = strategy_config.get('target_column', self.target_column)
            
            self.is_trained = True
            logger.info(f"模型已从 {filepath} 加载")
            return True
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        """
        df = data.copy()
        
        # 基础价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['high_low_ratio'] = df['high'] / df['low'] - 1
        df['open_close_ratio'] = df['open'] / df['close'] - 1
        
        # 移动平均线
        for window in [5, 10, 20]:
            df[f'ma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ma_ratio_{window}'] = df['close'] / df[f'ma_{window}'] - 1
        
        # 波动率
        df['volatility_5'] = df['returns'].rolling(window=5).std()
        df['volatility_10'] = df['returns'].rolling(window=10).std()
        
        # RSI
        df['rsi'] = self._calculate_rsi(df['close'])
        
        # MACD
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # 成交量特征（如果有）
        if 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_price_trend'] = df['volume'] * df['returns']
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """
        计算MACD指标
        """
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal