# -*- coding: utf-8 -*-
"""
神经网络架构优化策略
Neural Network Architecture Optimization Strategy

基于Chapter 17的神经网络超参数优化和架构搜索
使用网格搜索、随机搜索和贝叶斯优化进行模型选择
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List, Union
import warnings
import itertools
from datetime import datetime
warnings.filterwarnings('ignore')

from .keras_dl_strategy import KerasDLStrategy

logger = logging.getLogger(__name__)

# 尝试导入优化库
try:
    from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, TimeSeriesSplit
    from sklearn.metrics import make_scorer, mean_squared_error, accuracy_score, roc_auc_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn不可用，将使用简化优化")

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    logger.warning("Optuna不可用，无法使用贝叶斯优化")

class OneStepTimeSeriesSplit:
    """
    时间序列单步前向验证
    基于Chapter 17的实现
    """
    
    def __init__(self, n_splits: int = 5, test_size: int = None):
        self.n_splits = n_splits
        self.test_size = test_size
    
    def split(self, X, y=None, groups=None):
        """
        生成训练/测试索引
        """
        n_samples = len(X)
        
        if self.test_size is None:
            test_size = n_samples // (self.n_splits + 1)
        else:
            test_size = self.test_size
        
        for i in range(self.n_splits):
            # 训练集：从开始到当前分割点
            train_end = n_samples - (self.n_splits - i) * test_size
            train_indices = np.arange(0, train_end)
            
            # 测试集：当前分割点后的数据
            test_start = train_end
            test_end = min(test_start + test_size, n_samples)
            test_indices = np.arange(test_start, test_end)
            
            if len(test_indices) > 0:
                yield train_indices, test_indices
    
    def get_n_splits(self, X=None, y=None, groups=None):
        return self.n_splits

class NNOptimizationStrategy(KerasDLStrategy):
    """
    神经网络架构优化策略
    
    实现特性：
    - 超参数网格搜索
    - 随机搜索优化
    - 贝叶斯优化（Optuna）
    - 时间序列交叉验证
    - 自动特征选择
    - 模型集成
    """
    
    def __init__(self, name: str = "NNOptimization", **kwargs):
        super().__init__(name, **kwargs)
        
        # 优化参数
        self.optimization_method = kwargs.get('optimization_method', 'grid_search')  # grid_search, random_search, bayesian
        self.cv_folds = kwargs.get('cv_folds', 3)
        self.n_iter = kwargs.get('n_iter', 20)  # 随机搜索迭代次数
        self.n_trials = kwargs.get('n_trials', 50)  # 贝叶斯优化试验次数
        
        # 搜索空间
        self.param_grid = kwargs.get('param_grid', self._get_default_param_grid())
        self.scoring = kwargs.get('scoring', 'neg_mean_squared_error')
        
        # 优化结果
        self.best_params = None
        self.best_score = None
        self.cv_results = None
        self.optimization_history = []
        
        # 模型集成
        self.use_ensemble = kwargs.get('use_ensemble', False)
        self.ensemble_models = []
        self.ensemble_weights = []
        
        logger.info(f"神经网络优化策略 {name} 初始化完成")
    
    def _get_default_param_grid(self) -> Dict[str, List]:
        """
        获取默认参数搜索空间
        """
        return {
            'hidden_layers': [
                [32], [64], [128],
                [32, 16], [64, 32], [128, 64],
                [64, 32, 16], [128, 64, 32]
            ],
            'learning_rate': [0.001, 0.01, 0.1],
            'dropout_rate': [0.0, 0.2, 0.3, 0.5],
            'batch_size': [16, 32, 64],
            'activation': ['relu', 'tanh'],
            'l2_reg': [0.0, 0.001, 0.01],
            'optimizer': ['adam', 'sgd', 'rmsprop']
        }
    
    def _create_model_with_params(self, params: Dict[str, Any]) -> KerasDLStrategy:
        """
        使用指定参数创建模型
        """
        model_params = self.__dict__.copy()
        model_params.update(params)
        
        # 创建新的策略实例
        strategy = KerasDLStrategy(
            name=f"{self.name}_optimized",
            **model_params
        )
        
        return strategy
    
    def _evaluate_model(self, params: Dict[str, Any], X: np.ndarray, y: np.ndarray) -> float:
        """
        评估单个参数组合的模型性能
        """
        try:
            # 创建模型
            strategy = self._create_model_with_params(params)
            
            # 时间序列交叉验证
            tscv = OneStepTimeSeriesSplit(n_splits=self.cv_folds)
            scores = []
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # 训练模型
                strategy.fit(pd.DataFrame({
                    'close': np.random.randn(len(X_train)) * 100 + 1000,  # 模拟价格数据
                    'volume': np.random.randn(len(X_train)) * 1000 + 5000
                }))
                
                # 预测
                y_pred = strategy.predict(X_val)
                
                # 计算分数
                if self.target_column == 'price_direction':
                    score = accuracy_score(y_val, (y_pred > 0.5).astype(int))
                else:
                    score = -mean_squared_error(y_val, y_pred)  # 负MSE
                
                scores.append(score)
            
            avg_score = np.mean(scores)
            logger.info(f"参数 {params} 的平均分数: {avg_score:.6f}")
            
            return avg_score
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return float('-inf')
    
    def _grid_search_optimization(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        网格搜索优化
        """
        logger.info("开始网格搜索优化")
        
        # 生成所有参数组合
        param_names = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        best_score = float('-inf')
        best_params = None
        results = []
        
        for i, combination in enumerate(param_combinations):
            params = dict(zip(param_names, combination))
            
            logger.info(f"测试参数组合 {i+1}/{len(param_combinations)}: {params}")
            
            score = self._evaluate_model(params, X, y)
            
            results.append({
                'params': params,
                'score': score,
                'rank': 0  # 稍后计算
            })
            
            if score > best_score:
                best_score = score
                best_params = params
        
        # 计算排名
        results.sort(key=lambda x: x['score'], reverse=True)
        for i, result in enumerate(results):
            result['rank'] = i + 1
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'cv_results': results
        }
    
    def _random_search_optimization(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        随机搜索优化
        """
        logger.info(f"开始随机搜索优化，迭代次数: {self.n_iter}")
        
        best_score = float('-inf')
        best_params = None
        results = []
        
        for i in range(self.n_iter):
            # 随机选择参数
            params = {}
            for param_name, param_values in self.param_grid.items():
                params[param_name] = np.random.choice(param_values)
            
            logger.info(f"随机搜索迭代 {i+1}/{self.n_iter}: {params}")
            
            score = self._evaluate_model(params, X, y)
            
            results.append({
                'params': params,
                'score': score,
                'iteration': i + 1
            })
            
            if score > best_score:
                best_score = score
                best_params = params
        
        # 排序结果
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'cv_results': results
        }
    
    def _bayesian_optimization(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        贝叶斯优化（使用Optuna）
        """
        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna不可用，回退到随机搜索")
            return self._random_search_optimization(X, y)
        
        logger.info(f"开始贝叶斯优化，试验次数: {self.n_trials}")
        
        def objective(trial):
            # 定义搜索空间
            params = {}
            
            # 隐藏层架构
            n_layers = trial.suggest_int('n_layers', 1, 3)
            hidden_layers = []
            for i in range(n_layers):
                units = trial.suggest_categorical(f'layer_{i}_units', [16, 32, 64, 128, 256])
                hidden_layers.append(units)
            params['hidden_layers'] = hidden_layers
            
            # 其他超参数
            params['learning_rate'] = trial.suggest_loguniform('learning_rate', 1e-4, 1e-1)
            params['dropout_rate'] = trial.suggest_uniform('dropout_rate', 0.0, 0.5)
            params['batch_size'] = trial.suggest_categorical('batch_size', [16, 32, 64, 128])
            params['activation'] = trial.suggest_categorical('activation', ['relu', 'tanh', 'sigmoid'])
            params['l2_reg'] = trial.suggest_loguniform('l2_reg', 1e-5, 1e-1)
            params['optimizer'] = trial.suggest_categorical('optimizer', ['adam', 'sgd', 'rmsprop'])
            
            # 评估模型
            score = self._evaluate_model(params, X, y)
            
            return score
        
        # 创建研究
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.n_trials)
        
        return {
            'best_params': study.best_params,
            'best_score': study.best_value,
            'cv_results': {
                'trials': [{
                    'params': trial.params,
                    'score': trial.value,
                    'trial_number': trial.number
                } for trial in study.trials]
            }
        }
    
    def optimize_hyperparameters(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        超参数优化主函数
        """
        try:
            logger.info(f"开始超参数优化，方法: {self.optimization_method}")
            
            start_time = datetime.now()
            
            if self.optimization_method == 'grid_search':
                results = self._grid_search_optimization(X, y)
            elif self.optimization_method == 'random_search':
                results = self._random_search_optimization(X, y)
            elif self.optimization_method == 'bayesian':
                results = self._bayesian_optimization(X, y)
            else:
                raise ValueError(f"不支持的优化方法: {self.optimization_method}")
            
            end_time = datetime.now()
            optimization_time = (end_time - start_time).total_seconds()
            
            self.best_params = results['best_params']
            self.best_score = results['best_score']
            self.cv_results = results['cv_results']
            
            # 记录优化历史
            self.optimization_history.append({
                'method': self.optimization_method,
                'best_params': self.best_params,
                'best_score': self.best_score,
                'optimization_time': optimization_time,
                'timestamp': datetime.now()
            })
            
            logger.info(f"超参数优化完成，最佳分数: {self.best_score:.6f}")
            logger.info(f"最佳参数: {self.best_params}")
            logger.info(f"优化时间: {optimization_time:.2f}秒")
            
            return results
            
        except Exception as e:
            logger.error(f"超参数优化失败: {e}")
            raise
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练优化后的模型
        """
        try:
            # 更新输入维度
            self.input_dim = X.shape[1]
            
            # 执行超参数优化
            optimization_results = self.optimize_hyperparameters(X, y)
            
            # 使用最佳参数训练最终模型
            if self.best_params:
                # 更新当前实例的参数
                for param, value in self.best_params.items():
                    if hasattr(self, param):
                        setattr(self, param, value)
                
                # 重新构建和训练模型
                training_results = super()._train_model(X, y)
                
                # 合并结果
                final_results = {
                    'optimization_results': optimization_results,
                    'training_results': training_results,
                    'best_params': self.best_params,
                    'best_score': self.best_score
                }
                
                # 训练集成模型
                if self.use_ensemble:
                    ensemble_results = self._train_ensemble_models(X, y)
                    final_results['ensemble_results'] = ensemble_results
                
                return final_results
            else:
                logger.warning("未找到最佳参数，使用默认参数训练")
                return super()._train_model(X, y)
            
        except Exception as e:
            logger.error(f"优化模型训练失败: {e}")
            raise
    
    def _train_ensemble_models(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练集成模型
        """
        try:
            logger.info("开始训练集成模型")
            
            # 选择top-k个参数组合
            top_k = min(5, len(self.cv_results) if isinstance(self.cv_results, list) else 3)
            
            if isinstance(self.cv_results, list):
                top_params = [result['params'] for result in self.cv_results[:top_k]]
                top_scores = [result['score'] for result in self.cv_results[:top_k]]
            else:
                # 贝叶斯优化结果
                trials = self.cv_results.get('trials', [])
                sorted_trials = sorted(trials, key=lambda x: x['score'], reverse=True)
                top_params = [trial['params'] for trial in sorted_trials[:top_k]]
                top_scores = [trial['score'] for trial in sorted_trials[:top_k]]
            
            self.ensemble_models = []
            self.ensemble_weights = []
            
            for i, params in enumerate(top_params):
                # 创建并训练模型
                strategy = self._create_model_with_params(params)
                
                # 创建模拟数据进行训练
                mock_data = pd.DataFrame({
                    'close': np.random.randn(len(X)) * 100 + 1000,
                    'volume': np.random.randn(len(X)) * 1000 + 5000
                })
                
                strategy.fit(mock_data)
                
                self.ensemble_models.append(strategy)
                self.ensemble_weights.append(top_scores[i])
            
            # 归一化权重
            total_weight = sum(self.ensemble_weights)
            self.ensemble_weights = [w / total_weight for w in self.ensemble_weights]
            
            logger.info(f"集成模型训练完成，包含 {len(self.ensemble_models)} 个模型")
            
            return {
                'n_models': len(self.ensemble_models),
                'weights': self.ensemble_weights,
                'top_params': top_params
            }
            
        except Exception as e:
            logger.error(f"集成模型训练失败: {e}")
            return {}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        集成预测
        """
        try:
            if self.use_ensemble and self.ensemble_models:
                # 集成预测
                predictions = []
                
                for model, weight in zip(self.ensemble_models, self.ensemble_weights):
                    pred = model.predict(X)
                    predictions.append(pred * weight)
                
                ensemble_pred = np.sum(predictions, axis=0)
                return ensemble_pred
            else:
                # 单模型预测
                return super()._predict_model(X)
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            return super()._predict_model(X)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """
        获取优化报告
        """
        return {
            'optimization_method': self.optimization_method,
            'best_params': self.best_params,
            'best_score': self.best_score,
            'optimization_history': self.optimization_history,
            'cv_results': self.cv_results,
            'use_ensemble': self.use_ensemble,
            'ensemble_size': len(self.ensemble_models) if self.ensemble_models else 0
        }