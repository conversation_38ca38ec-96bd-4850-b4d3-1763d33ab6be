#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­æ¼”ç¤º
å±•ç¤ºå¦‚ä½•ä½¿ç”¨ focus_correction_expected_yield_factor.py
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# æ·»åŠ é¡¹ç›®è·¯å¾„
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from factor_analyze.focus_correction_expected_yield_factor import (
        FocusCorrectionExpectedYieldFactor,
        FocusCorrectionExpectedYieldForStocks,
        FocusCorrectionExpectedYieldForIndices
    )
    FACTOR_MODULE_AVAILABLE = True
    print("[INFO] å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­æ¨¡å—åŠ è½½æˆåŠŸ")
except ImportError as e:
    FACTOR_MODULE_AVAILABLE = False
    print(f"[ERROR] å› å­æ¨¡å—å¯¼å…¥å¤±è´¥: {e}")


def demo_basic_usage():
    """åŸºç¡€ä½¿ç”¨æ¼”ç¤º"""
    print("\n" + "="*60)
    print("1. åŸºç¡€ä½¿ç”¨æ¼”ç¤º")
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    # åˆ›å»ºä¸ªè‚¡å› å­å®ä¾‹
    print("\nğŸ“Š åˆ›å»ºä¸ªè‚¡å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­...")
    stock_factor = FocusCorrectionExpectedYieldForStocks()
    
    # è·å–å› å­ä¿¡æ¯
    factor_info = stock_factor.get_factor_info()
    print(f"âœ?å› å­åç§°: {factor_info['factor_name']}")
    print(f"âœ?å› å­ç±»åˆ«: {factor_info['factor_category']}")
    print(f"âœ?å› å­å…¬å¼: {factor_info['factor_formula']}")
    print(f"âœ?å›çœ‹å¤©æ•°: {factor_info['lookback_days']}")
    
    # è®¡ç®—å› å­
    print("\nğŸ“ˆ è®¡ç®—ä¸ªè‚¡å› å­...")
    test_securities = ['000001.SZ', '000002.SZ', '600000.SH']
    start_date = '2024-01-01'
    end_date = '2024-03-31'
    
    stock_result = stock_factor.calculate_factor(
        securities=test_securities,
        start_date=start_date,
        end_date=end_date
    )
    
    if not stock_result.empty:
        print(f"âœ?ä¸ªè‚¡å› å­è®¡ç®—æˆåŠŸï¼?)
        print(f"   è®°å½•æ•°é‡: {len(stock_result)}")
        print(f"   è‚¡ç¥¨æ•°é‡: {stock_result['security'].nunique()}")
        print(f"   æ—¶é—´èŒƒå›´: {stock_result['date'].min()} è‡?{stock_result['date'].max()}")
        
        # æ˜¾ç¤ºç»Ÿè®¡ä¿¡æ¯
        if 'focus_corrected_return_final' in stock_result.columns:
            final_factor = stock_result['focus_corrected_return_final']
            print(f"   å› å­å‡å€? {final_factor.mean():.4f}")
            print(f"   å› å­æ ‡å‡†å·? {final_factor.std():.4f}")
            print(f"   å› å­èŒƒå›´: [{final_factor.min():.4f}, {final_factor.max():.4f}]")
        
        # æ˜¾ç¤ºå‰å‡ æ¡è®°å½?
        print("\nğŸ“‹ å‰?æ¡å› å­è®°å½?")
        display_cols = ['date', 'security', 'weighted_expected_return', 'coverage_factor', 'focus_corrected_return_final']
        available_cols = [col for col in display_cols if col in stock_result.columns]
        if available_cols:
            print(stock_result[available_cols].head())
    else:
        print("â?ä¸ªè‚¡å› å­è®¡ç®—å¤±è´¥æˆ–æ— æ•°æ®")


def demo_index_factor():
    """æŒ‡æ•°å› å­æ¼”ç¤º"""
    print("\n" + "="*60)
    print("2. æŒ‡æ•°å› å­æ¼”ç¤º")
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    # åˆ›å»ºæŒ‡æ•°å› å­å®ä¾‹
    print("\nğŸ“Š åˆ›å»ºæŒ‡æ•°å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­...")
    index_factor = FocusCorrectionExpectedYieldForIndices()
    
    # æ˜¾ç¤ºæŒ‡æ•°ä¸“ç”¨é…ç½®
    print(f"âœ?æŒ‡æ•°å›çœ‹å¤©æ•°: {index_factor.lookback_days}")
    print(f"âœ?æŒ‡æ•°æœ€å°åˆ†æå¸ˆæ•? {index_factor.min_analyst_count}")
    print(f"âœ?æŒ‡æ•°æƒé‡è¡°å‡: {index_factor.weight_decay}")
    print(f"âœ?æŒ‡æ•°è¦†ç›–çª—å£: {index_factor.coverage_window}")
    
    # è®¡ç®—æŒ‡æ•°å› å­
    print("\nğŸ“ˆ è®¡ç®—æŒ‡æ•°å› å­...")
    test_indices = ['000300.SH', '000905.SH']
    start_date = '2024-01-01'
    end_date = '2024-03-31'
    
    index_result = index_factor.calculate_factor(
        securities=test_indices,
        start_date=start_date,
        end_date=end_date
    )
    
    if not index_result.empty:
        print(f"âœ?æŒ‡æ•°å› å­è®¡ç®—æˆåŠŸï¼?)
        print(f"   è®°å½•æ•°é‡: {len(index_result)}")
        print(f"   æŒ‡æ•°æ•°é‡: {index_result['security'].nunique()}")
        
        # æ˜¾ç¤ºç»Ÿè®¡ä¿¡æ¯
        if 'focus_corrected_return_final' in index_result.columns:
            final_factor = index_result['focus_corrected_return_final']
            print(f"   å› å­å‡å€? {final_factor.mean():.4f}")
            print(f"   å› å­æ ‡å‡†å·? {final_factor.std():.4f}")
    else:
        print("â?æŒ‡æ•°å› å­è®¡ç®—å¤±è´¥æˆ–æ— æ•°æ®")


def demo_custom_configuration():
    """è‡ªå®šä¹‰é…ç½®æ¼”ç¤?""
    print("\n" + "="*60)
    print("3. è‡ªå®šä¹‰é…ç½®æ¼”ç¤?)
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    # åˆ›å»ºè‡ªå®šä¹‰é…ç½?
    print("\nâš™ï¸ åˆ›å»ºè‡ªå®šä¹‰é…ç½?..")
    custom_config = {
        'lookback_days': 45,              # è‡ªå®šä¹‰å›çœ‹æœŸ
        'min_analyst_count': 2,           # é™ä½æœ€å°åˆ†æå¸ˆè¦æ±‚
        'weight_decay': 0.85,             # è°ƒæ•´æƒé‡è¡°å‡
        'coverage_window': 25,            # è°ƒæ•´è¦†ç›–çª—å£
        'analyst_weight_method': 'recency',  # ä½¿ç”¨æ—¶é—´æƒé‡
        'quality_filters': {
            'min_price': 2.0,             # æé«˜æœ€å°ä»·æ ¼è¦æ±?
            'min_market_cap': 5e8,        # è°ƒæ•´æœ€å°å¸‚å€?
            'exclude_st': True,
            'min_trading_days': 15
        }
    }
    
    # ä½¿ç”¨è‡ªå®šä¹‰é…ç½®åˆ›å»ºå› å­?
    custom_factor = FocusCorrectionExpectedYieldForStocks(config=custom_config)
    
    print(f"âœ?è‡ªå®šä¹‰å›çœ‹å¤©æ•? {custom_factor.lookback_days}")
    print(f"âœ?è‡ªå®šä¹‰æœ€å°åˆ†æå¸ˆæ•? {custom_factor.min_analyst_count}")
    print(f"âœ?è‡ªå®šä¹‰æƒé‡è¡°å‡? {custom_factor.weight_decay}")
    print(f"âœ?è‡ªå®šä¹‰æƒé‡æ–¹æ³? {custom_factor.config['analyst_weight_method']}")
    
    # è®¡ç®—è‡ªå®šä¹‰å› å­?
    print("\nğŸ“ˆ ä½¿ç”¨è‡ªå®šä¹‰é…ç½®è®¡ç®—å› å­?..")
    custom_result = custom_factor.calculate_factor(
        securities=['000001.SZ', '000002.SZ'],
        start_date='2024-01-01',
        end_date='2024-02-29'
    )
    
    if not custom_result.empty:
        print(f"âœ?è‡ªå®šä¹‰å› å­è®¡ç®—æˆåŠŸï¼Œå…?{len(custom_result)} æ¡è®°å½?)
        
        if 'focus_corrected_return_final' in custom_result.columns:
            final_factor = custom_result['focus_corrected_return_final']
            print(f"   è‡ªå®šä¹‰å› å­å‡å€? {final_factor.mean():.4f}")
    else:
        print("â?è‡ªå®šä¹‰å› å­è®¡ç®—å¤±è´¥æˆ–æ— æ•°æ?)


def demo_factor_analysis():
    """å› å­åˆ†ææ¼”ç¤º"""
    print("\n" + "="*60)
    print("4. å› å­åˆ†ææ¼”ç¤º")
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    # åˆ›å»ºå› å­å®ä¾‹
    factor = FocusCorrectionExpectedYieldForStocks()
    
    # è®¡ç®—å¤šä¸ªè‚¡ç¥¨çš„å› å­?
    print("\nğŸ“Š è®¡ç®—å¤šè‚¡ç¥¨å› å­è¿›è¡Œåˆ†æ?..")
    test_securities = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    result = factor.calculate_factor(
        securities=test_securities,
        start_date='2024-01-01',
        end_date='2024-03-31'
    )
    
    if not result.empty and 'focus_corrected_return_final' in result.columns:
        print(f"âœ?å¤šè‚¡ç¥¨å› å­è®¡ç®—æˆåŠŸï¼Œå…?{len(result)} æ¡è®°å½?)
        
        # æŒ‰è‚¡ç¥¨åˆ†ç»„åˆ†æ?
        print("\nğŸ“ˆ æŒ‰è‚¡ç¥¨åˆ†ç»„åˆ†æ?")
        stock_analysis = result.groupby('security')['focus_corrected_return_final'].agg([
            'count', 'mean', 'std', 'min', 'max'
        ]).round(4)
        print(stock_analysis)
        
        # æ—¶é—´åºåˆ—åˆ†æ
        print("\nğŸ“… æ—¶é—´åºåˆ—åˆ†æ:")
        if 'date' in result.columns:
            time_analysis = result.groupby('date')['focus_corrected_return_final'].agg([
                'count', 'mean', 'std'
            ]).round(4)
            print(time_analysis.head())
        
        # ç›¸å…³æ€§åˆ†æ?
        print("\nğŸ”— å› å­ç›¸å…³æ€§åˆ†æ?")
        correlation_cols = ['weighted_expected_return', 'coverage_factor', 'focus_corrected_return_final']
        available_corr_cols = [col for col in correlation_cols if col in result.columns]
        
        if len(available_corr_cols) >= 2:
            correlation_matrix = result[available_corr_cols].corr().round(4)
            print(correlation_matrix)
        
        # åˆ†ä½æ•°åˆ†æ?
        print("\nğŸ“Š å› å­åˆ†ä½æ•°åˆ†æ?")
        factor_values = result['focus_corrected_return_final']
        quantiles = [0.1, 0.25, 0.5, 0.75, 0.9]
        quantile_values = factor_values.quantile(quantiles).round(4)
        
        for q, v in quantile_values.items():
            print(f"   {q*100:4.0f}% åˆ†ä½æ•? {v}")
    else:
        print("â?å¤šè‚¡ç¥¨å› å­åˆ†æå¤±è´¥æˆ–æ— æ•°æ?)


def demo_data_pipeline_test():
    """æ•°æ®ç®¡é“æµ‹è¯•æ¼”ç¤º"""
    print("\n" + "="*60)
    print("5. æ•°æ®ç®¡é“æµ‹è¯•æ¼”ç¤º")
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    # åˆ›å»ºå› å­å®ä¾‹
    factor = FocusCorrectionExpectedYieldForStocks()
    
    # æµ‹è¯•æ•°æ®ç®¡é“çŠ¶æ€?
    print("\nğŸ”§ æµ‹è¯•æ•°æ®ç®¡é“çŠ¶æ€?..")
    print(f"âœ?æ•°æ®ç®¡é“å¯ç”¨æ€? {hasattr(factor, 'adapter_registry')}")
    print(f"âœ?åˆ†æå¸ˆé€‚é…å™? {hasattr(factor, 'analyst_adapter')}")
    print(f"âœ?è‚¡ç¥¨é€‚é…å™? {hasattr(factor, 'stock_adapter')}")
    
    # æµ‹è¯•æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆ
    print("\nğŸ“Š æµ‹è¯•æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆ...")
    mock_data = factor._generate_mock_analyst_data(
        '000001.SZ', '2024-01-01', '2024-01-31'
    )
    
    if not mock_data.empty:
        print(f"âœ?æ¨¡æ‹Ÿæ•°æ®ç”ŸæˆæˆåŠŸï¼Œå…± {len(mock_data)} æ¡è®°å½?)
        print(f"   åˆ†æå¸ˆæ•°é‡? {mock_data['analyst_id'].nunique()}")
        print(f"   æœºæ„æ•°é‡: {mock_data['institution'].nunique()}")
        print(f"   æ—¥æœŸèŒƒå›´: {mock_data['date'].min()} è‡?{mock_data['date'].max()}")
        
        # æ˜¾ç¤ºæ•°æ®æ ·æœ¬
        print("\nğŸ“‹ æ¨¡æ‹Ÿæ•°æ®æ ·æœ¬:")
        sample_cols = ['date', 'analyst_id', 'institution', 'target_price', 'expected_return']
        print(mock_data[sample_cols].head(3))
    else:
        print("â?æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆå¤±è´¥")
    
    # æµ‹è¯•æ•°æ®é¢„å¤„ç?
    print("\nğŸ”§ æµ‹è¯•æ•°æ®é¢„å¤„ç?..")
    if not mock_data.empty:
        processed_data = factor._preprocess_analyst_data(mock_data)
        print(f"âœ?æ•°æ®é¢„å¤„ç†æˆåŠŸï¼Œå¤„ç†å‰?{len(mock_data)} æ¡ï¼Œå¤„ç†å?{len(processed_data)} æ?)
        
        # æ£€æŸ¥æ•°æ®è´¨é‡?
        if not processed_data.empty:
            print("âœ?æ•°æ®è´¨é‡æ£€æŸ?")
            print(f"   ç›®æ ‡ä»·æ ¼èŒƒå›´: [{processed_data['target_price'].min():.2f}, {processed_data['target_price'].max():.2f}]")
            print(f"   é¢„æœŸæ”¶ç›Šç‡èŒƒå›? [{processed_data['expected_return'].min():.4f}, {processed_data['expected_return'].max():.4f}]")
            print(f"   æ— ç¼ºå¤±å€? {not processed_data.isnull().any().any()}")


def demo_performance_comparison():
    """æ€§èƒ½å¯¹æ¯”æ¼”ç¤º"""
    print("\n" + "="*60)
    print("6. æ€§èƒ½å¯¹æ¯”æ¼”ç¤º")
    print("="*60)
    
    if not FACTOR_MODULE_AVAILABLE:
        print("â?å› å­æ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æ¼”ç¤º")
        return
    
    import time
    
    # å¯¹æ¯”ä¸ªè‚¡å’ŒæŒ‡æ•°å› å­çš„æ€§èƒ½
    print("\nâš?ä¸ªè‚¡vsæŒ‡æ•°å› å­æ€§èƒ½å¯¹æ¯”...")
    
    test_securities = ['000001.SZ', '000002.SZ']
    start_date = '2024-01-01'
    end_date = '2024-02-29'
    
    # ä¸ªè‚¡å› å­æ€§èƒ½æµ‹è¯•
    print("\nğŸ“Š ä¸ªè‚¡å› å­æ€§èƒ½æµ‹è¯•...")
    stock_factor = FocusCorrectionExpectedYieldForStocks()
    
    start_time = time.time()
    stock_result = stock_factor.calculate_factor(
        securities=test_securities,
        start_date=start_date,
        end_date=end_date
    )
    stock_time = time.time() - start_time
    
    print(f"âœ?ä¸ªè‚¡å› å­è®¡ç®—è€—æ—¶: {stock_time:.2f} ç§?)
    print(f"âœ?ä¸ªè‚¡å› å­è®°å½•æ•? {len(stock_result) if not stock_result.empty else 0}")
    
    # æŒ‡æ•°å› å­æ€§èƒ½æµ‹è¯•
    print("\nğŸ“Š æŒ‡æ•°å› å­æ€§èƒ½æµ‹è¯•...")
    index_factor = FocusCorrectionExpectedYieldForIndices()
    
    start_time = time.time()
    index_result = index_factor.calculate_factor(
        securities=['000300.SH'],
        start_date=start_date,
        end_date=end_date
    )
    index_time = time.time() - start_time
    
    print(f"âœ?æŒ‡æ•°å› å­è®¡ç®—è€—æ—¶: {index_time:.2f} ç§?)
    print(f"âœ?æŒ‡æ•°å› å­è®°å½•æ•? {len(index_result) if not index_result.empty else 0}")
    
    # æ€§èƒ½å¯¹æ¯”
    print(f"\nâš?æ€§èƒ½å¯¹æ¯”ç»“æœ:")
    print(f"   ä¸ªè‚¡å› å­å¹³å‡æ¯è‚¡è€—æ—¶: {stock_time/len(test_securities):.2f} ç§?)
    print(f"   æŒ‡æ•°å› å­è€—æ—¶: {index_time:.2f} ç§?)
    
    if stock_time > 0 and index_time > 0:
        ratio = stock_time / index_time
        print(f"   ä¸ªè‚¡/æŒ‡æ•°å› å­è€—æ—¶æ¯? {ratio:.2f}")


def demo_factor_interpretation():
    """å› å­è§£é‡Šæ¼”ç¤º"""
    print("\n" + "="*60)
    print("7. å› å­è§£é‡Šæ¼”ç¤º")
    print("="*60)
    
    print("\nğŸ“š å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­è§£é‡Š:")
    print("="*50)
    
    print("\nğŸ¯ å› å­æ ¸å¿ƒæ€æƒ³:")
    print("   â€?é«˜å…³æ³¨åº¦è‚¡ç¥¨ç ”ç©¶æ›´å……åˆ†ï¼Œåˆ†æå¸ˆé¢„æµ‹åŒè´¨æ€§æ›´é«?)
    print("   â€?é¢„æµ‹è¯¯å·®ç›¸å¯¹è¾ƒä½ï¼Œç»™äºˆæ›´é«˜æƒé‡?)
    print("   â€?æœ‰åŠ©äºè¯†åˆ«åˆ†æå¸ˆé¢„æµ‹åå·®è¾ƒå°çš„è‚¡ç¥?)
    print("   â€?ç­›é€‰å‡ºæ›´å…·alphaçš„æŠ•èµ„æ ‡çš?)
    
    print("\nğŸ“Š å› å­è®¡ç®—é€»è¾‘:")
    print("   1. è·å–åˆ†æå¸ˆé¢„æœŸæ”¶ç›Šç‡æ•°æ®")
    print("   2. è®¡ç®—åŠ æƒé¢„æœŸæ”¶ç›Šç‡ï¼ˆè€ƒè™‘åˆ†æå¸ˆè´¨é‡?æœºæ„å£°èª‰ï¼?)
    print("   3. ç»Ÿè®¡å…³æ³¨åº¦ï¼ˆåˆ†æå¸ˆè¦†ç›–æ•°é‡ï¼Œæœºæ„å»é‡ï¼?)
    print("   4. å¯¹ä¸¤ä¸ªå› å­è¿›è¡Œæ¨ªæˆªé¢æ’åº")
    print("   5. è®¡ç®—æœ€ç»ˆæ ¡æ­£æ”¶ç›Šç‡ = Rank(åŠ æƒæ”¶ç›Šç? Ã— Rank(å…³æ³¨åº?")
    
    print("\nğŸ” å› å­å«ä¹‰è§£é‡Š:")
    print("   â€?é«˜å› å­å€¼ï¼šé«˜å…³æ³¨åº¦ + é«˜é¢„æœŸæ”¶ç›Šç‡ â†?å¸‚åœºä¸€è‡´çœ‹å¥?)
    print("   â€?ä½å› å­å€¼ï¼šä½å…³æ³¨åº¦ + ä½é¢„æœŸæ”¶ç›Šç‡ â†?å¸‚åœºä¸çœ‹å¥?)
    print("   â€?ä¸­ç­‰å› å­å€¼ï¼šå…³æ³¨åº¦ä¸é¢„æœŸæ”¶ç›Šç‡ä¸åŒ¹é…")
    
    print("\nğŸ’¡ æŠ•èµ„åº”ç”¨å»ºè®®:")
    print("   â€?å¤šå› å­æ¨¡å‹ï¼šä½œä¸ºæƒ…ç»ªå› å­çº³å…¥å¤šå› å­æ¨¡å?)
    print("   â€?é€‰è‚¡ç­–ç•¥ï¼šç­›é€‰é«˜å› å­å€¼è‚¡ç¥¨æ„å»ºæŠ•èµ„ç»„å?)
    print("   â€?é£é™©ç®¡ç†ï¼šå…³æ³¨å› å­å€¼å¼‚å¸¸å˜åŒ–çš„è‚¡ç¥¨")
    print("   â€?å¸‚åœºæ‹©æ—¶ï¼šè§‚å¯Ÿå› å­æ•´ä½“åˆ†å¸ƒå˜åŒ–åˆ¤æ–­å¸‚åœºæƒ…ç»?)
    
    print("\nâš ï¸ ä½¿ç”¨æ³¨æ„äº‹é¡¹:")
    print("   â€?æ•°æ®è´¨é‡ï¼šä¾èµ–é«˜è´¨é‡çš„åˆ†æå¸ˆé¢„æµ‹æ•°æ®")
    print("   â€?æ ·æœ¬åå·®ï¼šæ³¨æ„åˆ†æå¸ˆè¦†ç›–çš„ç”Ÿå­˜åå·?)
    print("   â€?æ—¶æ•ˆæ€§ï¼šåˆ†æå¸ˆæ•°æ®å¯èƒ½å­˜åœ¨å‘å¸ƒå»¶è¿?)
    print("   â€?å¸‚åœºç¯å¢ƒï¼šå› å­æ•ˆæœå¯èƒ½å—å¸‚åœºç¯å¢ƒå½±å“")


def main():
    """ä¸»æ¼”ç¤ºå‡½æ•?""
    print("ğŸ‰ å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­æ¼”ç¤ºç¨‹åº")
    print("ğŸ“Š åŸºäº https://factors.directory/zh/factors/emotion/focus-correction-expected-yield")
    print("ğŸ”§ æ¥å…¥data_pipelineçœŸå®æ•°æ®ç®¡é“ï¼Œæ”¯æŒä¸ªè‚¡å’ŒæŒ‡æ•°")
    
    # è¿è¡Œå„ä¸ªæ¼”ç¤º
    demo_basic_usage()
    demo_index_factor()
    demo_custom_configuration()
    demo_factor_analysis()
    demo_data_pipeline_test()
    demo_performance_comparison()
    demo_factor_interpretation()
    
    print("\n" + "="*60)
    print("ğŸŠ å…³æ³¨åº¦æ ¡æ­£é¢„æœŸæ”¶ç›Šç‡å› å­æ¼”ç¤ºå®Œæˆï¼?)
    print("="*60)
    
    print("\nğŸ“‹ æ¼”ç¤ºæ€»ç»“:")
    print("âœ?å› å­åˆå§‹åŒ–å’Œé…ç½®")
    print("âœ?ä¸ªè‚¡å’ŒæŒ‡æ•°å› å­è®¡ç®?)
    print("âœ?è‡ªå®šä¹‰é…ç½®ä½¿ç”?)
    print("âœ?å› å­åˆ†æå’Œè§£é‡?)
    print("âœ?æ•°æ®ç®¡é“é›†æˆæµ‹è¯•")
    print("âœ?æ€§èƒ½å¯¹æ¯”åˆ†æ")
    
    print("\nğŸš€ åç»­ä½¿ç”¨å»ºè®®:")
    print("   1. æ ¹æ®å®é™…éœ€æ±‚è°ƒæ•´å› å­å‚æ•?)
    print("   2. é›†æˆåˆ°å¤šå› å­æ¨¡å‹ä¸?)
    print("   3. è¿›è¡Œå†å²å›æµ‹éªŒè¯")
    print("   4. ç›‘æ§å› å­è¡¨ç°å’Œç¨³å®šæ€?)
    
    print("\nğŸ“ å¦‚æœ‰é—®é¢˜ï¼Œè¯·å‚è€ƒREADMEæ–‡æ¡£æˆ–è”ç³»å¼€å‘å›¢é˜?)


if __name__ == "__main__":
    main() 
