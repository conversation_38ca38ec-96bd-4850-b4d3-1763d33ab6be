# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
成交量择时策略
基于成交量分析的择时策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime
import warnings
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .base_timer import BaseTimer
    from .timing_core import TimingConfig, TimingSignal
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
            self._required_columns = kwargs.get('required_columns', ['close'])
            self._min_history = kwargs.get('min_history', 20)
            
        def validate_data(self, data):
            return True  # 简化验证
            
        def apply_threshold(self, strength):
            return strength
            
        def normalize_strength(self, value, vmin, vmax):
            return min(1.0, max(0.0, (value - vmin) / (vmax - vmin)))
    
    class TimingConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class TimingSignal:
        def __init__(self, timestamp, signal, strength, strategy, metadata):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata
    
# 导入标准适配器
try:
    from adapters import StockDataAdapter
except ImportError:
    # 如果导入失败，提供简单的模拟实现
    from adapters import StockDataAdapter

# 导入日志模块
try:
    from loguru import logger
except ImportError:
    # 如果导入失败，提供简单的日志实现
    from loguru import logger

warnings.filterwarnings('ignore')


class VolumeTimingStrategy(BaseTimer):
    """成交量择时策略"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化成交量择时策略
        
        Args:
            config: 策略配置字典
        """
        default_config = {
            'vol_ma_window': 20,
            'vol_spike_threshold': 2.0,
            'price_change_threshold': 0.02,
            'volume_price_corr_window': 10
        }
        
        if config:
            default_config.update(config)
        
        timing_config = TimingConfig(**default_config)
        super().__init__(
            timing_config,
            required_columns=['close', 'volume'],
            min_history=max(default_config['vol_ma_window'], default_config['volume_price_corr_window'])
        )
        
        self.vol_ma_window = default_config['vol_ma_window']
        self.vol_spike_threshold = default_config['vol_spike_threshold']
        self.price_change_threshold = default_config['price_change_threshold']
        self.volume_price_corr_window = default_config['volume_price_corr_window']
        
        self.data_adapter = StockDataAdapter()

    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法：基于最新数据计算信号"""
        if not self.validate_data(data):
            raise ValueError("数据验证失败")
        
        # 计算成交量指标
        volume_ma = data['volume'].rolling(self.vol_ma_window).mean()
        current_volume = data['volume'].iloc[-1]
        volume_ratio = current_volume / volume_ma.iloc[-1]
        
        # 计算价格变化
        price_change = data['close'].pct_change().iloc[-1]
        
        # 计算量价配合度
        price_returns = data['close'].pct_change()
        volume_returns = data['volume'].pct_change()
        corr_window = min(self.volume_price_corr_window, len(data))
        volume_price_corr = price_returns.rolling(corr_window).corr(volume_returns).iloc[-1]
        
        # 生成信号
        signal_value = 0
        if volume_ratio > self.vol_spike_threshold and price_change > self.price_change_threshold:
            signal_value = 1  # 放量上涨
        elif volume_ratio > self.vol_spike_threshold and price_change < -self.price_change_threshold:
            signal_value = -1  # 放量下跌
        
        # 计算信号强度
        strength = self.apply_threshold(
            self.normalize_strength(volume_ratio, vmin=1.0, vmax=3.0)
        )
        
        return TimingSignal(
            timestamp=datetime.now(),
            signal=int(signal_value),
            strength=strength,
            strategy="VolumeTimingStrategy",
            metadata={
                'volume_ratio': volume_ratio,
                'price_change': price_change,
                'volume_price_corr': volume_price_corr if not pd.isna(volume_price_corr) else 0.0
            }
        )
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算量能指标
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含量能指标的DataFrame
        """
        df = data.copy()
        
        # 计算成交量移动平均
        df['volume_ma'] = df['volume'].rolling(window=self.lookback_period).mean()
        
        # 计算量比（当前成交量/平均成交量）
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        # 计算价格变化率
        df['price_change'] = df['close'].pct_change()
        df['price_change_abs'] = abs(df['price_change'])
        
        # 计算量价相关性
        df['volume_price_corr'] = df['volume'].rolling(window=self.lookback_period).corr(df['close'])
        
        # 计算成交量标准差
        df['volume_std'] = df['volume'].rolling(window=self.lookback_period).std()
        
        # 计算量能强度指标
        df['volume_strength'] = (df['volume'] - df['volume_ma']) / df['volume_std']
        
        return df
    
    def detect_volume_patterns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        检测量价模式
        
        Args:
            data: 包含量能指标的DataFrame
            
        Returns:
            包含量价模式的DataFrame
        """
        df = data.copy()
        
        # 放量突破模式
        breakout_volume = df['volume_ratio'] > self.volume_threshold_high
        breakout_price = df['price_change'] > self.price_change_threshold
        df['breakout_pattern'] = (breakout_volume & breakout_price).astype(int)
        
        # 缩量整理模式
        consolidation_volume = df['volume_ratio'] < self.volume_threshold_low
        consolidation_price = df['price_change_abs'] < self.price_change_threshold
        df['consolidation_pattern'] = (consolidation_volume & consolidation_price).astype(int)
        
        # 放量下跌模式（可能的底部信号）
        volume_decline = (df['volume_ratio'] > self.volume_threshold_high) & (df['price_change'] < -self.price_change_threshold)
        df['volume_decline_pattern'] = volume_decline.astype(int)
        
        # 缩量上涨模式（可能的顶部信号）
        volume_rise = (df['volume_ratio'] < self.volume_threshold_low) & (df['price_change'] > self.price_change_threshold)
        df['volume_rise_pattern'] = volume_rise.astype(int)
        
        return df
    
    def detect_divergence(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        检测量价背离
        
        Args:
            data: 包含价格和成交量数据的DataFrame
            
        Returns:
            包含背离信号的DataFrame
        """
        df = data.copy()
        
        # 计算价格和成交量的短期趋势
        price_trend = df['close'].rolling(window=self.divergence_lookback).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == self.divergence_lookback else 0
        )
        
        volume_trend = df['volume'].rolling(window=self.divergence_lookback).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == self.divergence_lookback else 0
        )
        
        # 标准化趋势
        price_trend_norm = (price_trend - price_trend.rolling(window=50).mean()) / price_trend.rolling(window=50).std()
        volume_trend_norm = (volume_trend - volume_trend.rolling(window=50).mean()) / volume_trend.rolling(window=50).std()
        
        # 计算背离程度
        divergence = abs(price_trend_norm - volume_trend_norm)
        
        # 顶背离：价格上涨但成交量下降
        df['top_divergence'] = (
            (price_trend_norm > 0) & 
            (volume_trend_norm < 0) & 
            (divergence > self.divergence_threshold)
        ).astype(int)
        
        # 底背离：价格下跌但成交量上升
        df['bottom_divergence'] = (
            (price_trend_norm < 0) & 
            (volume_trend_norm > 0) & 
            (divergence > self.divergence_threshold)
        ).astype(int)
        
        return df
    
    def calculate_volume_signal(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算量价择时信号
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含择时信号的DataFrame
        """
        # 计算量能指标
        df = self.calculate_volume_indicators(data)
        
        # 检测量价模式
        df = self.detect_volume_patterns(df)
        
        # 检测量价背离
        df = self.detect_divergence(df)
        
        # 计算综合信号
        df['raw_signal'] = (
            df['breakout_pattern'] * 1.0 +           # 放量突破 +1
            df['consolidation_pattern'] * 0.0 +      # 缩量整理 0
            df['volume_decline_pattern'] * 0.5 +     # 放量下跌 +0.5（可能反转）
            df['volume_rise_pattern'] * (-0.5) +     # 缩量上涨 -0.5（可能见顶）
            df['bottom_divergence'] * 0.8 +          # 底背离 +0.8
            df['top_divergence'] * (-0.8)            # 顶背离 -0.8
        )
        
        # 信号平滑
        df['smoothed_signal'] = df['raw_signal'].rolling(window=self.signal_smoothing).mean()
        
        # 生成最终信号
        df['volume_timing_signal'] = np.where(
            df['smoothed_signal'] > self.min_signal_strength, 1,
            np.where(df['smoothed_signal'] < -self.min_signal_strength, -1, 0)
        )
        
        # 计算信号强度
        df['signal_strength'] = abs(df['smoothed_signal'])
        
        return df
    
    def calculate_position_size(self, signal: float, signal_strength: float, current_price: float) -> float:
        """
        计算仓位大小
        
        Args:
            signal: 交易信号 (-1, 0, 1)
            signal_strength: 信号强度 (0-1)
            current_price: 当前价格
            
        Returns:
            目标仓位 (-1 到 1)
        """
        if signal == 0:
            return 0.0
        
        # 基础仓位 = 信号方向 * 信号强度 * 最大仓位
        base_position = signal * signal_strength * self.max_position
        
        # 风险调整
        if self.entry_price is not None:
            # 计算未实现盈亏
            unrealized_pnl = (current_price - self.entry_price) / self.entry_price
            
            # 止损
            if (self.current_position > 0 and unrealized_pnl < -self.stop_loss) or \
               (self.current_position < 0 and unrealized_pnl > self.stop_loss):
                return 0.0
            
            # 止盈
            if (self.current_position > 0 and unrealized_pnl > self.take_profit) or \
               (self.current_position < 0 and unrealized_pnl < -self.take_profit):
                return 0.0
        
        return np.clip(base_position, -self.max_position, self.max_position)
    
    def generate_trading_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含交易信号的DataFrame
        """
        # 计算量价信号
        df = self.calculate_volume_signal(data)
        
        # 初始化交易相关列
        df['target_position'] = 0.0
        df['position_change'] = 0.0
        df['trade_signal'] = 0
        
        # 逐行计算交易信号
        for i in range(len(df)):
            if i < self.lookback_period:
                continue
                
            current_signal = df.iloc[i]['volume_timing_signal']
            signal_strength = df.iloc[i]['signal_strength']
            current_price = df.iloc[i]['close']
            
            # 计算目标仓位
            target_position = self.calculate_position_size(
                current_signal, signal_strength, current_price
            )
            
            df.iloc[i, df.columns.get_loc('target_position')] = target_position
            
            # 计算仓位变化
            position_change = target_position - self.current_position
            df.iloc[i, df.columns.get_loc('position_change')] = position_change
            
            # 生成交易信号
            if abs(position_change) > 0.1:  # 仓位变化超过10%才交易
                df.iloc[i, df.columns.get_loc('trade_signal')] = np.sign(position_change)
                
                # 更新状态
                if self.current_position == 0 and target_position != 0:
                    self.entry_price = current_price
                elif target_position == 0:
                    self.entry_price = None
                    
                self.current_position = target_position
        
        return df
    
    def backtest_strategy(self, data: pd.DataFrame) -> Dict:
        """
        回测量价择时策略
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            回测结果字典
        """
        # 重置状态
        self.current_position = 0.0
        self.entry_price = None
        
        # 生成交易信号
        df = self.generate_trading_signals(data)
        
        # 计算收益
        df['strategy_return'] = df['target_position'].shift(1) * df['close'].pct_change()
        df['cumulative_return'] = (1 + df['strategy_return']).cumprod()
        
        # 计算基准收益
        df['benchmark_return'] = df['close'].pct_change()
        df['benchmark_cumulative'] = (1 + df['benchmark_return']).cumprod()
        
        # 计算超额收益
        df['excess_return'] = df['strategy_return'] - df['benchmark_return']
        df['excess_cumulative'] = (1 + df['excess_return']).cumprod()
        
        # 计算性能指标
        total_return = df['cumulative_return'].iloc[-1] - 1
        benchmark_total_return = df['benchmark_cumulative'].iloc[-1] - 1
        excess_return = total_return - benchmark_total_return
        
        # 计算年化收益率
        trading_days = len(df)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (252 / trading_days) - 1
        
        # 计算波动率
        volatility = df['strategy_return'].std() * np.sqrt(252)
        benchmark_volatility = df['benchmark_return'].std() * np.sqrt(252)
        
        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        rolling_max = df['cumulative_return'].expanding().max()
        drawdown = (df['cumulative_return'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 计算胜率
        winning_trades = (df['strategy_return'] > 0).sum()
        total_trades = (df['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 计算交易次数
        trade_count = (df['trade_signal'] != 0).sum()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'benchmark_return': benchmark_total_return,
            'benchmark_annual_return': benchmark_annual_return,
            'excess_return': excess_return,
            'volatility': volatility,
            'benchmark_volatility': benchmark_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'trade_count': trade_count,
            'data': df
        }
    
    def get_current_signal(self, data: pd.DataFrame) -> Dict:
        """
        获取当前择时信号
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            当前信号字典
        """
        # 计算量价信号
        df = self.calculate_volume_signal(data)
        
        if len(df) == 0:
            return {'signal': 0, 'strength': 0, 'description': '数据不足'}
        
        latest = df.iloc[-1]
        
        signal = latest['volume_timing_signal']
        strength = latest['signal_strength']
        
        # 生成信号描述
        if signal == 1:
            description = f"买入信号 (强度: {strength:.2f})"
        elif signal == -1:
            description = f"卖出信号 (强度: {strength:.2f})"
        else:
            description = f"观望信号 (强度: {strength:.2f})"
        
        # 添加模式描述
        patterns = []
        if latest['breakout_pattern']:
            patterns.append('放量突破')
        if latest['consolidation_pattern']:
            patterns.append('缩量整理')
        if latest['volume_decline_pattern']:
            patterns.append('放量下跌')
        if latest['volume_rise_pattern']:
            patterns.append('缩量上涨')
        if latest['bottom_divergence']:
            patterns.append('底背离')
        if latest['top_divergence']:
            patterns.append('顶背离')
        
        pattern_desc = ', '.join(patterns) if patterns else '无明显模式'
        
        return {
            'signal': signal,
            'strength': strength,
            'description': description,
            'patterns': pattern_desc,
            'volume_ratio': latest['volume_ratio'],
            'price_change': latest['price_change'],
            'volume_strength': latest['volume_strength']
        }


def create_volume_timing_strategy(config: Optional[Dict] = None) -> VolumeTimingStrategy:
    """
    创建量价择时策略实例
    
    Args:
        config: 策略配置参数
        
    Returns:
        VolumeTimingStrategy实例
    """
    return VolumeTimingStrategy(config)


# 示例使用
if __name__ == "__main__":
    # 创建示例数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    
    # 生成模拟的OHLCV数据
    base_price = 100
    returns = np.random.normal(0.001, 0.02, 252)
    prices = base_price * np.cumprod(1 + returns)
    
    # 生成相关的成交量数据
    volumes = np.random.lognormal(10, 0.5, 252)
    # 在价格大幅波动时增加成交量
    volume_multiplier = 1 + abs(returns) * 5
    volumes = volumes * volume_multiplier
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices * (1 + np.random.normal(0, 0.005, 252)),
        'high': prices * (1 + abs(np.random.normal(0, 0.01, 252))),
        'low': prices * (1 - abs(np.random.normal(0, 0.01, 252))),
        'close': prices,
        'volume': volumes
    })
    
    # 创建策略实例
    strategy = create_volume_timing_strategy()
    
    # 回测策略
    results = strategy.backtest_strategy(data)
    
    # 输出结果
    print("=== 量价择时策略回测结果 ===")
    print(f"总收益率: {results['total_return']:.2%}")
    print(f"年化收益率: {results['annual_return']:.2%}")
    print(f"基准收益率: {results['benchmark_return']:.2%}")
    print(f"超额收益: {results['excess_return']:.2%}")
    print(f"波动率: {results['volatility']:.2%}")
    print(f"夏普比率: {results['sharpe_ratio']:.2f}")
    print(f"最大回撤: {results['max_drawdown']:.2%}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"交易次数: {results['trade_count']}")
    
    # 获取当前信号
    current_signal = strategy.get_current_signal(data)
    print(f"\n=== 当前信号 ===")
    print(f"信号: {current_signal['description']}")
    print(f"模式: {current_signal['patterns']}")
    print(f"量比: {current_signal['volume_ratio']:.2f}")
    print(f"价格变化: {current_signal['price_change']:.2%}") 