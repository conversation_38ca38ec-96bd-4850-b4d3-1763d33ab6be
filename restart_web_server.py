# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import time
from pathlib import Path

# Add project path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def main():
    """重启Web服务器以加载最新代码"""
    try:
        print("🔄 重启Web服务器以加载最新代码...")
        print("=" * 60)
        
        # 清除Python模块缓存
        modules_to_clear = []
        for module_name in list(sys.modules.keys()):
            if 'rdagent_integration' in module_name or 'multi_regime' in module_name:
                modules_to_clear.append(module_name)
        
        for module_name in modules_to_clear:
            if module_name in sys.modules:
                del sys.modules[module_name]
                print(f"🗑️ 清除模块缓存: {module_name}")
        
        print("✅ 模块缓存已清除")
        
        # 重新导入模块
        from rdagent_integration.scenarios.multi_regime_decision.web_interface import MultiRegimeWebInterface
        import uvicorn
        
        # 创建Web界面实例
        web_interface = MultiRegimeWebInterface()
        app = web_interface.app
        
        print("✅ Web界面重新初始化完成")
        print("🌐 访问地址: http://localhost:8000/multi-regime/")
        print()
        print("🔍 版本验证:")
        print("  现在代码中包含版本检查标记")
        print("  如果在控制台看到以下日志，说明代码已更新:")
        print("  '🔄 代码版本检查: 2025-08-01-14:25 - 图片显示功能已修复'")
        print()
        print("📋 测试步骤:")
        print("  1. 关闭之前的浏览器标签页")
        print("  2. 重新打开: http://localhost:8000/multi-regime/")
        print("  3. 按 Ctrl+F5 强制刷新")
        print("  4. 按 F12 打开控制台")
        print("  5. 点击【开始分析】按钮")
        print("  6. 查看控制台是否显示版本检查和图片相关日志")
        print()
        print("🎯 期望看到的日志顺序:")
        print("  - 🔄 代码版本检查: 2025-08-01-14:25...")
        print("  - 🎯 准备显示制度分析结果图片...")
        print("  - 🖼️ showPlotImage 被调用...")
        print("  - 📊 图片展示成功...")
        print("=" * 60)
        
        # 启动服务器
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info", reload=False)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
