# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import time
from pathlib import Path

# Add project path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def fix_import_warnings():
    """修复导入警告"""
    print("🔧 修复导入警告...")

    # 1. 修复监控配置参数问题
    try:
        from rdagent_integration.scenarios.multi_regime_decision.multi_regime_integration import MultiRegimeIntegration
        print("✅ 监控配置已修复")
    except Exception as e:
        print(f"⚠️ 监控配置警告: {e}")

    # 2. 修复配置模块导入问题
    try:
        # 确保config模块优先级
        config_path = project_root / "config"
        if config_path.exists() and str(config_path) not in sys.path:
            sys.path.insert(0, str(config_path))

        # 安全导入DATA_DIR
        try:
            from config import DATA_DIR
            print("✅ 配置模块导入成功")
        except ImportError:
            DATA_DIR = project_root / 'data_storage'
            print("⚠️ 使用默认DATA_DIR路径")

    except Exception as e:
        print(f"⚠️ 配置导入警告: {e}")

    # 3. 修复循环导入问题
    try:
        # 延迟导入adapters模块
        import importlib
        adapters_module = importlib.import_module('adapters')
        print("✅ adapters模块导入成功")
    except Exception as e:
        print(f"⚠️ adapters导入警告: {e}")

def clear_module_cache():
    """清除模块缓存"""
    print("🗑️ 清除模块缓存...")

    modules_to_clear = []
    for module_name in list(sys.modules.keys()):
        if any(keyword in module_name for keyword in [
            'rdagent_integration',
            'multi_regime',
            'strategy_autodev',
            'adapters',
            'config'
        ]):
            modules_to_clear.append(module_name)

    for module_name in modules_to_clear:
        if module_name in sys.modules:
            del sys.modules[module_name]
            print(f"  🗑️ 清除: {module_name}")

    print("✅ 模块缓存已清除")

def main():
    """重启Web服务器以加载最新代码"""
    try:
        print("🔄 重启Web服务器以加载最新代码...")
        print("=" * 60)

        # 修复导入警告
        fix_import_warnings()

        # 清除Python模块缓存
        clear_module_cache()

        # 重新导入模块
        print("📦 重新导入模块...")
        from rdagent_integration.scenarios.multi_regime_decision.web_interface import MultiRegimeWebInterface
        import uvicorn
        
        # 创建Web界面实例
        web_interface = MultiRegimeWebInterface()
        app = web_interface.app
        
        print("✅ Web界面重新初始化完成")
        print("🌐 访问地址: http://localhost:8000/multi-regime/")
        print()
        print("🔍 版本验证:")
        print("  现在代码中包含版本检查标记")
        print("  如果在控制台看到以下日志，说明代码已更新:")
        print("  '🔄 代码版本检查: 2025-08-01-14:25 - 图片显示功能已修复'")
        print()
        print("📋 测试步骤:")
        print("  1. 关闭之前的浏览器标签页")
        print("  2. 重新打开: http://localhost:8000/multi-regime/")
        print("  3. 按 Ctrl+F5 强制刷新")
        print("  4. 按 F12 打开控制台")
        print("  5. 点击【开始分析】按钮")
        print("  6. 查看控制台是否显示版本检查和图片相关日志")
        print()
        print("🎯 期望看到的日志顺序:")
        print("  - 🔄 代码版本检查: 2025-08-01-14:25...")
        print("  - 🎯 准备显示制度分析结果图片...")
        print("  - 🖼️ showPlotImage 被调用...")
        print("  - 📊 图片展示成功...")
        print("=" * 60)
        
        # 启动服务器
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info", reload=False)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
