# -*- coding: utf-8 -*-
"""
修复导入警告脚本
解决循环导入和配置参数问题
"""

import sys
import os
from pathlib import Path

def fix_monitoring_config():
    """修复监控配置参数问题"""
    try:
        # 修复多制度集成中的监控配置
        integration_file = "rdagent_integration/scenarios/multi_regime_decision/multi_regime_integration.py"
        
        if os.path.exists(integration_file):
            with open(integration_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复MonitoringConfig参数问题
            old_config = """config = MonitoringConfig(
                    check_interval=5.0,
                    alert_threshold=0.8
                )"""
            
            new_config = """config = MonitoringConfig(
                    # 移除不支持的参数，使用默认值
                )"""
            
            if old_config in content:
                content = content.replace(old_config, new_config)
                
                with open(integration_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ 修复了监控配置参数问题")
        
    except Exception as e:
        print(f"❌ 修复监控配置失败: {e}")

def fix_config_imports():
    """修复配置模块导入问题"""
    try:
        # 检查Independance目录中的config.py冲突
        independance_config = "Independance/attention_cnn_lstm_arima/config.py"
        if os.path.exists(independance_config):
            print(f"⚠️ 发现冲突的config文件: {independance_config}")
            print("   建议重命名或移动到其他目录以避免导入冲突")
        
        # 确保主config模块正确导出
        config_init = "config/__init__.py"
        if os.path.exists(config_init):
            print("✅ 主config模块初始化文件存在")
        
    except Exception as e:
        print(f"❌ 修复配置导入失败: {e}")

def fix_circular_imports():
    """修复循环导入问题"""
    try:
        # 检查adapters模块的循环导入
        adapters_init = "adapters/__init__.py"
        if os.path.exists(adapters_init):
            print("✅ adapters模块初始化文件存在")
            
            # 建议延迟导入来避免循环导入
            print("💡 建议在adapters/__init__.py中使用延迟导入:")
            print("   from .base_data_adapter import BaseDataAdapter  # 延迟导入")
        
    except Exception as e:
        print(f"❌ 修复循环导入失败: {e}")

def create_import_fix_patch():
    """创建导入修复补丁"""
    try:
        # 创建一个临时的导入修复模块
        patch_content = '''# -*- coding: utf-8 -*-
"""
导入修复补丁
解决循环导入和配置冲突问题
"""

import sys
import os
from pathlib import Path

def fix_import_paths():
    """修复导入路径"""
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 确保config模块优先级
    config_path = project_root / "config"
    if config_path.exists() and str(config_path) not in sys.path:
        sys.path.insert(0, str(config_path))

def safe_import(module_name, fallback=None):
    """安全导入模块"""
    try:
        return __import__(module_name)
    except ImportError as e:
        print(f"⚠️ 导入警告: {module_name} - {e}")
        return fallback

# 应用修复
fix_import_paths()

# 安全导入常用模块
DATA_DIR = safe_import('config').DATA_DIR if safe_import('config') else Path(__file__).parent / 'data_storage'
BaseDataAdapter = safe_import('adapters.base_data_adapter').BaseDataAdapter if safe_import('adapters.base_data_adapter') else None
'''
        
        with open("import_fix_patch.py", 'w', encoding='utf-8') as f:
            f.write(patch_content)
        
        print("✅ 创建了导入修复补丁: import_fix_patch.py")
        
    except Exception as e:
        print(f"❌ 创建导入修复补丁失败: {e}")

def main():
    """主函数"""
    print("🔧 开始修复导入警告...")
    print("=" * 60)
    
    # 修复监控配置
    fix_monitoring_config()
    
    # 修复配置导入
    fix_config_imports()
    
    # 修复循环导入
    fix_circular_imports()
    
    # 创建导入修复补丁
    create_import_fix_patch()
    
    print("=" * 60)
    print("📋 修复建议:")
    print("1. 重启Web服务器前，先导入修复补丁:")
    print("   import import_fix_patch")
    print("2. 如果仍有警告，可以忽略非关键警告")
    print("3. 对于循环导入，建议使用延迟导入或重构模块结构")
    print("4. 对于配置冲突，确保只有一个config模块被优先导入")
    print("=" * 60)

if __name__ == "__main__":
    main() 