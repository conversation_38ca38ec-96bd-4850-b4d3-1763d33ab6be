#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
æµ‹è¯•industry adapterä¿®å¤æ•ˆæœ
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_pipeline.adapter_manager import AdapterRegistry
from adapters.industry_data_adapter import IndustryDataAdapter

def test_industry_adapter():
    """æµ‹è¯•industry adapter"""
    print("=== æµ‹è¯•Industry Adapterä¿®å¤æ•ˆæœ ===")
    
    try:
        # æµ‹è¯•ç›´æ¥åˆ›å»ºadapter
        print("1. æµ‹è¯•ç›´æ¥åˆ›å»ºIndustryDataAdapter...")
        adapter = IndustryDataAdapter()
        print(f"   âœ?åˆ›å»ºæˆåŠŸ: {type(adapter).__name__}")
        print(f"   âœ?å¯ç”¨æ–¹æ³•: {[m for m in dir(adapter) if 'get_' in m and not m.startswith('_')]}")
        
        # æµ‹è¯•é€šè¿‡registryè·å–adapter
        print("\n2. æµ‹è¯•é€šè¿‡AdapterRegistryè·å–industry adapter...")
        registry = AdapterRegistry()
        industry_adapter = registry.get_adapter('industry')
        print(f"   âœ?è·å–æˆåŠŸ: {type(industry_adapter).__name__}")
        
        # æµ‹è¯•get_sw_dailyæ–¹æ³•æ˜¯å¦å­˜åœ¨
        print("\n3. æµ‹è¯•get_sw_dailyæ–¹æ³•...")
        if hasattr(industry_adapter, 'get_sw_daily'):
            print("   âœ?get_sw_dailyæ–¹æ³•å­˜åœ¨")
        else:
            print("   âœ?get_sw_dailyæ–¹æ³•ä¸å­˜åœ?)
            
        # æµ‹è¯•get_industry_dataæ–¹æ³•æ˜¯å¦å­˜åœ¨
        print("\n4. æµ‹è¯•get_industry_dataæ–¹æ³•...")
        if hasattr(industry_adapter, 'get_industry_data'):
            print("   âœ?get_industry_dataæ–¹æ³•å­˜åœ¨")
        else:
            print("   âœ?get_industry_dataæ–¹æ³•ä¸å­˜åœ?)
            
        print("\n=== æµ‹è¯•å®Œæˆï¼Œä¿®å¤æˆåŠŸï¼ ===")
        return True
        
    except Exception as e:
        print(f"   âœ?æµ‹è¯•å¤±è´¥: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_industry_adapter()
