# -*- coding: utf-8 -*-
"""
因子挖掘共享组件
Shared Components for Factor Mining

提供所有模块共享的组件和工具函数
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tu<PERSON>
from datetime import datetime, timedelta
import sqlite3
import json
from pathlib import Path

# 导入适配器
try:
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from adapters.factor_data_adapter import FactorDataAdapter
except ImportError:
    # 尝试相对导入
    try:
        from ...adapters import StockDataAdapter, MarketDataAdapter, FundamentalDataAdapter, FactorDataAdapter
    except ImportError:
        # 最后尝试绝对导入
        import sys
        sys.path.append(str(Path(__file__).parent.parent.parent))
        try:
            from adapters import StockDataAdapter, MarketDataAdapter, FundamentalDataAdapter, FactorDataAdapter
        except ImportError:
            # 如果所有导入都失败，设置为None
            StockDataAdapter = None
            MarketDataAdapter = None
            FundamentalDataAdapter = None
            FactorDataAdapter = None

# 检查哪些适配器可用
available_adapters = []
missing_adapters = []

for name, adapter in [
    ("StockDataAdapter", StockDataAdapter),
    ("MarketDataAdapter", MarketDataAdapter),
    ("FundamentalDataAdapter", FundamentalDataAdapter),
    ("FactorDataAdapter", FactorDataAdapter)
]:
    if adapter is not None:
        available_adapters.append(name)
    else:
        missing_adapters.append(name)

# 只在调试模式下显示适配器状态
if os.environ.get('FACTOR_DEBUG', '').lower() == 'true':
    if missing_adapters:
        logging.warning(f"以下数据适配器未找到: {', '.join(missing_adapters)}")
    if available_adapters:
        logging.info(f"以下数据适配器可用: {', '.join(available_adapters)}")

from factor_analyze.factor_core.common_types import FactorData, MiningConfig, FactorMetadata


class DataAdapterManager:
    """
    数据适配器管理器
    
    统一管理各种数据源的适配器，替代聚宽等第三方数据源
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.adapters = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self._init_adapters()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.DataAdapterManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_adapters(self):
        """初始化数据适配器"""
        try:
            if StockDataAdapter:
                self.adapters['stock'] = StockDataAdapter()
            if MarketDataAdapter:
                self.adapters['market'] = MarketDataAdapter()
            if FundamentalDataAdapter:
                self.adapters['fundamental'] = FundamentalDataAdapter()
            if FactorDataAdapter:
                self.adapters['factor'] = FactorDataAdapter()
            
            self.logger.info(f"已初始化 {len(self.adapters)} 个数据适配器")
        except Exception as e:
            self.logger.error(f"初始化数据适配器失败: {e}")
    
    def get_stock_data(self, 
                      stock_codes: List[str],
                      start_date: str,
                      end_date: str,
                      fields: List[str] = None) -> pd.DataFrame:
        """
        获取股票数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            
        Returns:
            股票数据
        """
        if 'stock' not in self.adapters:
            raise ValueError("股票数据适配器未初始化")
        
        try:
            return self.adapters['stock'].get_price_data(
                symbols=stock_codes,
                start_date=start_date,
                end_date=end_date,
                fields=fields or ['open', 'high', 'low', 'close', 'volume']
            )
        except Exception as e:
            self.logger.error(f"获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def get_market_data(self,
                       start_date: str,
                       end_date: str,
                       market_type: str = 'A股') -> pd.DataFrame:
        """
        获取市场数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            market_type: 市场类型
            
        Returns:
            市场数据
        """
        if 'market' not in self.adapters:
            raise ValueError("市场数据适配器未初始化")
        
        try:
            return self.adapters['market'].get_market_overview(
                start_date=start_date,
                end_date=end_date,
                market=market_type
            )
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return pd.DataFrame()
    
    def get_fundamental_data(self,
                           stock_codes: List[str],
                           start_date: str,
                           end_date: str,
                           fields: List[str] = None) -> pd.DataFrame:
        """
        获取基本面数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            
        Returns:
            基本面数据
        """
        if 'fundamental' not in self.adapters:
            raise ValueError("基本面数据适配器未初始化")
        
        try:
            return self.adapters['fundamental'].get_financial_data(
                symbols=stock_codes,
                start_date=start_date,
                end_date=end_date,
                fields=fields or ['pe', 'pb', 'roe', 'roa']
            )
        except Exception as e:
            self.logger.error(f"获取基本面数据失败: {e}")
            return pd.DataFrame()
    
    def create_factor_data(self,
                          stock_codes: List[str],
                          start_date: str,
                          end_date: str,
                          include_fundamental: bool = True) -> FactorData:
        """
        创建统一的因子数据结构
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            include_fundamental: 是否包含基本面数据
            
        Returns:
            因子数据结构
        """
        try:
            # 获取价格数据
            price_data = self.get_stock_data(stock_codes, start_date, end_date)
            
            # 获取成交量数据
            volume_data = None
            if not price_data.empty and 'volume' in price_data.columns:
                volume_data = price_data[['volume']]
            
            # 获取基本面数据
            fundamental_data = None
            if include_fundamental:
                fundamental_data = self.get_fundamental_data(stock_codes, start_date, end_date)
            
            # 获取市场数据
            market_data = self.get_market_data(start_date, end_date)
            
            # 计算收益率
            returns = None
            if not price_data.empty and 'close' in price_data.columns:
                returns = price_data['close'].pct_change()
            
            return FactorData(
                price_data=price_data,
                volume_data=volume_data,
                fundamental_data=fundamental_data,
                market_data=market_data,
                returns=returns,
                metadata={
                    'start_date': start_date,
                    'end_date': end_date,
                    'stock_codes': stock_codes,
                    'created_time': datetime.now().isoformat()
                }
            )
        except Exception as e:
            self.logger.error(f"创建因子数据失败: {e}")
            # 返回空的因子数据结构
            return FactorData(
                price_data=pd.DataFrame(),
                metadata={'error': str(e)}
            )


class ConfigManager:
    """
    配置管理器
    
    统一管理各种配置文件和参数
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "./config"
        self.configs = {}
        self.logger = self._setup_logger()
        self._load_configs()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.ConfigManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _load_configs(self):
        """加载配置文件"""
        config_dir = Path(self.config_path)
        if not config_dir.exists():
            self.logger.warning(f"配置目录不存在: {config_dir}")
            return
        
        for config_file in config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_name = config_file.stem
                    self.configs[config_name] = json.load(f)
                    self.logger.info(f"已加载配置文件: {config_name}")
            except Exception as e:
                self.logger.error(f"加载配置文件失败 {config_file}: {e}")
    
    def get_config(self, config_name: str, default: Any = None) -> Any:
        """获取配置"""
        return self.configs.get(config_name, default)
    
    def set_config(self, config_name: str, config_value: Any):
        """设置配置"""
        self.configs[config_name] = config_value
    
    def save_config(self, config_name: str, config_value: Any, file_path: Optional[str] = None):
        """保存配置到文件"""
        if file_path is None:
            file_path = os.path.join(self.config_path, f"{config_name}.json")
        
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_value, f, ensure_ascii=False, indent=2)
            self.configs[config_name] = config_value
            self.logger.info(f"已保存配置: {config_name}")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def create_mining_config(self, **kwargs) -> MiningConfig:
        """创建挖掘配置"""
        default_config = self.get_config('mining_default', {})
        default_config.update(kwargs)
        return MiningConfig.from_dict(default_config)


class FactorUtils:
    """
    因子工具函数集合
    
    提供常用的因子计算和处理函数
    """
    
    @staticmethod
    def calculate_returns(prices: pd.DataFrame, periods: int = 1) -> pd.DataFrame:
        """
        计算收益率
        
        Args:
            prices: 价格数据
            periods: 周期数
            
        Returns:
            收益率数据
        """
        return prices.pct_change(periods=periods)
    
    @staticmethod
    def calculate_rolling_stats(data: pd.DataFrame, window: int, func: str) -> pd.DataFrame:
        """
        计算滚动统计量
        
        Args:
            data: 输入数据
            window: 窗口大小
            func: 统计函数名称
            
        Returns:
            滚动统计量
        """
        if func == 'mean':
            return data.rolling(window=window).mean()
        elif func == 'std':
            return data.rolling(window=window).std()
        elif func == 'max':
            return data.rolling(window=window).max()
        elif func == 'min':
            return data.rolling(window=window).min()
        elif func == 'sum':
            return data.rolling(window=window).sum()
        else:
            raise ValueError(f"不支持的统计函数: {func}")
    
    @staticmethod
    def winsorize(data: pd.DataFrame, limits: Tuple[float, float] = (0.01, 0.01)) -> pd.DataFrame:
        """
        缩尾处理
        
        Args:
            data: 输入数据
            limits: 缩尾比例 (下限, 上限)
            
        Returns:
            缩尾后的数据
        """
        from scipy.stats import mstats
        
        result = data.copy()
        for col in data.columns:
            if data[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                result[col] = mstats.winsorize(data[col].dropna(), limits=limits)
        return result
    
    @staticmethod
    def standardize(data: pd.DataFrame, method: str = 'zscore') -> pd.DataFrame:
        """
        标准化处理
        
        Args:
            data: 输入数据
            method: 标准化方法('zscore', 'minmax', 'robust')
            
        Returns:
            标准化后的数据
        """
        if method == 'zscore':
            return (data - data.mean()) / data.std()
        elif method == 'minmax':
            return (data - data.min()) / (data.max() - data.min())
        elif method == 'robust':
            median = data.median()
            mad = (data - median).abs().median()
            return (data - median) / mad
        else:
            raise ValueError(f"不支持的标准化方法: {method}")
    
    @staticmethod
    def calculate_ic(factor: pd.Series, returns: pd.Series) -> float:
        """
        计算信息系数(IC)
        
        Args:
            factor: 因子值
            returns: 收益率
            
        Returns:
            IC值
        """
        # 对齐数据
        aligned_data = pd.concat([factor, returns], axis=1).dropna()
        if len(aligned_data) < 2:
            return np.nan
        
        return aligned_data.iloc[:, 0].corr(aligned_data.iloc[:, 1])
    
    @staticmethod
    def calculate_rank_ic(factor: pd.Series, returns: pd.Series) -> float:
        """
        计算排序信息系数(Rank IC)
        
        Args:
            factor: 因子值
            returns: 收益率
            
        Returns:
            Rank IC值
        """
        # 对齐数据
        aligned_data = pd.concat([factor, returns], axis=1).dropna()
        if len(aligned_data) < 2:
            return np.nan
        
        factor_rank = aligned_data.iloc[:, 0].rank()
        returns_rank = aligned_data.iloc[:, 1].rank()
        
        return factor_rank.corr(returns_rank)


# 全局适配器管理器实例
_global_adapter_manager = None
_global_data_pipeline = None

def get_adapter_manager():
    """
    获取全局适配器管理器实例
    
    Returns:
        DataAdapterManager: 适配器管理器实例
    """
    global _global_adapter_manager
    if _global_adapter_manager is None:
        _global_adapter_manager = DataAdapterManager()
    return _global_adapter_manager

def get_adapter(adapter_type: str = 'stock'):
    """
    获取指定类型的数据适配器
    
    Args:
        adapter_type: 适配器类型 ('stock', 'market', 'fundamental', 'factor')
        
    Returns:
        对应的数据适配器实例
    """
    manager = get_adapter_manager()
    if adapter_type in manager.adapters:
        return manager.adapters[adapter_type]
    else:
        logging.warning(f"适配器类型 {adapter_type} 不可用")
        return None

def get_data_pipeline():
    """
    获取数据管道实例
    
    Returns:
        DataAdapterManager: 数据管道实例
    """
    global _global_data_pipeline
    if _global_data_pipeline is None:
        _global_data_pipeline = DataAdapterManager()
    return _global_data_pipeline

def create_mock_data_pipeline():
    """
    创建模拟数据管道
    
    Returns:
        DataAdapterManager: 模拟数据管道实例
    """
    return DataAdapterManager()

def get_enhanced_qlib_pipeline():
    """
    获取增强的Qlib数据管道
    
    Returns:
        DataAdapterManager: 增强的Qlib数据管道实例
    """
    # 创建增强配置的数据管道
    enhanced_config = {
        'qlib_enabled': True,
        'enhanced_features': True,
        'cache_enabled': True
    }
    return DataAdapterManager(enhanced_config)

class ValidationUtils:
    """
    验证工具函数集合
    
    提供数据验证和质量检查功能
    """
    
    @staticmethod
    def check_data_quality(data: pd.DataFrame) -> Dict[str, Any]:
        """
        检查数据质量
        
        Args:
            data: 输入数据
            
        Returns:
            质量检查结果
        """
        result = {
            'total_rows': len(data),
            'total_columns': len(data.columns),
            'missing_ratio': data.isnull().sum().sum() / (len(data) * len(data.columns)),
            'duplicate_rows': data.duplicated().sum(),
            'numeric_columns': len(data.select_dtypes(include=[np.number]).columns),
            'datetime_columns': len(data.select_dtypes(include=['datetime64']).columns),
            'object_columns': len(data.select_dtypes(include=['object']).columns)
        }
        
        # 检查异常值
        numeric_data = data.select_dtypes(include=[np.number])
        if not numeric_data.empty:
            Q1 = numeric_data.quantile(0.25)
            Q3 = numeric_data.quantile(0.75)
            IQR = Q3 - Q1
            outliers = ((numeric_data < (Q1 - 1.5 * IQR)) | (numeric_data > (Q3 + 1.5 * IQR))).sum().sum()
            result['outliers'] = outliers
            result['outlier_ratio'] = outliers / (len(numeric_data) * len(numeric_data.columns))
        
        return result
    
    @staticmethod
    def validate_factor_data(factor_data: FactorData) -> Dict[str, Any]:
        """
        验证因子数据结构
        
        Args:
            factor_data: 因子数据
            
        Returns:
            验证结果
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查必需的价格数据
        if factor_data.price_data is None or factor_data.price_data.empty:
            result['is_valid'] = False
            result['errors'].append("价格数据为空")
        
        # 检查日期索引
        if factor_data.price_data is not None and not isinstance(factor_data.price_data.index, pd.DatetimeIndex):
            result['warnings'].append("价格数据索引不是日期时间格式")
        
        # 检查数据一致性
        if factor_data.returns is not None and factor_data.price_data is not None:
            if len(factor_data.returns) != len(factor_data.price_data):
                result['warnings'].append("收益率数据与价格数据长度不一致")
        
        return result
    
    @staticmethod
    def check_factor_validity(factor: pd.Series, min_coverage: float = 0.8) -> bool:
        """
        检查因子有效性
        
        Args:
            factor: 因子数据
            min_coverage: 最小覆盖率
            
        Returns:
            是否有效
        """
        if factor.empty:
            return False
        
        # 检查覆盖率
        coverage = 1 - factor.isnull().sum() / len(factor)
        if coverage < min_coverage:
            return False
        
        # 检查是否全为常数
        if factor.nunique() <= 1:
            return False
        
        # 检查是否包含无穷大值
        if np.isinf(factor).any():
            return False
        
        return True
