# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试月度加权特质波动率因子的权重计算
验证 ωk = 0.9^k 指数衰减权重的正确实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from factor_analyze.fundamental_factors.monthly_idiosyncratic_volatility_factor import StockMonthlyIdiosyncraticVolatilityFactor

def test_exponential_weight_calculation():
    """
    测试指数衰减权重计算是否符合 ωk = 0.9^k 公式
    """
    print("=== 测试月度加权特质波动率因子的权重计算 ===")
    
    # 创建因子实例
    factor = StockMonthlyIdiosyncraticVolatilityFactor()
    
    # 创建测试数据
    test_data = pd.Series([0.1, 0.2, 0.15, 0.25, 0.18])  # 5个波动率值
    
    print(f"\n测试数据: {test_data.values}")
    print(f"数据长度: {len(test_data)}")
    
    # 手动计算期望的权重（ωk = 0.9^k）
    n = len(test_data)
    expected_weights = np.array([0.9**k for k in range(n)])
    # 反转权重数组，使得最新数据（索引最大）对应k=0
    expected_weights = expected_weights[::-1]
    # 归一化权重
    expected_weights = expected_weights / expected_weights.sum()
    
    print(f"\n期望权重 (ωk = 0.9^k):")
    for i, w in enumerate(expected_weights):
        k = n - 1 - i  # 对应的k值
        print(f"  数据[{i}] (k={k}): 权重 = {w:.6f}")
    
    # 手动计算期望的加权平均值
    expected_result = np.average(test_data, weights=expected_weights)
    print(f"\n期望的加权平均值: {expected_result:.6f}")
    
    # 使用因子方法计算
    actual_result = factor._calculate_weighted_volatility(test_data, 'exponential')
    print(f"实际计算结果: {actual_result:.6f}")
    
    # 验证结果
    diff = abs(expected_result - actual_result)
    print(f"\n差异: {diff:.10f}")
    
    if diff < 1e-10:
        print("权重计算正确！完全符合ωk = 0.9^k 公式")
    else:
        print("权重计算有误")
    
    # 验证权重衰减特性
    print("\n=== 验证权重衰减特性 ===")
    print("最新数据权重最高，随时间衰减")
    for i in range(len(expected_weights)-1):
        ratio = expected_weights[i+1] / expected_weights[i]
        print(f"  权重[{i+1}] / 权重[{i}] = {ratio:.6f} (应该约等于0.9)")
    
    return diff < 1e-10

def test_different_data_lengths():
    """
    测试不同数据长度下的权重计算
    """
    print("\n=== 测试不同数据长度 ===")
    
    factor = StockMonthlyIdiosyncraticVolatilityFactor()
    
    for length in [3, 5, 10]:
        print(f"\n数据长度: {length}")
        test_data = pd.Series(np.random.rand(length))
        
        # 计算权重
        weights = np.array([0.9**k for k in range(length)])[::-1]
        weights = weights / weights.sum()
        
        print(f"权重分布: {weights}")
        print(f"权重和: {weights.sum():.6f}")
        print(f"最大权重位置: {np.argmax(weights)} (应该是最新数据位置{length-1})")
        
        # 验证最新数据权重最高
        assert np.argmax(weights) == length - 1, "最新数据权重应该最高"
        
        # 计算加权结果
        result = factor._calculate_weighted_volatility(test_data, 'exponential')
        print(f"加权结果: {result:.6f}")

if __name__ == "__main__":
    try:
        # 测试权重计算
        success = test_exponential_weight_calculation()
        
        # 测试不同数据长度
        test_different_data_lengths()
        
        if success:
            print("\n🎉 所有测试通过！权重计算完全符合ωk = 0.9^k 公式要求")
        else:
            print("\n⚠️  测试失败，需要检查权重计算实现")
            
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
