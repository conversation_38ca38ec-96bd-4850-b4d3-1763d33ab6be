# -*- coding: utf-8 -*-\nfrom __future__ import annotations

"""
Base utilities and common parent class for all sentiment-related factor implementations in this directory.
By inheriting from :class:`SentimentFactorBase`, concrete factor classes get:
1. A unified simple logger (stdout based, preventing duplicated definitions).
2. Automatic default-config merging – subclasses only need to implement
   ``_get_default_config`` and call ``super().__init__(config)`` in their ``__init__``.
3. Fallback shims for `BaseFactorMiner` / `FactorData` when the optional
   `factor_core` package is unavailable (unit tests or lightweight usage).
4. Common data adapter initialization and management.
5. Shared utility methods for common sentiment calculations.

This significantly reduces duplicated boiler-plate across each factor file.
"""

from typing import Optional, Dict, Any, List, Tuple, Union
import sys
import os
import warnings
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

__all__ = [
    "logger",
    "BaseFactorMiner",
    "FactorData",
    "Sentiment<PERSON><PERSON>Base",
    "SentimentResult",
]

# ---------------------------------------------------------------------------
# Lightweight stdout logger (fallback if project-level logger unavailable)
# ---------------------------------------------------------------------------
class _SimpleLogger:
    def info(self, msg):
        print(f"[INFO] {msg}")

    def warning(self, msg):
        print(f"[WARNING] {msg}")

    def error(self, msg):
        print(f"[ERROR] {msg}")

    def debug(self, msg):
        print(f"[DEBUG] {msg}")


logger = _SimpleLogger()

# ---------------------------------------------------------------------------
# Try to import canonical interfaces from factor_analyze.factor_core; provide shims otherwise
# ---------------------------------------------------------------------------
try:
    from factor_analyze.factor_core.base_interfaces import BaseFactorMiner as _CoreBaseFactorMiner
    from factor_analyze.factor_core.common_types import FactorData as _CoreFactorData

    BaseFactorMiner = _CoreBaseFactorMiner  # type: ignore
    FactorData = _CoreFactorData  # type: ignore
except ImportError:  # fallback stubs

    class BaseFactorMiner:  # pylint: disable=too-few-public-methods
        """Very small stub so other modules can run without `factor_core`."""

        def __init__(self, config: Optional[Dict[str, Any]] = None):
            self.config: Dict[str, Any] = config or {}

    class FactorData:  # pylint: disable=too-few-public-methods
        def __init__(self, data):
            self.data = data

    logger.warning("factor_core not available – using lightweight stubs for BaseFactorMiner / FactorData")

# ---------------------------------------------------------------------------
# Common data structures
# ---------------------------------------------------------------------------
@dataclass
class SentimentResult:
    """通用情绪分析结果数据类"""
    factor_name: str
    stock_code: str
    timestamp: str
    sentiment_score: float
    confidence_score: float
    signal: int  # -1, 0, 1
    metadata: Dict[str, Any]

# ---------------------------------------------------------------------------
# Try to import data adapters; provide mock implementations otherwise
# ---------------------------------------------------------------------------
try:
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.index_data_adapter import IndexDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    _ADAPTERS_AVAILABLE = True
except ImportError:
    logger.warning("Data adapters not available – using mock implementations")
    _ADAPTERS_AVAILABLE = False
    
    from adapters import StockDataAdapter
    
    class IndexDataAdapter:
        def get_index_data(self, *args, **kwargs):
            logger.warning("Using mock IndexDataAdapter")
            return pd.DataFrame()
    
    from adapters import MarketDataAdapter
    
    from adapters import FundamentalDataAdapter

# ---------------------------------------------------------------------------
# Shared parent class
# ---------------------------------------------------------------------------
class SentimentFactorBase(BaseFactorMiner):
    """Common functionality for sentiment-related factor miners.

    Sub-classes **must** implement ``_get_default_config`` that returns a
    dictionary of default parameters.  Inside their ``__init__`` they should
    call ``super().__init__(config)`` *before* performing any custom
    initialization so that ``self.config`` is ready for use.
    """

    def __init__(self, user_config: Optional[Dict[str, Any]] = None):
        # Allow subclass to define defaults lazily
        default_cfg = {}
        if hasattr(self, "_get_default_config") and callable(getattr(self, "_get_default_config")):
            default_cfg = getattr(self, "_get_default_config")() or {}

        merged_cfg = default_cfg.copy()
        if user_config:
            merged_cfg.update(user_config)

        super().__init__(merged_cfg)  # type: ignore[misc]

        # Initialize data adapters
        self._init_adapters()

        # Suppress warnings
        warnings.filterwarnings('ignore')

        # Log only once per subclass instance
        logger.info(f"{self.__class__.__name__} 初始化完成，配置: {self.config}")

    def _init_adapters(self):
        """Initialize data adapters"""
        if _ADAPTERS_AVAILABLE:
            self.stock_adapter = StockDataAdapter()
            self.index_adapter = IndexDataAdapter()
            self.market_adapter = MarketDataAdapter()
            self.fundamental_adapter = FundamentalDataAdapter()
        else:
            self.stock_adapter = StockDataAdapter()
            self.index_adapter = IndexDataAdapter()
            self.market_adapter = MarketDataAdapter()
            self.fundamental_adapter = FundamentalDataAdapter()

    # ---------------------------------------------------------------------
    # Implement abstract methods from BaseFactorMiner (if any)
    # ---------------------------------------------------------------------
    def _setup_logger(self):
        """Return the shared logger."""
        return logger

    def generate_factor_combinations(self, *args, **kwargs):
        """Default implementation for factor combinations."""
        return []

    def mine_factors(self, *args, **kwargs):
        """Default implementation for factor mining."""
        return []

    # ---------------------------------------------------------------------
    # Common utility methods for sentiment factors
    # ---------------------------------------------------------------------
    def _get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取股票数据的通用方法
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            股票数据DataFrame
        """
        try:
            df = self.stock_adapter.get_daily(
                ts_code=stock_code,
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', '')
            )
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date')
                df['return'] = df['close'].pct_change()
                return df.set_index('trade_date')
        except Exception as e:
            logger.error(f"获取股票{stock_code}数据失败: {e}")
        
        # 返回模拟数据
        return self._generate_mock_stock_data(stock_code, start_date, end_date)

    def _get_index_data(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取指数数据的通用方法
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            指数数据DataFrame
        """
        try:
            df = self.index_adapter.get_index_data(
                index_code=index_code,
                start_date=start_date,
                end_date=end_date
            )
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date')
                df['return'] = df['close'].pct_change()
                return df.set_index('trade_date')
        except Exception as e:
            logger.error(f"获取指数{index_code}数据失败: {e}")
        
        # 返回模拟数据
        return self._generate_mock_index_data(index_code, start_date, end_date)

    def _generate_mock_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟股票数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        np.random.seed(hash(stock_code) % 1000)
        
        n_days = len(dates)
        base_price = 100
        
        # 生成价格序列
        returns = np.random.normal(0.001, 0.02, n_days)
        prices = base_price * np.exp(np.cumsum(returns))
        
        # 生成成交量
        volumes = np.random.lognormal(15, 1, n_days)
        
        df = pd.DataFrame({
            'open': prices * np.random.uniform(0.98, 1.02, n_days),
            'high': prices * np.random.uniform(1.00, 1.05, n_days),
            'low': prices * np.random.uniform(0.95, 1.00, n_days),
            'close': prices,
            'volume': volumes,
            'return': returns
        }, index=dates)
        
        return df

    def _generate_mock_index_data(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟指数数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        np.random.seed(hash(index_code) % 1000)
        
        n_days = len(dates)
        base_price = 3000
        
        # 生成价格序列
        returns = np.random.normal(0.0005, 0.015, n_days)
        prices = base_price * np.exp(np.cumsum(returns))
        
        df = pd.DataFrame({
            'open': prices * np.random.uniform(0.995, 1.005, n_days),
            'high': prices * np.random.uniform(1.000, 1.02, n_days),
            'low': prices * np.random.uniform(0.98, 1.000, n_days),
            'close': prices,
            'volume': np.random.lognormal(20, 0.5, n_days),
            'return': returns
        }, index=dates)
        
        return df

    def _calculate_rolling_stats(self, data: pd.Series, window: int = 20) -> Dict[str, pd.Series]:
        """计算滚动统计量"""
        return {
            'mean': data.rolling(window=window).mean(),
            'std': data.rolling(window=window).std(),
            'min': data.rolling(window=window).min(),
            'max': data.rolling(window=window).max(),
            'skew': data.rolling(window=window).skew(),
            'kurt': data.rolling(window=window).kurt()
        }

    def _calculate_zscore(self, data: pd.Series, window: int = 20) -> pd.Series:
        """计算Z-score"""
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        return (data - rolling_mean) / (rolling_std + 1e-8)

    def _calculate_percentile_rank(self, data: pd.Series, window: int = 20) -> pd.Series:
        """计算百分位排名"""
        return data.rolling(window=window).rank(pct=True)

    def _standardize_factor(self, factor: pd.Series, method: str = 'zscore') -> pd.Series:
        """标准化因子值"""
        if method == 'zscore':
            return (factor - factor.mean()) / (factor.std() + 1e-8)
        elif method == 'minmax':
            return (factor - factor.min()) / (factor.max() - factor.min() + 1e-8)
        elif method == 'rank':
            return factor.rank(pct=True)
        else:
            return factor

    def _generate_signal(self, factor: pd.Series, 
                        upper_threshold: float = 0.7, 
                        lower_threshold: float = -0.7) -> pd.Series:
        """生成交易信号"""
        signals = pd.Series(0, index=factor.index)
        signals[factor > upper_threshold] = 1
        signals[factor < lower_threshold] = -1
        return signals

    def _calculate_abnormal_return(self, stock_return: pd.Series, 
                                  market_return: pd.Series) -> pd.Series:
        """计算异常收益"""
        return stock_return - market_return

    def _calculate_cumulative_abnormal_return(self, abnormal_returns: pd.Series, 
                                            window: int = 5) -> pd.Series:
        """计算累计异常收益"""
        return abnormal_returns.rolling(window=window).sum()

    # ---------------------------------------------------------------------
    # Abstract methods that subclasses should implement
    # ---------------------------------------------------------------------
    def _get_factor_name(self) -> str:
        """子类必须实现：返回因子名称"""
        raise NotImplementedError("Subclasses must implement _get_factor_name")

    def _get_factor_description(self) -> str:
        """子类必须实现：返回因子描述"""
        raise NotImplementedError("Subclasses must implement _get_factor_description")

    def _get_factor_category(self) -> str:
        """子类必须实现：返回因子类别"""
        return "sentiment"

    def calculate_factor(self, data: pd.DataFrame) -> pd.DataFrame:
        """子类必须实现：计算因子值"""
        raise NotImplementedError("Subclasses must implement calculate_factor")

    # ---------------------------------------------------------------------
    # Common interface methods
    # ---------------------------------------------------------------------
    def get_factor_info(self) -> Dict[str, Any]:
        """获取因子信息"""
        return {
            'name': self._get_factor_name(),
            'description': self._get_factor_description(),
            'category': self._get_factor_category(),
            'class_name': self.__class__.__name__,
            'config': self.config
        }

    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        if data.empty:
            logger.warning("输入数据为空")
            return False
        
        required_columns = ['close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            logger.warning(f"缺少必要的列: {missing_columns}")
            return False
        
        return True

    def batch_calculate(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """批量计算多只股票的因子值"""
        results = {}
        for stock_code, data in data_dict.items():
            try:
                if self.validate_data(data):
                    result = self.calculate_factor(data)
                    results[stock_code] = result
                else:
                    logger.warning(f"股票 {stock_code} 数据验证失败")
            except Exception as e:
                logger.error(f"计算股票 {stock_code} 因子时出错: {e}")
        
        return results

    # ---------------------------------------------------------------------
    # Placeholder hooks – subclasses can override
    # ---------------------------------------------------------------------
    def _apply_quality_filters(self, data: pd.DataFrame) -> pd.DataFrame:
        """应用数据质量过滤器 – 子类可以重写"""
        return data

    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理异常值 – 子类可以重写"""
        return data

    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理数据 – 子类可以重写"""
        data = self._apply_quality_filters(data)
        data = self._handle_outliers(data)
        return data 