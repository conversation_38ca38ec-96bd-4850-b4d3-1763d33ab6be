#!/usr/bin/env python
# coding: utf-8

# # ä»æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC è§’åº¦çœ‹å› å­æƒé‡?
# https://www.joinquant.com/view/community/detail/fecea85cbf6c35554ccb055225a0eee5?type=1

# #  å¼•è¨€

# <b>ç ”ç©¶ç›®çš„ï¼?/b>  
# 
# æœ¬æ–‡å‚è€ƒå¹¿å‘è¯åˆ¸ç ”æŠ¥ã€Šä»æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC è§’åº¦çœ‹å› å­æƒé‡ã€‹ï¼Œæ ¹æ®ç ”æŠ¥åˆ†æï¼Œç°é˜¶æ®µåº”ç”¨è¾ƒå¤šçš„å› å­åŠ æƒæ–¹æ³•ä¸»è¦æœ‰ä»¥ä¸‹å‡ ç§: ç­‰æƒåŠ æƒã€?IC åŠ æƒå’?IC_IR åŠ æƒã€ä»¥åŠæœ€ä¼˜åŒ– IC_IR åŠ æƒã€‚å…¶ä¸­ï¼Œç­‰æƒåŠ æƒæ˜¯å› å­åŠ æƒæœ€ä¼ ç»Ÿçš„æ–¹æ³•ï¼Œè¿™ç§æ–¹æ³•å—å› å­ä¹‹é—´æœ‰æ•ˆæ€§å·®å¼‚ã€çº¿æ€§ç›¸å…³æ€§å½±å“æ˜æ˜¾ã€‚è€?IC åŠ æƒã€?IC_IR åŠ æƒå¯¹ç­‰æƒæ–¹å¼å¿½è§†äº†å› å­æœ‰æ•ˆæ€§å·®å¼‚çš„é—®é¢˜è¿›è¡Œäº†æ”¹è¿›ï¼Œåœ¨å¤§éƒ¨åˆ†æƒ…å†µä¸‹ä¼šä¼˜äºç­‰æƒåŠ æƒå½¢å¼ã€‚æœ€å¤§åŒ–å¤åˆå› å­ IC_IR åŠ æƒå·²è¿ç”¨è¾ƒå¹¿ã€?
# 
# <b>ç ”ç©¶å†…å®¹ï¼?/b>  
# 
# ï¼?ï¼‰ä¼ ç»Ÿå› å­åŠ æƒæ–¹å¼çš„å±€é™æ€? é€‰æ‹© ZZ800 ä¸ºè‚¡ç¥¨æ± ï¼Œä»¥å¸‚å€¼å› å­å’Œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ä¸ºä¾‹ï¼Œåˆ†æç­‰æƒåŠ æƒä¸?IC åŠ æƒçš„å·®å¼‚ï¼Œæ ¹æ®å›æµ‹ç»“æœåˆ†æä¸¤ç§å› å­åŠ æƒæ–¹å¼çš„æ•ˆç”¨ï¼›  
# ï¼?ï¼‰è®¾è®¡æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„ç†è®ºæœ€ä¼˜æ¯”ä¾? æœ¬æ–‡æ²¿ç”¨ Qian çš„æœ€ä¼˜åŒ–ä½“ç³»è·å–å› å­æƒé‡ï¼Œä¸ä¹‹ä¸åŒçš„æ˜¯ï¼Œæˆ‘ä»¬å°†ä¼˜åŒ–ç›®æ ‡ç”±æœ€å¤§åŒ–å¤åˆå› å­ IC_IR å˜ä¸ºæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ ICã€‚ç†è®ºè§£æè§£çš„å½¢å¼è¡¨æ˜ï¼Œæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„æƒé‡ä¸ä¸¤æ–¹é¢å› ç´ æœ‰å…? ä¸€æ˜¯å› å­çš„æœ‰æ•ˆæ€§ï¼Œå³å› å­?IC; äºŒæ˜¯å› å­ä¹‹é—´çš„ç›¸å…³ç³»æ•°ã€?
# ï¼?ï¼‰æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„åº”ç”? æœ¬æ–‡é€šè¿‡ä¾‹å­å®è¯ç ”ç©¶å‘ç°ï¼Œæœ€å¤§åŒ–å•æœŸ IC èƒ½æœ‰æ•ˆè§£å†³â€œç­‰æƒâ€çš„é…ç½®åå·®é—®é¢˜ï¼Œåœ¨ç»å¤§éƒ¨åˆ†å› å­ç©ºé—´ï¼Œæœ€ä¼?IC åŠ æƒ æ‰€æ„å»ºçš„ç»„åˆï¼Œå…¶è¡¨ç°å‡ä¼˜äºæŒ‰ç…§â€œç­‰æƒâ€æ–¹å¼æ‰€æ„å»ºçš„ç»„åˆã€?
# 
# <b>ç ”ç©¶ç»“è®ºï¼?/b>  
# 
# ï¼?ï¼‰é€šè¿‡å¯¹å¸‚å€¼å› å­ä¸è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ä¸ºä¾‹è¿›è¡Œåˆ†æï¼ŒIC åŠ æƒå¯¹ç­‰æƒæ–¹å¼å¿½è§†äº†å› å­æœ‰æ•ˆæ€§å·®å¼‚çš„é—®é¢˜è¿›è¡Œäº†æ”¹è¿›ï¼Œåœ¨å¤§éƒ¨åˆ†æƒ…å†µä¸‹ä¼šä¼˜äºç­‰æƒåŠ æƒå½¢å¼ã€?
# ï¼?ï¼‰æœ¬æ–‡æ²¿ç”?Qian çš„æœ€ä¼˜åŒ–ä½“ç³»è·å–å› å­æƒé‡ï¼Œä¸ä¹‹ä¸åŒçš„æ˜¯ï¼Œæˆ‘ä»¬å°†ä¼˜åŒ–ç›®æ ‡ç”±æœ€å¤§åŒ–å¤åˆå› å­ IR å˜ä¸ºæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ ICï¼Œå¹¶æ ¹æ®è¯¥æ–¹æ³•è¿›è¡Œå› å­æƒé‡çš„è®¡ç®—ã€‚ç†è®ºè§£æè§£çš„å½¢å¼è¡¨æ˜ï¼Œæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„æƒé‡ä¸ä¸¤æ–¹é¢å› ç´ æœ‰å…? ä¸€æ˜¯å› å­çš„æœ‰æ•ˆæ€§ï¼Œå³å› å­?IC; äºŒæ˜¯å› å­ä¹‹é—´çš„ç›¸å…³ç³»æ•°ã€? 
# ï¼?ï¼‰é€šè¿‡ä»¥ä¸‹ 7 ä¸ªå› å­? å¸‚ç›ˆç?PB)ã€å¸‚å‡€ç?PE)ã€å¸‚é”€ç?PS)ã€è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ã€èµ„äº§è´Ÿå€ºç‡ã€åè½?å‰?1 æœˆç´¯è®¡æ”¶ç›?ã€æ¢æ‰‹ç‡(å‰?10 ä¸ªäº¤æ˜“æ—¥æ—¥å‡æ¢æ‰‹ç?ï¼Œè¿›è¡Œä¸åŒå› å­åŠ æƒæ–¹æ³•çš„æµ‹è¯•ã€‚æ–‡ç« å®è¯ç»“æœä¹Ÿè¡¨æ˜ï¼Œæœ€å¤§åŒ–å•æœŸ IC èƒ½æœ‰æ•ˆè§£å†³â€œç­‰æƒâ€çš„é…ç½®åå·®é—®é¢˜ï¼Œåœ¨ç»å¤§éƒ¨åˆ†æƒ…å†µï¼Œæœ€ä¼?IC åŠ æƒæ‰€æ„å»ºçš„ç»„åˆï¼Œå…¶è¡¨ç°å‡ä¼˜äºâ€œç­‰æƒâ€æ–¹å¼?æ‰€æ„å»ºçš„ç»„åˆï¼Œæœ€å¤§åŒ–å•æœŸ IC èƒ½å¤Ÿè·å¾—æœ€ä½³çš„ç»“æœã€?
# 

# # 1 å› å­ç­‰æƒåŠ æƒ

# ## 1.1 æ—¥æœŸåˆ—è¡¨è·å–

# åœ¨æ¯ä¸ªæœˆçš„æœˆæœ«å¯¹å› å­æ•°æ®è¿›è¡Œæå–ï¼Œå› æ­¤éœ€è¦å¯¹æ¯ä¸ªæœˆçš„æœˆæœ«æ—¥æœŸè¿›è¡Œç»Ÿè®¡ã€? 
# è¾“å…¥å‚æ•°åˆ†åˆ«ä¸?peroidã€start_date å’?end_dateï¼Œå…¶ä¸?peroid è¿›è¡Œå‘¨æœŸé€‰æ‹©ï¼Œå¯é€‰å‘¨æœŸä¸ºå‘?W)ã€æœˆ(M)å’Œå­£(Q)ï¼Œstart_dateå’Œend_date åˆ†åˆ«ä¸ºå¼€å§‹æ—¥æœŸå’Œç»“æŸæ—¥æœŸã€? 
# å‡½æ•°è¿”å›å€¼ä¸ºå¯¹åº”çš„æœˆæœ«æ—¥æœŸï¼Œå¦‚é€‰å–å¼€å§‹æ—¥æœŸä¸º 2017.1.1ï¼Œç»“æŸæ—¥æœŸä¸º 2018.1.1ã€?

# In[1]:


from jqdata import *
import datetime
import pandas as pd
import numpy as np
from six import StringIO
import warnings
import time
import pickle
from jqfactor import standardlize
from jqfactor import winsorize_med
from jqfactor import neutralize
import matplotlib.pyplot as plt
import scipy.stats as st
warnings.filterwarnings("ignore")
plt.rcParams['axes.unicode_minus']=False


# In[2]:


pd.__version__


# In[3]:


#è·å–æŒ‡å®šå‘¨æœŸçš„æ—¥æœŸåˆ—è¡?'Wã€Mã€Q'
def get_period_date(peroid,start_date, end_date):
    #è®¾å®šè½¬æ¢å‘¨æœŸperiod_type  è½¬æ¢ä¸ºå‘¨æ˜?W',æœ?M',å­£åº¦çº?Q',äº”åˆ†é’?5min',12å¤?12D'
    stock_data = get_price('000001.XSHE',start_date,end_date,'daily',fields=['close'])
    #è®°å½•æ¯ä¸ªå‘¨æœŸä¸­æœ€åä¸€ä¸ªäº¤æ˜“æ—¥
    stock_data['date']=stock_data.index
    #è¿›è¡Œè½¬æ¢ï¼Œå‘¨çº¿çš„æ¯ä¸ªå˜é‡éƒ½ç­‰äºé‚£ä¸€å‘¨ä¸­æœ€åä¸€ä¸ªäº¤æ˜“æ—¥çš„å˜é‡å€?
#     period_stock_data=stock_data.resample(peroid,how='last')
    period_stock_data=stock_data.resample(peroid).last()
    date=period_stock_data.index
    pydate_array = date.to_pydatetime()
    date_only_array = np.vectorize(lambda s: s.strftime('%Y-%m-%d'))(pydate_array )
    date_only_series = pd.Series(date_only_array)
    start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
    start_date=start_date-datetime.timedelta(days=1)
    start_date = start_date.strftime("%Y-%m-%d")
    date_list=date_only_series.values.tolist()
    date_list.insert(0,start_date)
    return date_list
get_period_date('M','2018-01-01', '2019-01-01')


# ## 1.2 è‚¡ç¥¨åˆ—è¡¨è·å–

# è‚¡ç¥¨æ±? ZZ800  
# è‚¡ç¥¨ç­›é€? å‰”é™¤ ST è‚¡ç¥¨ï¼Œå‰”é™¤ä¸Šå¸?3 ä¸ªæœˆå†…çš„è‚¡ç¥¨ï¼Œæ¯åªè‚¡ç¥¨è§†ä½œä¸€ä¸ªæ ·æœ? 
# ä»?ZZ800 ä¸ºä¾‹ï¼Œå– 2016-08-31 å½“å¤©çš„è‚¡ç¥¨æˆåˆ†è‚¡

# In[4]:


#å»é™¤ä¸Šå¸‚è·beginDateä¸è¶³3ä¸ªæœˆçš„è‚¡ç¥?
def delect_stop(stocks,beginDate,n=30*3):
    stockList = []
    beginDate = datetime.datetime.strptime(beginDate, "%Y-%m-%d")
    for stock in stocks:
        start_date = get_security_info(stock).start_date
        if start_date < (beginDate-datetime.timedelta(days = n)).date():
            stockList.append(stock)
    return stockList

#è·å–è‚¡ç¥¨æ±?
def get_stock_ZZ800(begin_date):
    begin_date = str(begin_date)
    stockList = get_index_stocks('000906.XSHG')
    #å‰”é™¤STè‚?
    st_data = get_extras('is_st', stockList, count = 1, end_date=begin_date)
    stockList = [stock for stock in stockList if not st_data[stock][0]]
    #å‰”é™¤åœç‰Œã€æ–°è‚¡åŠé€€å¸‚è‚¡ç¥?
    stockList = delect_stop(stockList, begin_date)
    return stockList
get_stock_ZZ800('2016-08-31')


# ## 1.3 æ•°æ®è·å–

# æœ¬ç« æ—¨åœ¨åˆ†æå› å­ç­‰æƒåŠ æƒå›æµ‹æ•ˆæœï¼Œè‚¡ç¥¨é€‰ä¸º ZZ800ï¼Œå›æµ‹æ—¶é—´ä¸º 2013.1.1 è‡?2018.1.1ï¼Œå› å­é€‰å®šä¸ºå¸‚å€¼å’Œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ï¼Œåœ¨æ¯ä¸ªæœˆæœ€åä¸€ä¸ªè‡ªç„¶æ—¥ï¼Œè·å–å½“å‰æœ€æ–°çš„å› å­æ•°æ®ä»¥åŠå¯¹åº”çš„è‚¡ç¥¨è¶…é¢æ”¶ç›Šã€‚æ•°æ®å…·ä½“è·å–å½“æ—¶å¦‚ä¸‹ä»£ç æ‰€ç¤ºã€?

# In[5]:


start = time.clock()
begin_date = '2013-01-01'
end_date = '2018-01-01'
dateList = get_period_date('M',begin_date, end_date)
factor = {}
for date in dateList[:-1]:
    stockList = get_stock_ZZ800(date)
    df_data= get_fundamentals(query(valuation.code, valuation.market_cap, indicator.inc_operation_profit_year_on_year).filter(valuation.code.in_(stockList)), date = date)         
    df_data = df_data.set_index(['code'])
    df_data['market_cap'] = -1 * df_data['market_cap'] 
    # å»æå€?
    df_data = winsorize_med(df_data, scale=1, inclusive=True, inf2nan=True, axis=0)
    # æ•°æ®æ ‡å‡†åŒ?
    df_data = standardlize(df_data, inf2nan=True, axis=0)
    
    df_data['total_55'] = 0.5 * df_data['market_cap'] + 0.5 * df_data['inc_operation_profit_year_on_year']
    df_data['total_64'] = 0.6 * df_data['market_cap'] + 0.4 * df_data['inc_operation_profit_year_on_year']
    # è·å–æ¨ªæˆªé¢æ”¶ç›Šç‡
    df_close = get_price(stockList, date, dateList[dateList.index(date)+1], 'daily', ['close'])
    if df_close.empty:
        continue
    df_pchg = df_close['close'].iloc[-1,:]/df_close['close'].iloc[0,:]-1
    # ä¸Šè¯æŒ‡æ•°æ”¶ç›Šç?
    index_close = get_price('000906.XSHG', date, dateList[dateList.index(date)+1], 'daily', ['close'])
    index_pchg = index_close['close'].iloc[-1]/index_close['close'].iloc[0]-1
    df_data['excess_pchg'] = df_pchg - index_pchg
    factor[date] = df_data  
end = time.clock()
print("time: ", end-start)


# ## 1.4 æ•°æ®åˆ†æ

# è€ƒè™‘ä¸€ä¸ªåŒ…å«å¸‚å€¼ã€è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡çš„ä¸¤å› å­æ¨¡å‹ï¼Œæœ¬ç« åˆ†åˆ«åŸºäºä¸¤ç§åŠ æƒæ–¹å¼è®¡ç®—å¤åˆå› å­å€¼ï¼Œç„¶åé€‰æ‹©å¤åˆå› å­å€¼æœ€é«˜çš„ 100 åªè‚¡ç¥¨æ„å»ºç»„åˆã€‚å…¶ä¸­ï¼Œç»„åˆ 1 ä¸ºç­‰æƒç»„åˆï¼Œå³å¸‚å€¼å’Œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡æŒ‰ç…§ç­‰æƒçš„æ–¹å¼åŠ æ€»ä¸ºå¤åˆå› å­; ç»„åˆ 2 ä¸?60/40 ç»„åˆï¼Œå³å¸‚å€¼å’Œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡å› å­çš„åŠ æƒæ¯”ä¾‹åˆ†åˆ«ä¸?60%ã€?0%ã€? 
# è‚¡ç¥¨æ±? ZZ800ï¼Œå‰”é™?ST è‚¡ç¥¨ä»¥åŠä¸Šå¸‚ 3 ä¸ªæœˆå†…çš„è‚¡ç¥¨  
# å¯¹æ¯”åŸºå‡†: ZZ800  
# äº¤æ˜“è´¹ç”¨: åƒåˆ†ä¹?1.5  
# è°ƒä»“å‘¨æœŸ: æœ?

# In[6]:


# å‡€å€¼è®¡ç®?
def calcNetValue(temPchg: list):
    # è´¹ç‡
    cost = 0.0015
    netValue = []
    netValue.append(1)
    for i in range(len(temPchg)):
        netValue.append(netValue[i]*(1+temPchg[i] - cost))
    netValue.pop(0)
    return netValue


# å‡€å€¼æ›²çº¿ç»˜åˆ?
def plotNetValue(dates, netValue):
    # é€šè¿‡figsizeå‚æ•°å¯ä»¥æŒ‡å®šç»˜å›¾å¯¹è±¡çš„å®½åº¦å’Œé«˜åº¦ï¼Œå•ä½ä¸ºè‹±å¯¸ï¼?
    fig = plt.figure(figsize=(20,8))
    ax = fig.add_subplot(111)
    xticks = list(range(len(netValue[list(netValue.keys())[0]])))
    for key in netValue.keys():
        ax.plot(xticks, netValue[key], label=key)
        for i in range(len(xticks)):  
            xticks[i] = xticks[i] + 0.1 
    # è®¾ç½®å›¾ä¾‹æ ·å¼
    ax.legend(loc = 2, fontsize = 10)
    # è®¾ç½®yæ ‡ç­¾æ ·å¼
    ax.set_ylabel('NetValue',fontsize=20)
    ax.set_title("Strategy's NetValue Performances", fontsize=20)
    plt.show() 

    
# æœˆåº¦æ”¶ç›ŠæŸ±çŠ¶å›¾ç»˜åˆ?
def plotBar(dates, pchg, label):
    # é€šè¿‡figsizeå‚æ•°å¯ä»¥æŒ‡å®šç»˜å›¾å¯¹è±¡çš„å®½åº¦å’Œé«˜åº¦ï¼Œå•ä½ä¸ºè‹±å¯¸ï¼?
    fig = plt.figure(figsize=(20,8))
    ax = fig.add_subplot(111)
    xticks = range(len(pchg))
    ax.bar(xticks, pchg, label = label) 
    plt.xticks([x+0.3 for x in xticks], xticks)
    ticks = [int(x) for x in np.linspace(0, len(dates)-1, 11)]
    plt.xticks(ticks, [dates[i] for i in ticks])
    # è®¾ç½®å›¾ä¾‹æ ·å¼
    ax.legend(loc='best',fontsize=15)
    # è®¾ç½®yæ ‡ç­¾æ ·å¼
    ax.set_ylabel('Algorithm_return', fontsize=15)
    # è®¾ç½®å›¾ç‰‡æ ‡é¢˜æ ·å¼
    ax.set_title("æœˆåº¦æ”¶ç›ŠæŸ±çŠ¶å›?, fontsize=15)
    plt.show()

    
def MaxDrawdown(return_list):
    '''æœ€å¤§å›æ’¤ç‡'''
    i = np.argmax((np.maximum.accumulate(return_list) - return_list) / np.maximum.accumulate(return_list))  # ç»“æŸä½ç½®
    if i == 0:
        return 0
    j = np.argmax(return_list[:i])  # å¼€å§‹ä½ç½?
    return (return_list[j] - return_list[i]) / (return_list[j])


# æ”¶ç›Šé£é™©ç»Ÿè®¡
def cal_indictor(netValue):
    result = {}
    total_return = netValue[-1] / netValue[0] - 1
    ann_return = pow((1+total_return), 240/float(len(netValue) * 20))-1
    pchg = []
    #è®¡ç®—æ”¶ç›Šç?
    for i in range(1, len(netValue)):
        pchg.append(netValue[i]/netValue[i-1] - 1)
    temp = 0
    for i in pchg:
        temp += pow(i-mean(pchg), 2)
    annualVolatility = sqrt(240/float((len(pchg) * 20-1))*temp)
    sharpe_ratio = (ann_return - 0.04)/annualVolatility
    result['TotalReturn'] = total_return
    result['AnnReturn'] = ann_return
    result['AnnVol'] = annualVolatility
    result['SR'] = sharpe_ratio
    result['MaxDown'] = MaxDrawdown(netValue)
    return result


# In[7]:


# @ç‰¹æ‹‰ä»?æ–°å¢ï¼Œç”¨äºå‡½æ•°æµ‹è¯?
pchg_55 = []
pchg_64 = []
tradingDays = sort(list(factor.keys()))
for date in tradingDays:
    temp = factor[date]
    temp_55 = temp.sort_values(['total_55'], ascending = False)
    temp_64 = temp.sort_values(['total_64'], ascending = False)
    pchg_55.append(np.mean(temp_55['excess_pchg'].iloc[:100]))
    pchg_64.append(np.mean(temp_64['excess_pchg'].iloc[:100]))

# pchg_55


# In[8]:


# tradingDays


# In[9]:


# ç»˜åˆ¶å‡€å€¼æ›²çº?
netValue = {}
netValue['ç­‰æƒç»„åˆ'] = calcNetValue(pchg_55)
netValue['60/40 ç»„åˆ'] = calcNetValue(pchg_64)
plotNetValue(tradingDays, netValue)
plotBar(tradingDays, pchg_55, label = 'ç­‰æƒç»„åˆ')
plotBar(tradingDays, pchg_64, label = '60/40 ç»„åˆ')
MonthWinRate1 = len([i for i in pchg_55 if i >0])/float(len(pchg_55))
MonthWinRate2 = len([i for i in pchg_64 if i >0])/float(len(pchg_64))


# In[10]:


# netValue['ç­‰æƒç»„åˆ']


# In[11]:


# æ”¶ç›Šé£é™©æŒ‡æ ‡ç»Ÿè®¡
result55 = cal_indictor(netValue['ç­‰æƒç»„åˆ'])
result64 = cal_indictor(netValue['60/40 ç»„åˆ'])
result = pd.DataFrame()
result['ç­‰æƒç»„åˆ'] = pd.Series(result55)
result['60/40 ç»„åˆ'] =pd.Series(result64)
result_output = result.T
result_output = result_output[['TotalReturn', 'AnnReturn', 'AnnVol', 'SR', 'MaxDown']]
result_output['MonthWinRate'] = [MonthWinRate1, MonthWinRate2]
result_output


# ç¬¬ä¸€å¼ å›¾ç»Ÿè®¡äº†ä¸¤å› å­ç­‰æƒç»„åˆå?60/40 ç»„åˆçš„å‡€å€¼æ›²çº¿å›¾ï¼Œä¸Šè¡¨ç»Ÿè®¡äº†ä¸¤ä¸ªç»„åˆçš„è¶…é¢æ”¶ç›ŠæŒ‡æ ‡å¯¹æ¯”ã€‚ä»ä¸­å¯çœ‹å‡ºï¼Œæé«˜äº†å¸‚å€¼æƒé‡çš„60/40 ç»„åˆï¼Œæ”¶ç›Šç‡é«˜äºç­‰æƒç»„åˆï¼ŒåŒæ—¶é£é™©ï¼ˆæœ€å¤§å›æ’¤ã€å¹´åŒ–æ³¢åŠ¨ç‡ï¼‰ä¹Ÿé«˜äºç­‰æƒç»„åˆã€‚æ­¤å¤–ï¼Œä»æœˆåº¦è§’åº¦æ¥çœ‹ï¼Œç­‰æƒå› å­ç»„åˆå’?60/40 ç»„åˆçš„ç¨³å®šæ€§éƒ½éå¸¸é«˜ã€?

# # 2 å› å­ IC åŠ æƒ

# ## 2.1 å› å­ IC åˆ†æ

# In[12]:


IC_marketcap = []
IC_iop = []
for date in tradingDays:
    temp = factor[date]
    IC_marketcap.append(st.pearsonr(temp['market_cap'], temp['excess_pchg'])[0])
    IC_iop.append(st.pearsonr(temp['inc_operation_profit_year_on_year'], temp['excess_pchg'])[0])
IC = pd.DataFrame(index = ['å¸‚å€?, 'è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç?])
IC['å‡å€?] = [np.nanmean(IC_marketcap), np.nanmean(IC_iop)]
IC['æ ‡å‡†å·?] = [np.nanstd(IC_marketcap), np.nanstd(IC_iop)]
IC['IR'] = [np.nanmean(IC_marketcap) / np.nanstd(IC_iop), np.nanmean(IC_iop) / np.nanstd(IC_iop)]
IC


# å¯¹æ¯”å¸‚å€¼å’Œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡çš„ IC åºåˆ—ç»Ÿè®¡ç‰¹å¾å¯å‘ç°ï¼Œå¸‚å€¼å› å­çš„ICå‡å€¼ï¼ˆ0.09ï¼‰æ˜æ˜¾ä¼˜äºè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ï¼?.028ï¼‰ï¼ŒåŒæ—¶å‰è€…IC åºåˆ—çš„æ³¢åŠ¨æ€§ä¹Ÿé«˜äºåè€…ã€‚ä» IR æ¥çœ‹ï¼Œå¸‚å€¼å› å­æ•ˆæœæ›´å¥½ï¼Œè¿™ç§æ•ˆæœç›¸å·®æ˜æ˜¾çš„æƒ…å†µä¸‹ï¼Œç®€å•çš„ç­‰æƒåŠ æƒå¹¶ä¸èƒ½ä½“ç°å¸‚å€¼å› å­çš„å¼ºæœ‰æ•ˆé€‰è‚¡æ•ˆåº”ï¼Œä»è€Œæ‹–ç´¯äº†å¤šå› å­ç»„åˆçš„è¡¨ç°ã€?

# ## 2.2 å› å­ IC ç­‰æƒç»„åˆåˆ†æ

# In[13]:


# è®¡ç®—å¸‚å€¼å› å­çš„æƒé‡
ratio_marketcap = np.nanmean(IC_marketcap) / (abs(np.nanmean(IC_marketcap)) + np.nanmean(IC_iop))
print('å¸‚å€¼å› å­æƒé‡? ', ratio_marketcap)
print('è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡å› å­æƒé‡? ', 1-ratio_marketcap)
pchg_IC_avg = []
for date in tradingDays:
    temp = factor[date]
    temp['IC_avg'] = ratio_marketcap * temp['market_cap'] + (1 - ratio_marketcap) * temp['inc_operation_profit_year_on_year']
    temp = temp.sort_values(['IC_avg'], ascending = False)
    pchg_IC_avg.append(np.mean(temp['excess_pchg'].iloc[:100]))
netValue['IC åŠ æƒç»„åˆ'] = calcNetValue(pchg_IC_avg)
plotNetValue(tradingDays, netValue)
plotBar(tradingDays, pchg_IC_avg, label = 'IC åŠ æƒç»„åˆ')
MonthWinRate3 = len([i for i in pchg_IC_avg if i >0])/float(len(pchg_IC_avg))
# æ”¶ç›Šé£é™©æŒ‡æ ‡ç»Ÿè®¡
result_IC_avg = cal_indictor(netValue['IC åŠ æƒç»„åˆ'])
result['IC åŠ æƒç»„åˆ'] = pd.Series(result_IC_avg)
result_output = result.T
result_output = result_output[['TotalReturn', 'AnnReturn', 'AnnVol', 'SR', 'MaxDown']]
result_output['MonthWinRate'] = [MonthWinRate1, MonthWinRate2, MonthWinRate3]
result_output


# ç¬¬ä¸€å¼ å›¾ç»Ÿè®¡äº†ä¸¤å› å­ç­‰æƒç»„åˆã€?0/40 ç»„åˆä»¥åŠ IC åŠ æƒå› å­ç»„åˆçš„å‡€å€¼æ›²çº¿å›¾ï¼Œä¸Šè¡¨ç»Ÿè®¡äº†è¿™ä¸‰ä¸ªç»„åˆçš„è¶…é¢æ”¶ç›ŠæŒ‡æ ‡å¯¹æ¯”ã€‚ä»ä¸­å¯çœ‹å‡ºï¼?
# å‡ºIC åŠ æƒç»„åˆçš„æ”¶ç›Šç‡æ˜æ˜¾é«˜äºç­‰æƒç»„åˆä»¥åŠ60/40 ç»„åˆï¼ŒåŒæ—¶é£é™©ï¼ˆæœ€å¤§å›æ’¤ã€å¹´åŒ–æ³¢åŠ¨ç‡ï¼‰ä¹Ÿé«˜äºç­‰æƒç»„åˆä»¥åŠ 60/40 ç»„åˆã€‚ä½†æ˜¯ä»å¤æ™®æ¯”ç‡æ¥çœ‹ï¼Œå› å­?IC åŠ æƒç»„åˆçš„æ”¶ç›Šé£é™©æ¯”é«˜äºå…¶ä½™ä¸¤ä¸ªç»„åˆã€‚ä»æœˆåº¦æ”¶ç›Šæ¥çœ‹ï¼Œå› å­?IC åŠ æƒç»„åˆçš„æœˆèƒœç‡ä¸?85%ï¼Œå¯è§æ¨¡å‹æ”¶ç›Šç¨³å®šæ€§å¾—åˆ°è¿›ä¸€æ­¥æé«˜ã€? 
# è¿›ä¸€æ­¥åˆ†æå‘ç°ï¼Œåœ¨å¸‚å€¼ã€è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡çš„ä¾‹å­ä¸­ï¼Œä¸¤å› å­æŒ‰ç…§ IC åŠ æƒçš„æƒé‡åˆ†åˆ«ä¸º 76.90% å’?23.10%ï¼›å¸‚å€¼å› å­çš„æƒé‡é«˜äºç­‰æƒå½¢å¼ï¼Œä¹Ÿé«˜äºä¸»è§‚çš?60/40 ç»„åˆã€‚ä¹Ÿå°±æ˜¯è¯´ï¼Œå› å­ IC åŠ æƒç»„åˆå¢åŠ äº†æ”¶ç›Šé«˜ã€æ³¢åŠ¨å¤§çš„â€œå¸‚å€¼å› å­â€æƒé‡ï¼Œå‡å°‘äº†æ”¶ç›Šä½ã€æ³¢åŠ¨å°çš„â€œè¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡â€æƒé‡ï¼Œä»è€Œä½¿å¾?IC åŠ æƒç»„åˆçš„æ•´ä½“æ”¶ç›Šã€æ³¢åŠ¨å‡é«˜äºç­‰æƒç»„åˆã€?

# # 3 æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC

# ## 3.1 ç†è®ºæœ€ä¼˜æ¯”ä¾‹çš„è®¡ç®—

# ç”±å‰é¢çš„åˆ†æå¯çŸ¥ï¼Œåœ¨å¯¹å› å­åŠ æƒæ—¶ï¼Œéœ€è€ƒè™‘å› å­æœ¬èº«çš„æœ‰æ•ˆæ€§ï¼ˆICï¼‰ï¼Œä½†å› å­?IC åŠ æƒå¹¶éåœ¨æ‰€æœ‰æƒ…å†µä¸‹éƒ½ä¼˜äºç­‰æƒç»„åˆã€‚é‚£ä¹ˆï¼Œä»ç†è®ºä¸Šçœ‹è‹¥ä»¥æœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC ä¸ºç›®æ ‡ï¼Œæœ€ä¼˜åŠ æƒæ¯”ä¾‹ä¸å“ªäº›å› ç´ ç›¸å…³å‘¢ï¼Ÿ  
# å‡è®¾æœ?M ä¸ªå› å­ï¼Œåˆ†åˆ«ä¸?$F_1ã€F_2ã€â€¦ã€F_M$ï¼Œå®ƒä»¬åŸºäºæƒé‡åºåˆ?$W=(w_1ï¼Œâ€¦ï¼Œw_M)$ åŠ æ€»ä¸ºå¤åˆå› å­ $F_C$ï¼Œå³
# <center>$F_c = W' * F = \sum_{i=1}^M w_i*F_i$ </center>   
# æŒ‰ç…§å‰é¢çš„è¯´æ˜ï¼Œæˆ‘ä»¬æœ€ä¼˜åŒ–çš„ç›®æ ‡å‡½æ•°ä¸ºï¼? 
# <center>$IC_c = corr(F_c, R) = \frac{\sum_{i=1}^M w_iIC_istd(F_i)}{std(F_c)}$ </center>   
# å‡è®¾ä¸¤å› å­æ¨ªæˆªé¢åæ–¹å·®ä¸º $\delta_{i,j} = cov(F_i, F_j)$ï¼Œç›¸åº”çš„åæ–¹å·®çŸ©é˜µä¸º $\sum = (\delta_{i,j})^M_{i,j=1}$ï¼›åŒæ—¶å¯¹æ¯ä¸€æœŸçš„å› å­å€¼è¿›è¡Œæ ‡å‡†åŒ–å¤„ç†ï¼Œä½¿å¾—å› å­æ ‡å‡†å·®å˜ä¸º1ï¼Œåˆ™ä¸Šé¢çš„å¤åˆå› å­?IC å¯ç®€åŒ–ä¸ºå¦‚ä¸‹å½¢å¼ï¼?
# <center>$IC_c = \frac{\sum_{i=1}^M w_iIC_i\delta_i}{\sqrt{W'\sum W}} = \frac{\sum_{i=1}^M w_iIC_i}{\sqrt{W'\sum W}}$ </center>   
# å¯¹ä¸Šé¢çš„ç­‰å¼æ±‚åå¯¼ï¼Œå¯å¾—å‡ºæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„è§£æè§£ä¸ºå¦‚ä¸‹å½¢å¼ï¼š  
# <center>$ W = \sum^{-1} \cdot IC $</center>   
# ä»ä¸Šé¢çš„è§£æå¼å¯çœ‹å‡ºï¼Œæœ€å¤§åŒ–å¤åˆå› å­å•æœŸIC çš„ç†è®ºæœ€ä¼˜æƒé‡ä¸ä¸¤æ–¹é¢çš„å› ç´ æœ‰å…³ï¼šå› å­ä¹‹é—´çš„åæ–¹å·®ï¼Œä»¥åŠå› å­ICã€?
# ä¸ºè¯´æ˜ç›¸å…³ç³»æ•°ï¼ˆåæ–¹å·®ï¼‰ä¸æœ€ä¼˜æƒé‡çš„å…³ç³»ï¼Œæˆ‘ä»¬ä»¥ä¸¤å› å­æ¨¡å‹çš„ä¾‹å­è¿›è¡Œè¯´æ˜ã€‚åœ¨ä¸€ä¸ªåªåŒ…å«ä¸¤ä¸ªå› å­çš„æ¨¡å‹ä¸­ï¼Œä¸Šè¿°ç­‰å¼å¯ä»¥ç®€åŒ–å¦‚ä¸‹å½¢å¼ï¼š
# <center>$ w_1 = \frac{IC_1}{IC_1 + IC_2} + \frac{p_{12}}{1 + p_{12}} * \frac{IC_1 - IC_2}{IC_1 + IC_2} $</center>  
# <center>$ w_2 = \frac{IC_2}{IC_1 + IC_2} + \frac{p_{12}}{1 + p_{12}} * \frac{IC_2 - IC_1}{IC_1 + IC_2} $</center>    
# ä»ä¸Šé¢çš„ç®€åŒ–å½¢å¼å¯ä»¥çœ‹å‡ºï¼Œè‹¥ä¸¤å› å­ä¸ºæ­£ç›¸å…³å…³ç³»ï¼Œåˆ™ç›¸æ¯”äº?IC åŠ æƒï¼Œæœ€ä¼?IC åŠ æƒæ–¹å¼ä¼šå¢åŠ æœ‰æ•ˆæ€§æ›´é«˜ï¼ˆå?IC æ›´å¤§ï¼‰çš„å› å­æƒé‡ï¼›è‹¥ä¸¤å› å­ä¸ºè´Ÿç›¸å…³å…³ç³»ï¼Œåˆ™æœ€ä¼?IC åŠ æƒä¼šå¢åŠ æœ‰æ•ˆæ€§ç›¸å¯¹è¾ƒä½çš„å› å­æƒé‡ï¼Œä»¥ç¨³å®šç»„åˆçš„æ”¶ç›Šã€? 
# 

# ## 3.2 ç»„åˆæµ‹è¯•

# æ¥ä¸‹æ¥ï¼Œåˆ©ç”¨å‰ä¸€éƒ¨åˆ†æ¨å¯¼çš„æœ€ä¼?IC åŠ æƒæ–¹å¼ï¼Œé’ˆå¯?7 ä¸ªå› å­æ„å»ºå¤šå› å­æ¨¡å‹: å¸‚ç›ˆç?PB)ã€å¸‚å‡€ç?PE)ã€å¸‚é”€ç?PS)ã€è¥ä¸šåˆ©æ¶¦åŒæ¯”å¢é•¿ç‡ã€èµ„äº§è´Ÿå€ºç‡ã€åè½?å‰?1 æœˆç´¯è®¡æ”¶ç›?ã€æ¢æ‰‹ç‡(å‰?10 ä¸ªäº¤æ˜“æ—¥æ—¥å‡æ¢æ‰‹ç?ã€‚æ¯ä¸ªæœˆæœ€åä¸€ä¸ªè‡ªç„¶æ—¥ï¼Œè·å–è¿™ 7 ä¸ªå› å­æ•°æ®ä»¥åŠä¸‹ä¸ªæœˆç›¸å¯¹ ZZ800 çš„è¶…é¢æ”¶ç›Šã€‚ä¸ºç»¼åˆæ¯”è¾ƒå‰æ–‡æ‰€æåŠçš„ä¸‰ç§åŠ æƒæ–¹å¼ï¼Œåœ¨æ­¤éƒ¨åˆ†çš„åº”ç”¨ä¸­æˆ‘ä»¬ä»ç„¶æ„å»º 3 ä¸ªç»„åˆè¿›è¡Œå¯¹æ¯”ï¼Œåˆ†åˆ«æ˜¯å› å­ç­‰æƒç»„åˆã€å› å­?IC åŠ æƒä»¥åŠæœ€ä¼?IC åŠ æƒç»„åˆã€? 
# è‚¡ç¥¨æ±? ZZ800ï¼Œå‰”é™?ST è‚¡ç¥¨ä»¥åŠä¸Šå¸‚ 3 ä¸ªæœˆå†…çš„è‚¡ç¥¨  
# å¯¹æ¯”åŸºå‡†: ZZ800  
# äº¤æ˜“è´¹ç”¨: åƒåˆ†ä¹?1.5  
# è°ƒä»“å‘¨æœŸ: æœ?

# In[14]:


start = time.clock()
begin_date = '2013-01-01'
end_date = '2018-01-01'
dateList = get_period_date('M',begin_date, end_date)
factor = {}
for date in dateList[:-1]:
    stockList = get_stock_ZZ800(date)
    df_data = get_fundamentals(query(valuation.code, valuation.pb_ratio, valuation.pcf_ratio, valuation.pe_ratio, indicator.inc_operation_profit_year_on_year, balance.total_liability, balance.total_assets).filter(valuation.code.in_(stockList)), date = date)         
    df_data = df_data.set_index(['code'])
    df_turnover = get_fundamentals_continuously(query(valuation.code, valuation.turnover_ratio).filter(valuation.code.in_(stockList)), end_date = date, count = 10)
    df_data['pb_ratio'] = df_data['pb_ratio']
    df_data['turnover_ratio'] = -1 * np.mean(df_turnover['turnover_ratio'])
    df_data['pe_ratio'] = -1 * df_data['pe_ratio']
    df_data['pcf_ratio'] = -1 * df_data['pcf_ratio']
    df_close = get_price(stockList, count = 21, end_date=date, frequency='daily', fields=['close'])['close']
    df_data['pchg'] = -1 * (df_close.iloc[-1] / df_close.iloc[0] - 1)
    df_data['AssetsLiab'] = df_data['total_assets'] / df_data['total_liability']
    # å»æå€?
    df_data = winsorize_med(df_data, scale=1, inclusive=True, inf2nan=True, axis=0)
    # æ•°æ®æ ‡å‡†åŒ?
    df_data = standardlize(df_data, inf2nan=True, axis=0)
    # è·å–æ¨ªæˆªé¢æ”¶ç›Šç‡
    df_close = get_price(stockList, date, dateList[dateList.index(date)+1], 'daily', ['close'])
    if df_close.empty:
        continue
    df_pchg = df_close['close'].iloc[-1,:]/df_close['close'].iloc[0,:]-1
    # ä¸Šè¯æŒ‡æ•°æ”¶ç›Šç?
    index_close = get_price('000906.XSHG', date, dateList[dateList.index(date)+1], 'daily', ['close'])
    index_pchg = index_close['close'].iloc[-1]/index_close['close'].iloc[0]-1
    df_data['excess_pchg'] = df_pchg - index_pchg
    factor[date] = df_data  
end = time.clock()
print("time: ", end-start)


# In[15]:


df_data.head(2)


# In[16]:


factor['2012-12-31'].head(2)


# In[17]:


pchg_mean = []
tradingDays = sort(list(factor.keys()))
# tradingDays


# In[18]:


len(pchg_IC_avg)


# In[19]:


# @ ç‰¹æ‹‰ä»?æ–°å¢ï¼Œæ›¿æ¢åŸä½œè€…çš„hard codeï¼Œä»¥åå¯ä»¥çµæ´»é€‰æ‹©å› å­

fac_nm=['pb_ratio', 'pcf_ratio', 'turnover_ratio', 'pchg', 'pe_ratio', 'AssetsLiab', 'inc_operation_profit_year_on_year']

names = locals()
factor_num = len(fac_nm)
for fi in fac_nm:
    names['IC_' + fi ] = []
    names['ratio_'+fi] = 0

#     print(names.get('IC_' + str(fi)), end=' ')
#     print(names.get('ratio_' + str(fi)), end=' ')

for date in tradingDays:
    temp = factor[date]
    temp['total_mean'] = 0
    
    for fi in fac_nm:
        temp['total_mean'] += 1/float(factor_num)*temp[fi]
    
    temp_mean = temp.sort_values(['total_mean'], ascending = False)
    # åŸä½œè€…è¿™é‡Œä¸ºä½•è¦ilocåˆ?00 ?        
    pchg_mean.append(np.mean(temp_mean['excess_pchg'].iloc[:100]))
    
    for fi in fac_nm:    
        names.get('IC_' + fi).append(st.pearsonr(temp[fi], temp['excess_pchg'])[0])
    
#         print(date, names.get('IC_' + fi))

# è®¡ç®—å¸‚å€¼å› å­çš„æƒé‡
totalWeight=0
for fi in fac_nm:
    totalWeight+=np.nanmean(names.get('IC_' + fi))
# totalWeight

arr_l = []
for fi in fac_nm:
    x = np.nanmean(names.get('IC_' + fi))
    arr_l.append(x)
    names['ratio_'+fi] = x/totalWeight

pchg_IC_avg = []
tempData = pd.DataFrame()
for date in tradingDays:
    temp = factor[date]
    temp['total_IC_avg'] = 0
    
    for fi in fac_nm:
        temp['total_IC_avg'] += names.get('ratio_'+fi) * temp[fi]

    temp_IC_avg = temp.sort_values(['total_IC_avg'], ascending = False)
    pchg_IC_avg.append(np.mean(temp_IC_avg['excess_pchg'].iloc[:100]))

    temp = factor[date][fac_nm]
    if tempData.empty:
        tempData = temp
    else:
        tempData = tempData.append(temp)

a = temp.cov()
weight = np.dot(np.linalg.inv(np.array(a)), np.array(arr_l))
pchg_IC_max = []

for date in tradingDays:
    temp = factor[date]
    temp['total_IC_max'] = 0
    for i in range(0, len(fac_nm)):
        temp['total_IC_max'] = weight[i] * temp[fac_nm[i]]

    temp_IC_max = temp.sort_values(['total_IC_max'], ascending = False)
    pchg_IC_max.append(np.mean(temp_IC_max['excess_pchg'].iloc[:100]))


# In[20]:


# ç»˜åˆ¶å‡€å€¼æ›²çº?
netValue = {}
netValue['ç­‰æƒç»„åˆ'] = calcNetValue(pchg_mean)
netValue['IC åŠ æƒç»„åˆ'] = calcNetValue(pchg_IC_avg)
netValue['æœ€ä¼?IC åŠ æƒç»„åˆ'] = calcNetValue(pchg_IC_max)


# In[21]:



plotNetValue(tradingDays, netValue)

MonthWinRate1 = len([i for i in pchg_mean if i >0])/float(len(pchg_mean))
MonthWinRate2 = len([i for i in pchg_IC_avg if i >0])/float(len(pchg_IC_avg))
MonthWinRate3 = len([i for i in pchg_IC_max if i >0])/float(len(pchg_IC_max))
# æ”¶ç›Šé£é™©æŒ‡æ ‡ç»Ÿè®¡
result_temp = cal_indictor(netValue['ç­‰æƒç»„åˆ'])
result = pd.DataFrame()
result['ç­‰æƒç»„åˆ'] = pd.Series(result_temp)
result_temp = cal_indictor(netValue['IC åŠ æƒç»„åˆ'])
result['IC åŠ æƒç»„åˆ'] = pd.Series(result_temp)
result_temp = cal_indictor(netValue['æœ€ä¼?IC åŠ æƒç»„åˆ'])
result['æœ€ä¼?IC åŠ æƒç»„åˆ'] = pd.Series(result_temp)

result_output = result.T
result_output = result_output[['TotalReturn', 'AnnReturn', 'AnnVol', 'SR', 'MaxDown']]
result_output['MonthWinRate'] = [MonthWinRate1, MonthWinRate2, MonthWinRate3]
result_output
    


# In[22]:


# @ç‰¹æ‹‰ä»?å…¬å…±å‡½æ•°ï¼Œè®¡ç®—æ¨ªæˆªé¢ï¼ˆæŸä¸€æœŸï¼‰IC_IRæœ€ä¼˜åŠ æ?
def getOptFacWgt(factor_num: int):
    for i in range(0, factor_num):
        IC_x = []


# åœ¨ä¸Šè¿?7 ä¸ªå› å­çš„ä¾‹å­ä¸­ï¼Œå›æµ‹æ—¶é—´åœ?2013 - 2018ï¼Œç­‰æƒç»„åˆçš„å¹´åŒ–è¶…é¢æ”¶ç›Šä¸?19.87%ï¼Œä½†å¤æ™®æ¯”ç‡è¾ƒä½ï¼Œä¸º1.00ã€‚å› å­?IC åŠ æƒç»„åˆçš„æ”¶ç›Šè¡¨ç°ç•¥ä¼˜äºç­‰æƒç»„åˆï¼Œå¹´åŒ–æ”¶ç›Šå¢åŠ è‡³ 22.27%ï¼Œå¤æ™®æ¯”ç‡ä¹Ÿæ¯”å…¶æ›´é«˜ï¼Œä¸º 1.09ã€‚ä¸‰ä¸ªç»„åˆä¸­è¡¨ç°æœ€å¥½çš„æ˜¯æœ€ä¼?IC åŠ æƒç»„åˆï¼Œå…¶å¹´åŒ–è¶…é¢è¾?23.61%ï¼Œå¤æ™®æ¯”ç‡åœ¨ä¸‰ç§ä¹Ÿæ˜¯æœ€é«˜ï¼Œä¸?1.17ã€? 
# 

# # æ€»ç»“

# ç›®å‰è€Œè¨€ï¼Œåº”ç”¨è¾ƒå¤šçš„å› å­åŠ æƒæ–¹æ³•ä¸»è¦æœ‰ä»¥ä¸‹å‡ ç§? ç­‰æƒåŠ æƒã€IC åŠ æƒå’?IC_IR åŠ æƒã€ä»¥åŠæœ€ä¼˜åŒ– IC_IR åŠ æƒã€‚å…¶ä¸­ï¼Œç­‰æƒåŠ æƒæ˜¯å› å­åŠ æƒæœ€ä¼ ç»Ÿçš„æ–¹æ³•ï¼Œè¿™ç§æ–¹æ³•å—å› å­ä¹‹é—´æœ‰æ•ˆæ€§å·®å¼‚ã€çº¿æ€§ç›¸å…³æ€§å½±å“æ˜æ˜¾ã€‚è€?IC åŠ æƒå¯¹ç­‰æƒæ–¹å¼å¿½è§†äº†å› å­æœ‰æ•ˆæ€§å·®å¼‚çš„é—®é¢˜è¿›è¡Œäº†æ”¹è¿›ï¼Œåœ¨å¤§éƒ¨åˆ†æƒ…å†µä¸‹ä¼šä¼˜äºç­‰æƒåŠ æƒå½¢å¼ã€?   
# Qian åœ¨ã€ŠQuantitative Equtiy Portfolio Managementã€‹ä¸€ä¹¦ä¸­æå‡ºä»¥æœ€å¤§åŒ–å¤åˆå› å­ IC_IR è·å¾—å› å­æƒé‡ï¼Œç»¼åˆè€ƒè™‘äº†å› å­çš„ IC å¤§å°ä»¥åŠ IC æ—¶é—´åºåˆ—çš„ç¨³å®šæ€§ï¼Œç›®å‰å·²æœ‰è®¸å¤šæ–‡ç« å¯¹æ­¤ç§åŠ æƒæ–¹å¼è¿›è¡Œäº†æµ‹è¯•ã€? 
# æœ¬æ–‡æ²¿ç”¨ Qian çš„æœ€ä¼˜åŒ–ä½“ç³»è·å–å› å­æƒé‡ï¼Œä¸ä¹‹ä¸åŒçš„æ˜¯ï¼Œæˆ‘ä»¬å°†ä¼˜åŒ–ç›®æ ‡ç”±æœ€å¤§åŒ–å¤åˆå› å­ IR å˜ä¸ºæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ ICã€‚ç†è®ºè§£æè§£çš„å½¢å¼è¡¨æ˜ï¼Œæœ€å¤§åŒ–å¤åˆå› å­å•æœŸ IC çš„æƒé‡ä¸ä¸¤æ–¹é¢å› ç´ æœ‰å…? ä¸€æ˜¯å› å­çš„æœ‰æ•ˆæ€§ï¼Œå³å› å­?IC;äºŒæ˜¯å› å­ä¹‹é—´çš„ç›¸å…³ç³»æ•°ã€‚åŒæ—¶ï¼Œæ–‡ç« å®è¯ç»“æœä¹Ÿè¡¨æ˜ï¼Œæœ€å¤§åŒ–å•æœŸ IC èƒ½æœ‰æ•ˆè§£å†³â€œç­‰æƒâ€çš„é…ç½®åå·®é—®é¢˜ï¼Œåœ¨ç»å¤§éƒ¨åˆ†å› å­ç©ºé—´ï¼Œæœ€ä¼?IC åŠ æƒæ‰€æ„å»ºçš„ç»„åˆï¼Œå…¶è¡¨ç°å‡ä¼˜äºâ€œç­‰æƒâ€æ–¹å¼?æ‰€æ„å»ºçš„ç»„åˆã€?

# 202206 ok

# In[ ]:




