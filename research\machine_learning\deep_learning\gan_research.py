#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAN Research Module
生成对抗网络研究模块

基于Chapter20的GAN技术，用于金融时间序列数据的生成和增强研究
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入基类
from research.base_classes.research_base import BaseFactorResearcher, ResearchResult

# 深度学习相关导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, Model
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_squared_error
    import matplotlib.pyplot as plt
    DEEP_LEARNING_AVAILABLE = True
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    logging.warning(f"深度学习库不可用: {e}")

class GANResearcher(BaseFactorResearcher):
    """
    生成对抗网络研究者
    
    基于Chapter20的GAN技术，专门用于金融时间序列数据的研究：
    1. 合成数据生成
    2. 数据增强
    3. 市场模式学习
    4. 风险场景生成
    """
    
    def __init__(self, name: str = "GANResearcher"):
        super().__init__(name, factor_categories=['gan', 'generative', 'synthetic_data'])
        self.generators = {}
        self.discriminators = {}
        self.gans = {}
        self.scalers = {}
        self.research_results = {}
        
        if not DEEP_LEARNING_AVAILABLE:
            logging.warning("⚠️ 深度学习库不可用，某些功能将受限")
    
    def build_generator(self, latent_dim: int, output_dim: int, 
                       hidden_dims: List[int] = [128, 256, 512]) -> Optional[Model]:
        """
        构建生成器网络
        
        Args:
            latent_dim: 潜在空间维度
            output_dim: 输出维度
            hidden_dims: 隐藏层维度列表
            
        Returns:
            生成器模型
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            model = keras.Sequential(name='generator')
            
            # 输入层
            model.add(layers.Dense(hidden_dims[0], input_dim=latent_dim))
            model.add(layers.LeakyReLU(alpha=0.2))
            model.add(layers.BatchNormalization(momentum=0.8))
            
            # 隐藏层
            for dim in hidden_dims[1:]:
                model.add(layers.Dense(dim))
                model.add(layers.LeakyReLU(alpha=0.2))
                model.add(layers.BatchNormalization(momentum=0.8))
            
            # 输出层
            model.add(layers.Dense(output_dim, activation='tanh'))
            
            logging.info(f"✅ 构建生成器成功，潜在维度: {latent_dim}, 输出维度: {output_dim}")
            return model
            
        except Exception as e:
            logging.error(f"❌ 构建生成器失败: {e}")
            return None
    
    def build_discriminator(self, input_dim: int, 
                           hidden_dims: List[int] = [512, 256, 128]) -> Optional[Model]:
        """
        构建判别器网络
        
        Args:
            input_dim: 输入维度
            hidden_dims: 隐藏层维度列表
            
        Returns:
            判别器模型
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            model = keras.Sequential(name='discriminator')
            
            # 输入层
            model.add(layers.Dense(hidden_dims[0], input_dim=input_dim))
            model.add(layers.LeakyReLU(alpha=0.2))
            model.add(layers.Dropout(0.3))
            
            # 隐藏层
            for dim in hidden_dims[1:]:
                model.add(layers.Dense(dim))
                model.add(layers.LeakyReLU(alpha=0.2))
                model.add(layers.Dropout(0.3))
            
            # 输出层
            model.add(layers.Dense(1, activation='sigmoid'))
            
            # 编译判别器
            model.compile(loss='binary_crossentropy', optimizer='adam', metrics=['accuracy'])
            
            logging.info(f"✅ 构建判别器成功，输入维度: {input_dim}")
            return model
            
        except Exception as e:
            logging.error(f"❌ 构建判别器失败: {e}")
            return None
    
    def build_gan(self, generator: Model, discriminator: Model) -> Optional[Model]:
        """
        构建GAN网络
        
        Args:
            generator: 生成器模型
            discriminator: 判别器模型
            
        Returns:
            GAN模型
        """
        if not DEEP_LEARNING_AVAILABLE or generator is None or discriminator is None:
            logging.error("❌ 模型不可用")
            return None
            
        try:
            # 冻结判别器权重
            discriminator.trainable = False
            
            # 构建GAN
            gan_input = keras.Input(shape=(generator.input_shape[1],))
            generated_data = generator(gan_input)
            gan_output = discriminator(generated_data)
            
            gan = Model(gan_input, gan_output, name='gan')
            gan.compile(loss='binary_crossentropy', optimizer='adam')
            
            logging.info("✅ 构建GAN成功")
            return gan
            
        except Exception as e:
            logging.error(f"❌ 构建GAN失败: {e}")
            return None
    
    def build_time_series_gan(self, sequence_length: int, feature_dim: int, 
                             latent_dim: int = 100) -> Optional[Tuple[Model, Model, Model]]:
        """
        构建时间序列GAN
        
        Args:
            sequence_length: 序列长度
            feature_dim: 特征维度
            latent_dim: 潜在空间维度
            
        Returns:
            (生成器, 判别器, GAN)模型元组
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 生成器 - 使用LSTM
            generator = keras.Sequential([
                layers.Dense(128, input_dim=latent_dim),
                layers.LeakyReLU(alpha=0.2),
                layers.Reshape((1, 128)),
                layers.LSTM(256, return_sequences=True),
                layers.LSTM(512, return_sequences=True),
                layers.TimeDistributed(layers.Dense(feature_dim, activation='tanh')),
                layers.Reshape((sequence_length, feature_dim))
            ], name='ts_generator')
            
            # 判别器 - 使用LSTM
            discriminator = keras.Sequential([
                layers.LSTM(512, input_shape=(sequence_length, feature_dim)),
                layers.Dense(256),
                layers.LeakyReLU(alpha=0.2),
                layers.Dropout(0.3),
                layers.Dense(128),
                layers.LeakyReLU(alpha=0.2),
                layers.Dropout(0.3),
                layers.Dense(1, activation='sigmoid')
            ], name='ts_discriminator')
            
            discriminator.compile(loss='binary_crossentropy', optimizer='adam', metrics=['accuracy'])
            
            # GAN
            discriminator.trainable = False
            gan_input = keras.Input(shape=(latent_dim,))
            generated_sequence = generator(gan_input)
            gan_output = discriminator(generated_sequence)
            gan = Model(gan_input, gan_output, name='ts_gan')
            gan.compile(loss='binary_crossentropy', optimizer='adam')
            
            logging.info(f"✅ 构建时间序列GAN成功，序列长度: {sequence_length}, 特征维度: {feature_dim}")
            return generator, discriminator, gan
            
        except Exception as e:
            logging.error(f"❌ 构建时间序列GAN失败: {e}")
            return None
    
    def preprocess_time_series_data(self, data: pd.DataFrame, 
                                   sequence_length: int = 60,
                                   scaling_method: str = 'minmax') -> Tuple[np.ndarray, Any]:
        """
        预处理时间序列数据
        
        Args:
            data: 时间序列数据
            sequence_length: 序列长度
            scaling_method: 缩放方法
            
        Returns:
            处理后的数据和缩放器
        """
        try:
            # 选择缩放器
            if scaling_method == 'standard':
                scaler = StandardScaler()
            elif scaling_method == 'minmax':
                scaler = MinMaxScaler(feature_range=(-1, 1))  # GAN通常使用tanh激活
            else:
                raise ValueError(f"不支持的缩放方法: {scaling_method}")
            
            # 缩放数据
            scaled_data = scaler.fit_transform(data)
            
            # 创建序列
            sequences = []
            for i in range(len(scaled_data) - sequence_length + 1):
                sequences.append(scaled_data[i:i + sequence_length])
            
            sequences = np.array(sequences)
            
            logging.info(f"✅ 数据预处理完成，序列形状: {sequences.shape}")
            return sequences, scaler
            
        except Exception as e:
            logging.error(f"❌ 数据预处理失败: {e}")
            return None, None
    
    def train_gan(self, generator: Model, discriminator: Model, gan: Model,
                 real_data: np.ndarray, latent_dim: int, epochs: int = 1000,
                 batch_size: int = 32) -> Dict[str, List[float]]:
        """
        训练GAN模型
        
        Args:
            generator: 生成器模型
            discriminator: 判别器模型
            gan: GAN模型
            real_data: 真实数据
            latent_dim: 潜在空间维度
            epochs: 训练轮数
            batch_size: 批次大小
            
        Returns:
            训练历史
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return {}
            
        try:
            # 训练历史
            d_losses = []
            g_losses = []
            
            # 真实和虚假标签
            real_labels = np.ones((batch_size, 1))
            fake_labels = np.zeros((batch_size, 1))
            
            for epoch in range(epochs):
                # 训练判别器
                # 真实数据
                idx = np.random.randint(0, real_data.shape[0], batch_size)
                real_batch = real_data[idx]
                
                # 生成虚假数据
                noise = np.random.normal(0, 1, (batch_size, latent_dim))
                fake_batch = generator.predict(noise, verbose=0)
                
                # 训练判别器
                d_loss_real = discriminator.train_on_batch(real_batch, real_labels)
                d_loss_fake = discriminator.train_on_batch(fake_batch, fake_labels)
                d_loss = 0.5 * np.add(d_loss_real, d_loss_fake)
                
                # 训练生成器
                noise = np.random.normal(0, 1, (batch_size, latent_dim))
                g_loss = gan.train_on_batch(noise, real_labels)
                
                # 记录损失
                d_losses.append(d_loss[0])
                g_losses.append(g_loss)
                
                # 打印进度
                if epoch % 100 == 0:
                    logging.info(f"Epoch {epoch}/{epochs} - D Loss: {d_loss[0]:.4f}, G Loss: {g_loss:.4f}")
            
            training_history = {
                'discriminator_losses': d_losses,
                'generator_losses': g_losses,
                'epochs': epochs
            }
            
            logging.info("✅ GAN训练完成")
            return training_history
            
        except Exception as e:
            logging.error(f"❌ GAN训练失败: {e}")
            return {}
    
    def generate_synthetic_data(self, generator: Model, num_samples: int, 
                               latent_dim: int, scaler: Any = None) -> Optional[np.ndarray]:
        """
        生成合成数据
        
        Args:
            generator: 生成器模型
            num_samples: 样本数量
            latent_dim: 潜在空间维度
            scaler: 数据缩放器
            
        Returns:
            生成的合成数据
        """
        if not DEEP_LEARNING_AVAILABLE or generator is None:
            logging.error("❌ 生成器不可用")
            return None
            
        try:
            # 生成噪声
            noise = np.random.normal(0, 1, (num_samples, latent_dim))
            
            # 生成数据
            synthetic_data = generator.predict(noise, verbose=0)
            
            # 反缩放
            if scaler is not None:
                if len(synthetic_data.shape) == 3:  # 时间序列数据
                    original_shape = synthetic_data.shape
                    synthetic_data = synthetic_data.reshape(-1, synthetic_data.shape[-1])
                    synthetic_data = scaler.inverse_transform(synthetic_data)
                    synthetic_data = synthetic_data.reshape(original_shape)
                else:
                    synthetic_data = scaler.inverse_transform(synthetic_data)
            
            logging.info(f"✅ 生成合成数据成功，形状: {synthetic_data.shape}")
            return synthetic_data
            
        except Exception as e:
            logging.error(f"❌ 生成合成数据失败: {e}")
            return None
    
    def evaluate_generated_data(self, real_data: np.ndarray, 
                               synthetic_data: np.ndarray) -> Dict[str, Any]:
        """
        评估生成数据质量
        
        Args:
            real_data: 真实数据
            synthetic_data: 合成数据
            
        Returns:
            评估结果
        """
        try:
            # 统计特征比较
            real_mean = np.mean(real_data, axis=0)
            synthetic_mean = np.mean(synthetic_data, axis=0)
            
            real_std = np.std(real_data, axis=0)
            synthetic_std = np.std(synthetic_data, axis=0)
            
            # 计算差异
            mean_diff = np.mean(np.abs(real_mean - synthetic_mean))
            std_diff = np.mean(np.abs(real_std - synthetic_std))
            
            # 相关性分析
            if len(real_data.shape) == 2 and real_data.shape[1] > 1:
                real_corr = np.corrcoef(real_data.T)
                synthetic_corr = np.corrcoef(synthetic_data.T)
                corr_diff = np.mean(np.abs(real_corr - synthetic_corr))
            else:
                corr_diff = 0
            
            evaluation = {
                'mean_difference': float(mean_diff),
                'std_difference': float(std_diff),
                'correlation_difference': float(corr_diff),
                'real_data_shape': real_data.shape,
                'synthetic_data_shape': synthetic_data.shape,
                'quality_score': 1.0 - (mean_diff + std_diff + corr_diff) / 3  # 简单质量评分
            }
            
            logging.info(f"✅ 数据质量评估完成，质量评分: {evaluation['quality_score']:.4f}")
            return evaluation
            
        except Exception as e:
            logging.error(f"❌ 数据质量评估失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def conduct_research(self, data: Union[pd.DataFrame, Dict[str, Any]], **kwargs) -> ResearchResult:
        """
        进行GAN研究
        
        Args:
            data: 研究数据
            **kwargs: 其他参数
            
        Returns:
            研究结果
        """
        research_type = kwargs.get('research_type', 'synthetic_generation')
        model_type = kwargs.get('model_type', 'basic_gan')
        epochs = kwargs.get('epochs', 500)
        
        try:
            if isinstance(data, pd.DataFrame):
                # 预处理数据
                if model_type == 'time_series_gan':
                    sequences, scaler = self.preprocess_time_series_data(data)
                    if sequences is None:
                        raise ValueError("时间序列数据预处理失败")
                    
                    # 构建时间序列GAN
                    generator, discriminator, gan = self.build_time_series_gan(
                        sequences.shape[1], sequences.shape[2]
                    )
                    
                    if generator is None:
                        raise ValueError("时间序列GAN构建失败")
                    
                    # 训练模型
                    training_history = self.train_gan(
                        generator, discriminator, gan, sequences, 100, epochs
                    )
                    
                    # 生成合成数据
                    synthetic_data = self.generate_synthetic_data(
                        generator, 100, 100, scaler
                    )
                    
                    # 评估数据质量
                    if synthetic_data is not None:
                        # 为评估准备真实数据
                        real_eval_data = scaler.inverse_transform(
                            sequences[:100].reshape(-1, sequences.shape[-1])
                        ).reshape(100, sequences.shape[1], sequences.shape[2])
                        
                        evaluation = self.evaluate_generated_data(real_eval_data, synthetic_data)
                    else:
                        evaluation = {"status": "generation_failed"}
                    
                else:
                    # 基础GAN
                    scaler = MinMaxScaler(feature_range=(-1, 1))
                    scaled_data = scaler.fit_transform(data)
                    
                    # 构建基础GAN
                    generator = self.build_generator(100, scaled_data.shape[1])
                    discriminator = self.build_discriminator(scaled_data.shape[1])
                    gan = self.build_gan(generator, discriminator)
                    
                    if generator is None or discriminator is None or gan is None:
                        raise ValueError("基础GAN构建失败")
                    
                    # 训练模型
                    training_history = self.train_gan(
                        generator, discriminator, gan, scaled_data, 100, epochs
                    )
                    
                    # 生成合成数据
                    synthetic_data = self.generate_synthetic_data(
                        generator, 100, 100, scaler
                    )
                    
                    # 评估数据质量
                    if synthetic_data is not None:
                        real_eval_data = scaler.inverse_transform(scaled_data[:100])
                        evaluation = self.evaluate_generated_data(real_eval_data, synthetic_data)
                    else:
                        evaluation = {"status": "generation_failed"}
                
                research_results = {
                    'research_type': research_type,
                    'model_type': model_type,
                    'training_history': training_history,
                    'data_evaluation': evaluation,
                    'epochs_trained': epochs,
                    'synthetic_data_generated': synthetic_data is not None
                }
                
                # 保存模型和结果
                model_key = f"{model_type}_{research_type}"
                if model_type == 'time_series_gan':
                    self.generators[model_key] = generator
                    self.discriminators[model_key] = discriminator
                    self.gans[model_key] = gan
                else:
                    self.generators[model_key] = generator
                    self.discriminators[model_key] = discriminator
                    self.gans[model_key] = gan
                
                self.scalers[model_key] = scaler
                self.research_results[model_key] = research_results
                
            else:
                research_results = {"status": "unsupported_data_type", "data_type": type(data).__name__}
            
            # 创建研究结果
            result = ResearchResult(
                title=f"GAN Research: {research_type}",
                description=f"生成对抗网络研究 - {research_type} using {model_type}",
                methodology="Chapter20 GAN techniques for financial time series generation",
                results=research_results,
                confidence_score=0.8,
                generated_at=datetime.now(),
                metadata={
                    'researcher': self.name,
                    'research_type': research_type,
                    'model_type': model_type,
                    'deep_learning_available': DEEP_LEARNING_AVAILABLE
                }
            )
            
            self.research_history.append(result)
            return result
            
        except Exception as e:
            logging.error(f"❌ GAN研究失败: {e}")
            
            # 创建失败结果
            result = ResearchResult(
                title="GAN Research Failed",
                description=f"GAN研究失败: {str(e)}",
                methodology="Chapter20 GAN techniques",
                results={"status": "failed", "error": str(e)},
                confidence_score=0.0,
                generated_at=datetime.now(),
                metadata={'researcher': self.name, 'error': str(e)}
            )
            
            self.research_history.append(result)
            return result
    
    def validate_findings(self, result: ResearchResult) -> bool:
        """
        验证GAN研究结果
        
        Args:
            result: 研究结果
            
        Returns:
            验证是否通过
        """
        try:
            if not result.results:
                return False
            
            # 检查状态
            if result.results.get('status') == 'failed':
                return False
            
            # 检查数据评估
            evaluation = result.results.get('data_evaluation', {})
            if evaluation and 'quality_score' in evaluation:
                if evaluation['quality_score'] < 0.3:  # 质量评分过低
                    return False
            
            # 检查是否成功生成数据
            if not result.results.get('synthetic_data_generated', False):
                return False
            
            # 检查置信度
            if result.confidence_score < 0.6:
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 验证研究结果失败: {e}")
            return False
    
    def get_model_summary(self, model_key: str) -> Dict[str, Any]:
        """
        获取模型摘要
        
        Args:
            model_key: 模型键
            
        Returns:
            模型摘要
        """
        if model_key not in self.generators:
            return {"status": "model_not_found"}
        
        try:
            generator = self.generators[model_key]
            discriminator = self.discriminators[model_key]
            gan = self.gans[model_key]
            
            summary = {
                'model_key': model_key,
                'generator_params': generator.count_params(),
                'discriminator_params': discriminator.count_params(),
                'gan_params': gan.count_params(),
                'generator_layers': len(generator.layers),
                'discriminator_layers': len(discriminator.layers),
                'generator_input_shape': generator.input_shape,
                'generator_output_shape': generator.output_shape,
                'discriminator_input_shape': discriminator.input_shape
            }
            
            return summary
            
        except Exception as e:
            logging.error(f"❌ 获取模型摘要失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def get_research_summary(self) -> Dict[str, Any]:
        """
        获取GAN研究摘要
        
        Returns:
            研究摘要
        """
        base_summary = super().get_research_summary()
        
        # 添加GAN特定信息
        gan_summary = {
            'available_generators': list(self.generators.keys()),
            'available_discriminators': list(self.discriminators.keys()),
            'available_gans': list(self.gans.keys()),
            'model_count': len(self.generators),
            'research_results_count': len(self.research_results),
            'deep_learning_available': DEEP_LEARNING_AVAILABLE,
            'supported_research_types': ['synthetic_generation', 'data_augmentation', 'market_simulation'],
            'supported_model_types': ['basic_gan', 'time_series_gan']
        }
        
        base_summary.update(gan_summary)
        return base_summary

# 注册函数
def register_gan_researcher() -> GANResearcher:
    """
    注册GAN研究者
    
    Returns:
        GAN研究者实例
    """
    researcher = GANResearcher()
    logging.info(f"✅ 注册GAN研究者: {researcher.name}")
    return researcher

# 快速研究函数
def quick_gan_research(data: pd.DataFrame, research_type: str = 'synthetic_generation',
                      model_type: str = 'basic_gan', epochs: int = 500) -> Dict[str, Any]:
    """
    快速GAN研究
    
    Args:
        data: 研究数据
        research_type: 研究类型
        model_type: 模型类型
        epochs: 训练轮数
        
    Returns:
        研究结果
    """
    researcher = GANResearcher()
    result = researcher.conduct_research(data, research_type=research_type, 
                                       model_type=model_type, epochs=epochs)
    return result.results

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'price': np.cumsum(np.random.randn(1000) * 0.01) + 100,
        'volume': np.random.exponential(1000, 1000),
        'returns': np.random.randn(1000) * 0.02
    })
    
    # 进行快速研究
    result = quick_gan_research(test_data, 'synthetic_generation', 'basic_gan', 100)
    print("GAN研究结果:", result)