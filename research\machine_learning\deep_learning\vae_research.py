#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Variational Autoencoder Research Module
变分自动编码器研究模块

基于Chapter20的VAE技术，用于金融时间序列数据的概率建模和生成研究
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入基类
from research.base_classes.research_base import BaseFactorResearcher, ResearchResult

# 深度学习相关导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, Model, backend as K
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_squared_error
    DEEP_LEARNING_AVAILABLE = True
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    logging.warning(f"深度学习库不可用: {e}")

class VAEResearcher(BaseFactorResearcher):
    """
    变分自动编码器研究者
    
    基于Chapter20的VAE技术，专门用于金融时间序列数据的研究：
    1. 概率分布学习
    2. 不确定性量化
    3. 风险建模
    4. 异常检测
    5. 合成数据生成
    """
    
    def __init__(self, name: str = "VAEResearcher"):
        super().__init__(name, factor_categories=['vae', 'probabilistic', 'uncertainty'])
        self.vaes = {}
        self.encoders = {}
        self.decoders = {}
        self.scalers = {}
        self.research_results = {}
        
        if not DEEP_LEARNING_AVAILABLE:
            logging.warning("⚠️ 深度学习库不可用，某些功能将受限")
    
    def sampling_layer(self, args):
        """
        VAE采样层
        
        Args:
            args: [z_mean, z_log_var]
            
        Returns:
            采样的潜在向量
        """
        z_mean, z_log_var = args
        batch = K.shape(z_mean)[0]
        dim = K.int_shape(z_mean)[1]
        epsilon = K.random_normal(shape=(batch, dim))
        return z_mean + K.exp(0.5 * z_log_var) * epsilon
    
    def build_vae(self, input_dim: int, latent_dim: int = 32, 
                  hidden_dims: List[int] = [128, 64]) -> Optional[Tuple[Model, Model, Model]]:
        """
        构建变分自动编码器
        
        Args:
            input_dim: 输入维度
            latent_dim: 潜在空间维度
            hidden_dims: 隐藏层维度列表
            
        Returns:
            (VAE, 编码器, 解码器)模型元组
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 编码器
            encoder_inputs = keras.Input(shape=(input_dim,), name='encoder_input')
            x = encoder_inputs
            
            for dim in hidden_dims:
                x = layers.Dense(dim, activation='relu')(x)
            
            z_mean = layers.Dense(latent_dim, name='z_mean')(x)
            z_log_var = layers.Dense(latent_dim, name='z_log_var')(x)
            
            # 采样层
            z = layers.Lambda(self.sampling_layer, output_shape=(latent_dim,), name='z')([z_mean, z_log_var])
            
            encoder = Model(encoder_inputs, [z_mean, z_log_var, z], name='encoder')
            
            # 解码器
            decoder_inputs = keras.Input(shape=(latent_dim,), name='decoder_input')
            x = decoder_inputs
            
            for dim in reversed(hidden_dims):
                x = layers.Dense(dim, activation='relu')(x)
            
            decoder_outputs = layers.Dense(input_dim, activation='linear', name='decoder_output')(x)
            decoder = Model(decoder_inputs, decoder_outputs, name='decoder')
            
            # VAE
            vae_outputs = decoder(encoder(encoder_inputs)[2])
            vae = Model(encoder_inputs, vae_outputs, name='vae')
            
            # VAE损失函数
            def vae_loss(y_true, y_pred):
                # 重构损失
                reconstruction_loss = K.mean(K.square(y_true - y_pred))
                
                # KL散度损失
                kl_loss = -0.5 * K.mean(1 + z_log_var - K.square(z_mean) - K.exp(z_log_var))
                
                return reconstruction_loss + kl_loss
            
            vae.compile(optimizer='adam', loss=vae_loss, metrics=['mse'])
            
            logging.info(f"✅ 构建VAE成功，输入维度: {input_dim}, 潜在维度: {latent_dim}")
            return vae, encoder, decoder
            
        except Exception as e:
            logging.error(f"❌ 构建VAE失败: {e}")
            return None
    
    def build_time_series_vae(self, sequence_length: int, feature_dim: int, 
                             latent_dim: int = 32) -> Optional[Tuple[Model, Model, Model]]:
        """
        构建时间序列VAE
        
        Args:
            sequence_length: 序列长度
            feature_dim: 特征维度
            latent_dim: 潜在空间维度
            
        Returns:
            (VAE, 编码器, 解码器)模型元组
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 编码器
            encoder_inputs = keras.Input(shape=(sequence_length, feature_dim), name='encoder_input')
            x = layers.LSTM(128, return_sequences=True)(encoder_inputs)
            x = layers.LSTM(64)(x)
            x = layers.Dense(32, activation='relu')(x)
            
            z_mean = layers.Dense(latent_dim, name='z_mean')(x)
            z_log_var = layers.Dense(latent_dim, name='z_log_var')(x)
            
            # 采样层
            z = layers.Lambda(self.sampling_layer, output_shape=(latent_dim,), name='z')([z_mean, z_log_var])
            
            encoder = Model(encoder_inputs, [z_mean, z_log_var, z], name='ts_encoder')
            
            # 解码器
            decoder_inputs = keras.Input(shape=(latent_dim,), name='decoder_input')
            x = layers.Dense(32, activation='relu')(decoder_inputs)
            x = layers.Dense(64, activation='relu')(x)
            x = layers.RepeatVector(sequence_length)(x)
            x = layers.LSTM(64, return_sequences=True)(x)
            x = layers.LSTM(128, return_sequences=True)(x)
            decoder_outputs = layers.TimeDistributed(layers.Dense(feature_dim))(x)
            
            decoder = Model(decoder_inputs, decoder_outputs, name='ts_decoder')
            
            # VAE
            vae_outputs = decoder(encoder(encoder_inputs)[2])
            vae = Model(encoder_inputs, vae_outputs, name='ts_vae')
            
            # VAE损失函数
            def vae_loss(y_true, y_pred):
                # 重构损失
                reconstruction_loss = K.mean(K.square(y_true - y_pred))
                
                # KL散度损失
                kl_loss = -0.5 * K.mean(1 + z_log_var - K.square(z_mean) - K.exp(z_log_var))
                
                return reconstruction_loss + 0.1 * kl_loss  # 调整KL权重
            
            vae.compile(optimizer='adam', loss=vae_loss, metrics=['mse'])
            
            logging.info(f"✅ 构建时间序列VAE成功，序列长度: {sequence_length}, 特征维度: {feature_dim}")
            return vae, encoder, decoder
            
        except Exception as e:
            logging.error(f"❌ 构建时间序列VAE失败: {e}")
            return None
    
    def preprocess_time_series_data(self, data: pd.DataFrame, 
                                   sequence_length: int = 60,
                                   scaling_method: str = 'standard') -> Tuple[np.ndarray, Any]:
        """
        预处理时间序列数据
        
        Args:
            data: 时间序列数据
            sequence_length: 序列长度
            scaling_method: 缩放方法
            
        Returns:
            处理后的数据和缩放器
        """
        try:
            # 选择缩放器
            if scaling_method == 'standard':
                scaler = StandardScaler()
            elif scaling_method == 'minmax':
                scaler = MinMaxScaler()
            else:
                raise ValueError(f"不支持的缩放方法: {scaling_method}")
            
            # 缩放数据
            scaled_data = scaler.fit_transform(data)
            
            # 创建序列
            sequences = []
            for i in range(len(scaled_data) - sequence_length + 1):
                sequences.append(scaled_data[i:i + sequence_length])
            
            sequences = np.array(sequences)
            
            logging.info(f"✅ 数据预处理完成，序列形状: {sequences.shape}")
            return sequences, scaler
            
        except Exception as e:
            logging.error(f"❌ 数据预处理失败: {e}")
            return None, None
    
    def generate_samples(self, decoder: Model, num_samples: int, 
                        latent_dim: int, scaler: Any = None) -> Optional[np.ndarray]:
        """
        从潜在空间生成样本
        
        Args:
            decoder: 解码器模型
            num_samples: 样本数量
            latent_dim: 潜在空间维度
            scaler: 数据缩放器
            
        Returns:
            生成的样本
        """
        if not DEEP_LEARNING_AVAILABLE or decoder is None:
            logging.error("❌ 解码器不可用")
            return None
            
        try:
            # 从标准正态分布采样
            z_samples = np.random.normal(0, 1, (num_samples, latent_dim))
            
            # 生成样本
            generated_samples = decoder.predict(z_samples, verbose=0)
            
            # 反缩放
            if scaler is not None:
                if len(generated_samples.shape) == 3:  # 时间序列数据
                    original_shape = generated_samples.shape
                    generated_samples = generated_samples.reshape(-1, generated_samples.shape[-1])
                    generated_samples = scaler.inverse_transform(generated_samples)
                    generated_samples = generated_samples.reshape(original_shape)
                else:
                    generated_samples = scaler.inverse_transform(generated_samples)
            
            logging.info(f"✅ 生成样本成功，形状: {generated_samples.shape}")
            return generated_samples
            
        except Exception as e:
            logging.error(f"❌ 生成样本失败: {e}")
            return None
    
    def compute_uncertainty(self, encoder: Model, data: np.ndarray, 
                           num_samples: int = 100) -> Dict[str, Any]:
        """
        计算不确定性
        
        Args:
            encoder: 编码器模型
            data: 输入数据
            num_samples: 采样次数
            
        Returns:
            不确定性分析结果
        """
        if not DEEP_LEARNING_AVAILABLE or encoder is None:
            logging.error("❌ 编码器不可用")
            return {"status": "failed", "error": "Encoder not available"}
            
        try:
            # 获取编码结果
            encoded_results = encoder.predict(data, verbose=0)
            z_mean, z_log_var = encoded_results[0], encoded_results[1]
            
            # 计算不确定性指标
            epistemic_uncertainty = np.mean(np.exp(z_log_var), axis=1)  # 认知不确定性
            aleatoric_uncertainty = np.var(z_mean, axis=1)  # 偶然不确定性
            total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty
            
            # 多次采样计算预测不确定性
            predictions = []
            for _ in range(num_samples):
                # 从潜在分布采样
                epsilon = np.random.normal(0, 1, z_mean.shape)
                z_sample = z_mean + np.exp(0.5 * z_log_var) * epsilon
                predictions.append(z_sample)
            
            predictions = np.array(predictions)
            prediction_uncertainty = np.var(predictions, axis=0)
            prediction_mean = np.mean(predictions, axis=0)
            
            uncertainty_results = {
                'epistemic_uncertainty': epistemic_uncertainty,
                'aleatoric_uncertainty': aleatoric_uncertainty,
                'total_uncertainty': total_uncertainty,
                'prediction_uncertainty': prediction_uncertainty,
                'prediction_mean': prediction_mean,
                'mean_epistemic': float(np.mean(epistemic_uncertainty)),
                'mean_aleatoric': float(np.mean(aleatoric_uncertainty)),
                'mean_total': float(np.mean(total_uncertainty))
            }
            
            logging.info("✅ 不确定性计算完成")
            return uncertainty_results
            
        except Exception as e:
            logging.error(f"❌ 不确定性计算失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def detect_anomalies_with_uncertainty(self, vae: Model, encoder: Model, 
                                         data: np.ndarray, threshold_percentile: float = 95) -> Dict[str, Any]:
        """
        基于不确定性的异常检测
        
        Args:
            vae: VAE模型
            encoder: 编码器模型
            data: 测试数据
            threshold_percentile: 异常阈值百分位数
            
        Returns:
            异常检测结果
        """
        if not DEEP_LEARNING_AVAILABLE or vae is None or encoder is None:
            logging.error("❌ 模型不可用")
            return {"status": "failed", "error": "Models not available"}
            
        try:
            # 重构误差
            reconstructed = vae.predict(data, verbose=0)
            reconstruction_errors = np.mean(np.square(data - reconstructed), axis=tuple(range(1, len(data.shape))))
            
            # 不确定性分析
            uncertainty_results = self.compute_uncertainty(encoder, data)
            total_uncertainty = uncertainty_results.get('total_uncertainty', np.zeros(len(data)))
            
            # 综合异常评分（重构误差 + 不确定性）
            anomaly_scores = reconstruction_errors + total_uncertainty
            
            # 确定异常阈值
            threshold = np.percentile(anomaly_scores, threshold_percentile)
            
            # 识别异常
            anomalies = anomaly_scores > threshold
            anomaly_indices = np.where(anomalies)[0]
            
            results = {
                'reconstruction_errors': reconstruction_errors,
                'uncertainty_scores': total_uncertainty,
                'anomaly_scores': anomaly_scores,
                'threshold': threshold,
                'anomalies': anomalies,
                'anomaly_indices': anomaly_indices.tolist(),
                'anomaly_count': len(anomaly_indices),
                'anomaly_rate': len(anomaly_indices) / len(data),
                'mean_reconstruction_error': float(np.mean(reconstruction_errors)),
                'mean_uncertainty': float(np.mean(total_uncertainty)),
                'mean_anomaly_score': float(np.mean(anomaly_scores))
            }
            
            logging.info(f"✅ 基于不确定性的异常检测完成，发现 {len(anomaly_indices)} 个异常点")
            return results
            
        except Exception as e:
            logging.error(f"❌ 异常检测失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def conduct_research(self, data: Union[pd.DataFrame, Dict[str, Any]], **kwargs) -> ResearchResult:
        """
        进行VAE研究
        
        Args:
            data: 研究数据
            **kwargs: 其他参数
            
        Returns:
            研究结果
        """
        research_type = kwargs.get('research_type', 'uncertainty_analysis')
        model_type = kwargs.get('model_type', 'basic_vae')
        epochs = kwargs.get('epochs', 100)
        
        try:
            if isinstance(data, pd.DataFrame):
                # 预处理数据
                if model_type == 'time_series_vae':
                    sequences, scaler = self.preprocess_time_series_data(data)
                    if sequences is None:
                        raise ValueError("时间序列数据预处理失败")
                    
                    # 构建时间序列VAE
                    vae, encoder, decoder = self.build_time_series_vae(
                        sequences.shape[1], sequences.shape[2]
                    )
                    
                    if vae is None:
                        raise ValueError("时间序列VAE构建失败")
                    
                    # 分割数据
                    X_train, X_test = train_test_split(sequences, test_size=0.2, random_state=42)
                    
                    # 训练模型
                    history = vae.fit(
                        X_train, X_train,
                        epochs=epochs,
                        batch_size=32,
                        validation_data=(X_test, X_test),
                        verbose=0
                    )
                    
                else:
                    # 基础VAE
                    scaler = StandardScaler()
                    scaled_data = scaler.fit_transform(data)
                    
                    # 构建基础VAE
                    vae, encoder, decoder = self.build_vae(scaled_data.shape[1])
                    
                    if vae is None:
                        raise ValueError("基础VAE构建失败")
                    
                    # 分割数据
                    X_train, X_test = train_test_split(scaled_data, test_size=0.2, random_state=42)
                    
                    # 训练模型
                    history = vae.fit(
                        X_train, X_train,
                        epochs=epochs,
                        batch_size=32,
                        validation_data=(X_test, X_test),
                        verbose=0
                    )
                
                # 进行研究分析
                if research_type == 'uncertainty_analysis':
                    uncertainty_results = self.compute_uncertainty(encoder, X_test)
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'uncertainty_analysis': uncertainty_results,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                    
                elif research_type == 'anomaly_detection':
                    anomaly_results = self.detect_anomalies_with_uncertainty(vae, encoder, X_test)
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'anomaly_detection': anomaly_results,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                    
                elif research_type == 'generation':
                    generated_samples = self.generate_samples(decoder, 100, 
                                                            encoder.output_shape[0][1], scaler)
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'generated_samples_shape': generated_samples.shape if generated_samples is not None else None,
                        'generation_successful': generated_samples is not None,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                    
                else:
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                
                # 保存模型和结果
                model_key = f"{model_type}_{research_type}"
                self.vaes[model_key] = vae
                self.encoders[model_key] = encoder
                self.decoders[model_key] = decoder
                self.scalers[model_key] = scaler
                self.research_results[model_key] = research_results
                
            else:
                research_results = {"status": "unsupported_data_type", "data_type": type(data).__name__}
            
            # 创建研究结果
            result = ResearchResult(
                title=f"VAE Research: {research_type}",
                description=f"变分自动编码器研究 - {research_type} using {model_type}",
                methodology="Chapter20 VAE techniques for probabilistic modeling",
                results=research_results,
                confidence_score=0.85,
                generated_at=datetime.now(),
                metadata={
                    'researcher': self.name,
                    'research_type': research_type,
                    'model_type': model_type,
                    'deep_learning_available': DEEP_LEARNING_AVAILABLE
                }
            )
            
            self.research_history.append(result)
            return result
            
        except Exception as e:
            logging.error(f"❌ VAE研究失败: {e}")
            
            # 创建失败结果
            result = ResearchResult(
                title="VAE Research Failed",
                description=f"VAE研究失败: {str(e)}",
                methodology="Chapter20 VAE techniques",
                results={"status": "failed", "error": str(e)},
                confidence_score=0.0,
                generated_at=datetime.now(),
                metadata={'researcher': self.name, 'error': str(e)}
            )
            
            self.research_history.append(result)
            return result
    
    def validate_findings(self, result: ResearchResult) -> bool:
        """
        验证VAE研究结果
        
        Args:
            result: 研究结果
            
        Returns:
            验证是否通过
        """
        try:
            if not result.results:
                return False
            
            # 检查状态
            if result.results.get('status') == 'failed':
                return False
            
            # 检查训练历史
            training_history = result.results.get('training_history', {})
            if training_history:
                final_loss = training_history.get('final_loss', float('inf'))
                if final_loss > 10.0:  # VAE损失通常较高
                    return False
            
            # 检查不确定性分析结果
            if 'uncertainty_analysis' in result.results:
                uncertainty = result.results['uncertainty_analysis']
                if uncertainty.get('status') == 'failed':
                    return False
            
            # 检查置信度
            if result.confidence_score < 0.6:
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 验证研究结果失败: {e}")
            return False
    
    def get_model_summary(self, model_key: str) -> Dict[str, Any]:
        """
        获取模型摘要
        
        Args:
            model_key: 模型键
            
        Returns:
            模型摘要
        """
        if model_key not in self.vaes:
            return {"status": "model_not_found"}
        
        try:
            vae = self.vaes[model_key]
            encoder = self.encoders[model_key]
            decoder = self.decoders[model_key]
            
            summary = {
                'model_key': model_key,
                'vae_params': vae.count_params(),
                'encoder_params': encoder.count_params(),
                'decoder_params': decoder.count_params(),
                'vae_layers': len(vae.layers),
                'encoder_layers': len(encoder.layers),
                'decoder_layers': len(decoder.layers),
                'input_shape': vae.input_shape,
                'output_shape': vae.output_shape,
                'latent_dim': encoder.output_shape[0][1]
            }
            
            return summary
            
        except Exception as e:
            logging.error(f"❌ 获取模型摘要失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def get_research_summary(self) -> Dict[str, Any]:
        """
        获取VAE研究摘要
        
        Returns:
            研究摘要
        """
        base_summary = super().get_research_summary()
        
        # 添加VAE特定信息
        vae_summary = {
            'available_vaes': list(self.vaes.keys()),
            'available_encoders': list(self.encoders.keys()),
            'available_decoders': list(self.decoders.keys()),
            'model_count': len(self.vaes),
            'research_results_count': len(self.research_results),
            'deep_learning_available': DEEP_LEARNING_AVAILABLE,
            'supported_research_types': ['uncertainty_analysis', 'anomaly_detection', 'generation', 'risk_modeling'],
            'supported_model_types': ['basic_vae', 'time_series_vae']
        }
        
        base_summary.update(vae_summary)
        return base_summary

# 注册函数
def register_vae_researcher() -> VAEResearcher:
    """
    注册VAE研究者
    
    Returns:
        VAE研究者实例
    """
    researcher = VAEResearcher()
    logging.info(f"✅ 注册VAE研究者: {researcher.name}")
    return researcher

# 快速研究函数
def quick_vae_research(data: pd.DataFrame, research_type: str = 'uncertainty_analysis',
                      model_type: str = 'basic_vae', epochs: int = 100) -> Dict[str, Any]:
    """
    快速VAE研究
    
    Args:
        data: 研究数据
        research_type: 研究类型
        model_type: 模型类型
        epochs: 训练轮数
        
    Returns:
        研究结果
    """
    researcher = VAEResearcher()
    result = researcher.conduct_research(data, research_type=research_type, 
                                       model_type=model_type, epochs=epochs)
    return result.results

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'price': np.cumsum(np.random.randn(1000) * 0.01) + 100,
        'volume': np.random.exponential(1000, 1000),
        'returns': np.random.randn(1000) * 0.02
    })
    
    # 进行快速研究
    result = quick_vae_research(test_data, 'uncertainty_analysis', 'basic_vae', 50)
    print("VAE研究结果:", result)