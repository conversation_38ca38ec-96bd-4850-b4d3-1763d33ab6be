# -*- coding: utf-8 -*-
"""
投资组合优化策略
Portfolio Optimization Strategy

基于现代投资组合理论的策略实现
集成均值方差优化和有效前沿计算
适配统一策略管理系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
import json

# 导入基础策略类
try:
    from ..base_ml_strategy import BaseMLStrategy
    BASE_STRATEGY_AVAILABLE = True
except ImportError:
    try:
        from strategy_autodev.core.base_strategy import BaseStrategy as BaseMLStrategy
        BASE_STRATEGY_AVAILABLE = True
    except ImportError:
        try:
            from ml_strategies.base_strategy import BaseMLStrategy
            BASE_STRATEGY_AVAILABLE = True
        except ImportError:
            BASE_STRATEGY_AVAILABLE = False
            logging.warning("基础策略类不可用，使用简化实现")

# 导入优化器
from .mean_variance_optimizer import MeanVarianceOptimizer
from .efficient_frontier_calculator import EfficientFrontierCalculator

# 导入数据适配器
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    DATA_ADAPTER_AVAILABLE = False
    logging.warning("数据适配器不可用")

logger = logging.getLogger(__name__)

# 如果基础策略类不可用，定义简化版本
if not BASE_STRATEGY_AVAILABLE:
    class BaseMLStrategy:
        def __init__(self, **kwargs):
            self.params = kwargs
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def fit(self, data):
            pass
        
        def predict(self, data):
            return None
        
        def backtest(self, start_date, end_date):
            return {}

class PortfolioOptimizationStrategy(BaseMLStrategy):
    """
    投资组合优化策略
    
    基于Markowitz现代投资组合理论的策略实现
    支持多种优化目标和约束条件
    """
    
    def __init__(self, 
                 symbols: List[str],
                 optimization_type: str = 'max_sharpe',
                 risk_free_rate: float = 0.02,
                 trading_days: int = 252,
                 rebalance_frequency: str = 'monthly',
                 lookback_window: int = 252,
                 min_weight: float = 0.0,
                 max_weight: float = 1.0,
                 **kwargs):
        """
        初始化投资组合优化策略
        
        Args:
            symbols: 股票代码列表
            optimization_type: 优化类型 ('max_sharpe', 'min_vol', 'target_return')
            risk_free_rate: 无风险利率
            trading_days: 年交易日数
            rebalance_frequency: 再平衡频率 ('daily', 'weekly', 'monthly', 'quarterly')
            lookback_window: 历史数据回望窗口
            min_weight: 最小权重约束
            max_weight: 最大权重约束
        """
        super().__init__(**kwargs)
        
        self.symbols = symbols
        self.optimization_type = optimization_type
        self.risk_free_rate = risk_free_rate
        self.trading_days = trading_days
        self.rebalance_frequency = rebalance_frequency
        self.lookback_window = lookback_window
        self.min_weight = min_weight
        self.max_weight = max_weight
        
        # 初始化优化器
        self.optimizer = MeanVarianceOptimizer(risk_free_rate, trading_days)
        self.frontier_calculator = EfficientFrontierCalculator(risk_free_rate, trading_days)
        
        # 初始化数据管理器
        if DATA_ADAPTER_AVAILABLE:
            self.data_manager = get_data_adapter_manager()
        else:
            self.data_manager = None
        
        # 策略状态
        self.current_weights = None
        self.last_rebalance_date = None
        self.performance_history = []
        self.rebalance_history = []
        
        self.logger = logging.getLogger(f"{__name__}.PortfolioOptimizationStrategy")
        self.logger.info(f"投资组合优化策略初始化完成: {optimization_type}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        """
        return {
            'name': 'PortfolioOptimizationStrategy',
            'description': '基于现代投资组合理论的优化策略',
            'category': 'portfolio_optimization',
            'symbols': self.symbols,
            'optimization_type': self.optimization_type,
            'risk_free_rate': self.risk_free_rate,
            'rebalance_frequency': self.rebalance_frequency,
            'lookback_window': self.lookback_window,
            'constraints': {
                'min_weight': self.min_weight,
                'max_weight': self.max_weight
            }
        }
    
    def _should_rebalance(self, current_date: datetime) -> bool:
        """
        判断是否需要再平衡
        """
        if self.last_rebalance_date is None:
            return True
        
        days_since_rebalance = (current_date - self.last_rebalance_date).days
        
        if self.rebalance_frequency == 'daily':
            return days_since_rebalance >= 1
        elif self.rebalance_frequency == 'weekly':
            return days_since_rebalance >= 7
        elif self.rebalance_frequency == 'monthly':
            return days_since_rebalance >= 30
        elif self.rebalance_frequency == 'quarterly':
            return days_since_rebalance >= 90
        else:
            return False
    
    def _get_rebalance_dates(self, start_date: str, end_date: str) -> List[datetime]:
        """
        获取再平衡日期列表
        """
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        if self.rebalance_frequency == 'daily':
            freq = 'D'
        elif self.rebalance_frequency == 'weekly':
            freq = 'W'
        elif self.rebalance_frequency == 'monthly':
            freq = 'M'
        elif self.rebalance_frequency == 'quarterly':
            freq = 'Q'
        else:
            freq = 'M'  # 默认月度
        
        dates = pd.date_range(start=start, end=end, freq=freq)
        return [date.to_pydatetime() for date in dates]
    
    def optimize_portfolio(self, 
                         prices: pd.DataFrame,
                         end_date: datetime) -> Optional[Dict[str, float]]:
        """
        优化投资组合权重
        
        Args:
            prices: 价格数据
            end_date: 优化截止日期
            
        Returns:
            优化后的权重字典
        """
        try:
            # 获取历史数据窗口
            end_idx = prices.index.get_loc(end_date, method='nearest')
            start_idx = max(0, end_idx - self.lookback_window)
            
            window_prices = prices.iloc[start_idx:end_idx+1]
            
            if len(window_prices) < 30:  # 最少需要30个观测值
                self.logger.warning(f"历史数据不足: {len(window_prices)}")
                return None
            
            # 计算收益率
            returns = self.optimizer.calculate_returns(window_prices)
            mean_returns = returns.mean()
            cov_matrix = returns.cov()
            
            # 执行优化
            if self.optimization_type == 'max_sharpe':
                result = self.optimizer.optimize_max_sharpe(mean_returns, cov_matrix)
            elif self.optimization_type == 'min_vol':
                result = self.optimizer.optimize_min_volatility(mean_returns, cov_matrix)
            else:
                self.logger.error(f"不支持的优化类型: {self.optimization_type}")
                return None
            
            if result.get('success'):
                # 应用权重约束
                weights = result['weights']
                constrained_weights = self._apply_weight_constraints(weights)
                return constrained_weights
            else:
                self.logger.error(f"优化失败: {result.get('message')}")
                return None
                
        except Exception as e:
            self.logger.error(f"投资组合优化失败: {e}")
            return None
    
    def _apply_weight_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """
        应用权重约束
        """
        # 应用最小最大权重约束
        constrained_weights = {}
        for symbol, weight in weights.items():
            constrained_weights[symbol] = np.clip(weight, self.min_weight, self.max_weight)
        
        # 重新归一化
        total_weight = sum(constrained_weights.values())
        if total_weight > 0:
            for symbol in constrained_weights:
                constrained_weights[symbol] /= total_weight
        
        return constrained_weights
    
    def calculate_portfolio_return(self, 
                                 weights: Dict[str, float],
                                 returns: pd.Series) -> float:
        """
        计算投资组合收益率
        """
        portfolio_return = 0.0
        for symbol, weight in weights.items():
            if symbol in returns.index:
                portfolio_return += weight * returns[symbol]
        return portfolio_return
    
    def fit(self, data: pd.DataFrame) -> None:
        """
        训练策略（对于投资组合优化，主要是验证数据）
        """
        self.logger.info("开始训练投资组合优化策略")
        
        # 验证数据包含所需的股票
        missing_symbols = set(self.symbols) - set(data.columns)
        if missing_symbols:
            self.logger.warning(f"缺少股票数据: {missing_symbols}")
        
        # 计算基础统计信息
        returns = self.optimizer.calculate_returns(data)
        self.logger.info(f"训练数据期间: {data.index[0]} 到 {data.index[-1]}")
        self.logger.info(f"数据观测数: {len(data)}")
        self.logger.info(f"平均收益率: {returns.mean().to_dict()}")
        
        self.logger.info("投资组合优化策略训练完成")
    
    def predict(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        预测（生成投资组合权重）
        """
        if data.empty:
            return {symbol: 1.0/len(self.symbols) for symbol in self.symbols}
        
        # 使用最新日期进行优化
        latest_date = data.index[-1]
        weights = self.optimize_portfolio(data, latest_date)
        
        if weights is None:
            # 如果优化失败，返回等权重
            return {symbol: 1.0/len(self.symbols) for symbol in self.symbols}
        
        return weights
    
    def backtest(self, 
                start_date: str, 
                end_date: str,
                initial_capital: float = 1000000.0) -> Dict[str, Any]:
        """
        回测策略
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            
        Returns:
            回测结果
        """
        try:
            self.logger.info(f"开始回测: {start_date} 到 {end_date}")
            
            # 获取价格数据
            prices = self.optimizer.get_market_data(self.symbols, start_date, end_date)
            if prices.empty:
                return {'success': False, 'message': '无法获取价格数据'}
            
            # 计算收益率
            returns = self.optimizer.calculate_returns(prices)
            
            # 获取再平衡日期
            rebalance_dates = self._get_rebalance_dates(start_date, end_date)
            
            # 初始化回测变量
            portfolio_values = []
            portfolio_returns = []
            weights_history = []
            current_weights = None
            
            # 遍历每个交易日
            for date in returns.index:
                date_dt = date.to_pydatetime() if hasattr(date, 'to_pydatetime') else date
                
                # 检查是否需要再平衡
                if any(abs((date_dt - rb_date).days) <= 1 for rb_date in rebalance_dates):
                    # 执行再平衡
                    new_weights = self.optimize_portfolio(prices, date_dt)
                    if new_weights is not None:
                        current_weights = new_weights
                        self.rebalance_history.append({
                            'date': date_dt,
                            'weights': current_weights.copy()
                        })
                        self.logger.debug(f"再平衡于 {date_dt}: {current_weights}")
                
                # 如果还没有权重，使用等权重
                if current_weights is None:
                    current_weights = {symbol: 1.0/len(self.symbols) for symbol in self.symbols}
                
                # 计算当日投资组合收益率
                daily_return = self.calculate_portfolio_return(current_weights, returns.loc[date])
                portfolio_returns.append(daily_return)
                
                # 记录权重历史
                weights_history.append({
                    'date': date_dt,
                    'weights': current_weights.copy(),
                    'return': daily_return
                })
            
            # 计算累计收益
            portfolio_returns_series = pd.Series(portfolio_returns, index=returns.index)
            cumulative_returns = (1 + portfolio_returns_series).cumprod()
            portfolio_values = cumulative_returns * initial_capital
            
            # 计算性能指标
            total_return = cumulative_returns.iloc[-1] - 1
            annual_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
            annual_volatility = portfolio_returns_series.std() * np.sqrt(252)
            sharpe_ratio = (annual_return - self.risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
            
            # 计算最大回撤
            rolling_max = cumulative_returns.expanding().max()
            drawdowns = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdowns.min()
            
            # 生成回测报告
            backtest_result = {
                'success': True,
                'strategy_info': self.get_strategy_info(),
                'backtest_period': f"{start_date} to {end_date}",
                'performance_metrics': {
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'annual_volatility': annual_volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'final_value': portfolio_values.iloc[-1]
                },
                'portfolio_data': {
                    'returns': portfolio_returns_series.to_dict(),
                    'cumulative_returns': cumulative_returns.to_dict(),
                    'portfolio_values': portfolio_values.to_dict(),
                    'weights_history': weights_history
                },
                'rebalance_history': self.rebalance_history,
                'statistics': {
                    'n_observations': len(portfolio_returns),
                    'n_rebalances': len(self.rebalance_history),
                    'avg_daily_return': portfolio_returns_series.mean(),
                    'daily_volatility': portfolio_returns_series.std()
                }
            }
            
            self.logger.info(f"回测完成 - 总收益率: {total_return:.4f}, 夏普比率: {sharpe_ratio:.4f}")
            return backtest_result
            
        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            return {'success': False, 'message': str(e)}
    
    def generate_analysis_report(self, backtest_result: Dict[str, Any]) -> str:
        """
        生成分析报告
        """
        if not backtest_result.get('success'):
            return "回测失败，无法生成报告"
        
        metrics = backtest_result['performance_metrics']
        stats = backtest_result['statistics']
        
        report = f"""
=== 投资组合优化策略分析报告 ===

策略信息:
- 策略名称: {backtest_result['strategy_info']['name']}
- 优化类型: {backtest_result['strategy_info']['optimization_type']}
- 再平衡频率: {backtest_result['strategy_info']['rebalance_frequency']}
- 回测期间: {backtest_result['backtest_period']}

性能指标:
- 总收益率: {metrics['total_return']:.4f} ({metrics['total_return']*100:.2f}%)
- 年化收益率: {metrics['annual_return']:.4f} ({metrics['annual_return']*100:.2f}%)
- 年化波动率: {metrics['annual_volatility']:.4f} ({metrics['annual_volatility']*100:.2f}%)
- 夏普比率: {metrics['sharpe_ratio']:.4f}
- 最大回撤: {metrics['max_drawdown']:.4f} ({metrics['max_drawdown']*100:.2f}%)
- 最终价值: ${metrics['final_value']:,.2f}

统计信息:
- 观测数量: {stats['n_observations']}
- 再平衡次数: {stats['n_rebalances']}
- 平均日收益率: {stats['avg_daily_return']:.6f}
- 日波动率: {stats['daily_volatility']:.6f}

最新权重分配:
"""
        
        if backtest_result['rebalance_history']:
            latest_weights = backtest_result['rebalance_history'][-1]['weights']
            for symbol, weight in latest_weights.items():
                report += f"- {symbol}: {weight:.4f} ({weight*100:.2f}%)\n"
        
        return report


# 策略注册函数
def create_portfolio_optimization_strategy(**kwargs) -> PortfolioOptimizationStrategy:
    """
    创建投资组合优化策略实例
    """
    return PortfolioOptimizationStrategy(**kwargs)


if __name__ == "__main__":
    # 测试代码
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN']
    
    strategy = PortfolioOptimizationStrategy(
        symbols=symbols,
        optimization_type='max_sharpe',
        rebalance_frequency='monthly'
    )
    
    # 运行回测
    result = strategy.backtest('2023-01-01', '2023-12-31')
    
    if result.get('success'):
        print(strategy.generate_analysis_report(result))
    else:
        print(f"回测失败: {result.get('message')}")