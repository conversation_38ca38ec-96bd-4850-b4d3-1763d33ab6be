# -*- coding: utf-8 -*-
"""
CNN时间序列策略测试
CNN Time Series Strategy Test

测试CNN策略的功能和性能
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入策略
from cnn_time_series_strategy import CNNTimeSeriesStrategy

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_sample_data(n_days=1000):
    """
    生成样本数据用于测试
    """
    np.random.seed(42)
    
    # 生成日期序列
    dates = pd.date_range(start='2020-01-01', periods=n_days, freq='D')
    
    # 生成价格数据（随机游走 + 趋势）
    returns = np.random.normal(0.001, 0.02, n_days)
    prices = [100]  # 初始价格
    
    for i in range(1, n_days):
        # 添加一些趋势和均值回归
        trend = 0.0001 * np.sin(i / 50)  # 周期性趋势
        mean_reversion = -0.1 * (prices[-1] / 100 - 1)  # 均值回归
        price_change = returns[i] + trend + mean_reversion
        new_price = prices[-1] * (1 + price_change)
        prices.append(new_price)
    
    # 生成OHLC数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # 生成开高低收
        open_price = price * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, price) * (1 + abs(np.random.normal(0, 0.01)))
        low_price = min(open_price, price) * (1 - abs(np.random.normal(0, 0.01)))
        close_price = price
        
        # 生成成交量
        volume = np.random.lognormal(10, 0.5)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df

def test_cnn_strategy():
    """
    测试CNN策略
    """
    logger.info("开始测试CNN时间序列策略")
    
    try:
        # 生成测试数据
        logger.info("生成测试数据...")
        data = generate_sample_data(1000)
        logger.info(f"生成数据: {len(data)} 行")
        
        # 创建策略实例
        logger.info("创建CNN策略实例...")
        strategy = CNNTimeSeriesStrategy(
            filters=16,
            kernel_size=3,
            sequence_length=20,
            prediction_type='classification',
            learning_rate=0.001,
            batch_size=32,
            epochs=10,
            validation_split=0.2
        )
        
        # 分割数据
        train_size = int(len(data) * 0.8)
        train_data = data[:train_size]
        test_data = data[train_size:]
        
        logger.info(f"训练数据: {len(train_data)} 行, 测试数据: {len(test_data)} 行")
        
        # 训练策略
        logger.info("开始训练策略...")
        success = strategy.train(train_data)
        
        if not success:
            logger.error("策略训练失败")
            return False
        
        logger.info("策略训练成功")
        
        # 生成预测
        logger.info("生成预测...")
        predictions = strategy.predict(test_data)
        logger.info(f"生成预测: {len(predictions)} 个")
        
        # 生成交易信号
        logger.info("生成交易信号...")
        signals = strategy.generate_signals(test_data)
        logger.info(f"生成信号: {len(signals)} 个")
        
        # 打印信号统计
        if len(signals) > 0:
            buy_signals = (signals['signal'] == 1).sum()
            sell_signals = (signals['signal'] == -1).sum()
            avg_confidence = signals['confidence'].mean()
            
            logger.info(f"信号统计: 买入={buy_signals}, 卖出={sell_signals}, 平均置信度={avg_confidence:.3f}")
        
        # 获取模型摘要
        summary = strategy.get_model_summary()
        logger.info(f"模型摘要: {summary}")
        
        logger.info("CNN策略测试完成")
        return True
        
    except Exception as e:
        logger.error(f"CNN策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_preparation():
    """
    测试数据准备功能
    """
    logger.info("测试数据准备功能")
    
    try:
        # 生成测试数据
        data = generate_sample_data(100)
        
        # 创建策略实例
        strategy = CNNTimeSeriesStrategy(sequence_length=10)
        
        # 测试序列准备
        X, y = strategy.prepare_sequences(data)
        
        logger.info(f"序列数据形状: X={X.shape}, y={y.shape}")
        
        if len(X) > 0:
            logger.info(f"特征维度: {X.shape[2]}")
            logger.info(f"序列长度: {X.shape[1]}")
            logger.info(f"样本数量: {X.shape[0]}")
            
            # 检查数据质量
            if np.isnan(X).any():
                logger.warning("特征数据包含NaN值")
            if np.isnan(y).any():
                logger.warning("标签数据包含NaN值")
            
            logger.info("数据准备测试通过")
            return True
        else:
            logger.error("序列数据为空")
            return False
            
    except Exception as e:
        logger.error(f"数据准备测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("开始CNN策略测试套件")
    
    # 测试数据准备
    if test_data_preparation():
        logger.info("✓ 数据准备测试通过")
    else:
        logger.error("✗ 数据准备测试失败")
    
    # 测试完整策略
    if test_cnn_strategy():
        logger.info("✓ CNN策略测试通过")
    else:
        logger.error("✗ CNN策略测试失败")
    
    logger.info("测试套件完成")