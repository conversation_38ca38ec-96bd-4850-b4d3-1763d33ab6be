# -*- coding: utf-8 -*-
"""
ADWM因子加权系统单元测试
测试自适应动态因子加权模型的各个组件
"""

import unittest
import numpy as np
import pandas as pd
import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factor_analyze.tools.adwm_factor_weighting_system import (
    ADWMConfig, GatingMechanism, ADWMModel, ADWMFactorWeightingSystem
)


class TestADWMComponents(unittest.TestCase):
    """测试ADWM模型组件"""
    
    def setUp(self):
        """初始化测试环境"""
        self.config = ADWMConfig()
        self.num_factors = 6
        self.batch_size = 32
        
    def test_gating_mechanism(self):
        """测试门控机制"""
        gating = GatingMechanism(
            input_dim=self.num_factors,
            num_gates=self.config.num_gates,
            hidden_dim=self.config.hidden_dim
        )
        
        # 创建测试输入
        x = torch.randn(self.batch_size, self.num_factors)
        
        # 前向传播
        gated_features, gate_values = gating(x)
        
        # 验证输出形状
        expected_feature_dim = self.config.num_gates * self.config.hidden_dim
        self.assertEqual(gated_features.shape, (self.batch_size, expected_feature_dim))
        self.assertEqual(len(gate_values), self.config.num_gates)
        
        # 验证门控值在0-1之间
        for gate in gate_values:
            self.assertTrue((gate >= 0).all() and (gate <= 1).all())
    
    def test_adwm_model(self):
        """测试ADWM主模型"""
        model = ADWMModel(self.num_factors, self.config)
        
        # 创建测试输入
        factor_values = torch.randn(self.batch_size, self.num_factors)
        
        # 前向传播
        predictions, weights, gate_values = model(factor_values)
        
        # 验证输出形状
        self.assertEqual(predictions.shape, (self.batch_size, 1))
        self.assertEqual(weights.shape, (self.batch_size, self.num_factors))
        
        # 验证权重约束
        # 权重和应该为1
        weight_sums = weights.sum(dim=1)
        self.assertTrue(torch.allclose(weight_sums, torch.ones_like(weight_sums), atol=1e-5))
        
        # 权重应该在min_weight和max_weight之间
        self.assertTrue((weights >= self.config.min_weight).all())
        self.assertTrue((weights <= self.config.max_weight).all())
    
    def test_weight_constraints(self):
        """测试权重约束功能"""
        model = ADWMModel(self.num_factors, self.config)
        
        # 创建超出范围的权重
        weights = torch.tensor([
            [0.8, 0.1, 0.1, 0.0, 0.0, 0.0],  # 有一个权重超过max_weight
            [-0.1, 0.2, 0.3, 0.3, 0.2, 0.1]  # 有负权重
        ])
        
        # 应用权重约束
        constrained_weights = model._apply_weight_constraints(weights)
        
        # 验证约束后的权重
        self.assertTrue((constrained_weights >= self.config.min_weight).all())
        self.assertTrue((constrained_weights <= self.config.max_weight).all())
        
        # 验证权重和为1
        weight_sums = constrained_weights.sum(dim=1)
        self.assertTrue(torch.allclose(weight_sums, torch.ones_like(weight_sums), atol=1e-5))


class TestADWMFactorWeightingSystem(unittest.TestCase):
    """测试ADWM因子加权系统"""
    
    def setUp(self):
        """初始化测试环境"""
        self.factor_names = ['momentum', 'value', 'quality', 'size', 'volatility', 'liquidity']
        self.system = ADWMFactorWeightingSystem(self.factor_names)
        
        # 创建测试数据
        self.n_samples = 100
        self.dates = pd.date_range('2024-01-01', periods=self.n_samples, freq='D')
        self.factor_data = self._create_factor_data()
        self.returns = pd.Series(
            np.random.randn(self.n_samples) * 0.01,
            index=self.dates
        )
        
    def _create_factor_data(self):
        """创建模拟因子数据"""
        data = {}
        for factor in self.factor_names:
            data[factor] = np.random.randn(self.n_samples)
        
        return pd.DataFrame(data, index=self.dates)
    
    def test_evaluate_factor_effectiveness(self):
        """测试因子有效性评估"""
        effectiveness = self.system.evaluate_factor_effectiveness(
            self.factor_data,
            self.returns,
            lookback=20
        )
        
        # 验证输出格式
        self.assertIsInstance(effectiveness, pd.DataFrame)
        self.assertEqual(len(effectiveness), len(self.factor_names))
        
        # 验证包含所有必要指标
        expected_metrics = ['ic_mean', 'ic_std', 'ir', 'sharpe', 'win_rate', 'max_drawdown']
        for metric in expected_metrics:
            self.assertIn(metric, effectiveness.columns)
    
    def test_train_model(self):
        """测试模型训练"""
        # 使用小的epoch数进行快速测试
        self.system.config.epochs = 2
        
        train_losses, val_losses = self.system.train(
            self.factor_data,
            self.returns,
            validation_split=0.2
        )
        
        # 验证返回值
        self.assertIsInstance(train_losses, list)
        self.assertIsInstance(val_losses, list)
        self.assertEqual(len(train_losses), 2)
        self.assertEqual(len(val_losses), 2)
        
        # 验证损失值合理
        for loss in train_losses + val_losses:
            self.assertGreater(loss, 0)
            self.assertLess(loss, 1000)  # 合理范围
    
    def test_get_dynamic_weights(self):
        """测试动态权重获取"""
        # 先进行简单训练
        self.system.config.epochs = 1
        self.system.train(self.factor_data, self.returns)
        
        # 获取权重
        weights_df = self.system.get_dynamic_weights(self.factor_data)
        
        # 验证输出
        self.assertIsInstance(weights_df, pd.DataFrame)
        self.assertEqual(len(weights_df), len(self.factor_data))
        self.assertEqual(list(weights_df.columns), self.factor_names)
        
        # 验证权重约束
        # 每行权重和应该接近1
        weight_sums = weights_df.sum(axis=1)
        self.assertTrue((weight_sums > 0.99).all() and (weight_sums < 1.01).all())
        
        # 权重应该在合理范围内
        self.assertTrue((weights_df >= 0).all().all())
        self.assertTrue((weights_df <= 1).all().all())
    
    def test_create_combined_signal(self):
        """测试组合信号创建"""
        # 先训练模型
        self.system.config.epochs = 1
        self.system.train(self.factor_data, self.returns)
        
        # 创建组合信号
        combined_signal = self.system.create_combined_signal(
            self.factor_data,
            update_weights=True
        )
        
        # 验证输出
        self.assertIsInstance(combined_signal, pd.Series)
        self.assertEqual(len(combined_signal), len(self.factor_data))
        
        # 验证信号值在合理范围内
        self.assertFalse(combined_signal.isnull().any())
        self.assertTrue(combined_signal.std() > 0)  # 应该有变化
    
    def test_backtest(self):
        """测试回测功能"""
        # 简单训练
        self.system.config.epochs = 1
        self.system.train(self.factor_data, self.returns)
        
        # 运行回测
        results = self.system.backtest(
            self.factor_data,
            self.returns,
            rebalance_freq=20
        )
        
        # 验证输出
        self.assertIsInstance(results, pd.DataFrame)
        self.assertGreater(len(results), 0)
        
        # 验证包含必要列
        expected_columns = ['date', 'signal', 'weights', 'next_return']
        for col in expected_columns:
            self.assertIn(col, results.columns)


class TestIntegration(unittest.TestCase):
    """ADWM系统集成测试"""
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 创建系统
        factor_names = ['momentum', 'value', 'quality']
        config = ADWMConfig(epochs=2, batch_size=16)
        system = ADWMFactorWeightingSystem(factor_names, config)
        
        # 创建数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        factor_data = pd.DataFrame({
            'momentum': np.random.randn(50),
            'value': np.random.randn(50),
            'quality': np.random.randn(50)
        }, index=dates)
        
        returns = pd.Series(np.random.randn(50) * 0.01, index=dates)
        
        # 训练
        train_losses, val_losses = system.train(factor_data, returns)
        
        # 获取权重
        weights = system.get_dynamic_weights(factor_data.tail(10))
        
        # 创建信号
        signal = system.create_combined_signal(factor_data.tail(10))
        
        # 验证工作流程完成
        self.assertIsNotNone(train_losses)
        self.assertIsNotNone(weights)
        self.assertIsNotNone(signal)


def run_tests():
    """运行所有测试"""
    unittest.main(argv=[''], exit=False)


if __name__ == "__main__":
    run_tests() 
