# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# coding: utf-8
"""
多维动量择时器
基于多维动量信号的智能择时策略
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
from abc import ABC, abstractmethod
import warnings
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
    from .base_timer import BaseTimer
    from .timing_core import strategy_autodev.TimingConfig, TimingSignal
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class StockDataAdapter:
        def get_stock_data(self, symbol, start_date, end_date, adjust='qfq'):
            print(f"警告：无法导入StockDataAdapter，请检查adapters模块")
            return pd.DataFrame()
    
    from loguru import logger
    
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
            self._required_columns = kwargs.get('required_columns', ['close'])
            self._min_history = kwargs.get('min_history', 20)
            
        def validate_data(self, data):
            return True  # 简化验证
            
        def apply_threshold(self, strength):
            return strength
            
        def normalize_strength(self, value, vmin, vmax):
            return min(1.0, max(0.0, (value - vmin) / (vmax - vmin)))
    
    class TimingConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class TimingSignal:
        def __init__(self, timestamp, signal, strength, strategy, metadata):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata

warnings.filterwarnings('ignore')


class MultiDimensionalMomentumTimer(BaseTimer):
    """
    多维动量择时策略
    综合多个维度的动量信号进行择时决策
    """
    
    def __init__(self, 
                 price_momentum_window: int = 20,
                 volume_momentum_window: int = 10,
                 volatility_window: int = 15,
                 rsi_window: int = 14,
                 macd_fast: int = 12,
                 macd_slow: int = 26,
                 macd_signal: int = 9):
        """
        初始化多维动量择时器
        
        Args:
            price_momentum_window: 价格动量计算窗口
            volume_momentum_window: 成交量动量计算窗口
            volatility_window: 波动率计算窗口
            rsi_window: RSI计算窗口
            macd_fast: MACD快线周期
            macd_slow: MACD慢线周期
            macd_signal: MACD信号线周期
        """
        max_window = max(price_momentum_window, volume_momentum_window, volatility_window, 
                        rsi_window, macd_fast, macd_slow, macd_signal)
        
        super().__init__(
            TimingConfig(lookback_period=max_window),
            required_columns=['close', 'volume'],
            min_history=max_window
        )
        
        self.price_momentum_window = price_momentum_window
        self.volume_momentum_window = volume_momentum_window
        self.volatility_window = volatility_window
        self.rsi_window = rsi_window
        self.macd_fast = macd_fast
        self.macd_slow = macd_slow
        self.macd_signal = macd_signal
        self.data_adapter = StockDataAdapter()

    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法：基于最新数据计算信号"""
        if not self.validate_data(data):
            raise ValueError("数据验证失败")
        
        # 计算各维度动量
        price_momentum = self.calculate_price_momentum(data)
        volume_momentum = self.calculate_volume_momentum(data)
        
        # 简化组合计算
        combined_momentum = (price_momentum + volume_momentum) / 2
        
        # 生成信号
        signal_value = 0
        if combined_momentum > 0.5:
            signal_value = 1
        elif combined_momentum < -0.5:
            signal_value = -1
            
        strength = self.apply_threshold(
            self.normalize_strength(abs(combined_momentum), vmin=0, vmax=1.0)
        )
        
        return TimingSignal(
            timestamp=datetime.now(),
            signal=int(signal_value),
            strength=strength,
            strategy="MultiDimensionalMomentum",
            metadata={
                'price_momentum': price_momentum,
                'volume_momentum': volume_momentum,
                'combined_momentum': combined_momentum
            }
        )
    
    def calculate_price_momentum(self, data: pd.DataFrame) -> float:
        """计算价格动量"""
        if len(data) < self.price_momentum_window:
            return 0.0
        
        returns = data['close'].pct_change(self.price_momentum_window)
        return float(returns.iloc[-1]) if not pd.isna(returns.iloc[-1]) else 0.0
    
    def calculate_volume_momentum(self, data: pd.DataFrame) -> float:
        """计算成交量动量"""
        if len(data) < self.volume_momentum_window:
            return 0.0
        
        volume_ma = data['volume'].rolling(self.volume_momentum_window).mean()
        current_ratio = data['volume'].iloc[-1] / volume_ma.iloc[-1]
        
        return float(current_ratio - 1.0) if not pd.isna(current_ratio) else 0.0


class MomentumCalculator(ABC):
    """动量计算器基类"""
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """计算动量指标"""
        pass


class PriceMomentum(MomentumCalculator):
    """价格动量计算器"""
    
    def __init__(self, periods: List[int] = None):
        """
        初始化
        
        Args:
            periods: 动量计算周期列表
        """
        self.periods = periods or [5, 10, 20, 60]
        
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """
        计算价格动量
        
        Args:
            data: 包含价格数据的DataFrame
            
        Returns:
            综合价格动量
        """
        if 'close' not in data.columns:
            raise ValueError("Price data must contain 'close' column")
            
        momentum_scores = []
        
        for period in self.periods:
            # 计算不同周期的动量
            momentum = data['close'].pct_change(period)
            
            # 标准化
            if momentum.std() > 0:
                momentum_z = (momentum - momentum.mean()) / momentum.std()
            else:
                momentum_z = momentum - momentum.mean()
                
            momentum_scores.append(momentum_z)
        
        # 加权平均，短期权重更高
        weights = np.array([2.0, 1.5, 1.0, 0.5])[:len(self.periods)]
        weights = weights / weights.sum()
        
        composite_momentum = pd.Series(0, index=data.index)
        for i, score in enumerate(momentum_scores):
            composite_momentum += weights[i] * score
            
        return composite_momentum


class VolumeMomentum(MomentumCalculator):
    """成交量动量计算器"""
    
    def __init__(self, lookback: int = 20):
        """
        初始化
        
        Args:
            lookback: 回看窗口
        """
        self.lookback = lookback
        
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """
        计算成交量动量
        
        Args:
            data: 包含成交量数据的DataFrame
            
        Returns:
            成交量动量
        """
        if 'volume' not in data.columns:
            raise ValueError("Data must contain 'volume' column")
            
        # 成交量移动平均
        volume_ma = data['volume'].rolling(self.lookback).mean()
        
        # 成交量比率
        volume_ratio = data['volume'] / volume_ma
        
        # 成交量变化率
        volume_change = data['volume'].pct_change(5)
        
        # 量价配合度
        if 'close' in data.columns:
            price_change = data['close'].pct_change()
            volume_price_corr = price_change.rolling(self.lookback).corr(
                data['volume'].pct_change()
            )
        else:
            volume_price_corr = pd.Series(0, index=data.index)
        
        # 综合成交量动量
        volume_momentum = (
            0.4 * self._standardize(volume_ratio) +
            0.3 * self._standardize(volume_change) +
            0.3 * volume_price_corr
        )
        
        return volume_momentum
    
    def _standardize(self, series: pd.Series) -> pd.Series:
        """标准化序列"""
        if series.std() > 0:
            return (series - series.mean()) / series.std()
        return series - series.mean()


class CapitalFlowMomentum(MomentumCalculator):
    """资金流动量计算器"""
    
    def __init__(self, periods: List[int] = None):
        """
        初始化
        
        Args:
            periods: 计算周期列表
        """
        self.periods = periods or [5, 10, 20]
        
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """
        计算资金流动量
        
        Args:
            data: 包含价格和成交额数据的DataFrame
            
        Returns:
            资金流动量
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning("Missing required columns for capital flow calculation")
            return pd.Series(0, index=data.index)
        
        # 计算资金流
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        money_flow = typical_price * data['volume']
        
        # 计算资金流向指标
        price_change = data['close'].diff()
        
        # 正向资金流
        positive_flow = money_flow.where(price_change > 0, 0)
        # 负向资金流
        negative_flow = money_flow.where(price_change < 0, 0)
        
        capital_momentum = pd.Series(0, index=data.index)
        
        for period in self.periods:
            # 资金流比率
            pos_sum = positive_flow.rolling(period).sum()
            neg_sum = negative_flow.rolling(period).sum().abs()
            
            # 避免除零
            total_flow = pos_sum + neg_sum
            flow_ratio = pos_sum / total_flow.where(total_flow > 0, 1)
            
            # 标准化并累加
            if flow_ratio.std() > 0:
                flow_z = (flow_ratio - 0.5) / flow_ratio.std()
            else:
                flow_z = flow_ratio - 0.5
                
            capital_momentum += flow_z / len(self.periods)
        
        return capital_momentum


class MarketBreadthMomentum(MomentumCalculator):
    """市场广度动量计算器"""
    
    def __init__(self, lookback: int = 20):
        """
        初始化
        
        Args:
            lookback: 回看窗口
        """
        self.lookback = lookback
        
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """
        计算市场广度动量
        
        Args:
            data: 市场数据，可以是指数或个股数据
            
        Returns:
            市场广度动量
        """
        if 'close' not in data.columns:
            return pd.Series(0, index=data.index)
        
        # 计算涨跌幅
        returns = data['close'].pct_change()
        
        # 计算连涨连跌天数
        up_days = (returns > 0).astype(int)
        down_days = (returns < 0).astype(int)
        
        # 计算连续信号
        up_streak = self._calculate_streak(up_days)
        down_streak = self._calculate_streak(down_days)
        
        # 计算动量强度
        momentum_strength = up_streak - down_streak
        
        # 计算移动平均
        ma_short = data['close'].rolling(5).mean()
        ma_long = data['close'].rolling(20).mean()
        
        # 均线动量
        ma_momentum = (ma_short - ma_long) / ma_long
        
        # 综合市场广度动量
        breadth_momentum = (
            0.6 * self._standardize(momentum_strength) +
            0.4 * self._standardize(ma_momentum)
        )
        
        return breadth_momentum
    
    def _calculate_streak(self, signals: pd.Series) -> pd.Series:
        """计算连续信号"""
        streak = signals.copy()
        for i in range(1, len(signals)):
            if signals.iloc[i] == 1 and signals.iloc[i-1] == 1:
                streak.iloc[i] = streak.iloc[i-1] + 1
        return streak
    
    def _standardize(self, series: pd.Series) -> pd.Series:
        """标准化序列"""
        if series.std() > 0:
            return (series - series.mean()) / series.std()
        return series - series.mean()


class DynamicWeightOptimizer:
    """动态权重优化器"""
    
    def __init__(self, lookback: int = 60, min_weight: float = 0.1):
        """
        初始化
        
        Args:
            lookback: 回看窗口
            min_weight: 最小权重
        """
        self.lookback = lookback
        self.min_weight = min_weight
        
    def optimize(self, momentum_df: pd.DataFrame, 
                returns: pd.Series) -> Dict[str, float]:
        """
        优化维度权重
        
        Args:
            momentum_df: 各维度动量DataFrame
            returns: 收益率序列
            
        Returns:
            优化后的权重字典
        """
        # 使用最近的数据
        recent_momentum = momentum_df.iloc[-self.lookback:]
        recent_returns = returns.iloc[-self.lookback:]
        
        # 计算各维度与未来收益的相关性
        correlations = {}
        forward_returns = recent_returns.shift(-1)
        
        for dim in recent_momentum.columns:
            # 计算信息系数(IC)
            ic = recent_momentum[dim].corr(forward_returns)
            # 计算IC的稳定性
            rolling_ic = recent_momentum[dim].rolling(20).corr(forward_returns)
            ic_std = rolling_ic.std()
            
            # 综合评分
            if ic_std > 0:
                score = ic / ic_std  # IC_IR
            else:
                score = ic
                
            correlations[dim] = max(0, score)  # 只考虑正相关
        
        # 归一化权重
        total_score = sum(correlations.values())
        if total_score > 0:
            weights = {
                dim: max(self.min_weight, score / total_score)
                for dim, score in correlations.items()
            }
            
            # 重新归一化确保权重和为1
            weight_sum = sum(weights.values())
            weights = {dim: w / weight_sum for dim, w in weights.items()}
        else:
            # 如果所有相关性都为负，使用均等权重
            n_dims = len(momentum_df.columns)
            weights = {dim: 1 / n_dims for dim in momentum_df.columns}
        
        return weights


class TimingSignalGenerator:
    """择时信号生成器"""
    
    def __init__(self, buy_threshold: float = 0.5, 
                 sell_threshold: float = -0.5,
                 holding_period: int = 5):
        """
        初始化
        
        Args:
            buy_threshold: 买入阈值
            sell_threshold: 卖出阈值
            holding_period: 最小持仓期
        """
        self.buy_threshold = buy_threshold
        self.sell_threshold = sell_threshold
        self.holding_period = holding_period
        
    def generate(self, momentum: pd.Series) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            momentum: 综合动量序列
            
        Returns:
            包含信号和持仓的DataFrame
        """
        signals = pd.DataFrame(index=momentum.index)
        signals['momentum'] = momentum
        signals['signal'] = 0
        signals['position'] = 0
        
        position = 0
        last_trade_idx = -self.holding_period
        
        for i in range(len(momentum)):
            current_momentum = momentum.iloc[i]
            
            # 检查是否满足最小持仓期
            if i - last_trade_idx < self.holding_period:
                signals.iloc[i, signals.columns.get_loc('position')] = position
                continue
            
            # 生成信号
            if position == 0:  # 空仓
                if current_momentum > self.buy_threshold:
                    signals.iloc[i, signals.columns.get_loc('signal')] = 1
                    position = 1
                    last_trade_idx = i
            elif position == 1:  # 持仓
                if current_momentum < self.sell_threshold:
                    signals.iloc[i, signals.columns.get_loc('signal')] = -1
                    position = 0
                    last_trade_idx = i
            
            signals.iloc[i, signals.columns.get_loc('position')] = position
        
        return signals


def create_multi_momentum_timer() -> MultiDimensionalMomentumTimer:
    """创建多维动量择时器实例"""
    return MultiDimensionalMomentumTimer()


def demo_multi_momentum_timing():
    """演示多维动量择时系统"""
    # 生成模拟数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', '2024-12-31', freq='D')
    
    # 创建一个有趋势的价格序列
    trend = np.linspace(100, 150, len(dates))
    noise = np.random.normal(0, 2, len(dates))
    cycles = 10 * np.sin(np.linspace(0, 4 * np.pi, len(dates)))
    
    price = trend + noise + cycles
    
    # 生成成交量（与价格变化相关）
    price_change = pd.Series(price).pct_change().fillna(0)
    volume_base = 1000000
    volume = volume_base * (1 + np.abs(price_change) * 10 + np.random.normal(0, 0.1, len(dates)))
    
    # 构建市场数据
    market_data = pd.DataFrame({
        'open': price * (1 + np.random.normal(0, 0.005, len(dates))),
        'high': price * (1 + np.abs(np.random.normal(0, 0.01, len(dates)))),
        'low': price * (1 - np.abs(np.random.normal(0, 0.01, len(dates)))),
        'close': price,
        'volume': volume
    }, index=dates)
    
    # 创建择时器
    timer = create_multi_momentum_timer()
    
    # 生成择时信号
    print("Generating timing signals...")
    signals = timer.generate_timing_signal(market_data)
    
    # 打印最新状态
    print("\nCurrent System State:")
    state = timer.get_current_state()
    for key, value in state.items():
        print(f"{key}: {value}")
    
    # 计算策略表现
    returns = market_data['close'].pct_change()
    strategy_returns = returns * signals['position'].shift(1)
    
    # 计算累积收益
    cum_returns = (1 + returns).cumprod()
    cum_strategy_returns = (1 + strategy_returns).cumprod()
    
    # 打印统计信息
    print("\nPerformance Statistics:")
    print(f"Buy & Hold Return: {(cum_returns.iloc[-1] - 1) * 100:.2f}%")
    print(f"Strategy Return: {(cum_strategy_returns.iloc[-1] - 1) * 100:.2f}%")
    print(f"Number of Trades: {(signals['signal'] != 0).sum()}")
    
    # 计算夏普比率
    sharpe_bh = returns.mean() / returns.std() * np.sqrt(252)
    sharpe_strategy = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)
    print(f"Buy & Hold Sharpe: {sharpe_bh:.2f}")
    print(f"Strategy Sharpe: {sharpe_strategy:.2f}")
    
    # 绘制分析图表
    timer.plot_analysis(market_data, signals)
    
    # 绘制累积收益对比
    import matplotlib.pyplot as plt
    plt.figure(figsize=(12, 6))
    plt.plot(dates, cum_returns - 1, label='Buy & Hold', linewidth=2)
    plt.plot(dates, cum_strategy_returns - 1, label='Multi-Momentum Strategy', linewidth=2)
    plt.xlabel('Date')
    plt.ylabel('Cumulative Return')
    plt.title('Strategy Performance Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    # 运行演示
    demo_multi_momentum_timing() 