# -*- coding: utf-8 -*-
"""
情感分析RNN策略
基于《Hands-On Machine Learning for Algorithmic Trading》Chapter 18
04_sentiment_analysis.ipynb

结合文本情感分析和价格数据进行交易决策
现已迁移至PyTorch框架
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架 - PyTorch
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    from torch.nn.utils.rnn import pad_sequence
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    warnings.warn("PyTorch不可用，情感分析RNN策略将无法正常工作")

# 文本处理
try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize
    from nltk.stem import WordNetLemmatizer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    warnings.warn("NLTK不可用，文本预处理功能受限")

# 数据处理
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, accuracy_score
from collections import Counter
import re

# 导入基础策略类
try:
    from .rnn_time_series_strategy import RNNTimeSeriesStrategy
except ImportError:
    try:
        from rnn_time_series_strategy import RNNTimeSeriesStrategy
    except ImportError:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from rnn_time_series_strategy import RNNTimeSeriesStrategy

# 导入数据管道
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from data_pipeline.data_adapter_compatibility import get_data_pipeline
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        warnings.warn("数据适配器不可用")

logger = logging.getLogger(__name__)

class TextTokenizer:
    """
    简单的文本分词器（PyTorch版本）
    """
    
    def __init__(self, max_features: int = 10000, oov_token: str = '<OOV>'):
        self.max_features = max_features
        self.oov_token = oov_token
        self.word_index = {}
        self.index_word = {}
        self.word_counts = Counter()
        
    def fit_on_texts(self, texts: List[str]):
        """训练分词器"""
        # 统计词频
        for text in texts:
            if pd.isna(text) or text == '':
                continue
            words = text.lower().split()
            self.word_counts.update(words)
        
        # 构建词汇表
        self.word_index = {self.oov_token: 1}
        self.index_word = {1: self.oov_token}
        
        # 按频率排序，取前max_features个词
        most_common = self.word_counts.most_common(self.max_features - 1)
        for i, (word, count) in enumerate(most_common, 2):
            self.word_index[word] = i
            self.index_word[i] = word
    
    def texts_to_sequences(self, texts: List[str]) -> List[List[int]]:
        """将文本转换为序列"""
        sequences = []
        for text in texts:
            if pd.isna(text) or text == '':
                sequences.append([])
                continue
            
            words = text.lower().split()
            sequence = []
            for word in words:
                if word in self.word_index:
                    sequence.append(self.word_index[word])
                else:
                    sequence.append(self.word_index[self.oov_token])
            sequences.append(sequence)
        
        return sequences

def pad_sequences_pytorch(sequences: List[List[int]], maxlen: int, padding: str = 'post', 
                         truncating: str = 'post', value: int = 0) -> np.ndarray:
    """
    PyTorch版本的序列填充函数
    """
    result = np.full((len(sequences), maxlen), value, dtype=np.int32)
    
    for i, seq in enumerate(sequences):
        if len(seq) == 0:
            continue
            
        if len(seq) > maxlen:
            if truncating == 'post':
                seq = seq[:maxlen]
            else:
                seq = seq[-maxlen:]
        
        if padding == 'post':
            result[i, :len(seq)] = seq
        else:
            result[i, -len(seq):] = seq
    
    return result

class SentimentRNNModel(nn.Module):
    """
    PyTorch多模态情感分析RNN模型
    """
    
    def __init__(self, vocab_size: int, embedding_dim: int, price_input_size: int,
                 hidden_sizes: List[int], max_sequence_length: int, 
                 dropout_rate: float = 0.2, fusion_method: str = 'concatenate',
                 embedding_matrix: Optional[np.ndarray] = None):
        super(SentimentRNNModel, self).__init__()
        
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.price_input_size = price_input_size
        self.hidden_sizes = hidden_sizes
        self.max_sequence_length = max_sequence_length
        self.dropout_rate = dropout_rate
        self.fusion_method = fusion_method
        
        # 文本嵌入层
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        if embedding_matrix is not None:
            self.embedding.weight.data.copy_(torch.from_numpy(embedding_matrix))
            self.embedding.weight.requires_grad = False  # 冻结预训练嵌入
        
        # 文本特征提取
        self.text_conv = nn.Conv1d(embedding_dim, 64, kernel_size=3, padding=1)
        self.text_pool = nn.MaxPool1d(kernel_size=2)
        self.text_lstm = nn.LSTM(32, 32, batch_first=True, dropout=dropout_rate)
        
        # 价格序列LSTM
        self.price_lstm_layers = nn.ModuleList()
        input_size = price_input_size
        for hidden_size in hidden_sizes:
            self.price_lstm_layers.append(
                nn.LSTM(input_size, hidden_size, batch_first=True, dropout=dropout_rate)
            )
            input_size = hidden_size
        
        # 融合层
        if fusion_method == 'concatenate':
            fusion_input_size = hidden_sizes[-1] + 32  # price_lstm + text_lstm
        elif fusion_method == 'weighted':
            fusion_input_size = 64  # 加权后的特征维度
        else:
            fusion_input_size = hidden_sizes[-1] + 32
        
        # 最终预测层
        self.fusion_dense1 = nn.Linear(fusion_input_size, 64)
        self.fusion_dropout1 = nn.Dropout(dropout_rate)
        self.fusion_dense2 = nn.Linear(64, 32)
        self.fusion_dropout2 = nn.Dropout(dropout_rate)
        self.output_layer = nn.Linear(32, 1)
        
        # 权重参数（用于加权融合）
        if fusion_method == 'weighted':
            self.price_weight_layer = nn.Linear(hidden_sizes[-1], 32)
            self.text_weight_layer = nn.Linear(32, 32)
    
    def forward(self, price_input, text_input):
        # 价格序列处理
        price_x = price_input
        for lstm_layer in self.price_lstm_layers:
            price_x, _ = lstm_layer(price_x)
        price_features = price_x[:, -1, :]  # 取最后一个时间步
        
        # 文本序列处理
        text_embedded = self.embedding(text_input)  # (batch, seq_len, embed_dim)
        text_embedded = text_embedded.transpose(1, 2)  # (batch, embed_dim, seq_len)
        text_conv = F.relu(self.text_conv(text_embedded))
        text_pooled = self.text_pool(text_conv)
        text_pooled = text_pooled.transpose(1, 2)  # (batch, seq_len, features)
        text_lstm_out, _ = self.text_lstm(text_pooled)
        text_features = text_lstm_out[:, -1, :]  # 取最后一个时间步
        
        # 特征融合
        if self.fusion_method == 'concatenate':
            combined = torch.cat([price_features, text_features], dim=1)
        elif self.fusion_method == 'weighted':
            price_weighted = F.relu(self.price_weight_layer(price_features))
            text_weighted = F.relu(self.text_weight_layer(text_features))
            combined = price_weighted + text_weighted  # 加权求和
        else:
            combined = torch.cat([price_features, text_features], dim=1)
        
        # 最终预测
        x = F.relu(self.fusion_dense1(combined))
        x = self.fusion_dropout1(x)
        x = F.relu(self.fusion_dense2(x))
        x = self.fusion_dropout2(x)
        output = self.output_layer(x)
        
        return output

class SentimentRNNStrategy(RNNTimeSeriesStrategy):
    """
    情感分析RNN策略 - PyTorch版本
    
    结合文本情感分析和价格时间序列数据，基于Chapter 18实现：
    - 新闻情感分析
    - 社交媒体情感分析
    - 多模态融合（文本+价格）
    - 词嵌入和预训练模型
    """
    
    def __init__(self, name: str = "Sentiment_RNN", **kwargs):
        # 情感分析特定参数
        self.max_features = kwargs.get('max_features', 10000)  # 词汇表大小
        self.max_sequence_length = kwargs.get('max_sequence_length', 100)  # 文本序列长度
        self.embedding_dim = kwargs.get('embedding_dim', 100)  # 词嵌入维度
        self.sentiment_window = kwargs.get('sentiment_window', 5)  # 情感数据窗口
        
        # 文本预处理参数
        self.use_pretrained_embeddings = kwargs.get('use_pretrained_embeddings', False)
        self.embedding_path = kwargs.get('embedding_path', None)  # GloVe等预训练嵌入路径
        self.text_preprocessing = kwargs.get('text_preprocessing', True)
        
        # 融合策略
        self.fusion_method = kwargs.get('fusion_method', 'concatenate')  # concatenate, weighted
        self.sentiment_weight = kwargs.get('sentiment_weight', 0.3)
        self.price_weight = kwargs.get('price_weight', 0.7)
        
        # 设置多模态默认参数
        kwargs.setdefault('multivariate', True)
        kwargs.setdefault('rnn_type', 'LSTM')
        kwargs.setdefault('sequence_length', 20)
        kwargs.setdefault('units', [64, 32])
        
        super().__init__(name, **kwargs)
        
        # 文本处理组件
        self.tokenizer = None
        self.embedding_matrix = None
        self.sentiment_scaler = MinMaxScaler()
        
        # 情感数据缓存
        self.sentiment_cache = {}
        
        logger.info(f"情感分析RNN策略 {name} 初始化完成")
        logger.info(f"配置: 词汇表={self.max_features}, 嵌入维度={self.embedding_dim}, 融合方法={self.fusion_method}")
    
    def preprocess_text(self, texts: List[str]) -> List[str]:
        """
        文本预处理
        
        Args:
            texts: 原始文本列表
            
        Returns:
            预处理后的文本列表
        """
        if not self.text_preprocessing or not NLTK_AVAILABLE:
            return texts
        
        try:
            # 下载必要的NLTK数据
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            
            try:
                nltk.data.find('corpora/stopwords')
            except LookupError:
                nltk.download('stopwords', quiet=True)
            
            try:
                nltk.data.find('corpora/wordnet')
            except LookupError:
                nltk.download('wordnet', quiet=True)
            
            stop_words = set(stopwords.words('english'))
            lemmatizer = WordNetLemmatizer()
            
            processed_texts = []
            for text in texts:
                if pd.isna(text) or text == '':
                    processed_texts.append('')
                    continue
                
                # 转换为小写
                text = str(text).lower()
                
                # 去除特殊字符
                text = re.sub(r'[^a-zA-Z\s]', '', text)
                
                # 分词
                tokens = word_tokenize(text)
                
                # 去除停用词和标点
                tokens = [token for token in tokens if token.isalpha() and token not in stop_words]
                
                # 词形还原
                tokens = [lemmatizer.lemmatize(token) for token in tokens]
                
                # 重新组合
                processed_text = ' '.join(tokens)
                processed_texts.append(processed_text)
            
            logger.info(f"文本预处理完成: {len(texts)} 条文本")
            return processed_texts
            
        except Exception as e:
            logger.warning(f"文本预处理失败: {e}，使用原始文本")
            return texts
    
    def prepare_text_data(self, texts: List[str]) -> np.ndarray:
        """
        准备文本数据用于模型训练
        
        Args:
            texts: 文本列表
            
        Returns:
            序列化的文本数据
        """
        try:
            # 预处理文本
            processed_texts = self.preprocess_text(texts)
            
            # 初始化或使用已有的tokenizer
            if self.tokenizer is None:
                self.tokenizer = TextTokenizer(
                    max_features=self.max_features,
                    oov_token='<OOV>'
                )
                self.tokenizer.fit_on_texts(processed_texts)
                logger.info(f"Tokenizer训练完成，词汇表大小: {len(self.tokenizer.word_index)}")
            
            # 文本序列化
            sequences = self.tokenizer.texts_to_sequences(processed_texts)
            
            # 填充序列
            padded_sequences = pad_sequences_pytorch(
                sequences,
                maxlen=self.max_sequence_length,
                padding='post',
                truncating='post'
            )
            
            logger.info(f"文本数据准备完成: shape={padded_sequences.shape}")
            return padded_sequences
            
        except Exception as e:
            logger.error(f"文本数据准备失败: {e}")
            return np.array([])
    
    def load_pretrained_embeddings(self) -> Optional[np.ndarray]:
        """
        加载预训练词嵌入（如GloVe）
        
        Returns:
            嵌入矩阵
        """
        if not self.use_pretrained_embeddings or not self.embedding_path:
            return None
        
        try:
            import os
            if not os.path.exists(self.embedding_path):
                logger.warning(f"预训练嵌入文件不存在: {self.embedding_path}")
                return None
            
            # 加载GloVe嵌入
            embeddings_index = {}
            with open(self.embedding_path, 'r', encoding='utf-8') as f:
                for line in f:
                    values = line.split()
                    word = values[0]
                    coefs = np.asarray(values[1:], dtype='float32')
                    embeddings_index[word] = coefs
            
            logger.info(f"加载预训练嵌入: {len(embeddings_index)} 个词向量")
            
            # 创建嵌入矩阵
            vocab_size = min(self.max_features, len(self.tokenizer.word_index)) + 1
            embedding_matrix = np.zeros((vocab_size, self.embedding_dim))
            
            for word, i in self.tokenizer.word_index.items():
                if i >= self.max_features:
                    continue
                embedding_vector = embeddings_index.get(word)
                if embedding_vector is not None:
                    embedding_matrix[i] = embedding_vector
            
            logger.info(f"嵌入矩阵创建完成: shape={embedding_matrix.shape}")
            return embedding_matrix
            
        except Exception as e:
            logger.error(f"预训练嵌入加载失败: {e}")
            return None
    
    def prepare_sentiment_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备情感特征
        
        Args:
            data: 包含价格和情感数据的DataFrame
            
        Returns:
            增强的特征DataFrame
        """
        try:
            df = data.copy()
            
            # 基础价格特征
            df = self._calculate_technical_indicators(df)
            
            # 情感特征（如果存在）
            sentiment_columns = [col for col in df.columns if 'sentiment' in col.lower()]
            
            if sentiment_columns:
                for col in sentiment_columns:
                    # 情感移动平均
                    df[f'{col}_ma_{self.sentiment_window}'] = df[col].rolling(window=self.sentiment_window).mean()
                    
                    # 情感变化率
                    df[f'{col}_change'] = df[col].pct_change()
                    
                    # 情感动量
                    df[f'{col}_momentum'] = df[col] - df[col].shift(self.sentiment_window)
                    
                    # 情感极值
                    df[f'{col}_extreme'] = np.where(
                        np.abs(df[col] - df[col].rolling(window=20).mean()) > 
                        2 * df[col].rolling(window=20).std(), 1, 0
                    )
            
            # 价格-情感交互特征
            if 'sentiment' in df.columns and 'returns' in df.columns:
                df['sentiment_return_corr'] = df['sentiment'].rolling(window=20).corr(df['returns'])
                df['sentiment_price_divergence'] = (df['sentiment'] > 0) & (df['returns'] < 0)
            
            logger.info(f"情感特征准备完成: {len(df.columns)} 个特征")
            return df
            
        except Exception as e:
            logger.error(f"情感特征准备失败: {e}")
            return data
    
    def _build_model(self) -> Optional[nn.Module]:
        """
        构建模型（实现抽象方法）
        """
        # 这个方法会在train_with_sentiment时调用
        return None
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型（实现抽象方法）
        """
        # 这个方法被train_with_sentiment覆盖
        return super()._train_model(X, y)
    
    def train_with_sentiment(self, price_data: pd.DataFrame, text_data: List[str]) -> Dict[str, Any]:
        """
        使用价格和文本数据训练模型
        
        Args:
            price_data: 价格时间序列数据
            text_data: 对应的文本数据
            
        Returns:
            训练结果
        """
        try:
            logger.info("开始训练多模态情感分析模型")
            
            if not PYTORCH_AVAILABLE:
                logger.error("PyTorch不可用，无法训练模型")
                return {'success': False, 'error': 'PyTorch不可用'}
            
            # 准备价格序列数据
            price_features = self.prepare_sentiment_features(price_data)
            X_price, y = self.prepare_sequences(price_features)
            
            if len(X_price) == 0:
                return {'success': False, 'error': '价格数据准备失败'}
            
            # 准备文本数据
            X_text = self.prepare_text_data(text_data)
            
            if len(X_text) == 0:
                return {'success': False, 'error': '文本数据准备失败'}
            
            # 对齐数据长度
            min_length = min(len(X_price), len(X_text), len(y))
            X_price = X_price[:min_length]
            X_text = X_text[:min_length]
            y = y[:min_length]
            
            # 加载预训练嵌入
            if self.use_pretrained_embeddings:
                self.embedding_matrix = self.load_pretrained_embeddings()
            
            # 构建模型
            vocab_size = len(self.tokenizer.word_index) + 1
            self.model = SentimentRNNModel(
                vocab_size=vocab_size,
                embedding_dim=self.embedding_dim,
                price_input_size=X_price.shape[2],
                hidden_sizes=self.units,
                max_sequence_length=self.max_sequence_length,
                dropout_rate=self.dropout_rate,
                fusion_method=self.fusion_method,
                embedding_matrix=self.embedding_matrix
            ).to(self.device)
            
            # 转换数据为PyTorch张量
            X_price_tensor = torch.FloatTensor(X_price).to(self.device)
            X_text_tensor = torch.LongTensor(X_text).to(self.device)
            y_tensor = torch.FloatTensor(y).to(self.device)
            
            # 分割训练和验证数据
            split_idx = int(len(X_price) * (1 - self.validation_split))
            X_price_train, X_price_val = X_price_tensor[:split_idx], X_price_tensor[split_idx:]
            X_text_train, X_text_val = X_text_tensor[:split_idx], X_text_tensor[split_idx:]
            y_train, y_val = y_tensor[:split_idx], y_tensor[split_idx:]
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_price_train, X_text_train, y_train)
            val_dataset = TensorDataset(X_price_val, X_text_val, y_val)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
            
            # 设置优化器和损失函数
            if self.optimizer_name.lower() == 'rmsprop':
                optimizer = optim.RMSprop(self.model.parameters(), lr=self.learning_rate)
            else:
                optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            
            criterion = nn.MSELoss()
            
            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(self.epochs):
                # 训练阶段
                self.model.train()
                train_loss = 0.0
                for batch_price, batch_text, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = self.model(batch_price, batch_text)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # 验证阶段
                self.model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_price, batch_text, batch_y in val_loader:
                        outputs = self.model(batch_price, batch_text)
                        loss = criterion(outputs.squeeze(), batch_y)
                        val_loss += loss.item()
                
                train_loss /= len(train_loader)
                val_loss /= len(val_loader)
                
                train_losses.append(train_loss)
                val_losses.append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= self.patience:
                        logger.info(f"早停在epoch {epoch+1}")
                        break
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 恢复最佳模型
            if hasattr(self, 'best_model_state'):
                self.model.load_state_dict(self.best_model_state)
            
            # 计算训练指标
            self.model.eval()
            with torch.no_grad():
                train_pred = self.model(X_price_train, X_text_train)
                train_mse = F.mse_loss(train_pred.squeeze(), y_train).item()
                train_mae = F.l1_loss(train_pred.squeeze(), y_train).item()
            
            self.training_history = {
                'loss': train_losses,
                'val_loss': val_losses
            }
            self.is_trained = True
            
            result = {
                'success': True,
                'train_samples': len(X_price),
                'train_mse': train_mse,
                'train_mae': train_mae,
                'epochs_trained': len(train_losses),
                'final_loss': train_losses[-1],
                'final_val_loss': val_losses[-1],
                'model_params': sum(p.numel() for p in self.model.parameters()),
                'text_vocab_size': len(self.tokenizer.word_index) if self.tokenizer else 0
            }
            
            logger.info(f"多模态情感分析模型训练完成: MSE={train_mse:.6f}, MAE={train_mae:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"情感分析模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def predict_with_sentiment(self, price_data: pd.DataFrame, text_data: List[str]) -> pd.Series:
        """
        使用价格和文本数据进行预测
        
        Args:
            price_data: 价格数据
            text_data: 文本数据
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            logger.warning("模型未训练，返回零预测")
            return pd.Series(0, index=price_data.index)
        
        try:
            # 准备预测数据
            price_features = self.prepare_sentiment_features(price_data)
            X_price, _ = self.prepare_sequences(price_features)
            X_text = self.prepare_text_data(text_data)
            
            if len(X_price) == 0 or len(X_text) == 0:
                logger.warning("预测数据准备失败")
                return pd.Series(0, index=price_data.index)
            
            # 对齐数据长度
            min_length = min(len(X_price), len(X_text))
            X_price = X_price[:min_length]
            X_text = X_text[:min_length]
            
            # 转换为张量
            X_price_tensor = torch.FloatTensor(X_price).to(self.device)
            X_text_tensor = torch.LongTensor(X_text).to(self.device)
            
            # 模型预测
            self.model.eval()
            with torch.no_grad():
                predictions = self.model(X_price_tensor, X_text_tensor)
                predictions = predictions.cpu().numpy().flatten()
            
            # 反标准化
            predictions = self.target_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
            
            # 创建预测序列
            pred_index = price_data.index[self.sequence_length:self.sequence_length + len(predictions)]
            pred_series = pd.Series(predictions, index=pred_index)
            
            # 扩展到完整索引
            result = pd.Series(0.0, index=price_data.index)
            result.loc[pred_series.index] = pred_series
            
            logger.info(f"多模态情感分析预测完成: {len(predictions)} 个预测值")
            return result
            
        except Exception as e:
            logger.error(f"情感分析预测失败: {e}")
            return pd.Series(0, index=price_data.index)
    
    def analyze_sentiment_impact(self, price_data: pd.DataFrame, text_data: List[str]) -> Dict[str, Any]:
        """
        分析情感对价格预测的影响
        
        Returns:
            情感影响分析结果
        """
        if not self.is_trained or self.model is None:
            return {'error': '模型未训练'}
        
        try:
            # 准备数据
            price_features = self.prepare_sentiment_features(price_data)
            X_price, _ = self.prepare_sequences(price_features)
            X_text = self.prepare_text_data(text_data)
            
            min_length = min(len(X_price), len(X_text))
            X_price = X_price[:min_length]
            X_text = X_text[:min_length]
            
            # 转换为张量
            X_price_tensor = torch.FloatTensor(X_price).to(self.device)
            X_text_tensor = torch.LongTensor(X_text).to(self.device)
            
            # 原始预测
            self.model.eval()
            with torch.no_grad():
                original_pred = self.model(X_price_tensor, X_text_tensor).cpu().numpy()
                
                # 中性情感预测（将文本输入设为零）
                neutral_text = torch.zeros_like(X_text_tensor)
                neutral_pred = self.model(X_price_tensor, neutral_text).cpu().numpy()
            
            # 计算情感影响
            sentiment_impact = original_pred - neutral_pred
            
            analysis = {
                'mean_sentiment_impact': float(np.mean(sentiment_impact)),
                'std_sentiment_impact': float(np.std(sentiment_impact)),
                'max_positive_impact': float(np.max(sentiment_impact)),
                'max_negative_impact': float(np.min(sentiment_impact)),
                'sentiment_correlation': float(np.corrcoef(sentiment_impact.flatten(), original_pred.flatten())[0, 1])
            }
            
            logger.info("情感影响分析完成")
            return analysis
            
        except Exception as e:
            logger.error(f"情感影响分析失败: {e}")
            return {'error': str(e)}
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        """
        if self.model is None:
            logger.error("没有训练好的模型可保存")
            return False
        
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'model_config': {
                    'vocab_size': self.model.vocab_size,
                    'embedding_dim': self.model.embedding_dim,
                    'price_input_size': self.model.price_input_size,
                    'hidden_sizes': self.model.hidden_sizes,
                    'max_sequence_length': self.model.max_sequence_length,
                    'dropout_rate': self.model.dropout_rate,
                    'fusion_method': self.model.fusion_method
                },
                'tokenizer': self.tokenizer,
                'embedding_matrix': self.embedding_matrix,
                'feature_scaler': self.feature_scaler,
                'target_scaler': self.target_scaler,
                'sentiment_scaler': self.sentiment_scaler,
                'strategy_config': {
                    'sequence_length': self.sequence_length,
                    'multivariate': self.multivariate,
                    'target_column': self.target_column,
                    'max_features': self.max_features,
                    'sentiment_window': self.sentiment_window
                }
            }, filepath)
            logger.info(f"情感分析模型已保存到: {filepath}")
            return True
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        """
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            
            # 重建模型
            config = checkpoint['model_config']
            self.model = SentimentRNNModel(
                vocab_size=config['vocab_size'],
                embedding_dim=config['embedding_dim'],
                price_input_size=config['price_input_size'],
                hidden_sizes=config['hidden_sizes'],
                max_sequence_length=config['max_sequence_length'],
                dropout_rate=config['dropout_rate'],
                fusion_method=config['fusion_method'],
                embedding_matrix=checkpoint.get('embedding_matrix')
            ).to(self.device)
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 加载组件
            self.tokenizer = checkpoint['tokenizer']
            self.embedding_matrix = checkpoint.get('embedding_matrix')
            self.feature_scaler = checkpoint['feature_scaler']
            self.target_scaler = checkpoint['target_scaler']
            self.sentiment_scaler = checkpoint['sentiment_scaler']
            
            # 加载策略配置
            strategy_config = checkpoint.get('strategy_config', {})
            self.sequence_length = strategy_config.get('sequence_length', self.sequence_length)
            self.multivariate = strategy_config.get('multivariate', self.multivariate)
            self.target_column = strategy_config.get('target_column', self.target_column)
            self.max_features = strategy_config.get('max_features', self.max_features)
            self.sentiment_window = strategy_config.get('sentiment_window', self.sentiment_window)
            
            self.is_trained = True
            logger.info(f"情感分析模型已从 {filepath} 加载")
            return True
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False

class NewsBasedRNNStrategy(SentimentRNNStrategy):
    """
    基于新闻的RNN策略 - PyTorch版本
    
    专门处理新闻文本数据的情感分析策略
    """
    
    def __init__(self, name: str = "News_RNN", **kwargs):
        # 新闻特定参数
        kwargs.setdefault('max_sequence_length', 200)  # 新闻文本较长
        kwargs.setdefault('embedding_dim', 200)
        kwargs.setdefault('sentiment_window', 3)  # 新闻影响周期较短
        kwargs.setdefault('fusion_method', 'weighted')
        kwargs.setdefault('sentiment_weight', 0.4)  # 新闻影响较大
        
        super().__init__(name, **kwargs)
        
        # 新闻特定配置
        self.news_categories = kwargs.get('news_categories', [
            'earnings', 'merger', 'regulatory', 'market', 'economic'
        ])
        
        logger.info(f"基于新闻的RNN策略 {name} 初始化完成")
    
    def extract_news_features(self, news_data: pd.DataFrame) -> pd.DataFrame:
        """
        提取新闻特征
        
        Args:
            news_data: 包含新闻数据的DataFrame
            
        Returns:
            新闻特征DataFrame
        """
        try:
            df = news_data.copy()
            
            # 新闻数量特征
            df['news_count'] = df.groupby(df.index.date)['headline'].count()
            
            # 新闻类别特征
            for category in self.news_categories:
                df[f'news_{category}'] = df['headline'].str.contains(
                    category, case=False, na=False
                ).astype(int)
            
            # 新闻长度特征
            df['headline_length'] = df['headline'].str.len()
            df['avg_headline_length'] = df.groupby(df.index.date)['headline_length'].mean()
            
            # 新闻时间特征
            df['news_hour'] = df.index.hour
            df['is_market_hours'] = ((df['news_hour'] >= 9) & (df['news_hour'] <= 16)).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"新闻特征提取失败: {e}")
            return news_data