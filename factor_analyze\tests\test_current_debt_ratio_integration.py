# -*- coding: utf-8 -*-
"""
çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­ä¸factor_miningæ¨¡å—é›†æˆæµ‹è¯•
Current Debt Ratio Factor Integration Test with factor_mining

éªŒè¯æ–°åˆ›å»ºçš„çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­èƒ½å¦è¢«factor_miningæ¨¡å—æ­£ç¡®è°ƒç”¨å’Œé›†æˆ?
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# æ·»åŠ é¡¹ç›®è·¯å¾„
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# å¯¼å…¥æˆ‘ä»¬çš„å› å­?
from factor_analyze.fundamental_factors.current_debt_ratio_factor import (
    CurrentDebtRatioFactorForStocks,
    CurrentDebtRatioFactorForIndices,
    calculate_current_debt_ratio_factor
)

# å°è¯•å¯¼å…¥factor_miningæ¨¡å—
FACTOR_MINING_AVAILABLE = False
try:
    from factor_mining import (
        FactorAnalyzeRegistry,
        IntegratedFactorMiner,
        get_integration_manager,
        list_available_base_factors
    )
    FACTOR_MINING_AVAILABLE = True
    print("[INFO] factor_miningæ¨¡å—å¯¼å…¥æˆåŠŸ")
except Exception as e:
    print(f"[WARNING] factor_miningæ¨¡å—å¯¼å…¥å¤±è´¥: {e}")


def test_direct_factor_usage():
    """æµ‹è¯•ç›´æ¥ä½¿ç”¨å› å­"""
    print("=" * 60)
    print("æµ‹è¯•1: ç›´æ¥ä½¿ç”¨çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?)
    print("=" * 60)
    
    try:
        # æµ‹è¯•ä¸ªè‚¡å› å­
        stock_factor = CurrentDebtRatioFactorForStocks()
        print("âœ?ä¸ªè‚¡å› å­å®ä¾‹åŒ–æˆåŠ?)
        
        # æµ‹è¯•æŒ‡æ•°å› å­
        index_factor = CurrentDebtRatioFactorForIndices()
        print("âœ?æŒ‡æ•°å› å­å®ä¾‹åŒ–æˆåŠ?)
        
        # æµ‹è¯•ç»Ÿä¸€æ¥å£
        test_stocks = ['000001.SZ', '000002.SZ']
        results = calculate_current_debt_ratio_factor(
            instruments=test_stocks,
            start_date='2023-01-01',
            end_date='2024-12-31',
            target_type='stock'
        )
        
        if not results.empty:
            print("âœ?ç»Ÿä¸€æ¥å£è°ƒç”¨æˆåŠŸ")
            print(f"  ç»“æœæ•°é‡: {len(results)}")
            print(f"  å› å­å€¼èŒƒå›? {results['current_debt_ratio'].min():.4f} - {results['current_debt_ratio'].max():.4f}")
        else:
            print("âœ?ç»Ÿä¸€æ¥å£è°ƒç”¨å¤±è´¥")
        
        return True
        
    except Exception as e:
        print(f"âœ?ç›´æ¥ä½¿ç”¨å› å­æµ‹è¯•å¤±è´¥: {e}")
        return False


def test_factor_mining_integration():
    """æµ‹è¯•ä¸factor_miningæ¨¡å—çš„é›†æˆ?""
    print("\n" + "=" * 60)
    print("æµ‹è¯•2: ä¸factor_miningæ¨¡å—é›†æˆ")
    print("=" * 60)
    
    if not FACTOR_MINING_AVAILABLE:
        print("âœ?factor_miningæ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡é›†æˆæµ‹è¯•")
        return False
    
    try:
        # æµ‹è¯•å› å­æ³¨å†Œè¡?
        registry = FactorAnalyzeRegistry()
        print("âœ?å› å­æ³¨å†Œè¡¨åˆ›å»ºæˆåŠ?)
        
        # æ‰‹åŠ¨æ³¨å†Œæˆ‘ä»¬çš„å› å­ï¼ˆå¦‚æœè¿˜æ²¡æœ‰æ³¨å†Œï¼‰
        registry.register_stock_factor(
            'current_debt_ratio',
            CurrentDebtRatioFactorForStocks,
            {
                'name': 'çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?è‚¡ç¥¨)',
                'category': 'fundamental',
                'description': 'è¡¡é‡ä¼ä¸šå¯¹çŸ­æœŸèèµ„çš„ä¾èµ–ç¨‹åº¦',
                'data_requirements': ['balance_sheet'],
                'calculation_period': 'quarterly'
            }
        )
        
        registry.register_index_factor(
            'current_debt_ratio',
            CurrentDebtRatioFactorForIndices,
            {
                'name': 'çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­?æŒ‡æ•°)',
                'category': 'fundamental',
                'description': 'åŸºäºæˆåˆ†è‚¡è®¡ç®—çš„æŒ‡æ•°çŸ­æœŸå¿å€ºå‹åŠ?,
                'data_requirements': ['balance_sheet', 'index_weights'],
                'calculation_period': 'quarterly'
            }
        )
        
        print("âœ?å› å­æ³¨å†ŒæˆåŠŸ")
        
        # æµ‹è¯•è·å–å› å­
        stock_factor_class = registry.get_stock_factor('current_debt_ratio')
        index_factor_class = registry.get_index_factor('current_debt_ratio')
        
        if stock_factor_class and index_factor_class:
            print("âœ?å› å­è·å–æˆåŠŸ")
            
            # å®ä¾‹åŒ–å› å­?
            stock_factor = stock_factor_class()
            index_factor = index_factor_class()
            print("âœ?å› å­å®ä¾‹åŒ–æˆåŠ?)
            
        else:
            print("âœ?å› å­è·å–å¤±è´¥")
            return False
        
        # æµ‹è¯•å› å­å…ƒæ•°æ?
        stock_metadata = registry.get_factor_metadata('stock', 'current_debt_ratio')
        index_metadata = registry.get_factor_metadata('index', 'current_debt_ratio')
        
        if stock_metadata and index_metadata:
            print("âœ?å› å­å…ƒæ•°æ®è·å–æˆåŠ?)
            print(f"  è‚¡ç¥¨å› å­: {stock_metadata['name']}")
            print(f"  æŒ‡æ•°å› å­: {index_metadata['name']}")
        else:
            print("âœ?å› å­å…ƒæ•°æ®è·å–å¤±è´?)
        
        # æµ‹è¯•åˆ—å‡ºå¯ç”¨å› å­
        available_factors = registry.list_available_factors()
        print(f"âœ?å¯ç”¨å› å­åˆ—è¡¨: {available_factors}")
        
        return True
        
    except Exception as e:
        print(f"âœ?factor_miningé›†æˆæµ‹è¯•å¤±è´¥: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integrated_factor_miner():
    """æµ‹è¯•é›†æˆå› å­æŒ–æ˜å™?""
    print("\n" + "=" * 60)
    print("æµ‹è¯•3: é›†æˆå› å­æŒ–æ˜å™?)
    print("=" * 60)
    
    if not FACTOR_MINING_AVAILABLE:
        print("âœ?factor_miningæ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡é›†æˆæŒ–æ˜å™¨æµ‹è¯?)
        return False
    
    try:
        # åˆ›å»ºé›†æˆå› å­æŒ–æ˜å™?
        miner = IntegratedFactorMiner()
        print("âœ?é›†æˆå› å­æŒ–æ˜å™¨åˆ›å»ºæˆåŠ?)
        
        # é…ç½®å› å­
        factor_configs = [
            {
                'factor_type': 'stock',
                'factor_id': 'current_debt_ratio',
                'config': {
                    'lookback_quarters': 8,
                    'calculation_method': 'latest'
                }
            }
        ]
        
        # åŠ è½½åŸºç¡€å› å­
        base_factors = miner.load_base_factors(factor_configs)
        if base_factors:
            print(f"âœ?åŸºç¡€å› å­åŠ è½½æˆåŠŸï¼Œæ•°é‡? {len(base_factors)}")
        else:
            print("âœ?åŸºç¡€å› å­åŠ è½½å¤±è´¥")
            return False
        
        # æµ‹è¯•è®¡ç®—å› å­
        test_symbols = ['000001.SZ', '000002.SZ']
        start_date = '2023-01-01'
        end_date = '2024-12-31'
        
        factor_results = miner.calculate_base_factors(test_symbols, start_date, end_date)
        
        if factor_results:
            print(f"âœ?å› å­è®¡ç®—æˆåŠŸï¼Œç»“æœæ•°é‡? {len(factor_results)}")
            for factor_name, result in factor_results.items():
                if hasattr(result, 'shape'):
                    print(f"  {factor_name}: {result.shape}")
                else:
                    print(f"  {factor_name}: {type(result)}")
        else:
            print("âœ?å› å­è®¡ç®—å¤±è´¥")
        
        return True
        
    except Exception as e:
        print(f"âœ?é›†æˆå› å­æŒ–æ˜å™¨æµ‹è¯•å¤±è´? {e}")
        import traceback
        traceback.print_exc()
        return False


def test_factor_combination():
    """æµ‹è¯•å› å­ç»„åˆåŠŸèƒ½"""
    print("\n" + "=" * 60)
    print("æµ‹è¯•4: å› å­ç»„åˆåŠŸèƒ½")
    print("=" * 60)
    
    try:
        # åˆ›å»ºæ¨¡æ‹Ÿçš„å› å­ç»“æ?
        dates = pd.date_range('2023-01-01', '2024-12-31', freq='Q')
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        
        # æ¨¡æ‹ŸçŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­ç»“æ?
        debt_ratio_data = []
        for date in dates:
            for symbol in symbols:
                debt_ratio_data.append({
                    'symbol': symbol,
                    'date': date,
                    'current_debt_ratio': np.random.uniform(0.3, 0.8)
                })
        
        debt_ratio_df = pd.DataFrame(debt_ratio_data)
        
        # æ¨¡æ‹Ÿå¦ä¸€ä¸ªå› å­ç»“æœï¼ˆæµåŠ¨æ¯”ç‡ï¼?
        liquidity_data = []
        for date in dates:
            for symbol in symbols:
                liquidity_data.append({
                    'symbol': symbol,
                    'date': date,
                    'liquidity_ratio': np.random.uniform(1.0, 3.0)
                })
        
        liquidity_df = pd.DataFrame(liquidity_data)
        
        print("âœ?æ¨¡æ‹Ÿå› å­æ•°æ®åˆ›å»ºæˆåŠŸ")
        print(f"  çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡æ•°æ? {debt_ratio_df.shape}")
        print(f"  æµåŠ¨æ¯”ç‡æ•°æ®: {liquidity_df.shape}")
        
        # ç®€å•çš„å› å­ç»„åˆ
        # åˆ›å»ºå¤åˆè´¢åŠ¡é£é™©è¯„åˆ† = (1 - çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç? * æµåŠ¨æ¯”ç‡
        merged_data = pd.merge(debt_ratio_df, liquidity_df, on=['symbol', 'date'])
        merged_data['composite_financial_score'] = (
            (1 - merged_data['current_debt_ratio']) * merged_data['liquidity_ratio']
        )
        
        print("âœ?å› å­ç»„åˆè®¡ç®—æˆåŠŸ")
        print(f"  å¤åˆå› å­æ•°æ®: {merged_data.shape}")
        print(f"  å¤åˆå› å­å€¼èŒƒå›? {merged_data['composite_financial_score'].min():.4f} - {merged_data['composite_financial_score'].max():.4f}")
        
        # æ˜¾ç¤ºéƒ¨åˆ†ç»“æœ
        print("\nå¤åˆè´¢åŠ¡é£é™©è¯„åˆ†ç¤ºä¾‹:")
        sample_data = merged_data.groupby('symbol').tail(1)[['symbol', 'current_debt_ratio', 'liquidity_ratio', 'composite_financial_score']]
        for _, row in sample_data.iterrows():
            print(f"  {row['symbol']}: å¿å€ºå‹åŠ?{row['current_debt_ratio']:.4f}, æµåŠ¨æ€?{row['liquidity_ratio']:.4f}, å¤åˆè¯„åˆ†={row['composite_financial_score']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"âœ?å› å­ç»„åˆæµ‹è¯•å¤±è´¥: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_factor_mining_pipeline():
    """æµ‹è¯•å› å­æŒ–æ˜æµæ°´çº?""
    print("\n" + "=" * 60)
    print("æµ‹è¯•5: å› å­æŒ–æ˜æµæ°´çº?)
    print("=" * 60)
    
    if not FACTOR_MINING_AVAILABLE:
        print("âœ?factor_miningæ¨¡å—ä¸å¯ç”¨ï¼Œè·³è¿‡æµæ°´çº¿æµ‹è¯?)
        return False
    
    try:
        # è·å–é›†æˆç®¡ç†å™?
        integration_manager = get_integration_manager()
        print("âœ?é›†æˆç®¡ç†å™¨è·å–æˆåŠ?)
        
        # æ£€æŸ¥é›†æˆçŠ¶æ€?
        status = integration_manager.get_integration_status()
        print(f"âœ?é›†æˆçŠ¶æ€? {status}")
        
        # åˆ—å‡ºå¯ç”¨çš„åŸºç¡€å› å­
        try:
            available_factors = list_available_base_factors()
            print(f"âœ?å¯ç”¨åŸºç¡€å› å­: {available_factors}")
        except Exception as e:
            print(f"âœ?è·å–å¯ç”¨åŸºç¡€å› å­å¤±è´¥: {e}")
        
        return True
        
    except Exception as e:
        print(f"âœ?å› å­æŒ–æ˜æµæ°´çº¿æµ‹è¯•å¤±è´? {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """è¿è¡Œæ‰€æœ‰æµ‹è¯?""
    print("çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­ä¸factor_miningæ¨¡å—é›†æˆæµ‹è¯•")
    print("=" * 80)
    
    test_results = []
    
    # è¿è¡Œå„é¡¹æµ‹è¯•
    test_results.append(("ç›´æ¥å› å­ä½¿ç”¨", test_direct_factor_usage()))
    test_results.append(("factor_miningé›†æˆ", test_factor_mining_integration()))
    test_results.append(("é›†æˆå› å­æŒ–æ˜å™?, test_integrated_factor_miner()))
    test_results.append(("å› å­ç»„åˆåŠŸèƒ½", test_factor_combination()))
    test_results.append(("å› å­æŒ–æ˜æµæ°´çº?, test_factor_mining_pipeline()))
    
    # æ±‡æ€»ç»“æ?
    print("\n" + "=" * 80)
    print("æµ‹è¯•ç»“æœæ±‡æ€?)
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "âœ?é€šè¿‡" if result else "âœ?å¤±è´¥"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\næ€»ä½“ç»“æœ: {passed}/{total} é¡¹æµ‹è¯•é€šè¿‡ ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("ğŸ‰ æ‰€æœ‰æµ‹è¯•é€šè¿‡ï¼çŸ­æœŸå¿å€ºå‹åŠ›æ¯”ç‡å› å­å·²æˆåŠŸé›†æˆåˆ°factor_miningæ¨¡å—")
    else:
        print("âš ï¸  éƒ¨åˆ†æµ‹è¯•å¤±è´¥ï¼Œè¯·æ£€æŸ¥ç›¸å…³æ¨¡å—çš„å…¼å®¹æ€?)
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 
