# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境验证脚本
确保在正确的rdagent conda环境中运行

Author: AI Assistant
Date: 2024
"""

import sys
import os
from pathlib import Path
import subprocess
from typing import Dict, List, Tuple, Any

class EnvironmentChecker:
    """环境检查器"""
    
    def __init__(self):
        self.project_root = Path("d:/PycharmProjects/my_rdagent")
        self.expected_env = "rdagent"
        self.check_results = {}
        
    def check_conda_environment(self) -> Tuple[bool, str]:
        """检查conda环境"""
        print("🔍 检查conda环境...")
        
        # 方法1: 检查环境变量
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print(f"  环境变量CONDA_DEFAULT_ENV: {conda_env}")
        
        # 方法2: 检查Python路径
        python_path = sys.executable
        print(f"  Python可执行文件路径: {python_path}")
        
        # 方法3: 检查sys.prefix
        python_prefix = sys.prefix
        print(f"  Python前缀路径: {python_prefix}")
        
        # 判断是否在rdagent环境中
        is_correct_env = (
            conda_env == self.expected_env or 
            self.expected_env in python_path or 
            self.expected_env in python_prefix
        )
        
        if is_correct_env:
            message = f"✅ 正确使用{self.expected_env}环境"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ 当前环境: {conda_env}, 应该使用: {self.expected_env}"
            print(f"  {message}")
            print(f"  请执行: conda activate {self.expected_env}")
            return False, message
    
    def check_python_version(self) -> Tuple[bool, str]:
        """检查Python版本"""
        print("\n🐍 检查Python版本...")
        
        version_info = sys.version_info
        version_str = f"{version_info.major}.{version_info.minor}.{version_info.micro}"
        
        print(f"  Python版本: {version_str}")
        print(f"  完整版本信息: {sys.version}")
        
        # 检查版本是否满足要求 (>= 3.7)
        if version_info >= (3, 7):
            message = f"✅ Python版本满足要求: {version_str}"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ Python版本过低: {version_str}, 需要 >= 3.7"
            print(f"  {message}")
            return False, message
    
    def check_project_directory(self) -> Tuple[bool, str]:
        """检查项目目录"""
        print("\n📁 检查项目目录...")
        
        current_dir = Path.cwd()
        print(f"  当前工作目录: {current_dir}")
        print(f"  期望项目根目录: {self.project_root}")
        
        # 检查是否在正确的目录或其子目录中
        try:
            # 检查当前目录是否是项目根目录或其子目录
            is_in_project = (
                current_dir == self.project_root or
                self.project_root in current_dir.parents or
                current_dir.name == "my_rdagent"
            )
            
            if is_in_project:
                message = "✅ 在正确的项目目录中"
                print(f"  {message}")
                return True, message
            else:
                message = f"❌ 不在项目目录中，请切换到: {self.project_root}"
                print(f"  {message}")
                return False, message
                
        except Exception as e:
            message = f"❌ 检查项目目录时出错: {e}"
            print(f"  {message}")
            return False, message
    
    def check_required_packages(self) -> Tuple[bool, str]:
        """检查必需的包"""
        print("\n📦 检查必需的包...")
        
        required_packages = [
            'pandas', 'numpy', 'scipy', 'sklearn', 
            'matplotlib', 'seaborn'
        ]
        
        missing_packages = []
        installed_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                installed_packages.append(package)
                print(f"  ✅ {package}: 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package}: 未安装")
        
        if not missing_packages:
            message = "✅ 所有必需包都已安装"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ 缺少包: {', '.join(missing_packages)}"
            print(f"  {message}")
            print(f"  安装命令: pip install {' '.join(missing_packages)}")
            return False, message
    
    def check_project_structure(self) -> Tuple[bool, str]:
        """检查项目结构"""
        print("\n🏗️ 检查项目结构...")
        
        required_dirs = [
            'factor_analyze',
            'research',
            'research/genetic_algorithm',
            'data_pipeline',
            'strategy_autodev'
        ]
        
        missing_dirs = []
        existing_dirs = []
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                existing_dirs.append(dir_name)
                print(f"  ✅ {dir_name}: 存在")
                
                # 检查__init__.py文件
                init_file = dir_path / '__init__.py'
                if init_file.exists():
                    print(f"    ✅ {dir_name}/__init__.py: 存在")
                else:
                    print(f"    ⚠️ {dir_name}/__init__.py: 缺失")
            else:
                missing_dirs.append(dir_name)
                print(f"  ❌ {dir_name}: 不存在")
        
        if not missing_dirs:
            message = "✅ 项目结构完整"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ 缺少目录: {', '.join(missing_dirs)}"
            print(f"  {message}")
            return False, message
    
    def check_key_files(self) -> Tuple[bool, str]:
        """检查关键文件"""
        print("\n📄 检查关键文件...")
        
        key_files = [
            'factor_analyze\behavioral_factors\genetic_trading_behavior_factor.py',
            'research/genetic_algorithm/genetic_factor_engine.py',
            'research/genetic_algorithm/kaiyuan_trading_behavior_research.py',
            'research/genetic_algorithm/integrate_kaiyuan_research.py'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                existing_files.append(file_path)
                file_size = full_path.stat().st_size
                print(f"  ✅ {file_path}: 存在 ({file_size} bytes)")
            else:
                missing_files.append(file_path)
                print(f"  ❌ {file_path}: 不存在")
        
        if not missing_files:
            message = "✅ 所有关键文件都存在"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ 缺少文件: {', '.join(missing_files)}"
            print(f"  {message}")
            return False, message
    
    def setup_python_path(self) -> Tuple[bool, str]:
        """设置Python路径"""
        print("\n🔧 设置Python路径...")
        
        if self.project_root.exists():
            project_root_str = str(self.project_root)
            if project_root_str not in sys.path:
                sys.path.insert(0, project_root_str)
                message = f"✅ 已添加项目根目录到Python路径: {project_root_str}"
                print(f"  {message}")
            else:
                message = "✅ 项目根目录已在Python路径中"
                print(f"  {message}")
            
            # 显示当前Python路径
            print("  当前Python路径:")
            for i, path in enumerate(sys.path[:5]):
                print(f"    {i+1}. {path}")
            
            return True, message
        else:
            message = f"❌ 项目根目录不存在: {self.project_root}"
            print(f"  {message}")
            return False, message
    
    def test_basic_imports(self) -> Tuple[bool, str]:
        """测试基本导入"""
        print("\n🧪 测试基本导入...")
        
        import_tests = [
            ('pandas', 'import pandas as pd'),
            ('numpy', 'import numpy as np'),
            ('scipy', 'import scipy'),
            ('sklearn', 'import sklearn')
        ]
        
        failed_imports = []
        successful_imports = []
        
        for name, import_stmt in import_tests:
            try:
                exec(import_stmt)
                successful_imports.append(name)
                print(f"  ✅ {name}: 导入成功")
            except Exception as e:
                failed_imports.append((name, str(e)))
                print(f"  ❌ {name}: 导入失败 - {e}")
        
        if not failed_imports:
            message = "✅ 所有基本库导入成功"
            print(f"  {message}")
            return True, message
        else:
            message = f"❌ 导入失败: {', '.join([name for name, _ in failed_imports])}"
            print(f"  {message}")
            return False, message
    
    def generate_fix_commands(self, failed_checks: List[str]) -> List[str]:
        """生成修复命令"""
        commands = []
        
        if 'conda_environment' in failed_checks:
            commands.append(f"conda activate {self.expected_env}")
        
        if 'required_packages' in failed_checks:
            commands.append("pip install pandas numpy scipy scikit-learn matplotlib seaborn")
        
        if 'project_directory' in failed_checks:
            commands.append(f"cd {self.project_root}")
        
        if 'project_structure' in failed_checks:
            commands.extend([
                f"mkdir -p {self.project_root}/factor_analyze",
                f"mkdir -p {self.project_root}/research/genetic_algorithm",
                f"mkdir -p {self.project_root}/data_pipeline",
                f"mkdir -p {self.project_root}/strategy_autodev"
            ])
        
        return commands
    
    def run_comprehensive_check(self) -> Dict[str, Any]:
        """运行全面检查"""
        print("🎯 rdagent环境全面检查")
        print("=" * 60)
        print(f"项目根目录: {self.project_root}")
        print(f"期望conda环境: {self.expected_env}")
        print("=" * 60)
        
        checks = {
            'conda_environment': self.check_conda_environment(),
            'python_version': self.check_python_version(),
            'project_directory': self.check_project_directory(),
            'required_packages': self.check_required_packages(),
            'project_structure': self.check_project_structure(),
            'key_files': self.check_key_files(),
            'python_path': self.setup_python_path(),
            'basic_imports': self.test_basic_imports()
        }
        
        # 统计结果
        passed_checks = [name for name, (success, _) in checks.items() if success]
        failed_checks = [name for name, (success, _) in checks.items() if not success]
        
        print("\n" + "=" * 60)
        print("📋 检查结果摘要")
        print("=" * 60)
        
        print(f"✅ 通过的检查 ({len(passed_checks)}/{len(checks)}):")
        for check in passed_checks:
            print(f"  - {check}")
        
        if failed_checks:
            print(f"\n❌ 失败的检查 ({len(failed_checks)}/{len(checks)}):")
            for check in failed_checks:
                print(f"  - {check}: {checks[check][1]}")
            
            print("\n🔧 建议的修复命令:")
            fix_commands = self.generate_fix_commands(failed_checks)
            for i, cmd in enumerate(fix_commands, 1):
                print(f"  {i}. {cmd}")
        
        # 总体评估
        if len(passed_checks) == len(checks):
            print("\n🎉 所有检查都通过！环境配置正确。")
            status = "success"
        elif len(passed_checks) >= len(checks) * 0.8:
            print("\n⚠️ 大部分检查通过，但仍有一些问题需要修复。")
            status = "warning"
        else:
            print("\n❌ 多个检查失败，需要修复环境配置。")
            status = "error"
        
        return {
            'status': status,
            'total_checks': len(checks),
            'passed_checks': len(passed_checks),
            'failed_checks': len(failed_checks),
            'check_details': checks,
            'fix_commands': self.generate_fix_commands(failed_checks) if failed_checks else []
        }

def main():
    """主函数"""
    try:
        checker = EnvironmentChecker()
        results = checker.run_comprehensive_check()
        
        # 返回适当的退出码
        if results['status'] == 'success':
            print("\n✨ 环境检查完成，可以安全地运行项目脚本！")
            return 0
        elif results['status'] == 'warning':
            print("\n⚠️ 环境基本可用，但建议修复发现的问题。")
            return 1
        else:
            print("\n❌ 环境配置有严重问题，请先修复后再运行项目脚本。")
            return 2
            
    except Exception as e:
        print(f"\n💥 环境检查过程中出现错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return 3

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)