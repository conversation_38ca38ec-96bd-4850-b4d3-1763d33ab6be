
# 数据适配器统一迁移指南

## 问题描述
项目中存在多个重复的数据适配器定义，导致导入混乱和警告。

## 解决方案
统一使用 `adapters/` 目录下的标准实现。

## 迁移步骤

### 1. 更新导入语句
将以下导入语句：
```python
# 旧方式（各种重复定义）
class StockDataAdapter:
    pass
class FundamentalDataAdapter:
    pass
```

替换为：
```python
# 新方式（统一导入）
from adapters.stock_data_adapter import StockDataAdapter
from adapters.fundamental_data_adapter import FundamentalDataAdapter
from adapters.market_data_adapter import MarketDataAdapter
from adapters.factor_data_adapter import FactorDataAdapter
```

### 2. 使用统一导入模块
或者使用我们提供的统一导入模块：
```python
from unified_adapter_import import (
    StockDataAdapter,
    FundamentalDataAdapter,
    MarketDataAdapter,
    FactorDataAdapter
)
```

### 3. 处理导入失败的情况
如果标准适配器不可用，统一导入模块会自动提供Mock版本。

## 标准实现位置
- `adapters/stock_data_adapter.py` - 股票数据适配器
- `adapters/fundamental_data_adapter.py` - 基本面数据适配器
- `adapters/market_data_adapter.py` - 市场数据适配器
- `adapters/factor_data_adapter.py` - 因子数据适配器

## 注意事项
1. 标准实现继承自 `BaseDataAdapter`
2. 提供完整的数据获取和处理功能
3. 支持Tushare Pro接口
4. 包含错误处理和日志记录
