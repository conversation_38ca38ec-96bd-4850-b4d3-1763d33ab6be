# -*- coding: utf-8 -*-
"""
增强DQN交易策略
基于Hands-On-Machine-Learning-for-Algorithmic-Trading Chapter21的强化学习交易环境
集成统一策略管理系统和data_pipeline

特性:
1. 基于OpenAI Gym的交易环境设计
2. Double Deep Q-Network (DDQN)算法
3. 优先级经验回放 (Prioritized Experience Replay)
4. 集成data_pipeline真实数据
5. 统一策略管理系统接口
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque, namedtuple
import random
import logging
from typing import Tuple, List, Optional, Dict, Any
from datetime import datetime, timedelta
import gym
from gym import spaces
from gym.utils import seeding
from sklearn.preprocessing import StandardScaler

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

# 导入项目模块
try:
    from data_adapter import DataAdapter
    from strategy_autodev.core.base_strategy import BaseStrategy
    from strategy_autodev.core.strategy_categories import StrategyCategory
except ImportError as e:
    print(f"警告：无法导入项目模块: {e}")
    # 定义基础类以确保代码可运行
    class BaseStrategy:
        def __init__(self, *args, **kwargs):
            pass
        
        def generate_signals(self, data):
            return pd.Series(0, index=data.index)
    
    class StrategyCategory:
        REINFORCEMENT_LEARNING = "reinforcement_learning"
    
    class DataAdapter:
        @staticmethod
        def get_stock_data(symbol, start_date, end_date):
            # 模拟数据
            dates = pd.date_range(start_date, end_date, freq='D')
            np.random.seed(42)
            prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.02)
            return pd.DataFrame({
                'open': prices * (1 + np.random.randn(len(dates)) * 0.001),
                'high': prices * (1 + np.abs(np.random.randn(len(dates))) * 0.002),
                'low': prices * (1 - np.abs(np.random.randn(len(dates))) * 0.002),
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, len(dates))
            }, index=dates)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 经验回放元组
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])


class PrioritizedReplayBuffer:
    """
    优先级经验回放缓冲区
    基于TD误差的优先级采样，提高学习效率
    """
    
    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4):
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.buffer = []
        self.pos = 0
        self.priorities = np.zeros((capacity,), dtype=np.float32)
    
    def push(self, state, action, reward, next_state, done):
        """添加经验到缓冲区"""
        max_prio = self.priorities.max() if self.buffer else 1.0
        
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
        
        self.buffer[self.pos] = Experience(state, action, reward, next_state, done)
        self.priorities[self.pos] = max_prio
        self.pos = (self.pos + 1) % self.capacity
    
    def sample(self, batch_size: int):
        """基于优先级采样"""
        if len(self.buffer) == self.capacity:
            prios = self.priorities
        else:
            prios = self.priorities[:self.pos]
        
        probs = prios ** self.alpha
        probs /= probs.sum()
        
        indices = np.random.choice(len(self.buffer), batch_size, p=probs)
        samples = [self.buffer[idx] for idx in indices]
        
        total = len(self.buffer)
        weights = (total * probs[indices]) ** (-self.beta)
        weights /= weights.max()
        
        return samples, indices, weights
    
    def update_priorities(self, batch_indices, batch_priorities):
        """更新优先级"""
        for idx, prio in zip(batch_indices, batch_priorities):
            self.priorities[idx] = prio
    
    def __len__(self):
        return len(self.buffer)


class DQNNetwork(nn.Module):
    """
    Deep Q-Network
    使用全连接层处理状态特征
    """
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 128):
        super(DQNNetwork, self).__init__()
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size)
        self.fc4 = nn.Linear(hidden_size, action_size)
        self.dropout = nn.Dropout(0.2)
    
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        return x


class TradingEnvironment(gym.Env):
    """
    股票交易环境
    基于OpenAI Gym接口设计
    """
    
    def __init__(self, data: pd.DataFrame, window_size: int = 10, 
                 trading_cost: float = 0.001, initial_balance: float = 100000):
        super(TradingEnvironment, self).__init__()
        
        self.data = data.copy()
        self.window_size = window_size
        self.trading_cost = trading_cost
        self.initial_balance = initial_balance
        
        # 计算技术指标
        self._calculate_features()
        
        # 动作空间：0=持有, 1=买入, 2=卖出
        self.action_space = spaces.Discrete(3)
        
        # 状态空间：技术指标窗口
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(window_size * len(self.feature_columns),), 
            dtype=np.float32
        )
        
        self.reset()
    
    def _calculate_features(self):
        """计算技术指标特征"""
        df = self.data.copy()
        
        # 价格相关特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # 移动平均
        for window in [5, 10, 20]:
            df[f'ma_{window}'] = df['close'].rolling(window).mean()
            df[f'price_ma_ratio_{window}'] = df['close'] / df[f'ma_{window}']
        
        # 波动率
        df['volatility_5'] = df['returns'].rolling(5).std()
        df['volatility_20'] = df['returns'].rolling(20).std()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量特征
        df['volume_ma_5'] = df['volume'].rolling(5).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma_5']
        
        # 价格位置
        df['price_position'] = (df['close'] - df['low'].rolling(20).min()) / \
                              (df['high'].rolling(20).max() - df['low'].rolling(20).min())
        
        # 选择特征列
        self.feature_columns = [
            'returns', 'log_returns', 'price_ma_ratio_5', 'price_ma_ratio_10', 
            'price_ma_ratio_20', 'volatility_5', 'volatility_20', 'rsi', 
            'volume_ratio', 'price_position'
        ]
        
        # 标准化特征
        scaler = StandardScaler()
        df[self.feature_columns] = scaler.fit_transform(df[self.feature_columns].fillna(0))
        
        self.data = df.dropna().reset_index(drop=True)
        logger.info(f"特征计算完成，数据长度: {len(self.data)}")
    
    def reset(self):
        """重置环境"""
        self.current_step = self.window_size
        self.balance = self.initial_balance
        self.shares_held = 0
        self.net_worth = self.initial_balance
        self.max_net_worth = self.initial_balance
        self.trades = []
        
        return self._get_observation()
    
    def _get_observation(self):
        """获取当前状态观测"""
        start_idx = self.current_step - self.window_size
        end_idx = self.current_step
        
        obs = self.data[self.feature_columns].iloc[start_idx:end_idx].values
        return obs.flatten().astype(np.float32)
    
    def step(self, action):
        """执行动作"""
        current_price = self.data.iloc[self.current_step]['close']
        
        # 执行交易
        reward = 0
        if action == 1:  # 买入
            if self.balance > current_price * (1 + self.trading_cost):
                shares_to_buy = self.balance // (current_price * (1 + self.trading_cost))
                cost = shares_to_buy * current_price * (1 + self.trading_cost)
                self.balance -= cost
                self.shares_held += shares_to_buy
                self.trades.append(('BUY', self.current_step, current_price, shares_to_buy))
        
        elif action == 2:  # 卖出
            if self.shares_held > 0:
                revenue = self.shares_held * current_price * (1 - self.trading_cost)
                self.balance += revenue
                self.trades.append(('SELL', self.current_step, current_price, self.shares_held))
                self.shares_held = 0
        
        # 计算净值
        self.net_worth = self.balance + self.shares_held * current_price
        
        # 计算奖励
        if self.current_step > self.window_size:
            prev_price = self.data.iloc[self.current_step - 1]['close']
            market_return = (current_price - prev_price) / prev_price
            portfolio_return = (self.net_worth - self.initial_balance) / self.initial_balance
            
            # 奖励函数：超额收益 + 风险调整
            reward = portfolio_return - market_return
            
            # 最大回撤惩罚
            if self.net_worth > self.max_net_worth:
                self.max_net_worth = self.net_worth
            else:
                drawdown = (self.max_net_worth - self.net_worth) / self.max_net_worth
                reward -= drawdown * 0.1
        
        # 移动到下一步
        self.current_step += 1
        done = self.current_step >= len(self.data) - 1
        
        obs = self._get_observation() if not done else np.zeros(self.observation_space.shape)
        info = {
            'net_worth': self.net_worth,
            'balance': self.balance,
            'shares_held': self.shares_held,
            'total_trades': len(self.trades)
        }
        
        return obs, reward, done, info


class DDQNAgent:
    """
    Double Deep Q-Network Agent
    """
    
    def __init__(self, state_size: int, action_size: int, lr: float = 0.001,
                 gamma: float = 0.95, epsilon: float = 1.0, epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01, memory_size: int = 10000, batch_size: int = 32):
        
        self.state_size = state_size
        self.action_size = action_size
        self.lr = lr
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.batch_size = batch_size
        
        # 神经网络
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.q_network = DQNNetwork(state_size, action_size).to(self.device)
        self.target_network = DQNNetwork(state_size, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)
        
        # 经验回放
        self.memory = PrioritizedReplayBuffer(memory_size)
        
        # 更新目标网络
        self.update_target_network()
        
        logger.info(f"DDQN Agent初始化完成，设备: {self.device}")
    
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """存储经验"""
        self.memory.push(state, action, reward, next_state, done)
    
    def act(self, state, training=True):
        """选择动作"""
        if training and np.random.random() <= self.epsilon:
            return random.randrange(self.action_size)
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return np.argmax(q_values.cpu().data.numpy())
    
    def replay(self):
        """经验回放训练"""
        if len(self.memory) < self.batch_size:
            return
        
        # 采样经验
        experiences, indices, weights = self.memory.sample(self.batch_size)
        
        states = torch.FloatTensor([e.state for e in experiences]).to(self.device)
        actions = torch.LongTensor([e.action for e in experiences]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in experiences]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in experiences]).to(self.device)
        dones = torch.BoolTensor([e.done for e in experiences]).to(self.device)
        weights = torch.FloatTensor(weights).to(self.device)
        
        # 当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Double DQN: 使用主网络选择动作，目标网络评估价值
        next_actions = self.q_network(next_states).max(1)[1].unsqueeze(1)
        next_q_values = self.target_network(next_states).gather(1, next_actions).detach()
        target_q_values = rewards.unsqueeze(1) + (self.gamma * next_q_values * ~dones.unsqueeze(1))
        
        # 计算TD误差
        td_errors = torch.abs(current_q_values - target_q_values).detach().cpu().numpy()
        
        # 加权损失
        loss = (weights.unsqueeze(1) * F.mse_loss(current_q_values, target_q_values, reduction='none')).mean()
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # 更新优先级
        self.memory.update_priorities(indices, td_errors.flatten() + 1e-6)
        
        # 衰减epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay


class EnhancedDQNTradingStrategy(BaseStrategy):
    """
    增强DQN交易策略
    集成统一策略管理系统
    """
    
    def __init__(self, symbol: str = "000001.SZ", window_size: int = 10,
                 training_episodes: int = 1000, **kwargs):
        super().__init__(**kwargs)
        
        self.symbol = symbol
        self.window_size = window_size
        self.training_episodes = training_episodes
        self.agent = None
        self.env = None
        self.is_trained = False
        
        logger.info(f"增强DQN交易策略初始化: {symbol}")
    
    def prepare_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """准备训练数据"""
        try:
            data = DataAdapter.get_stock_data(self.symbol, start_date, end_date)
            logger.info(f"获取数据成功: {len(data)} 条记录")
            return data
        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            raise
    
    def train(self, data: pd.DataFrame):
        """训练DQN模型"""
        logger.info("开始训练DQN模型...")
        
        # 创建环境
        self.env = TradingEnvironment(data, self.window_size)
        
        # 创建智能体
        state_size = self.env.observation_space.shape[0]
        action_size = self.env.action_space.n
        self.agent = DDQNAgent(state_size, action_size)
        
        # 训练循环
        scores = deque(maxlen=100)
        
        for episode in range(self.training_episodes):
            state = self.env.reset()
            total_reward = 0
            
            while True:
                action = self.agent.act(state)
                next_state, reward, done, info = self.env.step(action)
                
                self.agent.remember(state, action, reward, next_state, done)
                state = next_state
                total_reward += reward
                
                if done:
                    break
            
            scores.append(total_reward)
            
            # 经验回放
            if len(self.agent.memory) > self.agent.batch_size:
                self.agent.replay()
            
            # 更新目标网络
            if episode % 100 == 0:
                self.agent.update_target_network()
            
            # 记录进度
            if episode % 100 == 0:
                avg_score = np.mean(scores)
                logger.info(f"Episode {episode}, Average Score: {avg_score:.2f}, "
                          f"Epsilon: {self.agent.epsilon:.3f}, Net Worth: {info['net_worth']:.2f}")
        
        self.is_trained = True
        logger.info("DQN模型训练完成")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成交易信号"""
        if not self.is_trained or self.agent is None:
            logger.warning("模型未训练，返回空信号")
            return pd.Series(0, index=data.index)
        
        # 创建测试环境
        test_env = TradingEnvironment(data, self.window_size)
        state = test_env.reset()
        
        signals = []
        
        while True:
            action = self.agent.act(state, training=False)
            next_state, _, done, _ = test_env.step(action)
            
            # 转换动作为信号：0=持有, 1=买入, 2=卖出
            if action == 1:
                signal = 1  # 买入信号
            elif action == 2:
                signal = -1  # 卖出信号
            else:
                signal = 0  # 持有信号
            
            signals.append(signal)
            state = next_state
            
            if done:
                break
        
        # 对齐信号长度
        signal_series = pd.Series(0, index=data.index)
        start_idx = min(len(signals), len(data) - self.window_size)
        signal_series.iloc[self.window_size:self.window_size + start_idx] = signals[:start_idx]
        
        return signal_series
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'Enhanced DQN Trading Strategy',
            'category': StrategyCategory.REINFORCEMENT_LEARNING,
            'description': '基于Double Deep Q-Network的强化学习交易策略',
            'parameters': {
                'symbol': self.symbol,
                'window_size': self.window_size,
                'training_episodes': self.training_episodes
            },
            'is_trained': self.is_trained
        }


def create_strategy(symbol: str = "000001.SZ", **kwargs) -> EnhancedDQNTradingStrategy:
    """创建策略实例"""
    return EnhancedDQNTradingStrategy(symbol=symbol, **kwargs)


if __name__ == "__main__":
    # 测试策略
    strategy = create_strategy("000001.SZ")
    
    # 准备数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365*2)).strftime('%Y-%m-%d')
    
    data = strategy.prepare_data(start_date, end_date)
    
    # 训练模型
    strategy.train(data)
    
    # 生成信号
    signals = strategy.generate_signals(data)
    
    print(f"策略信息: {strategy.get_strategy_info()}")
    print(f"信号统计: {signals.value_counts()}")
    
    logger.info("增强DQN交易策略测试完成")