# -*- coding: utf-8 -*-
"""
基于因子的策略 (Factor-Based Strategy)
Factor-Based Strategy Implementation

基于"Hands-On Machine Learning for Algorithmic Trading" Chapter 05
实现基于因子的交易策略框架，整合多个alpha因子

参考文献:
- Fama, E. F., & French, K. R. (1993). Common risk factors in the returns on stocks and bonds
- <PERSON><PERSON>, M. M<PERSON> (1997). On persistence in mutual fund performance
- Barra Risk Model Handbook
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
import logging
from datetime import datetime, timedelta
import warnings
from abc import ABC, abstractmethod

# 导入数据适配器
try:
    from adapters import BaseDataAdapter
    from adapters.adapter_compatibility import DataAdapter as DataAdapterManager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from strategy_autodev.data_adapter import DataAdapterManager, BaseDataAdapter
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        try:
            from ...data_adapter import DataAdapterManager, BaseDataAdapter
            DATA_ADAPTER_AVAILABLE = True
        except ImportError:
            DATA_ADAPTER_AVAILABLE = False
            logging.warning("数据适配器不可用，使用模拟数据")

# 导入基础策略类
try:
    from strategy_autodev.core.base_strategy import BaseStrategy
    BASE_STRATEGY_AVAILABLE = True
except ImportError:
    try:
        from strategy_autodev.base_strategy import BaseStrategy
        BASE_STRATEGY_AVAILABLE = True
    except ImportError:
        try:
            from ...core.base_strategy import BaseStrategy
            BASE_STRATEGY_AVAILABLE = True
        except ImportError:
            BASE_STRATEGY_AVAILABLE = False
            logging.warning("基础策略类不可用，使用简化实现")

# 导入均值回归因子
try:
    from .mean_reversion_factor import MeanReversionFactor
except ImportError:
    MeanReversionFactor = None
    logging.warning("均值回归因子不可用")

logger = logging.getLogger(__name__)

# 如果基础策略类不可用，定义简化版本
if not BASE_STRATEGY_AVAILABLE:
    class BaseStrategy:
        def __init__(self, **kwargs):
            self.params = kwargs
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def initialize(self):
            pass
        
        def handle_data(self, context, data):
            pass

class BaseFactor(ABC):
    """
    基础因子抽象类
    """
    
    @abstractmethod
    def calculate_factor(self, 
                        price_data: pd.DataFrame,
                        symbol: str) -> pd.Series:
        """
        计算因子值
        
        Args:
            price_data: 价格数据
            symbol: 股票代码
            
        Returns:
            因子值序列
        """
        pass
    
    @abstractmethod
    def get_factor_name(self) -> str:
        """
        获取因子名称
        """
        pass

class MomentumFactor(BaseFactor):
    """
    动量因子
    """
    
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.logger = logging.getLogger(f"{__name__}.MomentumFactor")
    
    def calculate_factor(self, 
                        price_data: pd.DataFrame,
                        symbol: str) -> pd.Series:
        """
        计算动量因子
        """
        try:
            if price_data.empty or 'close' not in price_data.columns:
                return pd.Series()
            
            close_prices = price_data['close']
            
            # 计算动量（过去N天的累计收益率）
            momentum = close_prices.pct_change(periods=self.lookback_period)
            
            # 标准化
            momentum_mean = momentum.rolling(window=252).mean()
            momentum_std = momentum.rolling(window=252).std()
            
            standardized_momentum = (momentum - momentum_mean) / momentum_std
            
            return standardized_momentum.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算动量因子失败: {e}")
            return pd.Series()
    
    def get_factor_name(self) -> str:
        return f"momentum_{self.lookback_period}d"

class VolatilityFactor(BaseFactor):
    """
    波动率因子
    """
    
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.logger = logging.getLogger(f"{__name__}.VolatilityFactor")
    
    def calculate_factor(self, 
                        price_data: pd.DataFrame,
                        symbol: str) -> pd.Series:
        """
        计算波动率因子
        """
        try:
            if price_data.empty or 'close' not in price_data.columns:
                return pd.Series()
            
            close_prices = price_data['close']
            returns = close_prices.pct_change()
            
            # 计算滚动波动率
            volatility = returns.rolling(window=self.lookback_period).std()
            
            # 标准化（负波动率，因为低波动率通常更好）
            vol_mean = volatility.rolling(window=252).mean()
            vol_std = volatility.rolling(window=252).std()
            
            standardized_volatility = -(volatility - vol_mean) / vol_std
            
            return standardized_volatility.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算波动率因子失败: {e}")
            return pd.Series()
    
    def get_factor_name(self) -> str:
        return f"volatility_{self.lookback_period}d"

class ValueFactor(BaseFactor):
    """
    价值因子（简化版，基于价格相对位置）
    """
    
    def __init__(self, lookback_period: int = 252):
        self.lookback_period = lookback_period
        self.logger = logging.getLogger(f"{__name__}.ValueFactor")
    
    def calculate_factor(self, 
                        price_data: pd.DataFrame,
                        symbol: str) -> pd.Series:
        """
        计算价值因子
        """
        try:
            if price_data.empty or 'close' not in price_data.columns:
                return pd.Series()
            
            close_prices = price_data['close']
            
            # 计算价格相对于历史高点的位置（价值指标的代理）
            rolling_max = close_prices.rolling(window=self.lookback_period).max()
            price_position = close_prices / rolling_max
            
            # 价值因子：价格越低相对于历史高点，价值越高
            value_factor = 1 - price_position
            
            # 标准化
            value_mean = value_factor.rolling(window=252).mean()
            value_std = value_factor.rolling(window=252).std()
            
            standardized_value = (value_factor - value_mean) / value_std
            
            return standardized_value.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算价值因子失败: {e}")
            return pd.Series()
    
    def get_factor_name(self) -> str:
        return f"value_{self.lookback_period}d"

class FactorBasedStrategy(BaseStrategy):
    """
    基于因子的交易策略
    
    整合多个alpha因子，通过加权组合生成交易信号
    """
    
    def __init__(self,
                 factors: List[BaseFactor] = None,
                 factor_weights: Dict[str, float] = None,
                 rebalance_frequency: str = 'daily',
                 max_position_size: float = 0.1,
                 min_factor_score: float = 0.5,
                 data_adapter_name: str = 'default',
                 **kwargs):
        """
        初始化基于因子的策略
        
        Args:
            factors: 因子列表
            factor_weights: 因子权重字典
            rebalance_frequency: 再平衡频率
            max_position_size: 最大仓位大小
            min_factor_score: 最小因子得分阈值
            data_adapter_name: 数据适配器名称
        """
        super().__init__(**kwargs)
        
        # 初始化默认因子
        if factors is None:
            factors = [
                MomentumFactor(lookback_period=20),
                VolatilityFactor(lookback_period=20),
                ValueFactor(lookback_period=252)
            ]
            
            # 如果均值回归因子可用，添加它
            if MeanReversionFactor is not None:
                factors.append(MeanReversionFactor(
                    lookback_period=20,
                    zscore_window=252,
                    entry_threshold=2.0,
                    exit_threshold=0.5
                ))
        
        self.factors = factors
        
        # 初始化因子权重
        if factor_weights is None:
            # 等权重
            factor_weights = {factor.get_factor_name(): 1.0 / len(factors) 
                            for factor in factors}
        
        self.factor_weights = factor_weights
        self.rebalance_frequency = rebalance_frequency
        self.max_position_size = max_position_size
        self.min_factor_score = min_factor_score
        self.data_adapter_name = data_adapter_name
        
        self.logger = logging.getLogger(f"{__name__}.FactorBasedStrategy")
        
        # 初始化数据适配器
        if DATA_ADAPTER_AVAILABLE:
            self.data_manager = DataAdapterManager()
            self.data_adapter = self.data_manager.get_adapter(data_adapter_name)
        else:
            self.data_manager = None
            self.data_adapter = None
            self.logger.warning("数据适配器不可用，将使用模拟数据")
        
        # 存储因子数据
        self.factor_data = {}
        self.composite_scores = {}
        self.position_data = {}
        
        # 性能指标
        self.performance_metrics = {}
        
        self.logger.info(f"基于因子的策略初始化完成，使用{len(self.factors)}个因子")
    
    def fetch_market_data(self, 
                         symbols: List[str],
                         start_date: str,
                         end_date: str,
                         fields: List[str] = None) -> Dict[str, pd.DataFrame]:
        """
        获取市场数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 数据字段
            
        Returns:
            市场数据字典
        """
        try:
            if fields is None:
                fields = ['open', 'high', 'low', 'close', 'volume']
            
            market_data = {}
            
            if self.data_adapter:
                # 使用真实数据适配器
                for symbol in symbols:
                    try:
                        data = self.data_adapter.fetch_data(
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date,
                            fields=fields
                        )
                        
                        if data is not None and not data.empty:
                            market_data[symbol] = data
                        else:
                            self.logger.warning(f"无法获取{symbol}的数据，使用模拟数据")
                            market_data[symbol] = self._generate_mock_data(
                                start_date, end_date, symbol)
                    
                    except Exception as e:
                        self.logger.error(f"获取{symbol}数据失败: {e}，使用模拟数据")
                        market_data[symbol] = self._generate_mock_data(
                            start_date, end_date, symbol)
            else:
                # 使用模拟数据
                for symbol in symbols:
                    market_data[symbol] = self._generate_mock_data(
                        start_date, end_date, symbol)
            
            self.logger.info(f"成功获取{len(market_data)}只股票的数据")
            return market_data
            
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return {}
    
    def _generate_mock_data(self, 
                           start_date: str, 
                           end_date: str, 
                           symbol: str) -> pd.DataFrame:
        """
        生成模拟数据
        """
        try:
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            n_days = len(dates)
            
            # 生成随机价格数据
            np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
            
            # 基础价格
            base_price = 100 + (hash(symbol) % 100)
            
            # 生成价格序列（几何布朗运动）
            returns = np.random.normal(0.0005, 0.02, n_days)
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            prices = np.array(prices)
            
            # 生成OHLC数据
            high_noise = np.random.uniform(0.005, 0.02, n_days)
            low_noise = np.random.uniform(-0.02, -0.005, n_days)
            
            data = pd.DataFrame({
                'open': prices * (1 + np.random.uniform(-0.01, 0.01, n_days)),
                'high': prices * (1 + high_noise),
                'low': prices * (1 + low_noise),
                'close': prices,
                'volume': np.random.randint(100000, 1000000, n_days)
            }, index=dates)
            
            return data
            
        except Exception as e:
            self.logger.error(f"生成模拟数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_factor_scores(self, 
                              price_data: pd.DataFrame,
                              symbol: str) -> Dict[str, pd.Series]:
        """
        计算所有因子得分
        
        Args:
            price_data: 价格数据
            symbol: 股票代码
            
        Returns:
            因子得分字典
        """
        try:
            factor_scores = {}
            
            for factor in self.factors:
                try:
                    factor_name = factor.get_factor_name()
                    
                    if hasattr(factor, 'calculate_factor'):
                        # 标准因子
                        score = factor.calculate_factor(price_data, symbol)
                    elif hasattr(factor, 'calculate_mean_reversion_factor'):
                        # 均值回归因子
                        score = factor.calculate_mean_reversion_factor(price_data, symbol)
                    else:
                        self.logger.warning(f"因子{factor_name}没有有效的计算方法")
                        continue
                    
                    if not score.empty:
                        factor_scores[factor_name] = score
                        self.logger.debug(f"计算{symbol}的{factor_name}因子完成")
                    
                except Exception as e:
                    self.logger.error(f"计算{symbol}的{factor.get_factor_name()}因子失败: {e}")
                    continue
            
            return factor_scores
            
        except Exception as e:
            self.logger.error(f"计算{symbol}因子得分失败: {e}")
            return {}
    
    def calculate_composite_score(self, 
                                factor_scores: Dict[str, pd.Series],
                                symbol: str) -> pd.Series:
        """
        计算综合因子得分
        
        Args:
            factor_scores: 因子得分字典
            symbol: 股票代码
            
        Returns:
            综合因子得分序列
        """
        try:
            if not factor_scores:
                return pd.Series()
            
            # 对齐所有因子得分的时间索引
            all_dates = set()
            for score in factor_scores.values():
                all_dates.update(score.index)
            
            all_dates = sorted(all_dates)
            
            # 创建对齐的因子矩阵
            factor_matrix = pd.DataFrame(index=all_dates)
            
            for factor_name, score in factor_scores.items():
                factor_matrix[factor_name] = score.reindex(all_dates).fillna(0)
            
            # 计算加权综合得分
            composite_score = pd.Series(0.0, index=all_dates)
            
            total_weight = 0
            for factor_name in factor_matrix.columns:
                weight = self.factor_weights.get(factor_name, 0)
                if weight > 0:
                    composite_score += weight * factor_matrix[factor_name]
                    total_weight += weight
            
            # 标准化权重
            if total_weight > 0:
                composite_score = composite_score / total_weight
            
            # 存储综合得分
            self.composite_scores[symbol] = composite_score
            
            self.logger.debug(f"计算{symbol}综合因子得分完成")
            return composite_score
            
        except Exception as e:
            self.logger.error(f"计算{symbol}综合因子得分失败: {e}")
            return pd.Series()
    
    def generate_trading_signals(self, 
                               composite_score: pd.Series,
                               symbol: str) -> pd.Series:
        """
        生成交易信号
        
        Args:
            composite_score: 综合因子得分
            symbol: 股票代码
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        try:
            if composite_score.empty:
                return pd.Series()
            
            signals = pd.Series(0, index=composite_score.index)
            
            # 基于综合得分生成信号
            for date, score in composite_score.items():
                if pd.isna(score):
                    signals[date] = 0
                elif score >= self.min_factor_score:
                    signals[date] = 1  # 买入信号
                elif score <= -self.min_factor_score:
                    signals[date] = -1  # 卖出信号
                else:
                    signals[date] = 0  # 持有信号
            
            self.logger.debug(f"生成{symbol}交易信号完成")
            return signals
            
        except Exception as e:
            self.logger.error(f"生成{symbol}交易信号失败: {e}")
            return pd.Series()
    
    def calculate_position_sizes(self, 
                               signals: pd.Series,
                               composite_score: pd.Series,
                               symbol: str) -> pd.Series:
        """
        计算仓位大小
        
        Args:
            signals: 交易信号
            composite_score: 综合因子得分
            symbol: 股票代码
            
        Returns:
            仓位大小序列
        """
        try:
            if signals.empty or composite_score.empty:
                return pd.Series()
            
            positions = pd.Series(0.0, index=signals.index)
            
            for date in signals.index:
                signal = signals[date]
                score = composite_score[date]
                
                if signal != 0 and not pd.isna(score):
                    # 基于因子强度调整仓位大小
                    score_strength = min(abs(score) / self.min_factor_score, 2.0)
                    position_size = self.max_position_size * score_strength
                    
                    if signal > 0:
                        positions[date] = position_size
                    elif signal < 0:
                        positions[date] = -position_size
                else:
                    positions[date] = 0.0
            
            # 前向填充仓位
            positions = positions.fillna(method='ffill').fillna(0)
            
            # 存储仓位数据
            self.position_data[symbol] = positions
            
            self.logger.debug(f"计算{symbol}仓位大小完成")
            return positions
            
        except Exception as e:
            self.logger.error(f"计算{symbol}仓位大小失败: {e}")
            return pd.Series()
    
    def run_strategy_backtest(self, 
                            symbols: List[str],
                            start_date: str,
                            end_date: str) -> Dict[str, Any]:
        """
        运行策略回测
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果
        """
        try:
            # 获取市场数据
            market_data = self.fetch_market_data(symbols, start_date, end_date)
            
            if not market_data:
                return {'error': '无法获取市场数据'}
            
            strategy_results = {}
            
            for symbol in symbols:
                if symbol not in market_data:
                    continue
                
                price_data = market_data[symbol]
                
                # 计算因子得分
                factor_scores = self.calculate_factor_scores(price_data, symbol)
                
                if not factor_scores:
                    continue
                
                # 计算综合得分
                composite_score = self.calculate_composite_score(factor_scores, symbol)
                
                if composite_score.empty:
                    continue
                
                # 生成交易信号
                signals = self.generate_trading_signals(composite_score, symbol)
                
                # 计算仓位大小
                positions = self.calculate_position_sizes(signals, composite_score, symbol)
                
                # 计算收益率
                returns = self._calculate_strategy_returns(price_data, positions)
                
                # 计算性能指标
                performance = self._calculate_performance_metrics(returns, symbol)
                
                strategy_results[symbol] = {
                    'factor_scores': factor_scores,
                    'composite_score': composite_score,
                    'signals': signals,
                    'positions': positions,
                    'returns': returns,
                    'performance': performance
                }
            
            # 计算组合层面的结果
            portfolio_results = self._calculate_portfolio_results(strategy_results)
            
            backtest_result = {
                'individual_results': strategy_results,
                'portfolio_results': portfolio_results,
                'backtest_period': {'start': start_date, 'end': end_date},
                'strategy_parameters': {
                    'factors': [factor.get_factor_name() for factor in self.factors],
                    'factor_weights': self.factor_weights,
                    'max_position_size': self.max_position_size,
                    'min_factor_score': self.min_factor_score
                },
                'backtest_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"策略回测完成，回测了{len(strategy_results)}只股票")
            return backtest_result
            
        except Exception as e:
            self.logger.error(f"策略回测失败: {e}")
            return {'error': str(e)}
    
    def _calculate_strategy_returns(self, 
                                  price_data: pd.DataFrame,
                                  positions: pd.Series) -> pd.Series:
        """
        计算策略收益率
        """
        try:
            if price_data.empty or positions.empty:
                return pd.Series()
            
            # 计算价格变化率
            close_prices = price_data['close']
            price_returns = close_prices.pct_change().fillna(0)
            
            # 对齐数据
            aligned_positions = positions.reindex(price_returns.index, method='ffill').fillna(0)
            
            # 计算策略收益率（滞后一期的仓位）
            lagged_positions = aligned_positions.shift(1).fillna(0)
            strategy_returns = lagged_positions * price_returns
            
            return strategy_returns.fillna(0)
            
        except Exception as e:
            self.logger.error(f"计算策略收益率失败: {e}")
            return pd.Series()
    
    def _calculate_performance_metrics(self, 
                                     returns: pd.Series,
                                     symbol: str) -> Dict[str, float]:
        """
        计算性能指标
        """
        try:
            if returns.empty:
                return {}
            
            # 基础指标
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1
            annual_vol = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
            
            # 最大回撤
            cumulative_returns = (1 + returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # 胜率
            win_rate = (returns > 0).mean()
            
            # 信息比率
            information_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            
            # 偏度和峰度
            skewness = returns.skew()
            kurtosis = returns.kurtosis()
            
            # VaR和CVaR
            var_95 = np.percentile(returns, 5)
            cvar_95 = returns[returns <= var_95].mean()
            
            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'annual_volatility': annual_vol,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'information_ratio': information_ratio,
                'skewness': skewness,
                'kurtosis': kurtosis,
                'var_95': var_95,
                'cvar_95': cvar_95,
                'num_trades': (returns != 0).sum(),
                'avg_trade_return': returns[returns != 0].mean() if (returns != 0).any() else 0
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {}
    
    def _calculate_portfolio_results(self, 
                                   strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算组合层面的结果
        """
        try:
            if not strategy_results:
                return {}
            
            # 收集所有股票的收益率
            all_returns = {}
            for symbol, results in strategy_results.items():
                if 'returns' in results and not results['returns'].empty:
                    all_returns[symbol] = results['returns']
            
            if not all_returns:
                return {}
            
            # 创建收益率矩阵
            returns_df = pd.DataFrame(all_returns)
            
            # 等权重组合收益率
            portfolio_returns = returns_df.mean(axis=1)
            
            # 计算组合性能指标
            portfolio_performance = self._calculate_performance_metrics(
                portfolio_returns, 'portfolio')
            
            # 因子贡献分析
            factor_contribution = self._analyze_factor_contribution(strategy_results)
            
            # 相关性分析
            correlation_analysis = self._analyze_correlations(returns_df)
            
            portfolio_results = {
                'portfolio_returns': portfolio_returns,
                'portfolio_performance': portfolio_performance,
                'factor_contribution': factor_contribution,
                'correlation_analysis': correlation_analysis,
                'num_stocks': len(all_returns)
            }
            
            return portfolio_results
            
        except Exception as e:
            self.logger.error(f"计算组合结果失败: {e}")
            return {}
    
    def _analyze_factor_contribution(self, 
                                   strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析因子贡献
        """
        try:
            factor_contribution = {}
            
            # 收集所有因子得分
            all_factor_scores = {}
            for factor in self.factors:
                factor_name = factor.get_factor_name()
                all_factor_scores[factor_name] = []
            
            for symbol, results in strategy_results.items():
                factor_scores = results.get('factor_scores', {})
                for factor_name in all_factor_scores.keys():
                    if factor_name in factor_scores:
                        scores = factor_scores[factor_name]
                        if not scores.empty:
                            all_factor_scores[factor_name].extend(scores.values)
            
            # 计算每个因子的统计特征
            for factor_name, scores in all_factor_scores.items():
                if scores:
                    factor_contribution[factor_name] = {
                        'mean_score': np.mean(scores),
                        'std_score': np.std(scores),
                        'weight': self.factor_weights.get(factor_name, 0),
                        'contribution': self.factor_weights.get(factor_name, 0) * abs(np.mean(scores))
                    }
            
            return factor_contribution
            
        except Exception as e:
            self.logger.error(f"分析因子贡献失败: {e}")
            return {}
    
    def _analyze_correlations(self, returns_df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析相关性
        """
        try:
            if returns_df.empty:
                return {}
            
            # 计算相关性矩阵
            correlation_matrix = returns_df.corr()
            
            # 平均相关性
            avg_correlation = correlation_matrix.values[np.triu_indices_from(
                correlation_matrix.values, k=1)].mean()
            
            # 最大和最小相关性
            upper_triangle = correlation_matrix.values[np.triu_indices_from(
                correlation_matrix.values, k=1)]
            max_correlation = upper_triangle.max()
            min_correlation = upper_triangle.min()
            
            correlation_analysis = {
                'avg_correlation': avg_correlation,
                'max_correlation': max_correlation,
                'min_correlation': min_correlation,
                'correlation_matrix': correlation_matrix.to_dict()
            }
            
            return correlation_analysis
            
        except Exception as e:
            self.logger.error(f"分析相关性失败: {e}")
            return {}
    
    def generate_report(self) -> str:
        """
        生成策略分析报告
        """
        try:
            if not hasattr(self, 'backtest_result') or not self.backtest_result:
                return "没有回测结果可供报告"
            
            result = self.backtest_result
            
            report = f"""
=== 基于因子的策略分析报告 ===

回测时间: {result.get('backtest_timestamp', 'Unknown')}
回测期间: {result.get('backtest_period', {}).get('start', 'Unknown')} 至 {result.get('backtest_period', {}).get('end', 'Unknown')}

=== 策略参数 ===
使用因子: {', '.join(result.get('strategy_parameters', {}).get('factors', []))}
最大仓位: {self.max_position_size:.1%}
最小因子得分: {self.min_factor_score}

=== 因子权重 ===
"""
            
            for factor_name, weight in self.factor_weights.items():
                report += f"{factor_name}: {weight:.3f}\n"
            
            # 组合表现
            portfolio_performance = result.get('portfolio_results', {}).get('portfolio_performance', {})
            if portfolio_performance:
                report += f"""

=== 组合表现 ===
年化收益率: {portfolio_performance.get('annual_return', 0):.2%}
年化波动率: {portfolio_performance.get('annual_volatility', 0):.2%}
夏普比率: {portfolio_performance.get('sharpe_ratio', 0):.3f}
最大回撤: {portfolio_performance.get('max_drawdown', 0):.2%}
胜率: {portfolio_performance.get('win_rate', 0):.1%}
信息比率: {portfolio_performance.get('information_ratio', 0):.3f}
"""
            
            # 因子贡献分析
            factor_contribution = result.get('portfolio_results', {}).get('factor_contribution', {})
            if factor_contribution:
                report += "\n=== 因子贡献分析 ===\n"
                report += "因子名称\t权重\t平均得分\t贡献度\n"
                report += "-" * 50 + "\n"
                
                for factor_name, contrib in factor_contribution.items():
                    report += f"{factor_name}\t{contrib.get('weight', 0):.3f}\t"
                    report += f"{contrib.get('mean_score', 0):.3f}\t"
                    report += f"{contrib.get('contribution', 0):.3f}\n"
            
            # 个股表现摘要
            individual_results = result.get('individual_results', {})
            if individual_results:
                report += "\n=== 个股表现摘要 ===\n"
                report += "股票代码\t年化收益\t夏普比率\t最大回撤\t胜率\n"
                report += "-" * 60 + "\n"
                
                for symbol, stock_result in individual_results.items():
                    perf = stock_result.get('performance', {})
                    report += f"{symbol}\t{perf.get('annual_return', 0):.2%}\t"
                    report += f"{perf.get('sharpe_ratio', 0):.3f}\t"
                    report += f"{perf.get('max_drawdown', 0):.2%}\t"
                    report += f"{perf.get('win_rate', 0):.1%}\n"
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return f"报告生成失败: {e}"


if __name__ == "__main__":
    # 测试代码
    strategy = FactorBasedStrategy(
        max_position_size=0.1,
        min_factor_score=0.5
    )
    
    # 测试股票列表
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    # 运行策略回测
    print("=== 运行基于因子的策略回测 ===")
    result = strategy.run_strategy_backtest(
        symbols=test_symbols,
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    
    if 'error' not in result:
        strategy.backtest_result = result
        
        # 生成报告
        print("\n=== 生成策略分析报告 ===")
        report = strategy.generate_report()
        print(report)
        
        # 显示组合收益率统计
        portfolio_returns = result.get('portfolio_results', {}).get('portfolio_returns')
        if portfolio_returns is not None and not portfolio_returns.empty:
            print(f"\n=== 组合收益率统计 ===")
            print(f"总交易日: {len(portfolio_returns)}")
            print(f"平均日收益率: {portfolio_returns.mean():.4f}")
            print(f"收益率标准差: {portfolio_returns.std():.4f}")
            print(f"最大单日收益: {portfolio_returns.max():.4f}")
            print(f"最大单日亏损: {portfolio_returns.min():.4f}")
    else:
        print(f"策略回测失败: {result['error']}")