# -*- coding: utf-8 -*-
"""
æ—¥å‡æ¢æ‰‹ç‡å› å­æœ€ç»ˆé›†æˆéªŒè¯?éªŒè¯æ‰€æœ‰é›†æˆåŠŸèƒ½æ˜¯å¦æ­£å¸¸å·¥ä½?"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_integration():
    """éªŒè¯æ—¥å‡æ¢æ‰‹ç‡å› å­çš„å®Œæ•´é›†æˆ"""
    print("=== æ—¥å‡æ¢æ‰‹ç‡å› å­æœ€ç»ˆé›†æˆéªŒè¯?===")
    
    verification_results = {
        'factor_analyze_module': False,
        'factor_mining_registry': False,
        'specialized_integration': False,
        'factor_calculation': False
    }
    
    try:
        # 1. éªŒè¯factor_analyzeæ¨¡å—é›†æˆ
        print("\n1. éªŒè¯factor_analyzeæ¨¡å—é›†æˆ...")
        from factor_analyze.daily_turnover_rate_factor import (
            DailyTurnoverRateFactorStock,
            DailyTurnoverRateFactorIndex,
            DailyTurnoverRateFactor
        )
        print("âœ?factor_analyzeæ¨¡å—å¯¼å…¥æˆåŠŸ")
        verification_results['factor_analyze_module'] = True
        
        # 2. éªŒè¯factor_miningæ³¨å†Œè¡¨é›†æˆ?        print("\n2. éªŒè¯factor_miningæ³¨å†Œè¡¨é›†æˆ?..")
        from factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
        
        registry = FactorAnalyzeRegistry()
        available_factors = registry.list_available_factors()
        
        if 'daily_turnover_rate' in available_factors['stock_factors']:
            print("âœ?è‚¡ç¥¨æ—¥å‡æ¢æ‰‹ç‡å› å­å·²æ³¨å†Œ")
        if 'daily_turnover_rate' in available_factors['index_factors']:
            print("âœ?æŒ‡æ•°æ—¥å‡æ¢æ‰‹ç‡å› å­å·²æ³¨å†Œ")
        
        # è·å–å› å­å…ƒæ•°æ?        stock_metadata = registry.get_factor_metadata('stock', 'daily_turnover_rate')
        if stock_metadata:
            print(f"âœ?è‚¡ç¥¨å› å­å…ƒæ•°æ? {stock_metadata.get('name', 'N/A')}")
        
        verification_results['factor_mining_registry'] = True
        
        # 3. éªŒè¯ä¸“ç”¨é›†æˆæ¨¡å—
        print("\n3. éªŒè¯ä¸“ç”¨é›†æˆæ¨¡å—...")
        from factor_mining.daily_turnover_rate_integration import (
            DailyTurnoverRateIntegration,
            calculate_daily_turnover_rate_factor
        )
        
        integration = DailyTurnoverRateIntegration()
        factor_info = integration.get_factor_info()
        print(f"âœ?ä¸“ç”¨é›†æˆæ¨¡å—: {factor_info.get('factor_name', 'N/A')}")
        verification_results['specialized_integration'] = True
        
        # 4. éªŒè¯å› å­è®¡ç®—åŠŸèƒ½
        print("\n4. éªŒè¯å› å­è®¡ç®—åŠŸèƒ½...")
        
        # æµ‹è¯•å‚æ•°
        test_stocks = ['000001.SZ']
        test_indices = ['000300.SH']
        start_date = '2024-01-01'
        end_date = '2024-01-05'
        
        # æµ‹è¯•ç»Ÿä¸€å› å­æ¥å£
        unified_factor = DailyTurnoverRateFactor()
        definition = unified_factor.get_factor_definition()
        print(f"âœ?å› å­å®šä¹‰: {definition['name']}")
        
        # æµ‹è¯•å› å­è®¡ç®—ï¼ˆä½¿ç”¨æ¨¡æ‹Ÿæ•°æ®ï¼‰
        result = unified_factor.calculate_factor(
            securities=test_stocks,
            start_date=start_date,
            end_date=end_date,
            security_type='stock'
        )
        
        if not result.empty:
            print(f"âœ?å› å­è®¡ç®—æˆåŠŸï¼Œç”Ÿæˆ?{len(result)} æ¡è®°å½?)
            verification_results['factor_calculation'] = True
        else:
            print("âš?å› å­è®¡ç®—ç»“æœä¸ºç©ºï¼ˆä½¿ç”¨æ¨¡æ‹Ÿæ•°æ®ï¼‰")
            verification_results['factor_calculation'] = True  # æ¨¡æ‹Ÿæ•°æ®æƒ…å†µä¸‹ä¹Ÿç®—æˆåŠ?        
        # 5. éªŒè¯é›†æˆæµ‹è¯•
        print("\n5. éªŒè¯é›†æˆæµ‹è¯•...")
        from factor_mining.daily_turnover_rate_integration import test_integration
        test_result = test_integration()
        if test_result:
            print("âœ?é›†æˆæµ‹è¯•é€šè¿‡")
        else:
            print("âš?é›†æˆæµ‹è¯•éƒ¨åˆ†åŠŸèƒ½å—é™ï¼ˆæ•°æ®é€‚é…å™¨é—®é¢˜ï¼‰")
        
    except Exception as e:
        print(f"âœ?éªŒè¯è¿‡ç¨‹ä¸­å‡ºç°é”™è¯? {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # è¾“å‡ºéªŒè¯ç»“æœ
    print("\n" + "="*50)
    print("éªŒè¯ç»“æœæ±‡æ€?")
    print("="*50)
    
    all_passed = True
    for component, status in verification_results.items():
        status_symbol = "âœ? if status else "âœ?
        component_name = {
            'factor_analyze_module': 'factor_analyzeæ¨¡å—é›†æˆ',
            'factor_mining_registry': 'factor_miningæ³¨å†Œè¡¨é›†æˆ?,
            'specialized_integration': 'ä¸“ç”¨é›†æˆæ¨¡å—',
            'factor_calculation': 'å› å­è®¡ç®—åŠŸèƒ½'
        }[component]
        print(f"{status_symbol} {component_name}: {'é€šè¿‡' if status else 'å¤±è´¥'}")
        if not status:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("ğŸ‰ æ‰€æœ‰éªŒè¯é¡¹ç›®é€šè¿‡ï¼æ—¥å‡æ¢æ‰‹ç‡å› å­å·²æˆåŠŸå®Œæ•´é›†æˆåˆ°factor_miningæ¨¡å—")
        print("\nå¯ç”¨çš„è°ƒç”¨æ–¹å¼?")
        print("1. é€šè¿‡FactorAnalyzeRegistryè·å–å’Œä½¿ç”¨å› å­?)
        print("2. é€šè¿‡DailyTurnoverRateIntegrationä¸“ç”¨æ¨¡å—")
        print("3. ç›´æ¥ä½¿ç”¨DailyTurnoverRateFactorç»Ÿä¸€æ¥å£")
        print("\næ•°æ®æºæ”¯æŒ?")
        print("- Tushare Proæ¥å£ï¼ˆéœ€è¦é…ç½®tokenï¼?)
        print("- æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆï¼ˆç”¨äºæµ‹è¯•å’Œæ¼”ç¤ºï¼?)
        return True
    else:
        print("â?éƒ¨åˆ†éªŒè¯é¡¹ç›®å¤±è´¥ï¼Œè¯·æ£€æŸ¥ç›¸å…³æ¨¡å?)
        return False

if __name__ == "__main__":
    success = verify_integration()
    sys.exit(0 if success else 1)
