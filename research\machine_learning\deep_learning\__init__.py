#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep Learning Research Module
深度学习研究模块

本模块包含深度学习相关的研究功能，基于Chapter20的先进技术：
1. 自动编码器研究 - 特征提取和异常检测
2. 生成对抗网络研究 - 数据生成和增强
3. 变分自动编码器研究 - 概率建模和不确定性量化
"""

# 导入研究模块
try:
    from .autoencoder_research import <PERSON>encoder<PERSON><PERSON><PERSON><PERSON>, register_autoencoder_researcher, quick_autoencoder_research
    from .gan_research import GANResearcher, register_gan_researcher, quick_gan_research
    from .vae_research import VAEResearcher, register_vae_researcher, quick_vae_research
    
    CHAPTER20_RESEARCH_AVAILABLE = True
except ImportError as e:
    CHAPTER20_RESEARCH_AVAILABLE = False
    print(f"⚠️ Chapter20研究模块导入失败: {e}")

# 导出的研究者类
__all__ = [
    'AutoencoderResearcher',
    'GANResearcher', 
    'VAEResearcher',
    'register_autoencoder_researcher',
    'register_gan_researcher',
    'register_vae_researcher',
    'quick_autoencoder_research',
    'quick_gan_research',
    'quick_vae_research',
    'register_all_chapter20_researchers',
    'get_available_researchers',
    'get_unsupervised_researchers'
]

# 版本信息
__version__ = "1.1.0"
__author__ = "RDAgent Team"
__description__ = "Deep Learning Research Module with Chapter20 Integration"

def register_all_chapter20_researchers():
    """
    注册所有Chapter20研究者
    
    Returns:
        Dict[str, Any]: 注册的研究者字典
    """
    if not CHAPTER20_RESEARCH_AVAILABLE:
        print("⚠️ Chapter20研究模块不可用")
        return {}
    
    researchers = {}
    
    try:
        researchers['autoencoder'] = register_autoencoder_researcher()
        researchers['gan'] = register_gan_researcher()
        researchers['vae'] = register_vae_researcher()
        
        print(f"✅ 成功注册 {len(researchers)} 个Chapter20研究者")
        return researchers
        
    except Exception as e:
        print(f"❌ 注册Chapter20研究者失败: {e}")
        return {}

def get_available_researchers():
    """
    获取可用的研究者列表
    
    Returns:
        List[str]: 可用研究者名称列表
    """
    if not CHAPTER20_RESEARCH_AVAILABLE:
        return []
    
    return ['AutoencoderResearcher', 'GANResearcher', 'VAEResearcher']

def get_unsupervised_researchers():
    """
    获取无监督学习研究者
    
    Returns:
        Dict[str, str]: 无监督研究者及其描述
    """
    if not CHAPTER20_RESEARCH_AVAILABLE:
        return {}
    
    return {
        'AutoencoderResearcher': '自动编码器研究 - 特征提取、降维和异常检测',
        'GANResearcher': '生成对抗网络研究 - 数据生成和增强',
        'VAEResearcher': '变分自动编码器研究 - 概率建模和不确定性量化'
    }