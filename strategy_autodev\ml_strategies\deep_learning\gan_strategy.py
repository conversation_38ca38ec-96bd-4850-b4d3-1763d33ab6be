# -*- coding: utf-8 -*-
"""
GAN策略
基于Chapter20的生成对抗网络技术，用于金融数据生成和增强
适用于时间序列数据生成和数据增强
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架
try:
    from keras.layers import Input, Dense, Conv1D, Conv1DTranspose, LeakyReLU, Dropout, BatchNormalization
    from keras.layers import Reshape, Flatten, UpSampling1D, Activation
    from keras.models import Model, Sequential
    from keras.optimizers import Adam
    from keras.callbacks import EarlyStopping, ModelCheckpoint
    from keras import backend as K
    import tensorflow as tf
    KERAS_AVAILABLE = True
except ImportError:
    KERAS_AVAILABLE = False
    logging.warning("Keras不可用，GAN策略将使用简化实现")

# 数据管道集成
try:
    from data_pipeline.data_adapter import get_adapter
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    DATA_PIPELINE_AVAILABLE = False
    logging.warning("数据管道不可用，将使用模拟数据")

# 策略基类
try:
    from ..base_ml_strategy import BaseMLStrategy
except ImportError:
    from strategy_autodev.ml_strategies.base_ml_strategy import BaseMLStrategy

# 统一系统集成
try:
    from strategy_autodev.core.unified_system import get_unified_system
    UNIFIED_SYSTEM_AVAILABLE = True
except ImportError:
    UNIFIED_SYSTEM_AVAILABLE = False

logger = logging.getLogger(__name__)


class GANStrategy(BaseMLStrategy):
    """
    生成对抗网络策略
    
    功能:
    1. 生成合成金融时间序列数据
    2. 数据增强以改善模型训练
    3. 学习金融数据的潜在分布
    4. 生成用于回测的合成数据
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化GAN策略
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        
        # 策略配置
        self.config = config or {}
        self.latent_dim = self.config.get('latent_dim', 100)
        self.sequence_length = self.config.get('sequence_length', 20)
        self.batch_size = self.config.get('batch_size', 32)
        self.epochs = self.config.get('epochs', 100)
        self.d_steps = self.config.get('d_steps', 1)  # 判别器训练步数
        self.g_steps = self.config.get('g_steps', 1)  # 生成器训练步数
        
        # 模型组件
        self.generator = None
        self.discriminator = None
        self.gan = None
        
        # 数据管道
        self.data_adapter = None
        self._init_data_pipeline()
        
        # 策略状态
        self.is_trained = False
        self.feature_columns = []
        self.scaler = None
        self.training_history = []
        
        logger.info(f"GAN策略初始化完成，潜在维度: {self.latent_dim}")
    
    def _build_model(self) -> Any:
        """构建GAN模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，返回简化模型")
            return None
        
        # 这里会在训练时根据数据维度动态构建
        return None
    
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        # 选择数值特征列
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        exclude_cols = ['target', 'date', 'symbol']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        if not feature_cols:
            raise ValueError("没有找到可用的特征列")
        
        self.feature_columns = feature_cols
        return data[feature_cols]
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练GAN模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，使用简化训练")
            return {'status': 'simplified_training', 'loss': 0.0}
        
        try:
            # 构建模型
            input_shape = (self.sequence_length, X.shape[1])
            self.generator = self._build_generator(input_shape)
            self.discriminator = self._build_discriminator(input_shape)
            self.gan = self._build_gan()
            
            # 准备训练数据
            sequences = self._prepare_data(pd.DataFrame(X))
            
            # 训练GAN
            d_losses = []
            g_losses = []
            
            for epoch in range(self.epochs):
                # 训练判别器
                for _ in range(self.d_steps):
                    # 真实数据
                    idx = np.random.randint(0, sequences.shape[0], self.batch_size)
                    real_data = sequences[idx]
                    
                    # 生成假数据
                    noise = np.random.normal(0, 1, (self.batch_size, self.latent_dim))
                    fake_data = self.generator.predict(noise)
                    
                    # 训练判别器
                    d_loss_real = self.discriminator.train_on_batch(real_data, np.ones((self.batch_size, 1)))
                    d_loss_fake = self.discriminator.train_on_batch(fake_data, np.zeros((self.batch_size, 1)))
                    d_loss = 0.5 * np.add(d_loss_real, d_loss_fake)
                
                # 训练生成器
                for _ in range(self.g_steps):
                    noise = np.random.normal(0, 1, (self.batch_size, self.latent_dim))
                    g_loss = self.gan.train_on_batch(noise, np.ones((self.batch_size, 1)))
                
                d_losses.append(d_loss[0])
                g_losses.append(g_loss)
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}, D Loss: {d_loss[0]:.4f}, G Loss: {g_loss:.4f}")
            
            return {
                'final_d_loss': d_losses[-1],
                'final_g_loss': g_losses[-1],
                'epochs_trained': len(d_losses)
            }
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """使用GAN进行预测（生成合成数据）"""
        if not KERAS_AVAILABLE or self.generator is None:
            logger.warning("模型不可用，返回随机预测")
            return np.random.random(len(X))
        
        try:
            # 生成合成数据
            noise = np.random.normal(0, 1, (len(X), self.latent_dim))
            synthetic_data = self.generator.predict(noise)
            
            # 计算生成质量分数（简化实现）
            quality_scores = np.mean(synthetic_data, axis=(1, 2))
            
            # 归一化到[0,1]
            quality_scores = (quality_scores - np.min(quality_scores)) / (np.max(quality_scores) - np.min(quality_scores) + 1e-8)
            
            return quality_scores
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.zeros(len(X))
    
    def _init_data_pipeline(self):
        """初始化数据管道"""
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.data_adapter = get_adapter()
                logger.info("✅ 数据管道连接成功")
            except Exception as e:
                logger.warning(f"数据管道连接失败: {e}")
                self.data_adapter = None
        else:
            logger.warning("数据管道不可用，将使用模拟数据")
    
    def _build_generator(self, output_shape: Tuple[int, int]) -> Any:
        """
        构建生成器模型
        
        Args:
            output_shape: 输出形状 (sequence_length, features)
            
        Returns:
            生成器模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建生成器")
        
        model = Sequential(name='Generator')
        
        # 输入层
        model.add(Dense(128, input_dim=self.latent_dim))
        model.add(LeakyReLU(alpha=0.2))
        model.add(BatchNormalization(momentum=0.8))
        
        # 隐藏层
        model.add(Dense(256))
        model.add(LeakyReLU(alpha=0.2))
        model.add(BatchNormalization(momentum=0.8))
        
        model.add(Dense(512))
        model.add(LeakyReLU(alpha=0.2))
        model.add(BatchNormalization(momentum=0.8))
        
        # 输出层
        output_dim = output_shape[0] * output_shape[1]
        model.add(Dense(output_dim, activation='tanh'))
        model.add(Reshape(output_shape))
        
        # 输入噪声
        noise = Input(shape=(self.latent_dim,))
        generated_data = model(noise)
        
        generator = Model(noise, generated_data, name='Generator')
        logger.info("生成器模型构建完成")
        return generator
    
    def _build_discriminator(self, input_shape: Tuple[int, int]) -> Any:
        """
        构建判别器模型
        
        Args:
            input_shape: 输入形状 (sequence_length, features)
            
        Returns:
            判别器模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建判别器")
        
        model = Sequential(name='Discriminator')
        
        # 输入层
        model.add(Flatten(input_shape=input_shape))
        
        # 隐藏层
        model.add(Dense(512))
        model.add(LeakyReLU(alpha=0.2))
        model.add(Dropout(0.3))
        
        model.add(Dense(256))
        model.add(LeakyReLU(alpha=0.2))
        model.add(Dropout(0.3))
        
        model.add(Dense(128))
        model.add(LeakyReLU(alpha=0.2))
        model.add(Dropout(0.3))
        
        # 输出层
        model.add(Dense(1, activation='sigmoid'))
        
        # 输入数据
        data = Input(shape=input_shape)
        validity = model(data)
        
        discriminator = Model(data, validity, name='Discriminator')
        discriminator.compile(loss='binary_crossentropy', optimizer=Adam(0.0002, 0.5), metrics=['accuracy'])
        
        logger.info("判别器模型构建完成")
        return discriminator
    
    def _build_gan(self) -> Any:
        """
        构建GAN模型
        
        Returns:
            GAN模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建GAN")
        
        # 冻结判别器权重
        self.discriminator.trainable = False
        
        # GAN输入
        noise = Input(shape=(self.latent_dim,))
        
        # 生成数据
        generated_data = self.generator(noise)
        
        # 判别器判断
        validity = self.discriminator(generated_data)
        
        # GAN模型
        gan = Model(noise, validity, name='GAN')
        gan.compile(loss='binary_crossentropy', optimizer=Adam(0.0002, 0.5))
        
        logger.info("GAN模型构建完成")
        return gan
    
    def _prepare_data(self, data: pd.DataFrame) -> np.ndarray:
        """
        准备训练数据
        
        Args:
            data: 原始数据
            
        Returns:
            训练数据
        """
        # 选择特征列
        feature_cols = [col for col in data.columns if col not in ['date', 'symbol', 'target']]
        self.feature_columns = feature_cols
        
        # 提取特征
        features = data[feature_cols].values
        
        # 数据标准化到[-1, 1]范围（适合tanh激活函数）
        from sklearn.preprocessing import MinMaxScaler
        self.scaler = MinMaxScaler(feature_range=(-1, 1))
        features_scaled = self.scaler.fit_transform(features)
        
        # 创建序列数据
        sequences = []
        for i in range(len(features_scaled) - self.sequence_length + 1):
            sequences.append(features_scaled[i:i + self.sequence_length])
        
        sequences = np.array(sequences)
        
        logger.info(f"数据准备完成，训练样本: {len(sequences)}")
        return sequences
    
    def train(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练GAN模型
        
        Args:
            data: 训练数据
            
        Returns:
            训练结果
        """
        try:
            logger.info("开始训练GAN模型")
            
            # 准备数据
            train_data = self._prepare_data(data)
            
            # 构建模型
            input_shape = train_data.shape[1:]
            self.generator = self._build_generator(input_shape)
            self.discriminator = self._build_discriminator(input_shape)
            self.gan = self._build_gan()
            
            # 训练标签
            valid = np.ones((self.batch_size, 1))
            fake = np.zeros((self.batch_size, 1))
            
            # 训练历史
            d_losses = []
            g_losses = []
            
            for epoch in range(self.epochs):
                # 训练判别器
                for _ in range(self.d_steps):
                    # 选择真实数据批次
                    idx = np.random.randint(0, train_data.shape[0], self.batch_size)
                    real_data = train_data[idx]
                    
                    # 生成假数据
                    noise = np.random.normal(0, 1, (self.batch_size, self.latent_dim))
                    fake_data = self.generator.predict(noise, verbose=0)
                    
                    # 训练判别器
                    d_loss_real = self.discriminator.train_on_batch(real_data, valid)
                    d_loss_fake = self.discriminator.train_on_batch(fake_data, fake)
                    d_loss = 0.5 * np.add(d_loss_real, d_loss_fake)
                
                # 训练生成器
                for _ in range(self.g_steps):
                    noise = np.random.normal(0, 1, (self.batch_size, self.latent_dim))
                    g_loss = self.gan.train_on_batch(noise, valid)
                
                # 记录损失
                d_losses.append(d_loss[0])
                g_losses.append(g_loss)
                
                # 打印进度
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}/{self.epochs} - D损失: {d_loss[0]:.4f}, G损失: {g_loss:.4f}")
            
            self.is_trained = True
            self.training_history = {
                'discriminator_losses': d_losses,
                'generator_losses': g_losses
            }
            
            results = {
                'final_d_loss': d_losses[-1],
                'final_g_loss': g_losses[-1],
                'avg_d_loss': np.mean(d_losses),
                'avg_g_loss': np.mean(g_losses),
                'epochs_trained': self.epochs,
                'latent_dim': self.latent_dim
            }
            
            logger.info(f"GAN训练完成，最终G损失: {results['final_g_loss']:.6f}")
            return results
            
        except Exception as e:
            logger.error(f"GAN训练失败: {e}")
            return {'error': str(e)}
    
    def generate_data(self, num_samples: int = 100) -> np.ndarray:
        """
        生成合成数据
        
        Args:
            num_samples: 生成样本数量
            
        Returns:
            生成的数据
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 生成随机噪声
            noise = np.random.normal(0, 1, (num_samples, self.latent_dim))
            
            # 生成数据
            generated_data = self.generator.predict(noise, verbose=0)
            
            logger.info(f"生成 {num_samples} 个合成样本")
            return generated_data
            
        except Exception as e:
            logger.error(f"数据生成失败: {e}")
            return np.array([])
    
    def generate_dataframe(self, num_samples: int = 100, start_date: str = None) -> pd.DataFrame:
        """
        生成合成数据DataFrame
        
        Args:
            num_samples: 生成样本数量
            start_date: 开始日期
            
        Returns:
            生成的数据DataFrame
        """
        generated_data = self.generate_data(num_samples)
        
        if generated_data.size == 0:
            return pd.DataFrame()
        
        # 反标准化
        data_list = []
        for i, sequence in enumerate(generated_data):
            # 反标准化
            sequence_rescaled = self.scaler.inverse_transform(sequence)
            
            # 创建DataFrame
            df = pd.DataFrame(sequence_rescaled, columns=self.feature_columns)
            
            # 添加日期
            if start_date:
                base_date = pd.to_datetime(start_date)
                dates = [base_date + timedelta(days=j + i * self.sequence_length) for j in range(self.sequence_length)]
            else:
                dates = [f"sample_{i}_day_{j}" for j in range(self.sequence_length)]
            
            df['date'] = dates
            df['sample_id'] = i
            data_list.append(df)
        
        result_df = pd.concat(data_list, ignore_index=True)
        logger.info(f"生成DataFrame，形状: {result_df.shape}")
        return result_df
    
    def augment_data(self, original_data: pd.DataFrame, augment_ratio: float = 0.5) -> pd.DataFrame:
        """
        数据增强
        
        Args:
            original_data: 原始数据
            augment_ratio: 增强比例
            
        Returns:
            增强后的数据
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 计算需要生成的样本数
        num_augment = int(len(original_data) * augment_ratio)
        
        # 生成合成数据
        synthetic_df = self.generate_dataframe(num_augment)
        
        if synthetic_df.empty:
            return original_data
        
        # 合并数据
        synthetic_df['is_synthetic'] = True
        original_data_copy = original_data.copy()
        original_data_copy['is_synthetic'] = False
        
        augmented_data = pd.concat([original_data_copy, synthetic_df], ignore_index=True)
        
        logger.info(f"数据增强完成，原始: {len(original_data)}, 合成: {len(synthetic_df)}, 总计: {len(augmented_data)}")
        return augmented_data
    
    def evaluate_quality(self, real_data: pd.DataFrame, num_synthetic: int = 1000) -> Dict[str, float]:
        """
        评估生成数据质量
        
        Args:
            real_data: 真实数据
            num_synthetic: 合成数据数量
            
        Returns:
            质量评估指标
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 生成合成数据
            synthetic_data = self.generate_data(num_synthetic)
            real_sequences = self._prepare_data(real_data)
            
            # 统计特征比较
            real_mean = np.mean(real_sequences, axis=(0, 1))
            synthetic_mean = np.mean(synthetic_data, axis=(0, 1))
            
            real_std = np.std(real_sequences, axis=(0, 1))
            synthetic_std = np.std(synthetic_data, axis=(0, 1))
            
            # 计算相似度指标
            mean_diff = np.mean(np.abs(real_mean - synthetic_mean))
            std_diff = np.mean(np.abs(real_std - synthetic_std))
            
            # 分布相似度（简化版KL散度）
            real_flat = real_sequences.flatten()
            synthetic_flat = synthetic_data.flatten()
            
            # 计算直方图
            bins = 50
            real_hist, _ = np.histogram(real_flat, bins=bins, density=True)
            synthetic_hist, _ = np.histogram(synthetic_flat, bins=bins, density=True)
            
            # 避免零值
            real_hist = real_hist + 1e-8
            synthetic_hist = synthetic_hist + 1e-8
            
            # KL散度
            kl_divergence = np.sum(real_hist * np.log(real_hist / synthetic_hist))
            
            quality_metrics = {
                'mean_difference': float(mean_diff),
                'std_difference': float(std_diff),
                'kl_divergence': float(kl_divergence),
                'quality_score': float(1.0 / (1.0 + mean_diff + std_diff + kl_divergence))
            }
            
            logger.info(f"数据质量评估完成，质量分数: {quality_metrics['quality_score']:.4f}")
            return quality_metrics
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return {}
    
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        使用GAN进行预测（生成相似数据）
        
        Args:
            data: 输入数据
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 生成与输入数据相似数量的合成数据
            num_samples = max(1, len(data) // self.sequence_length)
            synthetic_df = self.generate_dataframe(num_samples)
            
            if synthetic_df.empty:
                return pd.DataFrame()
            
            # 添加预测标记
            synthetic_df['prediction_type'] = 'synthetic'
            synthetic_df['confidence'] = 0.8  # 固定置信度
            
            logger.info(f"GAN预测完成，生成 {len(synthetic_df)} 条记录")
            return synthetic_df
            
        except Exception as e:
            logger.error(f"GAN预测失败: {e}")
            return pd.DataFrame()
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': 'GANStrategy',
            'description': '基于生成对抗网络的数据生成和增强策略',
            'category': 'DEEP_LEARNING',
            'latent_dim': self.latent_dim,
            'sequence_length': self.sequence_length,
            'is_trained': self.is_trained,
            'features': self.feature_columns,
            'capabilities': [
                'data_generation',
                'data_augmentation',
                'synthetic_data_creation',
                'distribution_learning'
            ]
        }


def register_gan_strategy():
    """
    注册GAN策略到统一系统
    """
    if UNIFIED_SYSTEM_AVAILABLE:
        try:
            system = get_unified_system()
            system.register_strategy(
                "GANStrategy",
                GANStrategy,
                "DEEP_LEARNING",
                description="基于生成对抗网络的数据生成和增强策略",
                author="Strategy AutoDev",
                version="1.0.0",
                capabilities=[
                    "data_generation",
                    "data_augmentation",
                    "synthetic_data_creation",
                    "distribution_learning"
                ]
            )
            logger.info("✅ GANStrategy 注册成功")
            return True
        except Exception as e:
            logger.error(f"❌ GANStrategy 注册失败: {e}")
            return False
    else:
        logger.warning("统一系统不可用，跳过策略注册")
        return False


if __name__ == "__main__":
    # 注册策略
    register_gan_strategy()
    
    # 示例使用
    if DATA_PIPELINE_AVAILABLE:
        try:
            # 创建策略实例
            config = {
                'latent_dim': 100,
                'sequence_length': 20,
                'epochs': 100
            }
            
            strategy = GANStrategy(config)
            
            # 获取数据
            adapter = get_adapter()
            data = adapter.get_stock_data(['000001.SZ'], '2023-01-01', '2023-12-31')
            
            if not data.empty:
                # 训练模型
                results = strategy.train(data)
                print(f"训练结果: {results}")
                
                # 生成数据
                synthetic_data = strategy.generate_dataframe(50)
                print(f"生成数据: {synthetic_data.head()}")
                
                # 数据增强
                augmented_data = strategy.augment_data(data, 0.3)
                print(f"增强数据: {augmented_data.shape}")
                
                # 质量评估
                quality = strategy.evaluate_quality(data, 100)
                print(f"质量评估: {quality}")
            
        except Exception as e:
            logger.error(f"示例运行失败: {e}")
    else:
        logger.info("数据管道不可用，跳过示例")