# -*- coding: utf-8 -*-
"""
CNN时间序列策略
CNN Time Series Strategy

基于Chapter19的CNN时间序列实现，用于股票价格预测和交易信号生成
使用一维卷积神经网络处理时间序列数据
现已迁移至PyTorch框架
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架 - PyTorch
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    import torch.nn.functional as F
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import accuracy_score, roc_auc_score
    
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    warnings.warn("PyTorch不可用，CNN策略将无法正常工作")

# 导入基础策略类
try:
    from deep_learning_base import DeepLearningBaseStrategy
except ImportError:
    try:
        from .deep_learning_base import DeepLearningBaseStrategy
    except ImportError:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from deep_learning_base import DeepLearningBaseStrategy

# 导入数据管道
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from data_pipeline.data_adapter_compatibility import get_data_pipeline
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        warnings.warn("数据适配器不可用，将使用模拟数据")

# 确保sklearn导入
try:
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score, f1_score
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("sklearn不可用，某些功能可能受限")
    StandardScaler = None
    LabelEncoder = None

logger = logging.getLogger(__name__)

class CNNModel(nn.Module):
    """
    PyTorch CNN模型
    """
    
    def __init__(self, input_size: int, filters: int = 32, kernel_size: int = 3, 
                 pool_size: int = 2, conv_layers: int = 1, dense_units: int = 1,
                 dropout_rate: float = 0.2, prediction_type: str = 'classification'):
        super(CNNModel, self).__init__()
        
        self.prediction_type = prediction_type
        
        # 卷积层
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        
        # 第一个卷积层
        self.conv_layers.append(nn.Conv1d(input_size, filters, kernel_size, padding=kernel_size//2))
        self.pool_layers.append(nn.MaxPool1d(pool_size))
        
        # 额外的卷积层
        for i in range(conv_layers - 1):
            self.conv_layers.append(nn.Conv1d(filters * (i + 1), filters * (i + 2), kernel_size, padding=kernel_size//2))
            self.pool_layers.append(nn.MaxPool1d(pool_size))
        
        # 计算展平后的大小（需要根据实际输入计算）
        self.flatten_size = None
        
        # 全连接层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 这些层会在第一次前向传播时初始化
        self.dense = None
        self.output_layer = None
    
    def _init_fc_layers(self, flattened_size: int, dense_units: int):
        """初始化全连接层"""
        if dense_units > 1:
            self.dense = nn.Linear(flattened_size, dense_units)
            output_input_size = dense_units
        else:
            output_input_size = flattened_size
        
        # 输出层
        if self.prediction_type == 'classification':
            self.output_layer = nn.Linear(output_input_size, 1)
        else:
            self.output_layer = nn.Linear(output_input_size, 1)
    
    def forward(self, x):
        # x shape: (batch_size, sequence_length, features)
        # 转换为 (batch_size, features, sequence_length) for Conv1d
        x = x.transpose(1, 2)
        
        # 卷积层
        for conv, pool in zip(self.conv_layers, self.pool_layers):
            x = F.relu(conv(x))
            x = pool(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 初始化全连接层（如果还没有初始化）
        if self.dense is None:
            flattened_size = x.size(1)
            dense_units = 1  # 可以从外部传入
            self._init_fc_layers(flattened_size, dense_units)
            # 移动到正确的设备
            if x.is_cuda:
                if self.dense is not None:
                    self.dense = self.dense.cuda()
                self.output_layer = self.output_layer.cuda()
        
        # 全连接层
        if self.dense is not None:
            x = F.relu(self.dense(x))
            x = self.dropout(x)
        
        # 输出层
        x = self.output_layer(x)
        
        if self.prediction_type == 'classification':
            x = torch.sigmoid(x)
        
        return x

class CNNTimeSeriesStrategy(DeepLearningBaseStrategy):
    """
    CNN时间序列策略 - PyTorch版本
    
    使用一维卷积神经网络处理时间序列数据：
    - 自动提取时间序列模式
    - 支持多变量时间序列
    - 集成技术指标特征
    - 预测价格方向或收益率
    """
    
    def __init__(self, **kwargs):
        # CNN特定参数
        self.filters = kwargs.get('filters', 32)
        self.kernel_size = kwargs.get('kernel_size', 3)
        self.pool_size = kwargs.get('pool_size', 2)
        self.conv_layers = kwargs.get('conv_layers', 1)
        self.dense_units = kwargs.get('dense_units', 1)
        
        # 时间序列参数
        self.sequence_length = kwargs.get('sequence_length', 24)  # 24个时间步
        self.prediction_type = kwargs.get('prediction_type', 'classification')  # 'classification' or 'regression'
        
        # 特征工程参数
        self.use_technical_indicators = kwargs.get('use_technical_indicators', True)
        self.use_volume_features = kwargs.get('use_volume_features', True)
        self.use_price_features = kwargs.get('use_price_features', True)
        
        # PyTorch特定参数
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化父类
        super().__init__(name="CNNTimeSeriesStrategy", **kwargs)
        
        # 数据缓存
        self.feature_cache = {}
        self.model_cache = {}
        self.feature_scaler = None
        
        logger.info(f"CNN时间序列策略初始化完成: filters={self.filters}, kernel_size={self.kernel_size}, device={self.device}")
    
    def _prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备特征数据（实现抽象方法）
        """
        X, y = self.prepare_sequences(data)
        return X, y
    
    def _build_model(self) -> Optional[nn.Module]:
        """
        构建模型（实现抽象方法）
        """
        # 这个方法会在fit时调用，此时我们需要知道input_shape
        # 暂时返回None，在train方法中实际构建
        return None
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型（实现抽象方法）
        """
        try:
            # 构建模型
            input_size = X.shape[2]  # 特征数量
            self.model = CNNModel(
                input_size=input_size,
                filters=self.filters,
                kernel_size=self.kernel_size,
                pool_size=self.pool_size,
                conv_layers=self.conv_layers,
                dense_units=self.dense_units,
                dropout_rate=self.dropout_rate,
                prediction_type=self.prediction_type
            ).to(self.device)
            
            # 转换数据为PyTorch张量
            X_tensor = torch.FloatTensor(X).to(self.device)
            y_tensor = torch.FloatTensor(y).to(self.device)
            
            # 分割训练和验证数据
            split_idx = int(len(X) * (1 - self.validation_split))
            X_train, X_val = X_tensor[:split_idx], X_tensor[split_idx:]
            y_train, y_val = y_tensor[:split_idx], y_tensor[split_idx:]
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_train, y_train)
            val_dataset = TensorDataset(X_val, y_val)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
            
            # 设置优化器和损失函数
            optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            
            if self.prediction_type == 'classification':
                criterion = nn.BCELoss()
            else:
                criterion = nn.MSELoss()
            
            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(self.epochs):
                # 训练阶段
                self.model.train()
                train_loss = 0.0
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # 验证阶段
                self.model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = self.model(batch_X)
                        loss = criterion(outputs.squeeze(), batch_y)
                        val_loss += loss.item()
                
                train_loss /= len(train_loader)
                val_loss /= len(val_loader)
                
                train_losses.append(train_loss)
                val_losses.append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= 10:  # patience=10
                        logger.info(f"早停在epoch {epoch+1}")
                        break
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            
            # 恢复最佳模型
            if hasattr(self, 'best_model_state'):
                self.model.load_state_dict(self.best_model_state)
            
            return {
                'success': True,
                'history': {
                    'loss': train_losses,
                    'val_loss': val_losses
                },
                'train_score': train_losses[-1],
                'val_score': val_losses[-1]
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        模型预测（实现抽象方法）
        """
        if not self.is_trained or self.model is None:
            return np.array([])
        
        try:
            self.model.eval()
            X_tensor = torch.FloatTensor(X).to(self.device)
            
            with torch.no_grad():
                predictions = self.model(X_tensor)
                predictions = predictions.cpu().numpy().flatten()
            
            return predictions
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.array([])
    
    def prepare_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备时间序列数据
        
        Args:
            data: 原始数据
            
        Returns:
            X: 序列特征 (samples, sequence_length, features)
            y: 标签
        """
        try:
            # 计算特征
            features_df = self._prepare_features_dataframe(data)
            
            if len(features_df) < self.sequence_length + 1:
                logger.warning(f"数据长度不足: {len(features_df)} < {self.sequence_length + 1}")
                return np.array([]), np.array([])
            
            # 准备特征列
            feature_columns = self._get_feature_columns(features_df)
            
            # 创建序列数据
            X, y = self._create_sequences(features_df, feature_columns)
            
            logger.info(f"序列数据准备完成: X shape={X.shape}, y shape={y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"序列数据准备失败: {e}")
            return np.array([]), np.array([])
    
    def _prepare_features_dataframe(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备特征数据框
        """
        df = data.copy()
        
        # 基础价格特征
        if self.use_price_features:
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['price_change'] = df['close'] - df['open']
            df['high_low_ratio'] = df['high'] / df['low'] - 1
            df['open_close_ratio'] = df['open'] / df['close'] - 1
        
        # 技术指标
        if self.use_technical_indicators:
            # 移动平均线
            for window in [5, 10, 20]:
                df[f'ma_{window}'] = df['close'].rolling(window=window).mean()
                df[f'ma_ratio_{window}'] = df['close'] / df[f'ma_{window}'] - 1
            
            # 波动率
            df['volatility_5'] = df['returns'].rolling(window=5).std()
            df['volatility_10'] = df['returns'].rolling(window=10).std()
            
            # RSI
            df['rsi'] = self._calculate_rsi(df['close'])
            
            # MACD
            df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
            df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # 成交量特征
        if self.use_volume_features and 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_price_trend'] = df['volume'] * df['returns']
        
        # 计算目标变量
        if self.prediction_type == 'classification':
            # 预测价格方向（上涨=1，下跌=0）
            df['target'] = (df['close'].shift(-1) > df['close']).astype(int)
        else:
            # 预测收益率
            df['target'] = df['close'].pct_change().shift(-1)
        
        # 删除NaN值
        df = df.dropna()
        
        return df
    
    def _get_feature_columns(self, df: pd.DataFrame) -> List[str]:
        """
        获取特征列名
        """
        exclude_cols = ['target', 'open', 'high', 'low', 'close', 'volume']
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        return feature_cols
    
    def _create_sequences(self, df: pd.DataFrame, feature_columns: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列
        """
        # 标准化特征
        if StandardScaler is not None:
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(df[feature_columns])
            # 保存scaler用于预测
            self.feature_scaler = scaler
        else:
            logger.warning("StandardScaler不可用，跳过特征标准化")
            scaled_features = df[feature_columns].values
            self.feature_scaler = None
        
        # 创建序列
        X, y = [], []
        
        for i in range(len(scaled_features) - self.sequence_length):
            # 特征序列
            X.append(scaled_features[i:(i + self.sequence_length)])
            # 标签
            y.append(df['target'].iloc[i + self.sequence_length])
        
        return np.array(X), np.array(y)
    
    def train(self, data: pd.DataFrame) -> bool:
        """
        训练CNN模型
        
        Args:
            data: 训练数据
            
        Returns:
            训练是否成功
        """
        try:
            logger.info("开始训练CNN时间序列模型")
            
            if not PYTORCH_AVAILABLE:
                logger.error("PyTorch不可用，无法训练模型")
                return False
            
            # 准备数据
            X, y = self.prepare_sequences(data)
            
            if len(X) == 0:
                logger.error("训练数据为空")
                return False
            
            # 训练模型
            result = self._train_model(X, y)
            
            if result['success']:
                self.training_history = result['history']
                self.is_trained = True
                logger.info(f"训练完成 - 训练损失: {result['train_score']:.4f}, 验证损失: {result['val_score']:.4f}")
                return True
            else:
                logger.error(f"模型训练失败: {result.get('error', '未知错误')}")
                return False
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return False
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        生成预测
        
        Args:
            data: 预测数据
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            logger.error("模型未训练")
            return np.array([])
        
        try:
            # 准备数据
            X, _ = self.prepare_sequences(data)
            
            if len(X) == 0:
                logger.warning("预测数据为空")
                return np.array([])
            
            # 生成预测
            predictions = self._predict_model(X)
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.array([])
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 市场数据
            
        Returns:
            包含交易信号的数据框
        """
        try:
            # 生成预测
            predictions = self.predict(data)
            
            if len(predictions) == 0:
                logger.warning("无法生成预测，返回空信号")
                return pd.DataFrame()
            
            # 创建信号数据框
            signal_df = data.iloc[-len(predictions):].copy()
            signal_df['prediction'] = predictions
            
            if self.prediction_type == 'classification':
                # 分类：预测概率转换为信号
                signal_df['signal'] = np.where(predictions > 0.5, 1, -1)
                signal_df['confidence'] = np.abs(predictions - 0.5) * 2
            else:
                # 回归：预测收益率转换为信号
                signal_df['signal'] = np.where(predictions > 0, 1, -1)
                signal_df['confidence'] = np.abs(predictions)
            
            # 添加时间戳
            signal_df['timestamp'] = datetime.now()
            
            logger.info(f"生成交易信号: {len(signal_df)} 个信号")
            return signal_df
            
        except Exception as e:
            logger.error(f"信号生成失败: {e}")
            return pd.DataFrame()
    
    def get_model_summary(self) -> Dict[str, Any]:
        """
        获取模型摘要信息
        """
        summary = {
            'strategy_name': self.name,
            'model_type': 'CNN_Time_Series_PyTorch',
            'is_trained': self.is_trained,
            'framework': 'PyTorch',
            'device': str(self.device),
            'parameters': {
                'filters': self.filters,
                'kernel_size': self.kernel_size,
                'sequence_length': self.sequence_length,
                'prediction_type': self.prediction_type,
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size
            }
        }
        
        if self.model is not None:
            summary['model_params'] = sum(p.numel() for p in self.model.parameters())
        
        if hasattr(self, 'training_history') and self.training_history:
            summary['training_history'] = {
                'epochs': len(self.training_history['loss']),
                'final_loss': self.training_history['loss'][-1],
                'final_val_loss': self.training_history['val_loss'][-1]
            }
        
        return summary
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        """
        if not self.is_trained or self.model is None:
            logger.error("没有训练好的模型可保存")
            return False
        
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'model_config': {
                    'input_size': self.model.conv_layers[0].in_channels,
                    'filters': self.filters,
                    'kernel_size': self.kernel_size,
                    'pool_size': self.pool_size,
                    'conv_layers': self.conv_layers,
                    'dense_units': self.dense_units,
                    'dropout_rate': self.dropout_rate,
                    'prediction_type': self.prediction_type
                },
                'feature_scaler': self.feature_scaler
            }, filepath)
            logger.info(f"模型已保存到: {filepath}")
            return True
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        """
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            
            # 重建模型
            config = checkpoint['model_config']
            self.model = CNNModel(
                input_size=config['input_size'],
                filters=config['filters'],
                kernel_size=config['kernel_size'],
                pool_size=config['pool_size'],
                conv_layers=config['conv_layers'],
                dense_units=config['dense_units'],
                dropout_rate=config['dropout_rate'],
                prediction_type=config['prediction_type']
            ).to(self.device)
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 加载特征缩放器
            self.feature_scaler = checkpoint.get('feature_scaler')
            
            self.is_trained = True
            logger.info(f"模型已从 {filepath} 加载")
            return True
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """
        计算MACD指标
        """
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal