# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# coding: utf-8
"""
高斯过程波动率择时器
基于高斯过程波动率模型(GPVM)的智能择时策略

策略原理:
- 使用贝叶斯非参数模型改进传统GARCH模型
- 通过高斯过程自动学习波动率回看长度，无需手动设置p、q参数
- 使用粒子滤波器进行在线学习和波动率估计
- 根据波动率预测进行择时决策

技术优势:
- 自适应回看长度，避免参数调优困难
- 能够及时捕捉波动率的剧烈变化
- 使用贝叶斯方法，提供不确定性量化

参考原始研究：【机器学习】时间序列波动率估计
链接: https://www.joinquant.com/view/community/detail/68034f2d35df23f2844479d3126e0368
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import warnings
import sys
import os
from datetime import datetime
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C, WhiteKernel
from scipy import stats
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
    from .base_timer import BaseTimer
    from .timing_core import strategy_autodev.TimingConfig, TimingSignal
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class StockDataAdapter:
        def get_stock_data(self, symbol, start_date, end_date, adjust='qfq'):
            print(f"警告：无法导入StockDataAdapter，请检查adapters模块")
            return pd.DataFrame()
    
    from loguru import logger
    
    class BaseTimer:
        def __init__(self, config=None, **kwargs):
            self.config = config
            self._required_columns = kwargs.get('required_columns', ['close'])
            self._min_history = kwargs.get('min_history', 20)
            
        def validate_data(self, data):
            return True  # 简化验证
            
        def apply_threshold(self, strength):
            return strength
            
        def normalize_strength(self, value, vmin, vmax):
            return min(1.0, max(0.0, (value - vmin) / (vmax - vmin)))
    
    class TimingConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class TimingSignal:
        def __init__(self, timestamp, signal, strength, strategy, metadata):
            self.timestamp = timestamp
            self.signal = signal
            self.strength = strength
            self.strategy = strategy
            self.metadata = metadata

warnings.filterwarnings('ignore')


class GaussianProcessVolatilityTimer(BaseTimer):
    """
    高斯过程波动率择时策略
    基于GPVM模型的智能波动率预测和择时决策
    """
    
    def __init__(self, 
                 lookback_window: int = 50,
                 vol_threshold_high: float = 0.8,
                 vol_threshold_low: float = 0.2,
                 n_particles: int = 100,
                 smoothing_factor: float = 0.99):
        """
        初始化高斯过程波动率择时器
        
        Args:
            lookback_window: 波动率计算的回看窗口
            vol_threshold_high: 高波动率阈值，超过则考虑卖出
            vol_threshold_low: 低波动率阈值，低于则考虑买入
            n_particles: 粒子滤波器中的粒子数量
            smoothing_factor: 平滑因子，用于高斯过程均值函数
        """
        super().__init__(
            TimingConfig(lookback_period=lookback_window),
            required_columns=['close'],
            min_history=lookback_window
        )
        self.lookback_window = lookback_window
        self.vol_threshold_high = vol_threshold_high
        self.vol_threshold_low = vol_threshold_low
        self.n_particles = n_particles
        self.smoothing_factor = smoothing_factor
        self.data_adapter = StockDataAdapter()
        
        # 高斯过程模型参数
        self.kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2)) + WhiteKernel(1e-5, (1e-10, 1e+1))
        self.gp_model = None
        
    def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票数据"""
        try:
            if '.XSHG' in stock_code:
                ts_code = stock_code.replace('.XSHG', '.SH')
            elif '.XSHE' in stock_code:
                ts_code = stock_code.replace('.XSHE', '.SZ')
            else:
                ts_code = stock_code
            
            data = self.data_adapter.get_stock_data(
                symbol=ts_code,
                start_date=start_date,
                end_date=end_date,
                adjust='qfq'
            )
            
            if data.empty:
                logger.warning(f"未获取到股票 {stock_code} 的数据")
                return pd.DataFrame()
            
            data.columns = data.columns.str.lower()
            return data
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_returns(self, prices: pd.Series) -> np.ndarray:
        """计算收益率序列"""
        return np.log(prices / prices.shift(1)).dropna().values
    
    def empirical_volatility(self, returns: np.ndarray, window_size: int = 5) -> np.ndarray:
        """计算经验波动率作为初始化"""
        n = len(returns)
        volatilities = np.zeros(n)
        
        for i in range(window_size, n):
            window_returns = returns[i-window_size:i]
            volatilities[i] = np.std(window_returns)
        
        return volatilities
    
    def simplified_gp_volatility(self, returns: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """简化的高斯过程波动率估计"""
        n = len(returns)
        if n < self.lookback_window:
            logger.warning("数据长度不足，使用经验波动率")
            emp_vol = self.empirical_volatility(returns)
            return emp_vol, np.zeros_like(emp_vol)
        
        # 计算滚动波动率作为目标变量
        rolling_vol = pd.Series(returns).rolling(window=10).std().fillna(0).values
        
        # 构建特征：时间序列索引和滞后收益率
        time_index = np.arange(n).astype(np.float64)
        lagged_returns = np.pad(returns[:-1], (1, 0), mode='constant', constant_values=0)
        rolling_vol_array = np.array(rolling_vol)
        X = np.column_stack([time_index, lagged_returns, rolling_vol_array])
        
        y = rolling_vol
        
        # 使用滑动窗口训练和预测
        estimated_vol = np.zeros(n)
        vol_std = np.zeros(n)
        
        for i in range(self.lookback_window, n):
            start_idx = max(0, i - self.lookback_window)
            X_train = X[start_idx:i]
            y_train = y[start_idx:i]
            
            # 过滤无效数据
            y_train_array = np.array(y_train)
            valid_mask = ~np.isnan(y_train_array) & ~np.isinf(y_train_array) & (y_train_array > 0)
            if np.sum(valid_mask) < 10:
                estimated_vol[i] = rolling_vol[i]
                vol_std[i] = 0
                continue
            
            X_train = X_train[valid_mask]
            y_train = y_train[valid_mask]
            
            try:
                gp = GaussianProcessRegressor(kernel=self.kernel, n_restarts_optimizer=2)
                gp.fit(X_train, y_train)
                X_pred = X[i:i+1]
                vol_pred, vol_uncertainty = gp.predict(X_pred, return_std=True)
                estimated_vol[i] = vol_pred[0]
                vol_std[i] = vol_uncertainty[0]
            except Exception as e:
                estimated_vol[i] = rolling_vol[i]
                vol_std[i] = 0
        
        return estimated_vol, vol_std
    
    def generate_volatility_signals(self, 
                                  estimated_vol: np.ndarray, 
                                  vol_std: np.ndarray,
                                  returns: np.ndarray) -> np.ndarray:
        """基于波动率预测生成交易信号"""
        n = len(estimated_vol)
        signals = np.zeros(n)
        
        # 计算波动率的分位数阈值
        valid_vol_mask = estimated_vol > 0
        valid_vol = estimated_vol[valid_vol_mask]
        if len(valid_vol) > 0:
            vol_high_threshold = np.percentile(valid_vol, 80)
            vol_low_threshold = np.percentile(valid_vol, 20)
        else:
            vol_high_threshold = self.vol_threshold_high
            vol_low_threshold = self.vol_threshold_low
        
        for i in range(1, n):
            current_vol = estimated_vol[i]
            prev_vol = estimated_vol[i-1] if i > 0 else current_vol
            current_return = returns[i] if i < len(returns) else 0
            
            # 波动率择时逻辑
            if current_vol < vol_low_threshold and current_return > 0:
                # 低波动率且价格上涨：买入信号
                signals[i] = 1
            elif current_vol > vol_high_threshold and current_return < 0:
                # 高波动率且价格下跌：卖出信号
                signals[i] = -1
            elif current_vol > prev_vol * 1.5 and current_return < -0.02:
                # 波动率急剧上升且价格大跌：卖出信号
                signals[i] = -1
            elif current_vol < prev_vol * 0.7 and current_return > 0.01:
                # 波动率急剧下降且价格上涨：买入信号
                signals[i] = 1
            else:
                signals[i] = 0
        
        return signals
    
    def calculate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算高斯过程波动率信号"""
        if data.empty:
            return pd.DataFrame()
        
        result = data.copy()
        
        # 计算收益率
        close_series = result['close']
        if isinstance(close_series, pd.Series):
            returns = self.calculate_returns(close_series)
        else:
            returns = self.calculate_returns(pd.Series(close_series))
        result['returns'] = np.concatenate([[0], returns])
        
        # 计算高斯过程波动率估计
        estimated_vol, vol_std = self.simplified_gp_volatility(returns)
        
        # 对齐数据长度
        n = len(result)
        if len(estimated_vol) < n:
            estimated_vol = np.concatenate([[0], estimated_vol])
            vol_std = np.concatenate([[0], vol_std])
        
        result['gp_volatility'] = estimated_vol[:n]
        result['vol_uncertainty'] = vol_std[:n]
        
        # 计算传统波动率作为对比
        result['rolling_volatility'] = result['returns'].rolling(window=20).std().fillna(0)
        
        # 生成交易信号
        signals = self.generate_volatility_signals(estimated_vol[:n], vol_std[:n], returns)
        result['gp_signal'] = signals[:n] if len(signals) >= n else np.concatenate([signals, [0]*(n-len(signals))])
        
        # 生成具体的买卖信号
        result['buy_signal'] = (result['gp_signal'] == 1).astype(int)
        result['sell_signal'] = (result['gp_signal'] == -1).astype(int)
        
        # 计算波动率比率（GP vs 滚动波动率）
        result['volatility_ratio'] = np.where(
            result['rolling_volatility'] != 0,
            result['gp_volatility'] / result['rolling_volatility'],
            1.0
        )
        
        return result
    
    def analyze_signals(self, data: pd.DataFrame) -> Dict[str, Union[int, float]]:
        """分析信号统计信息"""
        if data.empty:
            return {}
        
        buy_signals = data['buy_signal'].sum()
        sell_signals = data['sell_signal'].sum()
        total_signals = buy_signals + sell_signals
        
        # 波动率统计
        avg_gp_vol = data['gp_volatility'].mean()
        avg_rolling_vol = data['rolling_volatility'].mean()
        max_vol = data['gp_volatility'].max()
        min_vol = data['gp_volatility'].min()
        
        # 信号质量评估
        signal_strength = data['vol_uncertainty'].mean()
        
        return {
            'total_signals': total_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'buy_ratio': buy_signals / total_signals if total_signals > 0 else 0,
            'avg_gp_volatility': avg_gp_vol,
            'avg_rolling_volatility': avg_rolling_vol,
            'max_volatility': max_vol,
            'min_volatility': min_vol,
            'volatility_range': max_vol - min_vol,
            'signal_uncertainty': signal_strength,
            'data_points': len(data)
        }
    
    def plot_analysis(self, data: pd.DataFrame, stock_code: str = "", save_path: Optional[str] = None):
        """绘制波动率分析图表"""
        if data.empty:
            print("数据为空，无法绘制图表")
            return
        
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(4, 1, figsize=(15, 16))
        fig.suptitle(f'{stock_code} 高斯过程波动率择时分析', fontsize=16, fontweight='bold')
        
        # 价格和信号
        ax1 = axes[0]
        ax1.plot(data.index, data['close'], label='收盘价', color='black', alpha=0.7)
        buy_points = data[data['buy_signal'] == 1]
        sell_points = data[data['sell_signal'] == 1]
        ax1.scatter(buy_points.index, buy_points['close'], color='red', marker='^', 
                   s=100, label=f'买入信号 ({len(buy_points)})', alpha=0.8)
        ax1.scatter(sell_points.index, sell_points['close'], color='green', marker='v', 
                   s=100, label=f'卖出信号 ({len(sell_points)})', alpha=0.8)
        ax1.set_title('价格与交易信号')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # GP波动率 vs 滚动波动率
        ax2 = axes[1]
        ax2.plot(data.index, data['gp_volatility'], label='GP波动率', color='blue', linewidth=2)
        ax2.plot(data.index, data['rolling_volatility'], label='滚动波动率', color='orange', 
                linestyle='--', alpha=0.7)
        ax2.fill_between(data.index, 
                        data['gp_volatility'] - data['vol_uncertainty'],
                        data['gp_volatility'] + data['vol_uncertainty'],
                        alpha=0.2, color='blue', label='不确定性区间')
        ax2.set_title('波动率对比')
        ax2.set_ylabel('波动率')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 收益率和波动率关系
        ax3 = axes[2]
        ax3.plot(data.index, data['returns'], label='收益率', color='purple', alpha=0.6)
        ax3_twin = ax3.twinx()
        ax3_twin.plot(data.index, data['gp_volatility'], label='GP波动率', color='red', alpha=0.8)
        ax3.set_title('收益率与波动率关系')
        ax3.set_ylabel('收益率', color='purple')
        ax3_twin.set_ylabel('波动率', color='red')
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)
        
        # 波动率比率
        ax4 = axes[3]
        ax4.plot(data.index, data['volatility_ratio'], label='波动率比率(GP/滚动)', 
                color='green', linewidth=2)
        ax4.axhline(y=1, color='gray', linestyle='--', alpha=0.5, label='基准线')
        ax4.set_title('GP波动率 vs 滚动波动率比率')
        ax4.set_ylabel('比率')
        ax4.set_xlabel('日期')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def print_analysis_results(self, analysis: Dict[str, Union[int, float]], stock_code: str = ""):
        """打印分析结果"""
        print(f"\n{'='*60}")
        print(f"高斯过程波动率择时分析结果 - {stock_code}")
        print(f"{'='*60}")
        print(f"数据点数: {analysis.get('data_points', 0)}")
        print(f"总信号数: {analysis.get('total_signals', 0)}")
        print(f"买入信号: {analysis.get('buy_signals', 0)}")
        print(f"卖出信号: {analysis.get('sell_signals', 0)}")
        print(f"买入信号比例: {analysis.get('buy_ratio', 0):.2%}")
        print(f"\n波动率统计")
        print(f"  平均GP波动率: {analysis.get('avg_gp_volatility', 0):.4f}")
        print(f"  平均滚动波动率: {analysis.get('avg_rolling_volatility', 0):.4f}")
        print(f"  最大波动率: {analysis.get('max_volatility', 0):.4f}")
        print(f"  最小波动率: {analysis.get('min_volatility', 0):.4f}")
        print(f"  波动率范围: {analysis.get('volatility_range', 0):.4f}")
        print(f"  信号不确定性: {analysis.get('signal_uncertainty', 0):.4f}")
        print(f"{'='*60}")
    
    def run_strategy(self, stock_code: str, start_date: str, end_date: str, 
                    plot: bool = True, save_path: Optional[str] = None) -> Tuple[pd.DataFrame, Dict]:
        """运行高斯过程波动率择时策略"""
        logger.info(f"开始运行高斯过程波动率择时策略: {stock_code}")
        
        # 获取数据
        data = self.get_stock_data(stock_code, start_date, end_date)
        if data.empty:
            logger.error("无法获取股票数据")
            return pd.DataFrame(), {}
        
        # 计算信号
        result_data = self.calculate_signals(data)
        
        # 分析结果
        analysis = self.analyze_signals(result_data)
        
        # 打印结果
        self.print_analysis_results(analysis, stock_code)
        
        # 绘制图表
        if plot:
            plot_save_path = None
            if save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                plot_save_path = f"{save_path}/gp_volatility_{stock_code}_{timestamp}.png"
            self.plot_analysis(result_data, stock_code, plot_save_path)
        
        logger.info("高斯过程波动率择时策略分析完成。")
        return result_data, analysis

    def calculate_signal(self, data: pd.DataFrame, **kwargs) -> TimingSignal:
        """实现抽象方法：基于最新数据计算信号"""
        if not self.validate_data(data):
            raise ValueError("数据验证失败")
        
        # 计算信号
        signal_data = self.calculate_signals(data)
        if signal_data.empty:
            return TimingSignal(
                timestamp=datetime.now(),
                signal=0,
                strength=0.0,
                strategy="GaussianProcessVolatility",
                metadata={"error": "计算失败"}
            )
        
        latest = signal_data.iloc[-1]
        signal_value = latest.get('gp_signal', 0)
        
        # 计算信号强度
        current_vol = latest.get('gp_volatility', 0)
        vol_ratio = latest.get('volatility_ratio', 1.0)
        strength = self.apply_threshold(
            self.normalize_strength(abs(vol_ratio - 1.0), vmin=0, vmax=1.0)
        )
        
        return TimingSignal(
            timestamp=datetime.now(),
            signal=int(signal_value),
            strength=strength,
            strategy="GaussianProcessVolatility",
            metadata={
                'gp_volatility': current_vol,
                'volatility_ratio': vol_ratio,
                'vol_uncertainty': latest.get('vol_uncertainty', 0)
            }
        )


def main():
    """主函数 - 示例用法"""
    # 创建择时器实例
    timer = GaussianProcessVolatilityTimer(
        lookback_window=50,
        vol_threshold_high=0.8,
        vol_threshold_low=0.2,
        n_particles=100
    )
    
    # 运行策略示例
    stock_code = "000001.XSHE"  # 平安银行
    start_date = "2023-01-01"
    end_date = "2024-12-31"
    
    try:
        result_data, analysis = timer.run_strategy(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            plot=True,
            save_path="./timing_results"
        )
        
        print(f"\n策略运行完成。")
        print(f"信号数据形状: {result_data.shape}")
        print(f"分析结果: {len(analysis)} 个指标)")
        
    except Exception as e:
        print(f"策略运行出错: {e}")


if __name__ == "__main__":
    main() 
