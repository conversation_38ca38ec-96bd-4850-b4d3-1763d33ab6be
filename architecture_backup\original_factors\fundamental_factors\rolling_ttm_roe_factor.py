# -*- coding: utf-8 -*-
"""
滚动TTM净资产收益率因子 (Rolling TTM ROE Factor)
基于公司财报数据，计算滚动12个月的净资产收益率。
滚动TTM净资产收益率 (TTM ROE) = 过去12个月净利润(TTM) / 平均股东权益

指标定义
- 过去12个月净利润(TTM): 最近四个季度的归属于母公司所有者的净利润之和。
- 平均股东权益: (期初股东权益 + 期末股东权益) / 2

数据来源
- 归母净利润使用 `n_income_attr_p` 来自利润表。
- 股东权益使用 `total_hldr_eqy_exc_min_int` 来自资产负债表。
- TTM计算方法：由于财报按季度披露，TTM利润为最近4个季度利润之和。
- 平均股东权益：使用本期与上期财报的股东权益计算平均值。
参考链接：https://factors.directory/zh/factors/basic-surface/net-asset-yield
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
import warnings
from loguru import logger

try:
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from factor_analyze.base_factor_miner import BaseFactorMiner
except ImportError as e:
    logger.warning(f"相关模块导入失败: {e}")
    # 定义桩模块以确保代码结构完整性
    class BaseFactorMiner:
        def __init__(self, config=None):
            self.config = config or {}
    
    # 已删除重复定义: FundamentalDataAdapter
warnings.filterwarnings('ignore')


class StockRollingTTMROEFactor(BaseFactorMiner):
    """
    个股滚动TTM净资产收益率（ROE）因子
    
    计算逻辑:
    1. 获取单只股票的利润表和资产负债表。
    2. 按报告期对齐财务数据。
    3. 计算TTM归母净利润。
    4. 计算平均股东权益。
    5. 计算并返回滚动TTM ROE。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化个股滚动TTM ROE因子计算器。
        
        Args:
            config: 配置字典，用于自定义参数。
        """
        super().__init__(config)
        
        # 因子基本信息
        self.factor_name = "rolling_ttm_roe"
        self.factor_description = "滚动TTM净资产收益率因子"
        self.factor_type = "fundamental"
        self.target_type = "stock"
        
        # 数据适配器
        try:
            self.data_adapter = FundamentalDataAdapter()
        except Exception as e:
            logger.warning(f"基础数据适配器初始化失败: {e}")
            self.data_adapter = None
        
        # 计算参数
        self.lookback_quarters = self.config.get('lookback_quarters', 4)  # TTM默认回看4个季度
        self.min_quarters = self.config.get('min_quarters', 4)  # 要求的最少季度数
        self.min_equity_threshold = self.config.get('min_equity_threshold', 1000)  # 最小股东权益阈值
        
        # 质量控制参数
        self.min_roe_threshold = self.config.get('min_roe_threshold', -1.0)  # ROE有效范围下限
        self.max_roe_threshold = self.config.get('max_roe_threshold', 1.0)   # ROE有效范围上限
        self.outlier_threshold = self.config.get('outlier_threshold', 3.0)   # Z-score异常值过滤阈值
        
        logger.info("[INFO] 个股滚动TTM ROE因子已初始化。")
    
    def calculate_factor(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        计算指定股票的滚动TTM ROE因子。
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含因子值的DataFrame。
        """
        try:
            logger.info(f"[INFO] 开始为 {symbol} 计算滚动TTM ROE因子。")
            
            if not self.data_adapter:
                logger.error("数据适配器不可用，计算中止。")
                return pd.DataFrame()
            
            # 设置默认日期范围
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365*3)).strftime('%Y%m%d')
            
            # 1. 获取利润表数据
            income_data = self._get_income_data(symbol, start_date, end_date)
            if income_data.empty:
                logger.warning(f"未能获取 {symbol} 的利润表数据。")
                return pd.DataFrame()
            
            # 2. 获取资产负债表数据
            balance_data = self._get_balance_data(symbol, start_date, end_date)
            if balance_data.empty:
                logger.warning(f"未能获取 {symbol} 的资产负债表数据。")
                return pd.DataFrame()
            
            # 3. 计算TTM数据
            ttm_data = self._calculate_ttm_data(income_data, balance_data)
            if ttm_data.empty:
                logger.warning(f"TTM数据计算失败: {symbol}")
                return pd.DataFrame()
            
            # 4. 计算滚动TTM ROE
            factor_data = self._calculate_rolling_ttm_roe(ttm_data)
            if factor_data.empty:
                logger.warning(f"滚动TTM ROE计算失败: {symbol}")
                return pd.DataFrame()
            
            # 5. 应用质量控制
            factor_data = self._apply_quality_control(factor_data)
            
            # 6. 格式化输出
            factor_data['symbol'] = symbol
            factor_data['factor_name'] = self.factor_name
            factor_data['calculation_date'] = datetime.now().strftime('%Y-%m-%d')
            
            logger.info(f"[INFO] {symbol} 的滚动TTM ROE因子计算完成，共 {len(factor_data)} 条记录。")
            return factor_data
            
        except Exception as e:
            logger.error(f"为 {symbol} 计算滚动TTM ROE因子时发生错误: {e}")
            return pd.DataFrame()
    
    def _get_income_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取并预处理利润表数据。
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            处理后的利润表数据。
        """
        try:
            data = self.data_adapter.get_income_statement(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if data.empty:
                return pd.DataFrame()
            
            # 检查必要字段
            required_fields = ['end_date', 'n_income_attr_p']
            missing_fields = [field for field in required_fields if field not in data.columns]
            
            if missing_fields:
                logger.warning(f"利润表数据缺少必要字段: {missing_fields}")
                return pd.DataFrame()
            
            # 数据清洗
            data['end_date'] = pd.to_datetime(data['end_date'])
            data = data.sort_values('end_date')
            
            # 移除归母净利润为空的记录
            data = data.dropna(subset=['n_income_attr_p'])
            
            logger.info(f"成功获取并处理了 {len(data)} 条利润表数据。")
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 的利润表数据时出错: {e}")
            return pd.DataFrame()
    
    def _get_balance_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取并预处理资产负债表数据。
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            处理后的资产负债表数据。
        """
        try:
            data = self.data_adapter.get_balance_sheet(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if data.empty:
                return pd.DataFrame()
            
            # 检查必要字段
            required_fields = ['end_date', 'total_hldr_eqy_exc_min_int']
            missing_fields = [field for field in required_fields if field not in data.columns]
            
            if missing_fields:
                logger.warning(f"资产负债表数据缺少必要字段: {missing_fields}")
                return pd.DataFrame()
            
            # 数据清洗
            data['end_date'] = pd.to_datetime(data['end_date'])
            data = data.sort_values('end_date')
            
            # 移除股东权益为空或为负的记录
            data = data.dropna(subset=['total_hldr_eqy_exc_min_int'])
            data = data[data['total_hldr_eqy_exc_min_int'] > 0]
            
            logger.info(f"成功获取并处理了 {len(data)} 条资产负债表数据。")
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 的资产负债表数据时出错: {e}")
            return pd.DataFrame()
    
    def _calculate_ttm_data(self, income_data: pd.DataFrame, balance_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算TTM财务数据。
        
        Args:
            income_data: 利润表数据
            balance_data: 资产负债表数据
            
        Returns:
            包含TTM数据的DataFrame。
        """
        try:
            if income_data.empty or balance_data.empty:
                return pd.DataFrame()
            
            # 按报告期合并数据
            merged_data = pd.merge(
                income_data[['end_date', 'n_income_attr_p']],
                balance_data[['end_date', 'total_hldr_eqy_exc_min_int']],
                on='end_date',
                how='inner'
            )
            
            if merged_data.empty:
                logger.warning("利润表和资产负债表数据在报告期上无法对齐。")
                return pd.DataFrame()
            
            merged_data = merged_data.sort_values('end_date')
            
            # 计算TTM归母净利润
            ttm_results = []
            
            for i in range(len(merged_data)):
                if i < self.lookback_quarters - 1:
                    continue
                
                # 截取最近4个季度的数据
                recent_data = merged_data.iloc[i-self.lookback_quarters+1:i+1]
                
                if len(recent_data) < self.min_quarters:
                    continue
                
                # 计算TTM归母净利润
                ttm_net_profit = recent_data['n_income_attr_p'].sum()
                
                # 计算平均股东权益
                current_equity = merged_data.iloc[i]['total_hldr_eqy_exc_min_int']
                if i > 0:
                    prev_equity = merged_data.iloc[i-1]['total_hldr_eqy_exc_min_int']
                    avg_equity = (current_equity + prev_equity) / 2
                else:
                    avg_equity = current_equity
                
                ttm_results.append({
                    'end_date': merged_data.iloc[i]['end_date'],
                    'ttm_net_profit': ttm_net_profit,
                    'avg_equity': avg_equity,
                    'current_equity': current_equity
                })
            
            if not ttm_results:
                logger.warning("未能计算出任何有效的TTM数据。")
                return pd.DataFrame()
            
            ttm_data = pd.DataFrame(ttm_results)
            logger.info(f"成功计算了 {len(ttm_data)} 条TTM数据。")
            return ttm_data
            
        except Exception as e:
            logger.error(f"计算TTM数据时出错: {e}")
            return pd.DataFrame()
    
    def _calculate_rolling_ttm_roe(self, ttm_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算滚动TTM ROE。
        
        Args:
            ttm_data: 包含TTM数据的DataFrame
            
        Returns:
            包含滚动TTM ROE的DataFrame。
        """
        try:
            if ttm_data.empty:
                return pd.DataFrame()
            
            data = ttm_data.copy()
            
            # 计算滚动TTM ROE
            data['rolling_ttm_roe'] = np.where(
                data['avg_equity'] > 0,
                data['ttm_net_profit'] / data['avg_equity'],
                np.nan
            )
            
            # 数据清洗
            data = data.dropna(subset=['rolling_ttm_roe'])
            data = data[np.isfinite(data['rolling_ttm_roe'])]
            
            # 应用最小股东权益阈值
            data = data[data['avg_equity'] >= self.min_equity_threshold]
            
            if data.empty:
                logger.warning("计算TTM ROE后，没有剩余有效数据。")
                return pd.DataFrame()
            
            logger.info(f"成功计算了 {len(data)} 条滚动TTM ROE数据。")
            return data
            
        except Exception as e:
            logger.error(f"计算滚动TTM ROE时出错: {e}")
            return pd.DataFrame()
    
    def _apply_quality_control(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        对计算出的因子值进行质量控制。
        
        Args:
            data: 包含因子值的DataFrame
            
        Returns:
            经过质量控制的DataFrame。
        """
        try:
            if data.empty:
                return data
            
            original_count = len(data)
            
            # 1. ROE范围过滤
            data = data[
                (data['rolling_ttm_roe'] >= self.min_roe_threshold) &
                (data['rolling_ttm_roe'] <= self.max_roe_threshold)
            ]
            
            # 2. Z-score异常值过滤
            if len(data) > 1:
                mean_roe = data['rolling_ttm_roe'].mean()
                std_roe = data['rolling_ttm_roe'].std()
                
                if std_roe > 0:
                    lower_bound = mean_roe - self.outlier_threshold * std_roe
                    upper_bound = mean_roe + self.outlier_threshold * std_roe
                    
                    data = data[
                        (data['rolling_ttm_roe'] >= lower_bound) &
                        (data['rolling_ttm_roe'] <= upper_bound)
                    ]
            
            filtered_count = len(data)
            logger.info(f"质量控制过滤: {original_count} -> {filtered_count}")
            
            return data
            
        except Exception as e:
            logger.error(f"应用质量控制时出错: {e}")
            return data
    
    def get_factor_config(self) -> Dict:
        """
        获取当前因子的配置信息。
        
        Returns:
            配置字典。
        """
        return {
            'factor_name': self.factor_name,
            'factor_description': self.factor_description,
            'factor_type': self.factor_type,
            'target_type': self.target_type,
            'lookback_quarters': self.lookback_quarters,
            'min_quarters': self.min_quarters,
            'min_equity_threshold': self.min_equity_threshold,
            'min_roe_threshold': self.min_roe_threshold,
            'max_roe_threshold': self.max_roe_threshold,
            'outlier_threshold': self.outlier_threshold,
            'data_source': 'Tushare',
            'update_frequency': 'quarterly',
            'original_url': 'https://factors.directory/zh/factors/basic-surface/net-asset-yield'
        }


class IndexRollingTTMROEFactor(BaseFactorMiner):
    """
    指数滚动TTM净资产收益率（ROE）因子
    
    通过对指数成分股的TTM ROE进行加权平均来计算。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化指数滚动TTM ROE因子计算器。
        
        Args:
            config: 配置字典。
        """
        super().__init__(config)
        
        # 因子基本信息
        self.factor_name = "index_rolling_ttm_roe"
        self.factor_description = "指数滚动TTM净资产收益率因子"
        self.factor_type = "fundamental"
        self.target_type = "index"
        
        # 依赖个股因子计算器
        self.stock_factor = StockRollingTTMROEFactor(config)
        
        # 加权方法
        self.weight_method = self.config.get('weight_method', 'market_cap')  # 'market_cap', 'equal'
        
        logger.info("[INFO] 指数滚动TTM ROE因子已初始化。")
    
    def calculate_factor(self, index_code: str, constituents: List[str], 
                        weights: Optional[Dict[str, float]] = None,
                        start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        计算指定指数的滚动TTM ROE因子。
        
        Args:
            index_code: 指数代码
            constituents: 成分股列表
            weights: 成分股权重字典 (可选)
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含指数因子值的DataFrame。
        """
        try:
            logger.info(f"[INFO] 开始为指数 {index_code} 计算滚动TTM ROE因子。")
            
            if not constituents:
                logger.error("成分股列表为空，无法计算。")
                return pd.DataFrame()
            
            # 1. 计算所有成分股的因子值
            stock_factors = {}
            for stock in constituents:
                factor_data = self.stock_factor.calculate_factor(stock, start_date, end_date)
                if not factor_data.empty:
                    stock_factors[stock] = factor_data
            
            if not stock_factors:
                logger.warning(f"未能计算出任何成分股的因子值: {index_code}")
                return pd.DataFrame()
            
            # 2. 进行加权平均
            index_factor = self._calculate_weighted_average(stock_factors, weights)
            
            if index_factor.empty:
                logger.warning(f"指数因子加权平均计算失败: {index_code}")
                return pd.DataFrame()
            
            # 3. 格式化输出
            index_factor['index_code'] = index_code
            index_factor['factor_name'] = self.factor_name
            index_factor['calculation_date'] = datetime.now().strftime('%Y-%m-%d')
            index_factor['constituent_count'] = len(stock_factors)
            
            logger.info(f"[INFO] 指数 {index_code} 的滚动TTM ROE因子计算完成。")
            return index_factor
            
        except Exception as e:
            logger.error(f"为指数 {index_code} 计算滚动TTM ROE因子时发生错误: {e}")
            return pd.DataFrame()
    
    def _calculate_weighted_average(self, stock_factors: Dict[str, pd.DataFrame], 
                                   weights: Optional[Dict[str, float]] = None) -> pd.DataFrame:
        """
        执行加权平均计算。
        
        Args:
            stock_factors: 包含所有成分股因子值的字典
            weights: 权重字典
            
        Returns:
            加权平均后的因子值DataFrame。
        """
        try:
            if not stock_factors:
                return pd.DataFrame()
            
            # 收集所有不重复的报告期
            all_dates = set()
            for factor_data in stock_factors.values():
                all_dates.update(factor_data['end_date'].dt.strftime('%Y-%m-%d'))
            
            all_dates = sorted(list(all_dates))
            
            # 按报告期逐一计算加权平均值
            results = []
            
            for date_str in all_dates:
                date_factors = []
                date_weights = []
                
                for stock, factor_data in stock_factors.items():
                    # 找到对应日期的因子数据
                    date_data = factor_data[
                        factor_data['end_date'].dt.strftime('%Y-%m-%d') == date_str
                    ]
                    
                    if not date_data.empty:
                        factor_value = date_data['rolling_ttm_roe'].iloc[0]
                        if not np.isnan(factor_value):
                            date_factors.append(factor_value)
                            
                            # 获取权重
                            if weights and stock in weights:
                                weight = weights[stock]
                            else:
                                weight = 1.0  # 默认为等权
                            date_weights.append(weight)
                
                if date_factors:
                    # 计算加权平均
                    date_factors = np.array(date_factors)
                    date_weights = np.array(date_weights)
                    date_weights = date_weights / date_weights.sum()  # 权重归一化
                    weighted_avg = np.sum(date_factors * date_weights)
                    
                    results.append({
                        'end_date': pd.to_datetime(date_str),
                        'rolling_ttm_roe': weighted_avg,
                        'valid_stocks': len(date_factors)
                    })
            
            if not results:
                return pd.DataFrame()
            
            result_df = pd.DataFrame(results)
            result_df = result_df.sort_values('end_date')
            
            logger.info(f"成功计算了 {len(result_df)} 条指数加权平均因子值。")
            return result_df
            
        except Exception as e:
            logger.error(f"计算指数加权平均时出错: {e}")
            return pd.DataFrame()
    
    def get_factor_config(self) -> Dict:
        """
        获取指数因子的配置信息。
        
        Returns:
            配置字典。
        """
        config = self.stock_factor.get_factor_config()
        config.update({
            'factor_name': self.factor_name,
            'factor_description': self.factor_description,
            'target_type': self.target_type,
            'weight_method': self.weight_method
        })
        return config


# 默认配置
default_config = {
    'lookback_quarters': 4,        # TTM回溯季度数
    'min_quarters': 4,             # 计算所需的最小季度数
    'min_equity_threshold': 1000,  # 最小股东权益（元）
    'min_roe_threshold': -1.0,     # ROE有效范围下限
    'max_roe_threshold': 1.0,      # ROE有效范围上限
    'outlier_threshold': 3.0,      # Z-score异常值过滤阈值
    'weight_method': 'market_cap'  # 指数加权方式
}


def create_rolling_ttm_roe_factor(target_type: str = 'stock', config: Optional[Dict] = None) -> BaseFactorMiner:
    """
    创建滚动TTM ROE因子实例的工厂函数。
    
    Args:
        target_type: 目标类型 ('stock' 或 'index')
        config: 配置字典
        
    Returns:
        对应的因子实例。
    """
    if config is None:
        config = default_config.copy()
    
    if target_type.lower() == 'stock':
        return StockRollingTTMROEFactor(config)
    elif target_type.lower() == 'index':
        return IndexRollingTTMROEFactor(config)
    else:
        raise ValueError(f"不支持的目标类型: {target_type}")


if __name__ == "__main__":
    # 示例代码
    logger.info("开始执行滚动TTM ROE因子示例代码。")
    
    # 1. 创建个股因子实例
    stock_factor = create_rolling_ttm_roe_factor('stock')
    print("个股因子配置:", stock_factor.get_factor_config())
    
    # 2. 创建指数因子实例
    index_factor = create_rolling_ttm_roe_factor('index')
    print("指数因子配置:", index_factor.get_factor_config())
    
    logger.info("滚动TTM ROE因子示例执行完毕。")
