# -*- coding: utf-8 -*-
"""
多制度联合决策系统
Multi-Regime Joint Decision System

集成波动率制度、流动性制度、通胀制度、相关性制度的联合决策框架
基于券商研报的专业制度识别方法，提供统一的制度分析和决策支持

核心功能：
1. 多制度联合识别
2. 制度转换检测优化
3. 实时监控和预警
4. 投资决策支持

作者: Multi-Regime Decision System
创建时间: 2025-01-21
版本: 1.0.0
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
import warnings
import json
from pathlib import Path
import sys

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入现有制度识别模块
try:
    from continuous_learning_system.market_regime_identifier import (
        MarketRegimeIdentifier, VolatilityRegimeFeature,
        CorrelationRegimeFeature, MomentumRegimeFeature
    )
    MARKET_REGIME_AVAILABLE = True
except ImportError as e:
    logging.warning(f"市场制度识别模块不可用: {e}")
    MARKET_REGIME_AVAILABLE = False

try:
    from strategy_autodev.Timing.economic_cycle_indicators import (
        EconomicCycleIndicators, VolatilityIndicators
    )
    TIMING_INDICATORS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"择时指标模块不可用: {e}")
    TIMING_INDICATORS_AVAILABLE = False

try:
    from risk_management.specialized.tengta_policy_risk import TengtaPolicyRiskManager
    TENGTA_POLICY_AVAILABLE = True
except ImportError as e:
    logging.warning(f"滕泰政策模块不可用: {e}")
    TENGTA_POLICY_AVAILABLE = False

REGIME_MODULES_AVAILABLE = MARKET_REGIME_AVAILABLE or TIMING_INDICATORS_AVAILABLE

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RegimeType(Enum):
    """制度类型枚举"""
    VOLATILITY = "volatility"
    LIQUIDITY = "liquidity"
    INFLATION = "inflation"
    CORRELATION = "correlation"
    CREDIT = "credit"

class RegimeState(Enum):
    """制度状态枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    EXTREME = "extreme"

class AlertLevel(Enum):
    """预警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class RegimeSignal:
    """制度信号数据类"""
    regime_type: RegimeType
    current_state: RegimeState
    previous_state: RegimeState
    confidence: float
    transition_probability: float
    signal_strength: float
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __str__(self) -> str:
        """返回制度信号的字符串表示"""
        return (f"{self.regime_type.value.upper()} 制度: "
                f"状态={self.current_state.value}, "
                f"置信度={self.confidence:.3f}, "
                f"信号强度={self.signal_strength:.3f}")

    def __repr__(self) -> str:
        """返回制度信号的详细表示"""
        return (f"RegimeSignal(regime_type={self.regime_type.value}, "
                f"current_state={self.current_state.value}, "
                f"confidence={self.confidence:.3f}, "
                f"signal_strength={self.signal_strength:.3f})")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于JSON序列化"""
        return {
            'regime_type': self.regime_type.value,
            'current_state': self.current_state.value,
            'previous_state': self.previous_state.value,
            'confidence': self.confidence,
            'transition_probability': self.transition_probability,
            'signal_strength': self.signal_strength,
            'timestamp': self.timestamp.isoformat() if isinstance(self.timestamp, datetime) else str(self.timestamp),
            'metadata': self.metadata
        }

@dataclass
class RegimeTransition:
    """制度转换数据类"""
    regime_type: RegimeType
    from_state: RegimeState
    to_state: RegimeState
    transition_date: datetime
    confidence: float
    duration_days: int
    impact_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RegimeAlert:
    """制度预警数据类"""
    alert_id: str
    regime_type: RegimeType
    alert_level: AlertLevel
    message: str
    timestamp: datetime
    recommended_actions: List[str]
    risk_factors: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)

class LiquidityRegimeIdentifier:
    """流动性制度识别器"""
    
    def __init__(self):
        self.name = "流动性制度识别"
        # 基于滕泰框架的阈值
        self.thresholds = {
            'm1_critical': 2.0,    # M1增速临界值
            'm1_warning': 5.0,     # M1增速预警值
            'm2_normal': 8.0,      # M2增速正常值
            'interbank_rate_high': 0.035,  # 银行间利率高位
            'interbank_rate_low': 0.015    # 银行间利率低位
        }
    
    def identify_regime(self, market_data: pd.DataFrame) -> RegimeSignal:
        """识别流动性制度"""
        try:
            # 模拟流动性数据（实际应从数据管道获取）
            current_date = market_data.index[-1] if not market_data.empty else datetime.now()
            
            # 模拟M1、M2增速数据
            m1_growth = np.random.normal(4.0, 2.0)  # 模拟M1增速
            m2_growth = np.random.normal(8.5, 1.5)  # 模拟M2增速
            interbank_rate = np.random.normal(0.025, 0.01)  # 模拟银行间利率
            
            # 流动性制度判断
            if m1_growth < self.thresholds['m1_critical']:
                current_state = RegimeState.LOW
                confidence = 0.9
            elif m1_growth < self.thresholds['m1_warning']:
                current_state = RegimeState.NORMAL
                confidence = 0.7
            else:
                current_state = RegimeState.HIGH
                confidence = 0.8
            
            # 计算转换概率（简化实现）
            transition_prob = abs(m1_growth - self.thresholds['m1_warning']) / 10.0
            signal_strength = min(confidence * (1 + transition_prob), 1.0)
            
            return RegimeSignal(
                regime_type=RegimeType.LIQUIDITY,
                current_state=current_state,
                previous_state=RegimeState.NORMAL,  # 简化实现
                confidence=confidence,
                transition_probability=transition_prob,
                signal_strength=signal_strength,
                timestamp=current_date if isinstance(current_date, datetime) else datetime.now(),
                metadata={
                    'm1_growth': m1_growth,
                    'm2_growth': m2_growth,
                    'interbank_rate': interbank_rate
                }
            )
            
        except Exception as e:
            logger.error(f"流动性制度识别失败: {e}")
            return self._default_signal()
    
    def _default_signal(self) -> RegimeSignal:
        """默认信号"""
        return RegimeSignal(
            regime_type=RegimeType.LIQUIDITY,
            current_state=RegimeState.NORMAL,
            previous_state=RegimeState.NORMAL,
            confidence=0.5,
            transition_probability=0.1,
            signal_strength=0.5,
            timestamp=datetime.now()
        )

class InflationRegimeIdentifier:
    """通胀制度识别器"""
    
    def __init__(self):
        self.name = "通胀制度识别"
        self.thresholds = {
            'cpi_low': 0.02,      # CPI低通胀阈值
            'cpi_high': 0.04,     # CPI高通胀阈值
            'ppi_low': 0.01,      # PPI低通胀阈值
            'ppi_high': 0.05      # PPI高通胀阈值
        }
    
    def identify_regime(self, market_data: pd.DataFrame) -> RegimeSignal:
        """识别通胀制度"""
        try:
            current_date = market_data.index[-1] if not market_data.empty else datetime.now()
            
            # 模拟通胀数据
            cpi_rate = np.random.normal(0.025, 0.01)
            ppi_rate = np.random.normal(0.03, 0.015)
            
            # 通胀制度判断
            avg_inflation = (cpi_rate + ppi_rate) / 2
            
            if avg_inflation < self.thresholds['cpi_low']:
                current_state = RegimeState.LOW
                confidence = 0.85
            elif avg_inflation > self.thresholds['cpi_high']:
                current_state = RegimeState.HIGH
                confidence = 0.9
            else:
                current_state = RegimeState.NORMAL
                confidence = 0.75
            
            transition_prob = abs(avg_inflation - 0.03) / 0.05
            signal_strength = confidence * (1 - transition_prob * 0.3)
            
            return RegimeSignal(
                regime_type=RegimeType.INFLATION,
                current_state=current_state,
                previous_state=RegimeState.NORMAL,
                confidence=confidence,
                transition_probability=transition_prob,
                signal_strength=signal_strength,
                timestamp=current_date if isinstance(current_date, datetime) else datetime.now(),
                metadata={
                    'cpi_rate': cpi_rate,
                    'ppi_rate': ppi_rate,
                    'avg_inflation': avg_inflation
                }
            )
            
        except Exception as e:
            logger.error(f"通胀制度识别失败: {e}")
            return self._default_signal()
    
    def _default_signal(self) -> RegimeSignal:
        """默认信号"""
        return RegimeSignal(
            regime_type=RegimeType.INFLATION,
            current_state=RegimeState.NORMAL,
            previous_state=RegimeState.NORMAL,
            confidence=0.5,
            transition_probability=0.1,
            signal_strength=0.5,
            timestamp=datetime.now()
        )

class MultiRegimeDecisionSystem:
    """多制度联合决策系统"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._default_config()
        
        # 初始化各制度识别器
        self.regime_identifiers = {}
        self._initialize_identifiers()
        
        # 历史数据存储
        self.regime_history: Dict[RegimeType, List[RegimeSignal]] = {
            regime_type: [] for regime_type in RegimeType
        }
        self.transition_history: List[RegimeTransition] = []
        self.alert_history: List[RegimeAlert] = []
        
        # 决策权重配置
        self.regime_weights = self.config.get('regime_weights', {
            RegimeType.VOLATILITY: 0.3,
            RegimeType.LIQUIDITY: 0.25,
            RegimeType.INFLATION: 0.2,
            RegimeType.CORRELATION: 0.15,
            RegimeType.CREDIT: 0.1
        })
        
        logger.info("多制度联合决策系统初始化完成")
    
    def _default_config(self) -> Dict[str, Any]:
        """默认配置"""
        return {
            'transition_detection': {
                'min_confidence': 0.7,
                'min_duration': 5,
                'lookback_window': 20
            },
            'alert_thresholds': {
                'warning': 0.6,
                'critical': 0.8,
                'emergency': 0.9
            },
            'regime_weights': {
                RegimeType.VOLATILITY: 0.3,
                RegimeType.LIQUIDITY: 0.25,
                RegimeType.INFLATION: 0.2,
                RegimeType.CORRELATION: 0.15,
                RegimeType.CREDIT: 0.1
            }
        }
    
    def _initialize_identifiers(self):
        """初始化制度识别器"""
        try:
            # 波动率制度识别器
            if TIMING_INDICATORS_AVAILABLE:
                self.regime_identifiers[RegimeType.VOLATILITY] = VolatilityIndicators()

            # 流动性制度识别器
            self.regime_identifiers[RegimeType.LIQUIDITY] = LiquidityRegimeIdentifier()

            # 通胀制度识别器
            self.regime_identifiers[RegimeType.INFLATION] = InflationRegimeIdentifier()

            # 相关性制度识别器（使用现有模块）
            if MARKET_REGIME_AVAILABLE:
                self.regime_identifiers[RegimeType.CORRELATION] = MarketRegimeIdentifier()

            logger.info(f"成功初始化 {len(self.regime_identifiers)} 个制度识别器")

        except Exception as e:
            logger.error(f"制度识别器初始化失败: {e}")

    def analyze_all_regimes(self, market_data: pd.DataFrame) -> Dict[RegimeType, RegimeSignal]:
        """分析所有制度状态"""
        regime_signals = {}

        for regime_type, identifier in self.regime_identifiers.items():
            try:
                if regime_type == RegimeType.VOLATILITY:
                    # 波动率制度分析
                    signal = self._analyze_volatility_regime(market_data, identifier)
                elif regime_type == RegimeType.LIQUIDITY:
                    # 流动性制度分析
                    signal = identifier.identify_regime(market_data)
                elif regime_type == RegimeType.INFLATION:
                    # 通胀制度分析
                    signal = identifier.identify_regime(market_data)
                elif regime_type == RegimeType.CORRELATION:
                    # 相关性制度分析
                    signal = self._analyze_correlation_regime(market_data, identifier)
                else:
                    continue

                regime_signals[regime_type] = signal

                # 更新历史记录
                self.regime_history[regime_type].append(signal)

                # 检测制度转换
                transition = self._detect_regime_transition(regime_type, signal)
                if transition:
                    self.transition_history.append(transition)

            except Exception as e:
                logger.error(f"分析 {regime_type.value} 制度失败: {e}")

        return regime_signals

    def _analyze_volatility_regime(self, market_data: pd.DataFrame, identifier) -> RegimeSignal:
        """分析波动率制度"""
        try:
            if TIMING_INDICATORS_AVAILABLE and hasattr(identifier, 'generate_volatility_signals'):
                # 使用现有的波动率指标
                start_date = market_data.index[0].strftime('%Y-%m-%d')
                end_date = market_data.index[-1].strftime('%Y-%m-%d')
                vol_signals = identifier.generate_volatility_signals(start_date, end_date)

                if not vol_signals.empty:
                    latest_vol = vol_signals['composite_volatility'].iloc[-1]
                    vol_regime = vol_signals['volatility_regime'].iloc[-1]

                    # 转换为标准制度状态
                    if vol_regime == 'low_vol':
                        current_state = RegimeState.LOW
                    elif vol_regime == 'high_vol':
                        current_state = RegimeState.HIGH
                    else:
                        current_state = RegimeState.NORMAL

                    confidence = 0.8
                    transition_prob = abs(vol_signals['volatility_signal'].iloc[-1]) / 2.0

                    return RegimeSignal(
                        regime_type=RegimeType.VOLATILITY,
                        current_state=current_state,
                        previous_state=RegimeState.NORMAL,
                        confidence=confidence,
                        transition_probability=transition_prob,
                        signal_strength=confidence * (1 - transition_prob * 0.2),
                        timestamp=market_data.index[-1],
                        metadata={
                            'volatility_value': latest_vol,
                            'volatility_regime': vol_regime
                        }
                    )

            # 简化的波动率制度分析
            returns = market_data['close'].pct_change().dropna()
            current_vol = returns.rolling(20).std().iloc[-1] * np.sqrt(252)

            if current_vol < 0.15:
                current_state = RegimeState.LOW
            elif current_vol > 0.35:
                current_state = RegimeState.HIGH
            else:
                current_state = RegimeState.NORMAL

            return RegimeSignal(
                regime_type=RegimeType.VOLATILITY,
                current_state=current_state,
                previous_state=RegimeState.NORMAL,
                confidence=0.7,
                transition_probability=0.2,
                signal_strength=0.7,
                timestamp=market_data.index[-1],
                metadata={'volatility_value': current_vol}
            )

        except Exception as e:
            logger.error(f"波动率制度分析失败: {e}")
            return self._default_regime_signal(RegimeType.VOLATILITY)

    def _analyze_correlation_regime(self, market_data: pd.DataFrame, identifier) -> RegimeSignal:
        """分析相关性制度"""
        try:
            if MARKET_REGIME_AVAILABLE and hasattr(identifier, 'predict'):
                # 检查模型是否已训练，如果没有则先训练
                if not hasattr(identifier, 'gmm') or identifier.gmm is None:
                    logger.info("相关性制度识别器未训练，正在训练模型...")
                    identifier.fit(market_data)
                    logger.info("相关性制度识别器训练完成")

                # 使用现有的相关性识别器
                regimes = identifier.predict(market_data)
                if not regimes.empty:
                    current_regime = regimes.iloc[-1]

                    # 简化的制度状态映射
                    if current_regime == 0:
                        current_state = RegimeState.LOW
                    elif current_regime == 1:
                        current_state = RegimeState.NORMAL
                    else:
                        current_state = RegimeState.HIGH

                    return RegimeSignal(
                        regime_type=RegimeType.CORRELATION,
                        current_state=current_state,
                        previous_state=RegimeState.NORMAL,
                        confidence=0.75,
                        transition_probability=0.15,
                        signal_strength=0.75,
                        timestamp=market_data.index[-1],
                        metadata={'regime_value': current_regime}
                    )

            # 简化的相关性制度分析
            returns = market_data['close'].pct_change().dropna()
            volume_change = market_data['volume'].pct_change().dropna()

            if len(returns) >= 60 and len(volume_change) >= 60:
                correlation = returns.rolling(60).corr(volume_change).iloc[-1]

                if abs(correlation) < 0.3:
                    current_state = RegimeState.LOW
                elif abs(correlation) > 0.7:
                    current_state = RegimeState.HIGH
                else:
                    current_state = RegimeState.NORMAL
            else:
                current_state = RegimeState.NORMAL
                correlation = 0.5

            return RegimeSignal(
                regime_type=RegimeType.CORRELATION,
                current_state=current_state,
                previous_state=RegimeState.NORMAL,
                confidence=0.6,
                transition_probability=0.2,
                signal_strength=0.6,
                timestamp=market_data.index[-1],
                metadata={'correlation_value': correlation}
            )

        except Exception as e:
            logger.error(f"相关性制度分析失败: {e}")
            return self._default_regime_signal(RegimeType.CORRELATION)

    def _default_regime_signal(self, regime_type: RegimeType) -> RegimeSignal:
        """默认制度信号"""
        return RegimeSignal(
            regime_type=regime_type,
            current_state=RegimeState.NORMAL,
            previous_state=RegimeState.NORMAL,
            confidence=0.5,
            transition_probability=0.1,
            signal_strength=0.5,
            timestamp=datetime.now()
        )

    def _detect_regime_transition(self, regime_type: RegimeType, current_signal: RegimeSignal) -> Optional[RegimeTransition]:
        """检测制度转换（优化算法）"""
        try:
            history = self.regime_history[regime_type]
            if len(history) < 2:
                return None

            previous_signal = history[-2]

            # 检查是否发生状态转换
            if current_signal.current_state != previous_signal.current_state:
                # 验证转换的持续性和置信度
                min_confidence = self.config['transition_detection']['min_confidence']

                if current_signal.confidence >= min_confidence:
                    # 计算转换影响评分
                    impact_score = self._calculate_transition_impact(regime_type,
                                                                   previous_signal.current_state,
                                                                   current_signal.current_state)

                    transition = RegimeTransition(
                        regime_type=regime_type,
                        from_state=previous_signal.current_state,
                        to_state=current_signal.current_state,
                        transition_date=current_signal.timestamp,
                        confidence=current_signal.confidence,
                        duration_days=1,  # 简化实现
                        impact_score=impact_score,
                        metadata={
                            'previous_confidence': previous_signal.confidence,
                            'transition_probability': current_signal.transition_probability
                        }
                    )

                    logger.info(f"检测到 {regime_type.value} 制度转换: {previous_signal.current_state.value} -> {current_signal.current_state.value}")
                    return transition

            return None

        except Exception as e:
            logger.error(f"制度转换检测失败: {e}")
            return None

    def _calculate_transition_impact(self, regime_type: RegimeType, from_state: RegimeState, to_state: RegimeState) -> float:
        """计算制度转换影响评分"""
        # 基础影响评分矩阵
        impact_matrix = {
            (RegimeState.LOW, RegimeState.HIGH): 0.9,
            (RegimeState.HIGH, RegimeState.LOW): 0.9,
            (RegimeState.LOW, RegimeState.NORMAL): 0.5,
            (RegimeState.NORMAL, RegimeState.LOW): 0.5,
            (RegimeState.NORMAL, RegimeState.HIGH): 0.7,
            (RegimeState.HIGH, RegimeState.NORMAL): 0.7,
            (RegimeState.NORMAL, RegimeState.EXTREME): 1.0,
            (RegimeState.EXTREME, RegimeState.NORMAL): 1.0
        }

        base_impact = impact_matrix.get((from_state, to_state), 0.3)

        # 根据制度类型调整影响评分
        regime_multiplier = {
            RegimeType.VOLATILITY: 1.2,
            RegimeType.LIQUIDITY: 1.1,
            RegimeType.INFLATION: 1.0,
            RegimeType.CORRELATION: 0.9,
            RegimeType.CREDIT: 1.0
        }

        multiplier = regime_multiplier.get(regime_type, 1.0)
        return min(base_impact * multiplier, 1.0)

    def generate_joint_decision(self, regime_signals: Dict[RegimeType, RegimeSignal]) -> Dict[str, Any]:
        """生成联合决策"""
        try:
            # 计算综合制度评分
            weighted_score = 0.0
            total_weight = 0.0
            regime_states = {}

            for regime_type, signal in regime_signals.items():
                weight = self.regime_weights.get(regime_type, 0.1)

                # 将制度状态转换为数值评分
                state_score = self._state_to_score(signal.current_state)
                weighted_score += weight * state_score * signal.confidence
                total_weight += weight

                regime_states[regime_type.value] = {
                    'state': signal.current_state.value,
                    'confidence': signal.confidence,
                    'signal_strength': signal.signal_strength
                }

            # 标准化综合评分
            if total_weight > 0:
                composite_score = weighted_score / total_weight
            else:
                composite_score = 0.5

            # 生成投资建议
            investment_advice = self._generate_investment_advice(composite_score, regime_signals)

            # 风险评估
            risk_assessment = self._assess_overall_risk(regime_signals)

            return {
                'timestamp': datetime.now(),
                'composite_score': composite_score,
                'regime_states': regime_states,
                'investment_advice': investment_advice,
                'risk_assessment': risk_assessment,
                'confidence': np.mean([signal.confidence for signal in regime_signals.values()])
            }

        except Exception as e:
            logger.error(f"联合决策生成失败: {e}")
            return self._default_decision()

    def _state_to_score(self, state: RegimeState) -> float:
        """制度状态转换为数值评分"""
        score_mapping = {
            RegimeState.LOW: 0.2,
            RegimeState.NORMAL: 0.5,
            RegimeState.HIGH: 0.8,
            RegimeState.EXTREME: 1.0
        }
        return score_mapping.get(state, 0.5)

    def _generate_investment_advice(self, composite_score: float, regime_signals: Dict[RegimeType, RegimeSignal]) -> Dict[str, Any]:
        """生成投资建议"""
        advice = {
            'overall_stance': 'neutral',
            'asset_allocation': {},
            'risk_management': [],
            'specific_actions': []
        }

        # 基于综合评分的总体建议
        if composite_score < 0.3:
            advice['overall_stance'] = 'defensive'
            advice['asset_allocation'] = {
                'equity': 0.3,
                'bonds': 0.5,
                'cash': 0.2
            }
            advice['risk_management'].append('降低风险敞口')
            advice['specific_actions'].append('增加现金和债券配置')

        elif composite_score > 0.7:
            advice['overall_stance'] = 'aggressive'
            advice['asset_allocation'] = {
                'equity': 0.7,
                'bonds': 0.2,
                'cash': 0.1
            }
            advice['risk_management'].append('适度增加风险敞口')
            advice['specific_actions'].append('增加股票配置')

        else:
            advice['overall_stance'] = 'balanced'
            advice['asset_allocation'] = {
                'equity': 0.5,
                'bonds': 0.3,
                'cash': 0.2
            }

        # 基于具体制度的调整建议
        for regime_type, signal in regime_signals.items():
            if regime_type == RegimeType.VOLATILITY and signal.current_state == RegimeState.HIGH:
                advice['specific_actions'].append('考虑波动率对冲策略')
            elif regime_type == RegimeType.LIQUIDITY and signal.current_state == RegimeState.LOW:
                advice['specific_actions'].append('增加流动性资产配置')
            elif regime_type == RegimeType.INFLATION and signal.current_state == RegimeState.HIGH:
                advice['specific_actions'].append('考虑通胀保护资产')

        return advice

    def _assess_overall_risk(self, regime_signals: Dict[RegimeType, RegimeSignal]) -> Dict[str, Any]:
        """评估整体风险"""
        risk_factors = []
        risk_score = 0.0

        for regime_type, signal in regime_signals.items():
            if signal.current_state in [RegimeState.HIGH, RegimeState.EXTREME]:
                risk_factors.append(f"{regime_type.value}制度处于{signal.current_state.value}状态")
                risk_score += 0.2

            if signal.transition_probability > 0.5:
                risk_factors.append(f"{regime_type.value}制度可能发生转换")
                risk_score += 0.1

        # 确定风险等级
        if risk_score < 0.3:
            risk_level = 'low'
        elif risk_score < 0.6:
            risk_level = 'medium'
        else:
            risk_level = 'high'

        return {
            'risk_level': risk_level,
            'risk_score': min(risk_score, 1.0),
            'risk_factors': risk_factors,
            'monitoring_priority': risk_factors[:3]  # 前3个重点监控
        }

    def _default_decision(self) -> Dict[str, Any]:
        """默认决策"""
        return {
            'timestamp': datetime.now(),
            'composite_score': 0.5,
            'regime_states': {},
            'investment_advice': {
                'overall_stance': 'neutral',
                'asset_allocation': {'equity': 0.5, 'bonds': 0.3, 'cash': 0.2},
                'risk_management': [],
                'specific_actions': []
            },
            'risk_assessment': {
                'risk_level': 'medium',
                'risk_score': 0.5,
                'risk_factors': [],
                'monitoring_priority': []
            },
            'confidence': 0.5
        }
