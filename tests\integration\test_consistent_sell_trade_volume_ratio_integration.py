# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性下跌成交量比率因子集成测试
Consistent Sell Trade Volume Ratio Factor Integration Test

测试目标：
1. 验证因子是否正确集成到 factor_mining 模块
2. 测试因子的计算功能
3. 验证数据适配器的使用
4. 检查因子元数据的完整性

作者: AI Assistant
创建时间: 2025-01-27
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = current_dir
sys.path.insert(0, project_root)

def test_factor_integration():
    """
    测试一致性下跌成交量比率因子的集成
    """
    print("=" * 60)
    print("一致性下跌成交量比率因子集成测试")
    print("=" * 60)
    
    try:
        # 1. 测试因子注册表导入
        print("\n1. 测试因子注册表导入...")
        from factor_analyze.factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
        registry = FactorAnalyzeRegistry()
        print("✔ 因子注册表导入成功")
        
        # 2. 检查因子是否已注册
        print("\n2. 检查因子注册状态...")
        available_factors = registry.list_available_factors()
        
        stock_factors = available_factors.get('stock_factors', [])
        index_factors = available_factors.get('index_factors', [])
        
        print(f"已注册股票因子数量: {len(stock_factors)}")
        print(f"已注册指数因子数量: {len(index_factors)}")
        
        # 检查一致性下跌成交量比率因子是否在列表中
        factor_id = 'consistent_sell_trade_volume_ratio'
        if factor_id in stock_factors:
            print(f"✔ 股票版本因子 '{factor_id}' 已成功注册")
        else:
            print(f"❌ 股票版本因子 '{factor_id}' 未找到")
            print(f"可用股票因子: {stock_factors}")
        
        if factor_id in index_factors:
            print(f"✔ 指数版本因子 '{factor_id}' 已成功注册")
        else:
            print(f"❌ 指数版本因子 '{factor_id}' 未找到")
            print(f"可用指数因子: {index_factors}")
        
        # 3. 获取因子类
        print("\n3. 测试因子类获取...")
        stock_factor_class = registry.get_stock_factor(factor_id)
        index_factor_class = registry.get_index_factor(factor_id)
        
        if stock_factor_class:
            print(f"✔ 股票因子类获取成功: {stock_factor_class.__name__}")
        else:
            print("❌ 股票因子类获取失败")
        
        if index_factor_class:
            print(f"✔ 指数因子类获取成功: {index_factor_class.__name__}")
        else:
            print("❌ 指数因子类获取失败")
        
        # 4. 检查因子元数据
        print("\n4. 检查因子元数据...")
        stock_metadata = registry.get_factor_metadata('stock', factor_id)
        index_metadata = registry.get_factor_metadata('index', factor_id)
        
        if stock_metadata:
            print("✔ 股票因子元数据获取成功")
            print(f"  - 名称: {stock_metadata.get('name')}")
            print(f"  - 类别: {stock_metadata.get('category')}")
            print(f"  - 描述: {stock_metadata.get('description')}")
            print(f"  - 数据要求: {stock_metadata.get('data_requirements')}")
            print(f"  - 计算周期: {stock_metadata.get('calculation_period')}")
        else:
            print("❌ 股票因子元数据获取失败")
        
        if index_metadata:
            print("✔ 指数因子元数据获取成功")
            print(f"  - 名称: {index_metadata.get('name')}")
            print(f"  - 类别: {index_metadata.get('category')}")
            print(f"  - 描述: {index_metadata.get('description')}")
        else:
            print("❌ 指数因子元数据获取失败")
        
        # 5. 测试因子实例化
        print("\n5. 测试因子实例化...")
        if stock_factor_class:
            try:
                stock_factor = stock_factor_class()
                print("✔ 股票因子实例化成功")
                
                # 检查因子方法
                if hasattr(stock_factor, 'calculate_factor'):
                    print("✔ 股票因子包含 calculate_factor 方法")
                if hasattr(stock_factor, 'batch_calculate'):
                    print("✔ 股票因子包含 batch_calculate 方法")
                    
            except Exception as e:
                print(f"❌ 股票因子实例化失败: {e}")
        
        if index_factor_class:
            try:
                index_factor = index_factor_class()
                print("✔ 指数因子实例化成功")
                
                # 检查因子方法
                if hasattr(index_factor, 'calculate_factor'):
                    print("✔ 指数因子包含 calculate_factor 方法")
                if hasattr(index_factor, 'batch_calculate'):
                    print("✔ 指数因子包含 batch_calculate 方法")
                    
            except Exception as e:
                print(f"❌ 指数因子实例化失败: {e}")
        
        # 6. 测试因子计算（使用模拟数据）
        print("\n6. 测试因子计算功能...")
        if stock_factor_class:
            try:
                stock_factor = stock_factor_class()
                
                # 使用模拟数据测试
                test_symbol = '000001.SZ'
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
                
                print(f"测试股票: {test_symbol}")
                print(f"测试时间范围: {start_date} 到 {end_date}")
                
                result = stock_factor.calculate_factor(
                    symbol=test_symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if result is not None:
                    print(f"✔ 股票因子计算成功")
                    if isinstance(result, (int, float)):
                        print(f"  因子值: {result:.6f}")
                    elif isinstance(result, pd.Series) and not result.empty:
                        print(f"  因子序列长度: {len(result)}")
                        print(f"  最新因子值: {result.iloc[-1]:.6f}")
                    elif isinstance(result, pd.DataFrame) and not result.empty:
                        print(f"  因子数据框形状: {result.shape}")
                        print(f"  最新因子值: {result.iloc[-1, 0]:.6f}")
                else:
                    print("❌ 股票因子计算返回空值")
                    
            except Exception as e:
                print(f"❌ 股票因子计算失败: {e}")
        
        print("\n=" * 60)
        print("集成测试完成")
        print("=" * 60)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 factor_mining 模块可用")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_direct_factor_import():
    """
    直接测试因子导入
    """
    print("\n" + "=" * 60)
    print("直接因子导入测试")
    print("=" * 60)
    
    try:
        # 直接导入因子类
        from factor_analyze.volume_factors.consistent_sell_trade_volume_ratio_factor import (
            ConsistentSellTradeVolumeRatioFactorStock,
            ConsistentSellTradeVolumeRatioFactorIndex
        )
        
        print("✔ 因子类直接导入成功")
        print(f"  股票因子类: {ConsistentSellTradeVolumeRatioFactorStock.__name__}")
        print(f"  指数因子类: {ConsistentSellTradeVolumeRatioFactorIndex.__name__}")
        
        # 测试实例化
        stock_factor = ConsistentSellTradeVolumeRatioFactorStock()
        index_factor = ConsistentSellTradeVolumeRatioFactorIndex()
        
        print("✔ 因子实例化成功")
        
        # 检查方法
        methods = ['calculate_factor', 'batch_calculate', 'get_default_config']
        for method in methods:
            if hasattr(stock_factor, method):
                print(f"✔ 股票因子包含方法: {method}")
            if hasattr(index_factor, method):
                print(f"✔ 指数因子包含方法: {method}")
        
    except Exception as e:
        print(f"❌ 直接导入测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行集成测试
    test_factor_integration()
    
    # 运行直接导入测试
    test_direct_factor_import()
    
    print("\n测试完成。")
