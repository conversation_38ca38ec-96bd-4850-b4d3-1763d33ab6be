#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Autoencoder Research Module
自动编码器研究模块

基于Chapter20的自动编码器技术，用于金融时间序列数据的特征提取、降维和异常检测研究
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入基类
from research.base_classes.research_base import BaseFactorResearcher, ResearchResult

# 深度学习相关导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, Model
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    DEEP_LEARNING_AVAILABLE = True
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    logging.warning(f"深度学习库不可用: {e}")

class AutoencoderResearcher(BaseFactorResearcher):
    """
    自动编码器研究者
    
    基于Chapter20的自动编码器技术，专门用于金融时间序列数据的研究：
    1. 特征提取和降维
    2. 异常检测
    3. 数据去噪
    4. 潜在表示学习
    """
    
    def __init__(self, name: str = "AutoencoderResearcher"):
        super().__init__(name, factor_categories=['autoencoder', 'unsupervised', 'feature_extraction'])
        self.models = {}
        self.scalers = {}
        self.research_results = {}
        
        if not DEEP_LEARNING_AVAILABLE:
            logging.warning("⚠️ 深度学习库不可用，某些功能将受限")
    
    def build_dense_autoencoder(self, input_dim: int, encoding_dims: List[int] = [64, 32, 16],
                               activation: str = 'relu', output_activation: str = 'linear') -> Optional[Model]:
        """
        构建全连接自动编码器
        
        Args:
            input_dim: 输入维度
            encoding_dims: 编码层维度列表
            activation: 激活函数
            output_activation: 输出层激活函数
            
        Returns:
            自动编码器模型
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 输入层
            input_layer = keras.Input(shape=(input_dim,))
            
            # 编码器
            encoded = input_layer
            for dim in encoding_dims:
                encoded = layers.Dense(dim, activation=activation)(encoded)
            
            # 解码器
            decoded = encoded
            for dim in reversed(encoding_dims[:-1]):
                decoded = layers.Dense(dim, activation=activation)(decoded)
            decoded = layers.Dense(input_dim, activation=output_activation)(decoded)
            
            # 创建模型
            autoencoder = Model(input_layer, decoded, name='dense_autoencoder')
            encoder = Model(input_layer, encoded, name='encoder')
            
            # 编译模型
            autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
            logging.info(f"✅ 构建全连接自动编码器成功，编码维度: {encoding_dims}")
            return autoencoder, encoder
            
        except Exception as e:
            logging.error(f"❌ 构建全连接自动编码器失败: {e}")
            return None
    
    def build_sparse_autoencoder(self, input_dim: int, encoding_dim: int = 32,
                                sparsity_constraint: float = 1e-5) -> Optional[Model]:
        """
        构建稀疏自动编码器
        
        Args:
            input_dim: 输入维度
            encoding_dim: 编码维度
            sparsity_constraint: 稀疏性约束
            
        Returns:
            稀疏自动编码器模型
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 输入层
            input_layer = keras.Input(shape=(input_dim,))
            
            # 编码器（添加稀疏性约束）
            encoded = layers.Dense(encoding_dim, activation='relu',
                                 activity_regularizer=keras.regularizers.l1(sparsity_constraint))(input_layer)
            
            # 解码器
            decoded = layers.Dense(input_dim, activation='linear')(encoded)
            
            # 创建模型
            autoencoder = Model(input_layer, decoded, name='sparse_autoencoder')
            encoder = Model(input_layer, encoded, name='sparse_encoder')
            
            # 编译模型
            autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
            logging.info(f"✅ 构建稀疏自动编码器成功，编码维度: {encoding_dim}")
            return autoencoder, encoder
            
        except Exception as e:
            logging.error(f"❌ 构建稀疏自动编码器失败: {e}")
            return None
    
    def build_convolutional_autoencoder(self, input_shape: Tuple[int, int, int]) -> Optional[Model]:
        """
        构建卷积自动编码器（用于时间序列数据的2D表示）
        
        Args:
            input_shape: 输入形状 (height, width, channels)
            
        Returns:
            卷积自动编码器模型
        """
        if not DEEP_LEARNING_AVAILABLE:
            logging.error("❌ 深度学习库不可用")
            return None
            
        try:
            # 输入层
            input_layer = keras.Input(shape=input_shape)
            
            # 编码器
            x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(input_layer)
            x = layers.MaxPooling2D((2, 2), padding='same')(x)
            x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
            encoded = layers.MaxPooling2D((2, 2), padding='same')(x)
            
            # 解码器
            x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(encoded)
            x = layers.UpSampling2D((2, 2))(x)
            x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(x)
            x = layers.UpSampling2D((2, 2))(x)
            decoded = layers.Conv2D(input_shape[-1], (3, 3), activation='sigmoid', padding='same')(x)
            
            # 创建模型
            autoencoder = Model(input_layer, decoded, name='conv_autoencoder')
            
            # 编译模型
            autoencoder.compile(optimizer='adam', loss='binary_crossentropy', metrics=['mae'])
            
            logging.info(f"✅ 构建卷积自动编码器成功，输入形状: {input_shape}")
            return autoencoder
            
        except Exception as e:
            logging.error(f"❌ 构建卷积自动编码器失败: {e}")
            return None
    
    def preprocess_time_series_data(self, data: pd.DataFrame, 
                                   sequence_length: int = 60,
                                   scaling_method: str = 'standard') -> Tuple[np.ndarray, Any]:
        """
        预处理时间序列数据
        
        Args:
            data: 时间序列数据
            sequence_length: 序列长度
            scaling_method: 缩放方法 ('standard', 'minmax')
            
        Returns:
            处理后的数据和缩放器
        """
        try:
            # 选择缩放器
            if scaling_method == 'standard':
                scaler = StandardScaler()
            elif scaling_method == 'minmax':
                scaler = MinMaxScaler()
            else:
                raise ValueError(f"不支持的缩放方法: {scaling_method}")
            
            # 缩放数据
            scaled_data = scaler.fit_transform(data)
            
            # 创建序列
            sequences = []
            for i in range(len(scaled_data) - sequence_length + 1):
                sequences.append(scaled_data[i:i + sequence_length])
            
            sequences = np.array(sequences)
            
            logging.info(f"✅ 数据预处理完成，序列形状: {sequences.shape}")
            return sequences, scaler
            
        except Exception as e:
            logging.error(f"❌ 数据预处理失败: {e}")
            return None, None
    
    def detect_anomalies(self, model: Model, data: np.ndarray, 
                        threshold_percentile: float = 95) -> Dict[str, Any]:
        """
        使用自动编码器检测异常
        
        Args:
            model: 训练好的自动编码器模型
            data: 测试数据
            threshold_percentile: 异常阈值百分位数
            
        Returns:
            异常检测结果
        """
        if not DEEP_LEARNING_AVAILABLE or model is None:
            logging.error("❌ 模型不可用")
            return {"status": "failed", "error": "Model not available"}
            
        try:
            # 预测重构
            reconstructed = model.predict(data)
            
            # 计算重构误差
            mse = np.mean(np.square(data - reconstructed), axis=1)
            
            # 确定异常阈值
            threshold = np.percentile(mse, threshold_percentile)
            
            # 识别异常
            anomalies = mse > threshold
            anomaly_indices = np.where(anomalies)[0]
            
            results = {
                'reconstruction_errors': mse,
                'threshold': threshold,
                'anomalies': anomalies,
                'anomaly_indices': anomaly_indices.tolist(),
                'anomaly_count': len(anomaly_indices),
                'anomaly_rate': len(anomaly_indices) / len(data),
                'mean_reconstruction_error': np.mean(mse),
                'std_reconstruction_error': np.std(mse)
            }
            
            logging.info(f"✅ 异常检测完成，发现 {len(anomaly_indices)} 个异常点")
            return results
            
        except Exception as e:
            logging.error(f"❌ 异常检测失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def extract_features(self, encoder: Model, data: np.ndarray) -> Optional[np.ndarray]:
        """
        使用编码器提取特征
        
        Args:
            encoder: 编码器模型
            data: 输入数据
            
        Returns:
            提取的特征
        """
        if not DEEP_LEARNING_AVAILABLE or encoder is None:
            logging.error("❌ 编码器不可用")
            return None
            
        try:
            features = encoder.predict(data)
            logging.info(f"✅ 特征提取完成，特征形状: {features.shape}")
            return features
            
        except Exception as e:
            logging.error(f"❌ 特征提取失败: {e}")
            return None
    
    def conduct_research(self, data: Union[pd.DataFrame, Dict[str, Any]], **kwargs) -> ResearchResult:
        """
        进行自动编码器研究
        
        Args:
            data: 研究数据
            **kwargs: 其他参数
            
        Returns:
            研究结果
        """
        research_type = kwargs.get('research_type', 'feature_extraction')
        model_type = kwargs.get('model_type', 'dense')
        
        try:
            if isinstance(data, pd.DataFrame):
                # 预处理数据
                sequences, scaler = self.preprocess_time_series_data(data)
                if sequences is None:
                    raise ValueError("数据预处理失败")
                
                # 分割数据
                X_train, X_test = train_test_split(sequences, test_size=0.2, random_state=42)
                
                # 构建模型
                if model_type == 'dense':
                    autoencoder, encoder = self.build_dense_autoencoder(sequences.shape[-1])
                elif model_type == 'sparse':
                    autoencoder, encoder = self.build_sparse_autoencoder(sequences.shape[-1])
                else:
                    raise ValueError(f"不支持的模型类型: {model_type}")
                
                if autoencoder is None:
                    raise ValueError("模型构建失败")
                
                # 训练模型
                history = autoencoder.fit(
                    X_train, X_train,
                    epochs=50,
                    batch_size=32,
                    validation_data=(X_test, X_test),
                    verbose=0
                )
                
                # 进行研究分析
                if research_type == 'anomaly_detection':
                    anomaly_results = self.detect_anomalies(autoencoder, X_test)
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'anomaly_detection': anomaly_results,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                elif research_type == 'feature_extraction':
                    features = self.extract_features(encoder, X_test)
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'extracted_features_shape': features.shape if features is not None else None,
                        'feature_reduction_ratio': features.shape[1] / sequences.shape[-1] if features is not None else 0,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                else:
                    research_results = {
                        'research_type': research_type,
                        'model_type': model_type,
                        'training_history': {
                            'final_loss': history.history['loss'][-1],
                            'final_val_loss': history.history['val_loss'][-1]
                        }
                    }
                
                # 保存模型和结果
                model_key = f"{model_type}_{research_type}"
                self.models[model_key] = {'autoencoder': autoencoder, 'encoder': encoder}
                self.scalers[model_key] = scaler
                self.research_results[model_key] = research_results
                
            else:
                research_results = {"status": "unsupported_data_type", "data_type": type(data).__name__}
            
            # 创建研究结果
            result = ResearchResult(
                title=f"Autoencoder Research: {research_type}",
                description=f"自动编码器研究 - {research_type} using {model_type} model",
                methodology="Chapter20 Autoencoder techniques for financial time series",
                results=research_results,
                confidence_score=0.85,
                generated_at=datetime.now(),
                metadata={
                    'researcher': self.name,
                    'research_type': research_type,
                    'model_type': model_type,
                    'deep_learning_available': DEEP_LEARNING_AVAILABLE
                }
            )
            
            self.research_history.append(result)
            return result
            
        except Exception as e:
            logging.error(f"❌ 自动编码器研究失败: {e}")
            
            # 创建失败结果
            result = ResearchResult(
                title="Autoencoder Research Failed",
                description=f"自动编码器研究失败: {str(e)}",
                methodology="Chapter20 Autoencoder techniques",
                results={"status": "failed", "error": str(e)},
                confidence_score=0.0,
                generated_at=datetime.now(),
                metadata={'researcher': self.name, 'error': str(e)}
            )
            
            self.research_history.append(result)
            return result
    
    def validate_findings(self, result: ResearchResult) -> bool:
        """
        验证自动编码器研究结果
        
        Args:
            result: 研究结果
            
        Returns:
            验证是否通过
        """
        try:
            if not result.results:
                return False
            
            # 检查状态
            if result.results.get('status') == 'failed':
                return False
            
            # 检查训练历史
            training_history = result.results.get('training_history', {})
            if training_history:
                final_loss = training_history.get('final_loss', float('inf'))
                if final_loss > 1.0:  # 损失过大
                    return False
            
            # 检查置信度
            if result.confidence_score < 0.6:
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 验证研究结果失败: {e}")
            return False
    
    def get_model_summary(self, model_key: str) -> Dict[str, Any]:
        """
        获取模型摘要
        
        Args:
            model_key: 模型键
            
        Returns:
            模型摘要
        """
        if model_key not in self.models:
            return {"status": "model_not_found"}
        
        try:
            model_info = self.models[model_key]
            autoencoder = model_info['autoencoder']
            encoder = model_info['encoder']
            
            summary = {
                'model_key': model_key,
                'autoencoder_params': autoencoder.count_params(),
                'encoder_params': encoder.count_params(),
                'autoencoder_layers': len(autoencoder.layers),
                'encoder_layers': len(encoder.layers),
                'input_shape': autoencoder.input_shape,
                'output_shape': autoencoder.output_shape,
                'encoder_output_shape': encoder.output_shape
            }
            
            return summary
            
        except Exception as e:
            logging.error(f"❌ 获取模型摘要失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    def get_research_summary(self) -> Dict[str, Any]:
        """
        获取自动编码器研究摘要
        
        Returns:
            研究摘要
        """
        base_summary = super().get_research_summary()
        
        # 添加自动编码器特定信息
        autoencoder_summary = {
            'available_models': list(self.models.keys()),
            'model_count': len(self.models),
            'research_results_count': len(self.research_results),
            'deep_learning_available': DEEP_LEARNING_AVAILABLE,
            'supported_research_types': ['feature_extraction', 'anomaly_detection', 'denoising'],
            'supported_model_types': ['dense', 'sparse', 'convolutional']
        }
        
        base_summary.update(autoencoder_summary)
        return base_summary

# 注册函数
def register_autoencoder_researcher() -> AutoencoderResearcher:
    """
    注册自动编码器研究者
    
    Returns:
        自动编码器研究者实例
    """
    researcher = AutoencoderResearcher()
    logging.info(f"✅ 注册自动编码器研究者: {researcher.name}")
    return researcher

# 快速研究函数
def quick_autoencoder_research(data: pd.DataFrame, research_type: str = 'feature_extraction',
                              model_type: str = 'dense') -> Dict[str, Any]:
    """
    快速自动编码器研究
    
    Args:
        data: 研究数据
        research_type: 研究类型
        model_type: 模型类型
        
    Returns:
        研究结果
    """
    researcher = AutoencoderResearcher()
    result = researcher.conduct_research(data, research_type=research_type, model_type=model_type)
    return result.results

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'feature1': np.random.randn(1000),
        'feature2': np.random.randn(1000),
        'feature3': np.random.randn(1000)
    })
    
    # 进行快速研究
    result = quick_autoencoder_research(test_data, 'feature_extraction', 'dense')
    print("自动编码器研究结果:", result)