# -*- coding: utf-8 -*-
"""
质量因子基类
提供质量因子的通用功能和方法，减少代码重复
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
import warnings
from sklearn.preprocessing import StandardScaler
from abc import ABC, abstractmethod

warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    # 尝试导入数据适配器，如果失败则使用模拟模式
    import importlib
    base_adapter = importlib.import_module('adapters.base_data_adapter')
    fund_adapter = importlib.import_module('adapters.fundamental_data_adapter')
    stock_adapter = importlib.import_module('adapters.stock_data_adapter')
    index_adapter = importlib.import_module('adapters.index_data_adapter')
    
    BaseDataAdapter = base_adapter.BaseDataAdapter
    FundamentalDataAdapter = fund_adapter.FundamentalDataAdapter
    StockDataAdapter = stock_adapter.StockDataAdapter
    IndexDataAdapter = index_adapter.IndexDataAdapter
    
    DATA_PIPELINE_AVAILABLE = True
    print("[INFO] 数据适配器模块加载成功")
except Exception as e:
    DATA_PIPELINE_AVAILABLE = False
    print(f"[WARNING] 数据适配器模块加载失败: {e}")
    
    # 创建模拟适配器类
    class BaseDataAdapter:
        pass
    
    # 已删除重复定义: FundamentalDataAdapter
class BaseQualityFactor(ABC):
    """
    质量因子基类
    
    提供质量因子的通用功能：
    1. 配置管理
    2. 数据适配器初始化
    3. 数据处理和标准化
    4. 统计信息计算
    5. 因子信息管理
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化质量因子基类
        
        Args:
            config: 因子配置参数
        """
        self.config = config or self._get_default_config()
        self.factor_name = self._get_factor_name()
        self.factor_description = self._get_factor_description()
        
        # 初始化数据适配器
        self._init_data_adapters()
        
        # 缓存
        self._cache = {}
    
    @abstractmethod
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置（子类必须实现）
        
        Returns:
            默认配置字典
        """
        pass
    
    @abstractmethod
    def _get_factor_name(self) -> str:
        """
        获取因子名称（子类必须实现）
        
        Returns:
            因子名称
        """
        pass
    
    @abstractmethod
    def _get_factor_description(self) -> str:
        """
        获取因子描述（子类必须实现）
        
        Returns:
            因子描述
        """
        pass
    
    def _init_data_adapters(self):
        """
        初始化数据适配器
        """
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.fundamental_adapter = FundamentalDataAdapter()
                self.stock_adapter = StockDataAdapter()
                self.index_adapter = IndexDataAdapter()
                print("[INFO] 数据适配器初始化成功")
            except Exception as e:
                print(f"[WARNING] 数据适配器初始化失败: {e}")
                self._init_mock_adapters()
        else:
            self._init_mock_adapters()
    
    def _init_mock_adapters(self):
        """
        初始化模拟数据适配器
        """
        print("[INFO] 使用模拟数据适配器")
        self.fundamental_adapter = None
        self.stock_adapter = None
        self.index_adapter = None
    
    def get_factor_info(self) -> Dict[str, Any]:
        """
        获取因子基本信息
        
        Returns:
            因子信息字典
        """
        return {
            'factor_name': self.factor_name,
            'factor_description': self.factor_description,
            'factor_type': 'quality',
            'data_frequency': 'quarterly',
            'applicable_targets': ['stock', 'index'],
            'config': self.config
        }
    
    def _standardize_factor(self, data: pd.DataFrame, factor_column: str) -> pd.DataFrame:
        """
        标准化因子值
        
        Args:
            data: 原始数据
            factor_column: 因子列名
            
        Returns:
            标准化后的数据
        """
        if data.empty or factor_column not in data.columns:
            return data
        
        method = self.config.get('normalize_method', 'zscore')
        
        if method == 'zscore':
            # Z-score标准化
            mean_val = data[factor_column].mean()
            std_val = data[factor_column].std()
            
            if std_val > 0:
                data[f'{factor_column}_standardized'] = (data[factor_column] - mean_val) / std_val
                # 限制在合理范围内
                factor_range = self.config.get('factor_range', [-5.0, 5.0])
                data[f'{factor_column}_standardized'] = data[f'{factor_column}_standardized'].clip(
                    factor_range[0], factor_range[1]
                )
                # 替换原值
                data[factor_column] = data[f'{factor_column}_standardized']
                data.drop(f'{factor_column}_standardized', axis=1, inplace=True)
        
        elif method == 'minmax':
            # Min-Max标准化
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            data[factor_column] = scaler.fit_transform(data[[factor_column]]).flatten()
        
        return data
    
    def _winsorize_data(self, data: pd.DataFrame, factor_column: str) -> pd.DataFrame:
        """
        缩尾处理
        
        Args:
            data: 原始数据
            factor_column: 因子列名
            
        Returns:
            缩尾处理后的数据
        """
        if data.empty or factor_column not in data.columns:
            return data
        
        if not self.config.get('use_winsorize', True):
            return data
        
        limits = self.config.get('winsorize_limits', (0.01, 0.99))
        lower_val = data[factor_column].quantile(limits[0])
        upper_val = data[factor_column].quantile(limits[1])
        
        data[factor_column] = data[factor_column].clip(lower_val, upper_val)
        
        print(f"[INFO] 缩尾处理 {factor_column}: [{lower_val:.4f}, {upper_val:.4f}]")
        return data
    
    def _apply_quality_filters(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用数据质量过滤
        
        Args:
            data: 原始数据
            
        Returns:
            过滤后的数据
        """
        original_count = len(data)
        
        # 基本过滤
        if 'total_assets' in data.columns:
            min_assets = self.config.get('min_total_assets', 1e6)
            data = data[data['total_assets'] >= min_assets]
        
        # 异常值过滤
        outlier_threshold = self.config.get('outlier_threshold', 3.0)
        for col in data.select_dtypes(include=[np.number]).columns:
            if col in ['symbol', 'end_date', 'ann_date', 'ts_code']:
                continue
            
            mean_val = data[col].mean()
            std_val = data[col].std()
            
            if std_val > 0:
                data = data[
                    np.abs(data[col] - mean_val) <= outlier_threshold * std_val
                ]
        
        filtered_count = len(data)
        print(f"[INFO] 质量过滤：{original_count} -> {filtered_count} 条记录")
        
        return data
    
    def _calculate_statistics(self, data: pd.DataFrame, factor_column: str) -> Dict[str, Any]:
        """
        计算因子统计信息
        
        Args:
            data: 因子数据
            factor_column: 因子列名
            
        Returns:
            统计信息字典
        """
        if data.empty or factor_column not in data.columns:
            return {}
        
        factor_values = data[factor_column].dropna()
        
        if len(factor_values) == 0:
            return {}
        
        return {
            'count': len(factor_values),
            'mean': float(factor_values.mean()),
            'std': float(factor_values.std()),
            'min': float(factor_values.min()),
            'max': float(factor_values.max()),
            'q25': float(factor_values.quantile(0.25)),
            'q50': float(factor_values.quantile(0.50)),
            'q75': float(factor_values.quantile(0.75)),
            'skewness': float(factor_values.skew()),
            'kurtosis': float(factor_values.kurtosis())
        }
    
    def _safe_divide(self, numerator, denominator, default_value=np.nan):
        """
        安全除法，避免除零错误
        
        Args:
            numerator: 分子
            denominator: 分母
            default_value: 默认值
            
        Returns:
            除法结果
        """
        result = np.where(
            (pd.isna(denominator)) | (denominator == 0),
            default_value,
            numerator / denominator
        )
        return result
    
    def _generate_date_range(self, start_date: str, end_date: str, freq: str = 'Q') -> List[pd.Timestamp]:
        """
        生成日期范围
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            freq: 频率 ('Q' 季度, 'Y' 年度)
            
        Returns:
            日期列表
        """
        try:
            start = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            if freq == 'Q':
                # 季度末日期
                dates = pd.date_range(start=start, end=end, freq='Q')
            elif freq == 'Y':
                # 年末日期
                dates = pd.date_range(start=start, end=end, freq='Y')
            else:
                # 其他频率
                dates = pd.date_range(start=start, end=end, freq=freq)
            
            return dates.tolist()
        except Exception as e:
            print(f"[ERROR] 生成日期范围失败: {e}")
            return []
    
    def _merge_financial_data(self, *dataframes: pd.DataFrame) -> pd.DataFrame:
        """
        合并财务数据
        
        Args:
            *dataframes: 要合并的数据框
            
        Returns:
            合并后的数据框
        """
        if not dataframes:
            return pd.DataFrame()
        
        # 取第一个非空的数据框作为基础
        merged_data = None
        for df in dataframes:
            if not df.empty:
                merged_data = df.copy()
                break
        
        if merged_data is None:
            return pd.DataFrame()
        
        # 依次合并其他数据框
        for df in dataframes:
            if df.empty or df is merged_data:
                continue
            
            try:
                # 找到公共列用于合并
                common_cols = ['ts_code', 'end_date']
                if 'symbol' in df.columns:
                    common_cols = ['symbol', 'end_date']
                
                # 只保留存在的列
                merge_cols = [col for col in common_cols if col in merged_data.columns and col in df.columns]
                
                if merge_cols:
                    merged_data = pd.merge(
                        merged_data, df, 
                        on=merge_cols, 
                        how='outer', 
                        suffixes=('', '_y')
                    )
                    
                    # 删除重复的列
                    cols_to_drop = [col for col in merged_data.columns if col.endswith('_y')]
                    merged_data.drop(cols_to_drop, axis=1, inplace=True)
                    
            except Exception as e:
                print(f"[WARNING] 合并数据时出错: {e}")
                continue
        
        # 按时间排序
        if 'end_date' in merged_data.columns:
            merged_data = merged_data.sort_values('end_date')
        
        return merged_data
    
    def _handle_missing_data(self, data: pd.DataFrame, method: str = 'drop') -> pd.DataFrame:
        """
        处理缺失数据
        
        Args:
            data: 原始数据
            method: 处理方法 ('drop', 'forward_fill', 'backward_fill', 'interpolate')
            
        Returns:
            处理后的数据
        """
        if data.empty:
            return data
        
        if method == 'drop':
            return data.dropna()
        elif method == 'forward_fill':
            return data.fillna(method='ffill')
        elif method == 'backward_fill':
            return data.fillna(method='bfill')
        elif method == 'interpolate':
            return data.interpolate()
        else:
            return data
    
    @abstractmethod
    def calculate_factor(self, *args, **kwargs) -> pd.DataFrame:
        """
        计算因子（子类必须实现）
        
        Returns:
            因子数据
        """
        pass


class BaseStockQualityFactor(BaseQualityFactor):
    """
    股票质量因子基类
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化股票质量因子基类
        
        Args:
            config: 因子配置参数
        """
        super().__init__(config)
    
    def _get_financial_data(self, symbol: str, start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        获取财务数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            财务数据字典
        """
        financial_data = {}
        
        try:
            if self.fundamental_adapter:
                # 获取资产负债表
                balance_sheet = self.fundamental_adapter.get_balance_sheet(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                financial_data['balance_sheet'] = balance_sheet
                
                # 获取利润表
                income_statement = self.fundamental_adapter.get_income_statement(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                financial_data['income_statement'] = income_statement
                
                # 获取现金流量表
                cash_flow = self.fundamental_adapter.get_cash_flow(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                financial_data['cash_flow'] = cash_flow
                
            else:
                # 使用模拟数据
                financial_data = self._generate_mock_financial_data(symbol, start_date, end_date)
                
        except Exception as e:
            print(f"[WARNING] 获取真实财务数据失败: {e}，使用模拟数据")
            financial_data = self._generate_mock_financial_data(symbol, start_date, end_date)
        
        return financial_data
    
    def _generate_mock_financial_data(self, symbol: str, start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        生成模拟财务数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            模拟财务数据字典
        """
        print(f"[INFO] 为 {symbol} 生成模拟财务数据")
        
        # 生成时间序列
        dates = self._generate_date_range(start_date, end_date, 'Q')
        
        if not dates:
            return {'balance_sheet': pd.DataFrame(), 'income_statement': pd.DataFrame(), 'cash_flow': pd.DataFrame()}
        
        np.random.seed(hash(symbol) % 2**32)  # 确保同一股票的模拟数据一致
        
        # 基础数值
        base_revenue = np.random.uniform(1e8, 1e10)
        base_assets = np.random.uniform(5e8, 5e10)
        
        balance_sheet_data = []
        income_statement_data = []
        cash_flow_data = []
        
        for i, date in enumerate(dates):
            # 增长因子
            growth_factor = 1 + np.random.normal(0.05, 0.1)
            
            # 资产负债表数据
            total_assets = base_assets * (growth_factor ** i) * np.random.uniform(0.95, 1.05)
            current_assets = total_assets * np.random.uniform(0.3, 0.6)
            cash = current_assets * np.random.uniform(0.1, 0.3)
            receivables = current_assets * np.random.uniform(0.2, 0.5)
            inventory = current_assets * np.random.uniform(0.1, 0.4)
            
            total_liabilities = total_assets * np.random.uniform(0.3, 0.7)
            current_liabilities = total_liabilities * np.random.uniform(0.4, 0.8)
            short_term_debt = current_liabilities * np.random.uniform(0.1, 0.3)
            
            total_equity = total_assets - total_liabilities
            
            balance_sheet_data.append({
                'ts_code': symbol,
                'symbol': symbol,
                'end_date': date,
                'total_assets': total_assets,
                'total_cur_assets': current_assets,
                'money_cap': cash,
                'accounts_receiv': receivables,
                'inventories': inventory,
                'total_liab': total_liabilities,
                'total_cur_liab': current_liabilities,
                'st_borr': short_term_debt,
                'total_hldr_eqy_exc_min_int': total_equity
            })
            
            # 利润表数据
            revenue = base_revenue * (growth_factor ** i) * np.random.uniform(0.9, 1.1)
            operating_cost = revenue * np.random.uniform(0.6, 0.8)
            gross_profit = revenue - operating_cost
            
            sell_exp = revenue * np.random.uniform(0.02, 0.10)
            admin_exp = revenue * np.random.uniform(0.02, 0.08)
            operating_profit = gross_profit - sell_exp - admin_exp
            
            total_profit = operating_profit * np.random.uniform(0.8, 1.2)
            income_tax = total_profit * np.random.uniform(0.15, 0.25)
            net_income = total_profit - income_tax
            
            income_statement_data.append({
                'ts_code': symbol,
                'symbol': symbol,
                'end_date': date,
                'revenue': revenue,
                'oper_cost': operating_cost,
                'gross_profit': gross_profit,
                'sell_exp': sell_exp,
                'admin_exp': admin_exp,
                'oper_profit': operating_profit,
                'total_profit': total_profit,
                'income_tax': income_tax,
                'n_income': net_income
            })
            
            # 现金流量表数据
            operating_cash_flow = net_income * np.random.uniform(0.8, 1.2)
            investing_cash_flow = -total_assets * np.random.uniform(0.05, 0.15)
            financing_cash_flow = -net_income * np.random.uniform(0.3, 0.8)
            
            cash_flow_data.append({
                'ts_code': symbol,
                'symbol': symbol,
                'end_date': date,
                'n_cashflow_act': operating_cash_flow,
                'n_cashflow_inv_act': investing_cash_flow,
                'n_cashflow_fin_act': financing_cash_flow
            })
        
        return {
            'balance_sheet': pd.DataFrame(balance_sheet_data),
            'income_statement': pd.DataFrame(income_statement_data),
            'cash_flow': pd.DataFrame(cash_flow_data)
        }


class BaseIndexQualityFactor(BaseQualityFactor):
    """
    指数质量因子基类
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化指数质量因子基类
        
        Args:
            config: 因子配置参数
        """
        super().__init__(config)
    
    def _get_index_constituents(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取指数成分股
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            成分股数据
        """
        try:
            if self.index_adapter:
                constituents = self.index_adapter.get_index_constituents(
                    index_code=index_code,
                    start_date=start_date,
                    end_date=end_date
                )
                return constituents
            else:
                # 使用模拟数据
                return self._generate_mock_constituents(index_code, start_date, end_date)
                
        except Exception as e:
            print(f"[WARNING] 获取成分股数据失败: {e}，使用模拟数据")
            return self._generate_mock_constituents(index_code, start_date, end_date)
    
    def _generate_mock_constituents(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        生成模拟成分股数据
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            模拟成分股数据
        """
        print(f"[INFO] 为 {index_code} 生成模拟成分股数据")
        
        # 根据指数代码生成不同的成分股
        if '000300' in index_code or '沪深300' in index_code:
            # 沪深300成分股
            mock_stocks = [f'{str(i).zfill(6)}.SH' if i % 2 == 0 else f'{str(i).zfill(6)}.SZ' 
                          for i in range(1, 301)]
        elif '000905' in index_code or '中证500' in index_code:
            # 中证500成分股
            mock_stocks = [f'{str(i).zfill(6)}.SH' if i % 2 == 0 else f'{str(i).zfill(6)}.SZ' 
                          for i in range(301, 801)]
        else:
            # 默认50只成分股
            mock_stocks = [f'{str(i).zfill(6)}.SH' if i % 2 == 0 else f'{str(i).zfill(6)}.SZ' 
                          for i in range(1, 51)]
        
        # 生成权重
        weights = np.random.dirichlet(np.ones(len(mock_stocks)))
        
        constituents_data = []
        for stock, weight in zip(mock_stocks, weights):
            constituents_data.append({
                'index_code': index_code,
                'con_code': stock,
                'weight': weight,
                'trade_date': end_date
            })
        
        return pd.DataFrame(constituents_data)
    
    def _calculate_weighted_factor(self, stock_factors: pd.DataFrame, 
                                  constituents: pd.DataFrame, 
                                  factor_column: str) -> pd.DataFrame:
        """
        计算加权因子值
        
        Args:
            stock_factors: 成分股因子数据
            constituents: 成分股权重数据
            factor_column: 因子列名
            
        Returns:
            加权因子数据
        """
        # 合并因子数据和权重数据
        merged_data = pd.merge(
            stock_factors, 
            constituents, 
            left_on='symbol', 
            right_on='con_code', 
            how='inner'
        )
        
        if merged_data.empty:
            return pd.DataFrame()
        
        # 按日期分组计算加权平均
        result_data = []
        for date, group in merged_data.groupby('end_date'):
            # 只保留有效数据
            valid_data = group.dropna(subset=[factor_column])
            
            if not valid_data.empty:
                # 计算加权平均
                weighted_factor = np.average(
                    valid_data[factor_column], 
                    weights=valid_data['weight']
                )
                
                result_data.append({
                    'index_code': constituents['index_code'].iloc[0],
                    'end_date': date,
                    f'{factor_column}_weighted': weighted_factor,
                    'constituent_count': len(valid_data)
                })
        
        return pd.DataFrame(result_data) 