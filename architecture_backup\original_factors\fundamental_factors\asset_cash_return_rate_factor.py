# -*- coding: utf-8 -*-
"""
资产现金回报率因子(Asset Cash Return Rate Factor)
基于 https://factors.directory/zh/factors/basic-surface/total-assets-cash-recovery-rate 的实现

因子说明：
资产现金回报率（Asset Cash Return Rate）衡量的是企业利用其总资产创造经营性现金流的能力。
该比率越高，说明企业运用资产创造现金流的效率越高，资产的现金回收周期越短，企业现金获取能力越强。

因子公式：
资产现金回报率= 最近12个月经营活动产生的现金流量净额(TTM) / 平均总资产

其中：
- 最近12个月经营活动产生的现金流量净额(TTM)：指企业在最近12个月内，通过日常经营活动实际获得的现金流入减去现金流出后的净额
- 平均总资产：指企业在某一时间段内平均拥有的资产总额，等于期初总资产和期末总资产之和的二分之一
- 平均总资产= (期初总资产+ 期末总资产) / 2

因子解释：
一个较高的资产现金回报率通常意味着企业拥有较强的资产运营能力、高质量的盈利能力和稳健的现金流。
该指标不仅反映了企业的盈利能力，更重要的是反映了盈利的质量，因为该指标关注的是真实的现金流，
而非会计利润，可以有效避免因会计操作产生的盈利虚增问题。

适用标的：个股、指数
数据频率：季度（财务数据）

作者: AI Assistant
创建时间: 2025-01-27
原始网页链接: https://factors.directory/zh/factors/basic-surface/total-assets-cash-recovery-rate
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 数据管道导入
DATA_PIPELINE_AVAILABLE = False
try:
    from data_pipeline.adapter_manager import AdapterRegistry
    DATA_PIPELINE_AVAILABLE = True
    print("[INFO] 数据管道模块加载成功")
except Exception as e:
    print(f"[WARNING] 数据管道模块未找到: {e}")

# 基础数据适配器导入
ADAPTER_AVAILABLE = False
try:
    from adapters.base_data_adapter import BaseDataAdapter
    ADAPTER_AVAILABLE = True
    print("[INFO] 基础适配器模块加载成功")
except Exception as e:
    print(f"[WARNING] 基础适配器模块未找到: {e}")

# 尝试导入FundamentalDataAdapter（如果存在）
FUNDAMENTAL_ADAPTER_AVAILABLE = False
try:
    # 检查是否存在其他因子文件中定义的FundamentalDataAdapter
    from factor_analyze.rolling_deducted_roe_factor import FundamentalDataAdapter
    FUNDAMENTAL_ADAPTER_AVAILABLE = True
    print("[INFO] FundamentalDataAdapter从其他因子文件导入成功")
except Exception as e:
    print(f"[WARNING] FundamentalDataAdapter未找到: {e}")
    # 创建模拟的FundamentalDataAdapter
    # 已删除重复定义: FundamentalDataAdapter
# 基础因子类导入
BASE_FACTOR_AVAILABLE = False
try:
    from factor_analyze.factor_core.base_interfaces import BaseFactorMiner
    BASE_FACTOR_AVAILABLE = True
except Exception:
    # 创建基础类以避免导入错误
    class BaseFactorMiner:
        def __init__(self, *args, **kwargs):
            pass
        
        def _setup_logger(self):
            """设置日志器"""
            pass
        
        def generate_factor_combinations(self, *args, **kwargs):
            """生成因子组合"""
            return []
        
        def mine_factors(self, *args, **kwargs):
            """挖掘因子"""
            return []


class StockAssetCashReturnRateFactor(BaseFactorMiner):
    """
    个股资产现金回报率因子
    
    核心功能：
    1. 获取个股季度现金流量表数据（经营活动现金流净额）
    2. 获取个股季度资产负债表数据（总资产）
    3. 计算滚动12个月的经营活动现金流净额
    4. 计算平均总资产
    5. 计算资产现金回报率
    6. 输出因子值和相关统计信息
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化个股资产现金回报率因子
        
        Args:
            config: 因子配置参数
        """
        super().__init__()
        self.config = config or self._get_default_config()
        
        # 初始化数据适配器
        if FUNDAMENTAL_ADAPTER_AVAILABLE:
            try:
                self.fundamental_adapter = FundamentalDataAdapter()
                print("[INFO] 基础数据适配器初始化成功")
                self.use_mock_data = False
            except Exception as e:
                print(f"[WARNING] 基础数据适配器初始化失败: {e}")
                self._init_mock_adapters()
        else:
            self._init_mock_adapters()
        
        # 因子参数
        self.lookback_years = self.config.get('lookback_years', 3)        # 回看年数
        self.min_quarters = self.config.get('min_quarters', 8)            # 最少季度数
        self.outlier_threshold = self.config.get('outlier_threshold', 1.0) # 异常值阈值
        self.min_ratio_threshold = self.config.get('min_ratio_threshold', -0.5)  # 比率最小阈值
        self.max_ratio_threshold = self.config.get('max_ratio_threshold', 1.0)   # 比率最大阈值
        
        # 数据缓存
        self.financial_cache = {}
        
        print("[INFO] 个股资产现金回报率因子初始化完成")
    
    def _setup_logger(self):
        """设置日志器"""
        import logging
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def generate_factor_combinations(self, *args, **kwargs):
        """生成因子组合"""
        return [{
            'factor_name': self.config.get('factor_name', '资产现金回报率因子'),
            'factor_code': self.config.get('factor_code', 'asset_cash_return_rate'),
            'parameters': self.config
        }]
    
    def mine_factors(self, symbols: List[str], start_date: str, end_date: str, **kwargs):
        """挖掘因子"""
        return self.calculate_factor(symbols, start_date, end_date)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'factor_name': '资产现金回报率因子',
            'factor_code': 'asset_cash_return_rate',
            'factor_type': 'fundamental',
            'factor_category': 'profitability',
            'description': '计算企业利用总资产创造经营性现金流的能力，反映资产运营效率和盈利质量',
            'formula': '最近12个月经营活动现金流净额(TTM) / 平均总资产',
            'data_source': {
                'balance_sheet': ['quarterly_balance_sheet', 'financial_report'],
                'cash_flow': ['quarterly_cashflow', 'financial_report']
            },
            'output_columns': [
                'asset_cash_return_rate',      # 主因子值：资产现金回报率
                'ttm_operating_cash_flow',     # TTM经营活动现金流净额
                'avg_total_assets',            # 平均总资产
                'current_total_assets',        # 当前总资产
                'previous_total_assets'        # 上期总资产
            ],
            'risk_warnings': [
                '负现金流时需要特别关注企业经营状况',
                '极端值可能反映特殊事件或数据异常',
                '应结合行业特征进行分析'
            ]
        }
    
    def _init_mock_adapters(self):
        """
        初始化模拟数据适配器
        """
        print("[INFO] 使用模拟数据适配器")
        self.use_mock_data = True
    
    def _generate_mock_data(self, symbols: List[str], start_date: str, end_date: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        生成模拟财务数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            (现金流量数据, 资产负债表数据)
        """
        print("[INFO] 生成模拟财务数据")
        
        # 生成季度日期序列
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        quarters = pd.date_range(start=start, end=end, freq='Q')
        
        cashflow_data = []
        balance_data = []
        
        for symbol in symbols:
            # 设置随机种子以保证可重复性
            np.random.seed(hash(symbol) % 2**32)
            
            for quarter_end in quarters:
                # 模拟经营活动现金流净额（单位：万元）
                base_cf = np.random.lognormal(mean=16, sigma=0.8)  # 基础经营现金流
                seasonal_factor = 1 + 0.2 * np.sin(2 * np.pi * quarter_end.month / 12)  # 季节性因子
                trend_factor = 1 + 0.05 * (quarter_end.year - start.year)  # 趋势因子
                operating_cf = base_cf * seasonal_factor * trend_factor
                
                # 模拟总资产（单位：万元）
                base_assets = np.random.lognormal(mean=18, sigma=0.6)  # 基础总资产
                growth_factor = 1 + 0.08 * (quarter_end.year - start.year)  # 增长因子
                total_assets = base_assets * growth_factor
                
                # 现金流量数据
                cashflow_data.append({
                    'symbol': symbol,
                    'end_date': quarter_end,
                    'operating_cash_flow': operating_cf,  # 经营活动产生的现金流净额
                    'report_type': 'quarterly'
                })
                
                # 资产负债表数据
                balance_data.append({
                    'symbol': symbol,
                    'end_date': quarter_end,
                    'total_assets': total_assets,  # 总资产
                    'report_type': 'quarterly'
                })
        
        return pd.DataFrame(cashflow_data), pd.DataFrame(balance_data)
    
    def get_financial_data(self, symbols: List[str], start_date: str, end_date: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        获取财务数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            (现金流量数据, 资产负债表数据)
        """
        cache_key = f"{'-'.join(symbols)}_{start_date}_{end_date}"
        
        if cache_key in self.financial_cache:
            print("[INFO] 使用缓存的财务数据")
            return self.financial_cache[cache_key]
        
        if hasattr(self, 'use_mock_data') and self.use_mock_data:
            cashflow_data, balance_data = self._generate_mock_data(symbols, start_date, end_date)
        else:
            try:
                # 获取现金流量数据
                cashflow_data = self.fundamental_adapter.get_cashflow_data(
                    symbols=symbols,
                    start_date=start_date,
                    end_date=end_date,
                    fields=['operating_cash_flow']  # 经营活动产生的现金流净额
                )
                
                # 获取资产负债表数据
                balance_data = self.fundamental_adapter.get_balance_sheet_data(
                    symbols=symbols,
                    start_date=start_date,
                    end_date=end_date,
                    fields=['total_assets']  # 总资产
                )
                
            except Exception as e:
                print(f"[WARNING] 获取真实数据失败，使用模拟数据: {e}")
                cashflow_data, balance_data = self._generate_mock_data(symbols, start_date, end_date)
        
        # 缓存数据
        self.financial_cache[cache_key] = (cashflow_data, balance_data)
        
        return cashflow_data, balance_data
    
    def _calculate_ttm_operating_cash_flow(self, cashflow_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算TTM经营活动现金流净额
        
        Args:
            cashflow_data: 现金流量数据
            
        Returns:
            包含TTM经营活动现金流净额的数据
        """
        result_data = []
        
        for symbol in cashflow_data['symbol'].unique():
            symbol_data = cashflow_data[cashflow_data['symbol'] == symbol].copy()
            symbol_data = symbol_data.sort_values('end_date')
            
            for i, row in symbol_data.iterrows():
                current_date = row['end_date']
                
                # 获取过去4个季度的数据
                past_4_quarters = symbol_data[
                    (symbol_data['end_date'] <= current_date) &
                    (symbol_data['end_date'] > current_date - pd.DateOffset(months=15))
                ].tail(4)
                
                if len(past_4_quarters) >= 4:
                    # 计算TTM经营活动现金流净额
                    ttm_operating_cf = past_4_quarters['operating_cash_flow'].sum()
                    
                    result_data.append({
                        'symbol': symbol,
                        'end_date': current_date,
                        'ttm_operating_cash_flow': ttm_operating_cf
                    })
        
        return pd.DataFrame(result_data)
    
    def _calculate_avg_total_assets(self, balance_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算平均总资产
        
        Args:
            balance_data: 资产负债表数据
            
        Returns:
            包含平均总资产的数据
        """
        result_data = []
        
        for symbol in balance_data['symbol'].unique():
            symbol_data = balance_data[balance_data['symbol'] == symbol].copy()
            symbol_data = symbol_data.sort_values('end_date')
            
            for i, row in symbol_data.iterrows():
                current_assets = row['total_assets']
                
                # 获取上一期总资产
                if i > 0:
                    previous_assets = symbol_data.iloc[i-1]['total_assets']
                else:
                    # 如果是第一期，使用当期资产作为上期资产
                    previous_assets = current_assets
                
                # 计算平均总资产
                avg_total_assets = (current_assets + previous_assets) / 2
                
                result_data.append({
                    'symbol': symbol,
                    'end_date': row['end_date'],
                    'avg_total_assets': avg_total_assets,
                    'current_total_assets': current_assets,
                    'previous_total_assets': previous_assets
                })
        
        return pd.DataFrame(result_data)
    
    def _calculate_asset_cash_return_rate(self, ttm_data: pd.DataFrame, avg_assets_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算资产现金回报率
        
        Args:
            ttm_data: TTM经营活动现金流数据
            avg_assets_data: 平均总资产数据
            
        Returns:
            包含资产现金回报率的数据
        """
        # 合并数据
        merged_data = pd.merge(
            ttm_data, avg_assets_data,
            on=['symbol', 'end_date'],
            how='inner'
        )
        
        result_data = []
        
        for _, row in merged_data.iterrows():
            ttm_cf = row['ttm_operating_cash_flow']
            avg_assets = row['avg_total_assets']
            
            # 计算资产现金回报率
            if avg_assets > 0:
                asset_cash_return_rate = ttm_cf / avg_assets
            else:
                asset_cash_return_rate = np.nan
            
            # 异常值处理
            if not np.isnan(asset_cash_return_rate):
                if asset_cash_return_rate < self.min_ratio_threshold or asset_cash_return_rate > self.max_ratio_threshold:
                    asset_cash_return_rate = np.nan
            
            result_data.append({
                'symbol': row['symbol'],
                'end_date': row['end_date'],
                'asset_cash_return_rate': asset_cash_return_rate,
                'ttm_operating_cash_flow': ttm_cf,
                'avg_total_assets': avg_assets,
                'current_total_assets': row['current_total_assets'],
                'previous_total_assets': row['previous_total_assets']
            })
        
        return pd.DataFrame(result_data)
    
    def calculate_single_stock_factor(self, symbol: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        计算单只股票的资产现金回报率因子
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子计算结果
        """
        try:
            print(f"[INFO] 开始计算{symbol} 的资产现金回报率因子")
            
            # 扩展时间范围以获取足够的历史数据
            extended_start = (pd.to_datetime(start_date) - pd.DateOffset(years=self.lookback_years)).strftime('%Y-%m-%d')
            
            # 获取财务数据
            cashflow_data, balance_data = self.get_financial_data([symbol], extended_start, end_date)
            
            if cashflow_data.empty or balance_data.empty:
                return {
                    'symbol': symbol,
                    'status': 'failed',
                    'error': '财务数据为空',
                    'factor_name': '资产现金回报率因子'
                }
            
            # 计算TTM经营活动现金流
            ttm_data = self._calculate_ttm_operating_cash_flow(cashflow_data)
            
            # 计算平均总资产
            avg_assets_data = self._calculate_avg_total_assets(balance_data)
            
            # 计算资产现金回报率
            factor_data = self._calculate_asset_cash_return_rate(ttm_data, avg_assets_data)
            
            # 筛选目标时间范围的数据
            factor_data = factor_data[
                (factor_data['end_date'] >= start_date) &
                (factor_data['end_date'] <= end_date)
            ]
            
            if factor_data.empty:
                return {
                    'symbol': symbol,
                    'status': 'failed',
                    'error': '目标时间范围内无有效数据',
                    'factor_name': '资产现金回报率因子'
                }
            
            # 计算统计信息
            valid_values = factor_data['asset_cash_return_rate'].dropna()
            
            if len(valid_values) == 0:
                return {
                    'symbol': symbol,
                    'status': 'failed',
                    'error': '无有效因子值',
                    'factor_name': '资产现金回报率因子'
                }
            
            stats = {
                'mean': valid_values.mean(),
                'std': valid_values.std(),
                'min': valid_values.min(),
                'max': valid_values.max(),
                'count': len(valid_values),
                'latest_value': valid_values.iloc[-1] if len(valid_values) > 0 else np.nan
            }
            
            return {
                'symbol': symbol,
                'status': 'success',
                'factor_data': factor_data.to_dict('records'),
                'statistics': stats,
                'factor_name': '资产现金回报率因子',
                'calculation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'symbol': symbol,
                'status': 'failed',
                'error': str(e),
                'factor_name': '资产现金回报率因子'
            }
    
    def calculate_batch_factor(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        批量计算多只股票的资产现金回报率因子
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            批量计算结果
        """
        print(f"[INFO] 开始批量计算 {len(symbols)} 只股票的资产现金回报率因子")
        
        results = []
        
        for symbol in symbols:
            result = self.calculate_single_stock_factor(symbol, start_date, end_date)
            results.append(result)
        
        # 汇总统计
        successful_count = sum(1 for r in results if r['status'] == 'success')
        failed_count = len(results) - successful_count
        
        return {
            'batch_results': results,
            'summary': {
                'total_symbols': len(symbols),
                'successful_count': successful_count,
                'failed_count': failed_count,
                'success_rate': successful_count / len(symbols) if len(symbols) > 0 else 0
            },
            'factor_name': '资产现金回报率因子'
        }


class IndexAssetCashReturnRateFactor(BaseFactorMiner):
    """
    指数资产现金回报率因子
    
    基于成分股的加权平均计算指数层面的资产现金回报率
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化指数资产现金回报率因子
        
        Args:
            config: 因子配置参数
        """
        super().__init__()
        self.config = config or self._get_default_config()
        
        # 初始化个股因子计算器
        self.stock_factor = StockAssetCashReturnRateFactor(config)
        
        # 指数权重获取（模拟）
        self.use_equal_weight = True  # 是否使用等权重
        
        print("[INFO] 指数资产现金回报率因子初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'factor_name': '指数资产现金回报率因子',
            'factor_code': 'index_asset_cash_return_rate',
            'factor_type': 'fundamental',
            'factor_category': 'profitability',
            'description': '基于成分股加权平均计算指数层面的资产现金回报率',
            'formula': '加权平均(成分股资产现金回报率)',
            'data_source': {
                'constituent_stocks': ['index_constituent', 'market_data'],
                'stock_weights': ['index_weight', 'market_data']
            },
            'output_columns': [
                'index_asset_cash_return_rate',  # 指数资产现金回报率
            ]
        }
    
    def _get_index_constituents(self, index_code: str, date: str) -> List[str]:
        """
        获取指数成分股（模拟）
        
        Args:
            index_code: 指数代码
            date: 日期
            
        Returns:
            成分股列表
        """
        # 模拟成分股数据
        if '000300' in index_code:  # 沪深300
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH']
        elif '000905' in index_code:  # 中证500
            return ['000858.SZ', '002415.SZ', '600009.SH', '600015.SH', '600028.SH']
        else:
            return ['000001.SZ', '000002.SZ', '600000.SH']  # 默认成分股
    
    def calculate_index_factor(self, index_code: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        计算指数资产现金回报率因子
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            指数因子计算结果
        """
        try:
            print(f"[INFO] 开始计算指数 {index_code} 的资产现金回报率因子")
            
            # 获取指数成分股
            constituents = self._get_index_constituents(index_code, end_date)
            
            if not constituents:
                return {
                    'index_code': index_code,
                    'status': 'failed',
                    'error': '无法获取指数成分股',
                    'factor_name': '指数资产现金回报率因子'
                }
            
            # 计算成分股因子
            stock_results = self.stock_factor.calculate_batch_factor(constituents, start_date, end_date)
            
            # 提取成功的股票数据
            valid_stock_data = []
            for result in stock_results['batch_results']:
                if result['status'] == 'success':
                    valid_stock_data.extend(result['factor_data'])
            
            if not valid_stock_data:
                return {
                    'index_code': index_code,
                    'status': 'failed',
                    'error': '成分股因子计算失败',
                    'factor_name': '指数资产现金回报率因子'
                }
            
            # 按日期分组计算指数因子
            stock_df = pd.DataFrame(valid_stock_data)
            index_factor_data = []
            
            for date in stock_df['end_date'].unique():
                date_data = stock_df[stock_df['end_date'] == date]
                
                # 计算等权重平均（简化处理）
                valid_values = date_data['asset_cash_return_rate'].dropna()
                
                if len(valid_values) > 0:
                    index_value = valid_values.mean()
                    
                    index_factor_data.append({
                        'index_code': index_code,
                        'end_date': date,
                        'index_asset_cash_return_rate': index_value,
                        'constituent_count': len(valid_values)
                    })
            
            if not index_factor_data:
                return {
                    'index_code': index_code,
                    'status': 'failed',
                    'error': '无有效指数因子值',
                    'factor_name': '指数资产现金回报率因子'
                }
            
            # 计算统计信息
            index_df = pd.DataFrame(index_factor_data)
            valid_values = index_df['index_asset_cash_return_rate'].dropna()
            
            stats = {
                'mean': valid_values.mean(),
                'std': valid_values.std(),
                'min': valid_values.min(),
                'max': valid_values.max(),
                'count': len(valid_values),
                'latest_value': valid_values.iloc[-1] if len(valid_values) > 0 else np.nan
            }
            
            return {
                'index_code': index_code,
                'status': 'success',
                'factor_data': index_factor_data,
                'statistics': stats,
                'constituent_stocks': constituents,
                'factor_name': '指数资产现金回报率因子',
                'calculation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'index_code': index_code,
                'status': 'failed',
                'error': str(e),
                'factor_name': '指数资产现金回报率因子'
            }


class AssetCashReturnRateFactor:
    """
    资产现金回报率因子统一接口
    
    提供个股和指数的统一调用接口
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化资产现金回报率因子
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.stock_factor = StockAssetCashReturnRateFactor(config)
        self.index_factor = IndexAssetCashReturnRateFactor(config)
    
    def calculate_stock_factor(self, symbols: Union[str, List[str]], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        计算个股因子
        
        Args:
            symbols: 股票代码或股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子计算结果
        """
        if isinstance(symbols, str):
            return self.stock_factor.calculate_single_stock_factor(symbols, start_date, end_date)
        else:
            return self.stock_factor.calculate_batch_factor(symbols, start_date, end_date)
    
    def calculate_index_factor(self, index_codes: Union[str, List[str]], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        计算指数因子
        
        Args:
            index_codes: 指数代码或指数代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子计算结果
        """
        if isinstance(index_codes, str):
            return self.index_factor.calculate_index_factor(index_codes, start_date, end_date)
        else:
            results = []
            for index_code in index_codes:
                result = self.index_factor.calculate_index_factor(index_code, start_date, end_date)
                results.append(result)
            return {'batch_results': results}


def test_asset_cash_return_rate_factor():
    """
    测试资产现金回报率因子
    """
    print("\n=== 资产现金回报率因子测试 ===")
    
    # 初始化因子计算器
    factor_calculator = AssetCashReturnRateFactor()
    
    # 测试个股因子
    print("\n1. 测试个股因子")
    stock_result = factor_calculator.calculate_stock_factor(
        symbols='000001.SZ',
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    print(f"个股因子结果: {stock_result['status']}")
    if stock_result['status'] == 'success':
        print(f"统计信息: {stock_result['statistics']}")
    
    # 测试批量个股因子
    print("\n2. 测试批量个股因子")
    batch_result = factor_calculator.calculate_stock_factor(
        symbols=['000001.SZ', '000002.SZ', '600000.SH'],
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    print(f"批量计算结果: {batch_result['summary']}")
    
    # 测试指数因子
    print("\n3. 测试指数因子")
    index_result = factor_calculator.calculate_index_factor(
        index_codes='000300.SH',
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    print(f"指数因子结果: {index_result['status']}")
    if index_result['status'] == 'success':
        print(f"统计信息: {index_result['statistics']}")
    
    print("\n=== 测试完成 ===")


if __name__ == '__main__':
    test_asset_cash_return_rate_factor()