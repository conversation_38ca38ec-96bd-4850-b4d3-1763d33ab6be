# -*- coding: utf-8 -*-
"""
综合回测验证系统
测试Phase 1-3中实现的所有因子、策略和风控功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import json
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 导入所有新增的因子
from research.factor_research_final.analysis.sentiment_analysis.mda_sentiment_score import MdaSentimentScoreFactor
from research.factor_research_final.analysis.sentiment_analysis.text_similarity_score import TextSimilarityScoreFactor
from factor_analyze.factor_core.momentum_factors import Concept<PERSON>lusterMomentumFactor
from factor_analyze.macro_factors import FedLiquidityIndexFactor
from factor_analyze.valuation_factors import ConvertibleBondValueFactor
from factor_analyze import FactorTimingSignal

# 导入所有新增的策略
from strategy_autodev import (
    AITextAnalysisStrategy,
    OptimalFactorTimingStrategy,
    ConceptClusterRotationStrategy,
    LiquidityDrivenAllocationStrategy
)

# 导入集成系统
from factor_analyze.factor_core.integrated_factor_manager import IntegratedFactorManager
from strategy_autodev.integrated_strategy_manager import IntegratedStrategyManager
from strategy_autodev.backtest.integrated_backtest_engine import IntegratedBacktestEngine

# 导入风控系统
from risk_management import EnhancedUnifiedRiskService


class ComprehensiveBacktestValidator:
    """
    综合回测验证器
    """
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.validation_report = []
        
        # 初始化组件
        self.factor_manager = IntegratedFactorManager()
        self.strategy_manager = IntegratedStrategyManager()
        self.risk_service = EnhancedUnifiedRiskService()
        
        # 测试参数
        self.test_periods = [
            ('2023-01-01', '2023-12-31', '2023年完整年度'),
            ('2024-01-01', '2024-06-30', '2024年上半年'),
            ('2023-07-01', '2024-06-30', '最近12个月')
        ]
        
        self.test_universes = [
            ('hs300', '沪深300成分股'),
            ('zz500', '中证500成分股'),
            ('custom', '自定义股票池')
        ]
        
    def generate_test_data(self, start_date: str, end_date: str, 
                          universe: str = 'hs300') -> pd.DataFrame:
        """生成测试数据"""
        # 模拟股票数据
        if universe == 'hs300':
            stocks = [f'60{str(i).zfill(4)}.SH' for i in range(1, 51)]
        elif universe == 'zz500':
            stocks = [f'00{str(i).zfill(4)}.SZ' for i in range(1, 51)]
        else:
            stocks = [f'60{str(i).zfill(4)}.SH' for i in range(1, 26)] + \
                     [f'00{str(i).zfill(4)}.SZ' for i in range(1, 26)]
                    
        # 生成日期范围
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 生成价格数据
        np.random.seed(42)
        price_data = {}
        
        for stock in stocks:
            # 生成带趋势的价格序列
            trend = np.random.uniform(-0.0001, 0.0002)
            volatility = np.random.uniform(0.015, 0.025)
            
            returns = np.random.normal(trend, volatility, len(dates))
            prices = 100 * np.exp(np.cumsum(returns))
            
            price_data[stock] = prices
            
        return pd.DataFrame(price_data, index=dates)
        
    def test_single_factor(self, factor_name: str, factor_class, 
                          test_data: pd.DataFrame) -> Dict:
        """测试单个因子"""
        print(f"\n测试因子: {factor_name}")
        
        try:
            start_time = time.time()
            
            # 计算因子值
            factor = factor_class()
            factor_values = factor.calculate(test_data)
            
            # 计算IC和IR
            returns = test_data.pct_change().shift(-1)
            
            ic_series = []
            for date in factor_values.index[:-1]:
                if date in returns.index:
                    ic = factor_values.loc[date].corr(returns.loc[date])
                    ic_series.append(ic)
                    
            ic_series = pd.Series(ic_series)
            
            # 统计指标
            ic_mean = ic_series.mean()
            ic_std = ic_series.std()
            ir = ic_mean / ic_std if ic_std > 0 else 0
            ic_positive_rate = (ic_series > 0).mean()
            
            execution_time = time.time() - start_time
            
            result = {
                'factor_name': factor_name,
                'status': 'success',
                'ic_mean': ic_mean,
                'ic_std': ic_std,
                'ir': ir,
                'ic_positive_rate': ic_positive_rate,
                'execution_time': execution_time,
                'coverage': (~factor_values.isna()).mean().mean()
            }
            
            print(f"  IC均值: {ic_mean:.4f}")
            print(f"  IR: {ir:.4f}")
            print(f"  IC胜率: {ic_positive_rate:.2%}")
            
        except Exception as e:
            result = {
                'factor_name': factor_name,
                'status': 'failed',
                'error': str(e)
            }
            print(f"  测试失败: {e}")
            
        return result
        
    def test_single_strategy(self, strategy_name: str, strategy_class,
                           test_data: pd.DataFrame, 
                           start_date: str, end_date: str) -> Dict:
        """测试单个策略"""
        print(f"\n测试策略: {strategy_name}")
        
        try:
            start_time = time.time()
            
            # 创建策略实例
            strategy = strategy_class(
                name=strategy_name,
                start_date=start_date,
                end_date=end_date
            )
            
            # 运行回测
            backtest_engine = IntegratedBacktestEngine(
                strategy=strategy,
                start_date=start_date,
                end_date=end_date,
                initial_capital=1000000,
                risk_service=self.risk_service
            )
            
            results = backtest_engine.run(test_data)
            
            execution_time = time.time() - start_time
            
            # 提取关键指标
            metrics = results.get('performance_metrics', {})
            
            result = {
                'strategy_name': strategy_name,
                'status': 'success',
                'total_return': metrics.get('total_return', 0),
                'annual_return': metrics.get('annual_return', 0),
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'max_drawdown': metrics.get('max_drawdown', 0),
                'win_rate': metrics.get('win_rate', 0),
                'execution_time': execution_time
            }
            
            print(f"  年化收益: {result['annual_return']:.2%}")
            print(f"  夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"  最大回撤: {result['max_drawdown']:.2%}")
            
        except Exception as e:
            result = {
                'strategy_name': strategy_name,
                'status': 'failed',
                'error': str(e)
            }
            print(f"  测试失败: {e}")
            
        return result
        
    def test_integrated_system(self, test_data: pd.DataFrame,
                             start_date: str, end_date: str) -> Dict:
        """测试集成系统"""
        print("\n测试集成系统...")
        
        try:
            start_time = time.time()
            
            # 使用集成因子管理器
            self.factor_manager.calculate_all_factors(test_data)
            
            # 使用集成策略管理器
            strategies = ['AITextAnalysisStrategy', 
                         'OptimalFactorTimingStrategy',
                         'ConceptClusterRotationStrategy']
            
            combined_result = self.strategy_manager.run_combined_strategies(
                strategies=strategies,
                data=test_data,
                start_date=start_date,
                end_date=end_date,
                weights={'AITextAnalysisStrategy': 0.4,
                        'OptimalFactorTimingStrategy': 0.4,
                        'ConceptClusterRotationStrategy': 0.2}
            )
            
            execution_time = time.time() - start_time
            
            result = {
                'test_type': 'integrated_system',
                'status': 'success',
                'combined_return': combined_result.get('combined_return', 0),
                'combined_sharpe': combined_result.get('combined_sharpe', 0),
                'execution_time': execution_time
            }
            
            print(f"  组合收益: {result['combined_return']:.2%}")
            print(f"  组合夏普: {result['combined_sharpe']:.2f}")
            
        except Exception as e:
            result = {
                'test_type': 'integrated_system',
                'status': 'failed',
                'error': str(e)
            }
            print(f"  测试失败: {e}")
            
        return result
        
    def test_risk_management(self, test_positions: Dict[str, float],
                           portfolio_value: float) -> Dict:
        """测试风险管理功能"""
        print("\n测试风险管理系统...")
        
        try:
            # 测试多资产配置优化
            from risk_management import AssetRiskInfo
            
            asset_universe = {
                '股票': AssetRiskInfo(
                    asset_class='equity',
                    expected_return=0.08,
                    volatility=0.20,
                    liquidity_score=0.9,
                    max_allocation=0.4,
                    min_allocation=0.0
                ),
                '债券': AssetRiskInfo(
                    asset_class='bond',
                    expected_return=0.04,
                    volatility=0.05,
                    liquidity_score=0.95,
                    max_allocation=0.8,
                    min_allocation=0.2
                ),
                '商品': AssetRiskInfo(
                    asset_class='commodity',
                    expected_return=0.06,
                    volatility=0.25,
                    liquidity_score=0.6,
                    max_allocation=0.2,
                    min_allocation=0.0
                )
            }
            
            correlation = pd.DataFrame([
                [1.0, -0.1, 0.2],
                [-0.1, 1.0, 0.0],
                [0.2, 0.0, 1.0]
            ], index=['股票', '债券', '商品'], 
               columns=['股票', '债券', '商品'])
            
            # 执行优化
            optimization_result = self.risk_service.optimize_multi_asset_allocation(
                asset_universe=asset_universe,
                correlation_matrix=correlation,
                optimization_method='risk_parity'
            )
            
            result = {
                'test_type': 'risk_management',
                'status': 'success',
                'optimization_method': optimization_result['method'],
                'optimal_weights': optimization_result['weights'],
                'expected_return': optimization_result['metrics']['expected_return'],
                'volatility': optimization_result['metrics']['volatility'],
                'sharpe_ratio': optimization_result['metrics']['sharpe_ratio']
            }
            
            print(f"  优化方法: {result['optimization_method']}")
            print(f"  预期收益: {result['expected_return']:.2%}")
            print(f"  预期波动: {result['volatility']:.2%}")
            
        except Exception as e:
            result = {
                'test_type': 'risk_management',
                'status': 'failed',
                'error': str(e)
            }
            print(f"  测试失败: {e}")
            
        return result
        
    def run_comprehensive_validation(self):
        """运行综合验证"""
        print("\n" + "="*60)
        print("综合回测验证系统")
        print("="*60)
        
        # 定义要测试的因子
        factors_to_test = [
            ('MD&A情绪得分', MdaSentimentScoreFactor),
            ('文本相似度得分', TextSimilarityScoreFactor),
            ('概念聚类动量', ConceptClusterMomentumFactor),
            ('美联储流动性指数', FedLiquidityIndexFactor),
            ('可转债价值', ConvertibleBondValueFactor),
            ('因子择时信号', FactorTimingSignal)
        ]
        
        # 定义要测试的策略
        strategies_to_test = [
            ('AI文本分析策略', AITextAnalysisStrategy),
            ('最优因子择时策略', OptimalFactorTimingStrategy),
            ('概念聚类轮动策略', ConceptClusterRotationStrategy),
            ('流动性驱动配置策略', LiquidityDrivenAllocationStrategy)
        ]
        
        all_results = []
        
        # 测试每个时间段和股票池组
        for (start_date, end_date, period_name) in self.test_periods[:1]:  # 简化测试
            for (universe, universe_name) in self.test_universes[:1]:  # 简化测试
                
                print(f"\n{'='*40}")
                print(f"测试期间: {period_name}")
                print(f"股票池: {universe_name}")
                print(f"{'='*40}")
                
                # 生成测试数据
                test_data = self.generate_test_data(start_date, end_date, universe)
                
                # 测试因子
                print("\n### 因子测试 ###")
                for factor_name, factor_class in factors_to_test:
                    result = self.test_single_factor(factor_name, factor_class, test_data)
                    result['period'] = period_name
                    result['universe'] = universe_name
                    all_results.append(result)
                    
                # 测试策略
                print("\n### 策略测试 ###")
                for strategy_name, strategy_class in strategies_to_test:
                    result = self.test_single_strategy(
                        strategy_name, strategy_class, 
                        test_data, start_date, end_date
                    )
                    result['period'] = period_name
                    result['universe'] = universe_name
                    all_results.append(result)
                    
                # 测试集成系统
                integrated_result = self.test_integrated_system(
                    test_data, start_date, end_date
                )
                integrated_result['period'] = period_name
                integrated_result['universe'] = universe_name
                all_results.append(integrated_result)
                
        # 测试风险管理
        risk_result = self.test_risk_management(
            {'股票': 500000, '债券': 300000, '商品': 200000},
            1000000
        )
        all_results.append(risk_result)
        
        # 保存结果
        self.save_validation_results(all_results)
        
        # 生成验证报告
        self.generate_validation_report(all_results)
        
    def save_validation_results(self, results: List[Dict]):
        """保存验证结果"""
        # 保存为JSON
        with open('comprehensive_validation_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
        # 保存为CSV
        df = pd.DataFrame(results)
        df.to_csv('comprehensive_validation_results.csv', index=False, encoding='utf-8')
        
        print("\n验证结果已保存至:")
        print("  - comprehensive_validation_results.json")
        print("  - comprehensive_validation_results.csv")
        
    def generate_validation_report(self, results: List[Dict]):
        """生成验证报告"""
        print("\n" + "="*60)
        print("验证报告总结")
        print("="*60)
        
        # 统计成功率
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('status') == 'success')
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        print(f"\n总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {success_rate:.1%}")
        
        # 因子测试总结
        factor_results = [r for r in results if 'ic_mean' in r]
        if factor_results:
            print("\n### 因子测试总结 ###")
            for result in factor_results:
                if result['status'] == 'success':
                    print(f"\n{result['factor_name']}:")
                    print(f"  IC均值: {result['ic_mean']:.4f}")
                    print(f"  IR: {result['ir']:.4f}")
                    print(f"  覆盖率: {result['coverage']:.1%}")
                    
        # 策略测试总结
        strategy_results = [r for r in results if 'annual_return' in r]
        if strategy_results:
            print("\n### 策略测试总结 ###")
            for result in strategy_results:
                if result['status'] == 'success':
                    print(f"\n{result['strategy_name']}:")
                    print(f"  年化收益: {result['annual_return']:.2%}")
                    print(f"  夏普比率: {result['sharpe_ratio']:.2f}")
                    print(f"  最大回撤: {result['max_drawdown']:.2%}")
                    
        # 性能总结
        execution_times = [r.get('execution_time', 0) for r in results 
                          if 'execution_time' in r]
        if execution_times:
            print(f"\n### 性能总结 ###")
            print(f"平均执行时间: {np.mean(execution_times):.3f}秒")
            print(f"最大执行时间: {np.max(execution_times):.3f}秒")


def main():
    """主函数"""
    validator = ComprehensiveBacktestValidator()
    validator.run_comprehensive_validation()


if __name__ == "__main__":
    main()
