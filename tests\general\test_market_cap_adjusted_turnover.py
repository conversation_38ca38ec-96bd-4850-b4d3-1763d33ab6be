# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
市值中性化换手率残差因子测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_factor_import():
    """测试因子导入"""
    try:
        from factor_analyze.liquidity_factors.market_cap_adjusted_turnover_factor import (
            StockMarketCapAdjustedTurnoverFactor, 
            IndexMarketCapAdjustedTurnoverFactor,
            MarketCapAdjustedTurnoverConfig
        )
        print("✔ 因子模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 因子模块导入失败: {e}")
        return False

def test_factor_creation():
    """测试因子创建"""
    try:
        from factor_analyze.liquidity_factors.market_cap_adjusted_turnover_factor import (
            StockMarketCapAdjustedTurnoverFactor, 
            IndexMarketCapAdjustedTurnoverFactor
        )
        
        # 测试个股因子创建
        stock_factor = StockMarketCapAdjustedTurnoverFactor()
        print(f"✔ 个股因子创建成功: {stock_factor.factor_name}")
        
        # 测试指数因子创建
        index_factor = IndexMarketCapAdjustedTurnoverFactor()
        print(f"✔ 指数因子创建成功: {index_factor.factor_name}")
        
        return True
    except Exception as e:
        print(f"❌ 因子创建失败: {e}")
        return False

def test_factor_calculation():
    """测试因子计算"""
    try:
        from factor_analyze.liquidity_factors.market_cap_adjusted_turnover_factor import StockMarketCapAdjustedTurnoverFactor
        
        # 创建因子实例
        factor = StockMarketCapAdjustedTurnoverFactor()
        
        # 测试计算（使用模拟数据）
        result = factor.calculate_factor(
            securities=['000001.SZ', '000002.SZ'],
            start_date='2023-01-01',
            end_date='2023-01-31'
        )
        
        print("✔ 因子计算成功")
        print(f"  - 因子名称: {result['factor_name']}")
        print(f"  - 计算日期: {result['calculation_date']}")
        print(f"  - 股票数量: {result['securities_count']}")
        print(f"  - 数据点数: {result['data_points']}")
        
        if result['factor_data'] is not None:
            print("  - 因子数据预览:")
            print(result['factor_data'].head())
        else:
            print(f"  - 错误原因: {result.get('error_reason', '未知错误')}")
        
        return True
    except Exception as e:
        print(f"❌ 因子计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_factor_mining_compatibility():
    """测试与factor_mining模块的兼容性"""
    try:
        from factor_analyze.liquidity_factors.market_cap_adjusted_turnover_factor import StockMarketCapAdjustedTurnoverFactor
        
        # 检查是否继承了BaseFactorMiner
        factor = StockMarketCapAdjustedTurnoverFactor()
        
        # 检查必要的方法是否存在
        required_methods = ['calculate_factor', 'mine_factors', 'generate_factor_combinations']
        for method in required_methods:
            if hasattr(factor, method):
                print(f"✔ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        print("✔ 与factor_mining模块兼容")
        return True
    except Exception as e:
        print(f"❌ factor_mining兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 市值中性化换手率残差因子测试 ===")
    print()
    
    tests = [
        ("因子导入测试", test_factor_import),
        ("因子创建测试", test_factor_creation),
        ("因子计算测试", test_factor_calculation),
        ("factor_mining兼容性测试", test_factor_mining_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("🎉 所有测试通过！因子已准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")

if __name__ == "__main__":
    main()
