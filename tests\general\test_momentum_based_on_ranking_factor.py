# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于排序的动量因子测试文件

测试内容：
1. 个股排序动量因子基础功能
2. 指数排序动量因子基础功能
3. 因子计算逻辑验证
4. 排序算法验证
5. 配置参数测试
6. 边界条件测试
7. 集成测试
"""

import unittest
import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from factor_analyze.fundamental_factors.momentum_based_on_ranking_factor import (
        StockMomentumRankingFactor,
        IndexMomentumRankingFactor
    )
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestStockMomentumRankingFactor(unittest.TestCase):
    """个股排序动量因子测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.factor = StockMomentumRankingFactor()
        self.test_securities = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
        self.start_date = '2024-01-01'
        self.end_date = '2024-01-31'
    
    def test_initialization(self):
        """测试因子初始化"""
        self.assertIsNotNone(self.factor)
        self.assertIsNotNone(self.factor.config)
        self.assertEqual(self.factor.lookback_days, 20)
        self.assertEqual(self.factor.min_trading_days, 100)
        self.assertEqual(self.factor.min_stocks_for_ranking, 10)
        print("个股因子初始化测试通过")
    
    def test_basic_calculation(self):
        """测试基础计算功能"""
        result = self.factor.calculate_factor(
            securities=self.test_securities,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        if not result.empty:
            # 检查必要列
            required_columns = ['security', 'date', 'momentum_ranking_factor']
            for col in required_columns:
                self.assertIn(col, result.columns)
            
            # 检查因子值范围
            factor_values = result['momentum_ranking_factor']
            self.assertTrue(factor_values.min() >= 0)
            self.assertTrue(factor_values.max() <= 1)
            
        print(f"个股因子基础计算测试通过，结果行数: {len(result)}")
    
    def test_single_security_calculation(self):
        """测试单个证券计算"""
        result = self.factor.calculate_factor(
            securities='000001.SZ',
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        print(f"单个证券计算测试通过，结果行数: {len(result)}")
    
    def test_ranking_logic(self):
        """测试排序逻辑"""
        # 使用较多股票测试排序
        many_securities = [f"{600000 + i:06d}.SH" for i in range(15)]
        
        result = self.factor.calculate_factor(
            securities=many_securities,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        if not result.empty and len(result) > 0:
            # 检查每个日期的排序
            for date, date_group in result.groupby('date'):
                if len(date_group) >= self.factor.min_stocks_for_ranking:
                    # 检查排序因子值的分布
                    factor_values = date_group['momentum_ranking_factor'].values
                    self.assertTrue(len(np.unique(factor_values)) > 1 or len(factor_values) == 1)
                    break
        
        print("排序逻辑测试通过")
    
    def test_config_customization(self):
        """测试配置自定义"""
        custom_config = {
            'lookback_days': 10,
            'min_trading_days': 50,
            'min_stocks_for_ranking': 5,
            'ranking_method': 'min'
        }
        
        custom_factor = StockMomentumRankingFactor(config=custom_config)
        
        self.assertEqual(custom_factor.lookback_days, 10)
        self.assertEqual(custom_factor.min_trading_days, 50)
        self.assertEqual(custom_factor.min_stocks_for_ranking, 5)
        self.assertEqual(custom_factor.ranking_method, 'min')
        
        print("配置自定义测试通过")
    
    def test_empty_securities_list(self):
        """测试空证券列表"""
        result = self.factor.calculate_factor(
            securities=[],
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertTrue(result.empty)
        
        print("空证券列表测试通过")


class TestIndexMomentumRankingFactor(unittest.TestCase):
    """指数排序动量因子测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.factor = IndexMomentumRankingFactor()
        self.test_indices = ['000300.SH', '000905.SH']
        self.start_date = '2024-01-01'
        self.end_date = '2024-01-31'
    
    def test_initialization(self):
        """测试因子初始化"""
        self.assertIsNotNone(self.factor)
        self.assertIsNotNone(self.factor.config)
        self.assertIsNotNone(self.factor.stock_factor)
        self.assertEqual(self.factor.weight_method, 'equal')
        self.assertEqual(self.factor.min_constituent_ratio, 0.8)
        
        print("指数因子初始化测试通过")
    
    def test_basic_calculation(self):
        """测试基础计算功能"""
        result = self.factor.calculate_factor(
            index_codes=self.test_indices,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        if not result.empty:
            # 检查必要列
            required_columns = ['index_code', 'date', 'momentum_ranking_factor']
            for col in required_columns:
                self.assertIn(col, result.columns)
            
            # 检查因子值范围
            factor_values = result['momentum_ranking_factor']
            self.assertTrue(factor_values.min() >= 0)
            self.assertTrue(factor_values.max() <= 1)
        
        print(f"指数因子基础计算测试通过，结果行数: {len(result)}")
    
    def test_single_index_calculation(self):
        """测试单个指数计算"""
        result = self.factor.calculate_factor(
            index_codes='000300.SH',
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        print(f"单个指数计算测试通过，结果行数: {len(result)}")
    
    def test_config_customization(self):
        """测试配置自定义"""
        custom_config = {
            'weight_method': 'market_cap',
            'min_constituent_ratio': 0.7,
            'lookback_days': 15
        }
        
        custom_factor = IndexMomentumRankingFactor(config=custom_config)
        
        self.assertEqual(custom_factor.weight_method, 'market_cap')
        self.assertEqual(custom_factor.min_constituent_ratio, 0.7)
        
        print("指数因子配置自定义测试通过")
    
    def test_empty_indices_list(self):
        """测试空指数列表"""
        result = self.factor.calculate_factor(
            index_codes=[],
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertTrue(result.empty)
        
        print("空指数列表测试通过")


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def test_factor_consistency(self):
        """测试因子一致性"""
        stock_factor = StockMomentumRankingFactor()
        index_factor = IndexMomentumRankingFactor()
        
        # 测试相同配置下的一致性
        config = {'lookback_days': 15, 'ranking_method': 'dense'}
        
        stock_factor_custom = StockMomentumRankingFactor(config=config)
        index_factor_custom = IndexMomentumRankingFactor(config=config)
        
        self.assertEqual(stock_factor_custom.lookback_days, index_factor_custom.stock_factor.lookback_days)
        self.assertEqual(stock_factor_custom.ranking_method, index_factor_custom.stock_factor.ranking_method)
        
        print("因子一致性测试通过")
    
    def test_factor_value_distribution(self):
        """测试因子值分布"""
        stock_factor = StockMomentumRankingFactor()
        
        # 使用足够多的股票进行测试
        many_securities = [f"{600000 + i:06d}.SH" for i in range(20)]
        
        result = stock_factor.calculate_factor(
            securities=many_securities,
            start_date='2024-01-01',
            end_date='2024-01-15'
        )
        
        if not result.empty:
            factor_values = result['momentum_ranking_factor']
            
            # 检查值域
            self.assertTrue(factor_values.min() >= 0)
            self.assertTrue(factor_values.max() <= 1)
            
            # 检查分布合理性（如果有足够数据）
            if len(factor_values) > 10:
                std_dev = factor_values.std()
                self.assertTrue(std_dev >= 0)  # 标准差非负
        
        print("因子值分布测试通过")


def run_tests():
    """运行所有测试"""
    print("开始运行基于排序的动量因子测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加个股因子测试
    test_suite.addTest(TestStockMomentumRankingFactor('test_initialization'))
    test_suite.addTest(TestStockMomentumRankingFactor('test_basic_calculation'))
    test_suite.addTest(TestStockMomentumRankingFactor('test_single_security_calculation'))
    test_suite.addTest(TestStockMomentumRankingFactor('test_ranking_logic'))
    test_suite.addTest(TestStockMomentumRankingFactor('test_config_customization'))
    test_suite.addTest(TestStockMomentumRankingFactor('test_empty_securities_list'))
    
    # 添加指数因子测试
    test_suite.addTest(TestIndexMomentumRankingFactor('test_initialization'))
    test_suite.addTest(TestIndexMomentumRankingFactor('test_basic_calculation'))
    test_suite.addTest(TestIndexMomentumRankingFactor('test_single_index_calculation'))
    test_suite.addTest(TestIndexMomentumRankingFactor('test_config_customization'))
    test_suite.addTest(TestIndexMomentumRankingFactor('test_empty_indices_list'))
    
    # 添加集成测试
    test_suite.addTest(TestIntegration('test_factor_consistency'))
    test_suite.addTest(TestIntegration('test_factor_value_distribution'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print(f"测试完成")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    if success:
        print("\n🎉 所有测试通过了")
    else:
        print("\n部分测试失败")
    
    return success


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
