# -*- coding: utf-8 -*-
"""
Outer PDF 集成测试脚本
测试从 outer 目录 PDF 文件中提取的策略和因子的集成效果
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

def test_volume_timing_strategy():
    """测试量价择时策略"""
    print("=== 测试量价择时策略 ===")
    
    try:
        from strategy_autodev.Timing.volume_timing_strategy import create_volume_timing_strategy
        
        # 创建示例数据
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        # 生成模拟的OHLCV数据
        base_price = 100
        returns = np.random.normal(0.001, 0.02, 100)
        prices = base_price * np.cumprod(1 + returns)
        
        # 生成相关的成交量数据
        volumes = np.random.lognormal(10, 0.5, 100)
        volume_multiplier = 1 + abs(returns) * 5
        volumes = volumes * volume_multiplier
        
        data = pd.DataFrame({
            'date': dates,
            'open': prices * (1 + np.random.normal(0, 0.005, 100)),
            'high': prices * (1 + abs(np.random.normal(0, 0.01, 100))),
            'low': prices * (1 - abs(np.random.normal(0, 0.01, 100))),
            'close': prices,
            'volume': volumes
        })
        
        # 创建策略实例
        strategy = create_volume_timing_strategy()
        
        # 回测策略
        results = strategy.backtest_strategy(data)
        
        # 输出结果
        print(f"✓ 量价择时策略测试成功")
        print(f"  总收益率: {results['total_return']:.2%}")
        print(f"  夏普比率: {results['sharpe_ratio']:.2f}")
        print(f"  最大回撤: {results['max_drawdown']:.2%}")
        print(f"  交易次数: {results['trade_count']}")
        
        # 获取当前信号
        current_signal = strategy.get_current_signal(data)
        print(f"  当前信号: {current_signal['description']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 量价择时策略测试失败: {str(e)}")
        return False

def test_ai_innovation_factor():
    """测试AI创新能力因子"""
    print("\n=== 测试AI创新能力因子 ===")
    
    try:
        from factor_analyze.ai_ml_factors.ai_innovation_factor import create_ai_innovation_factor
        
        # 创建示例数据
        np.random.seed(42)
        n_stocks = 50
        
        # 模拟股票数据
        stock_data = pd.DataFrame({
            'stock_id': [f'stock_{i:03d}' for i in range(n_stocks)],
            'company_name': [f'公司{i}' if i % 3 == 0 else f'AI科技{i}' if i % 3 == 1 else f'传统制造{i}' for i in range(n_stocks)],
            'industry': np.random.choice(['软件', '互联网', '电子', '制造', '金融', '医疗'], n_stocks),
            'revenue': np.random.lognormal(10, 1, n_stocks),
            'rd_expense': np.random.lognormal(8, 1.5, n_stocks),
            'total_employees': np.random.randint(100, 10000, n_stocks),
            'tech_employees': np.random.randint(10, 1000, n_stocks),
            'user_count': np.random.lognormal(12, 2, n_stocks),
            'market_share': np.random.uniform(0, 0.3, n_stocks),
            'main_business': [f'主营业务{i}' if i % 4 != 0 else f'人工智能算法开发{i}' for i in range(n_stocks)]
        })
        
        # 模拟收益率数据
        returns = pd.Series(np.random.normal(0.001, 0.02, n_stocks), index=stock_data.index)
        
        # 创建因子实例
        factor = create_ai_innovation_factor()
        
        # 计算因子
        factor_data = factor.calculate_ai_innovation_factor(stock_data)
        
        # 评估因子表现
        performance = factor.evaluate_factor_performance(stock_data, returns)
        
        print(f"✓ AI创新能力因子测试成功")
        print(f"  样本数量: {len(factor_data)}")
        print(f"  因子均值: {factor_data['ai_innovation_factor'].mean():.4f}")
        print(f"  因子标准差: {factor_data['ai_innovation_factor'].std():.4f}")
        print(f"  IC值: {performance['ic']:.4f}")
        print(f"  多空收益: {performance['long_short_return']:.4f}")
        
        # AI相关公司统计
        ai_companies = factor_data[factor_data['ai_relevance'] > 0.5]
        print(f"  AI相关公司数量: {len(ai_companies)}")
        print(f"  AI相关公司占比: {len(ai_companies)/len(factor_data):.2%}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI创新能力因子测试失败: {str(e)}")
        return False

def test_convertible_bond_strategy():
    """测试可转债投资策略"""
    print("\n=== 测试可转债投资策略 ===")
    
    try:
        from strategy_autodev.convertible_bond_investment_strategy import create_convertible_bond_strategy
        
        # 创建示例数据
        np.random.seed(42)
        n_bonds = 20
        n_days = 100
        
        # 模拟转债数据
        bond_data = pd.DataFrame({
            'bond_code': [f'CB{i:03d}' for i in range(n_bonds)],
            'bond_name': [f'转债{i}' for i in range(n_bonds)],
            'stock_code': [f'{i+1:06d}' for i in range(n_bonds)],
            'industry': np.random.choice(['计算机', '医药生物', '食品饮料', '机械设备', '化工'], n_bonds),
            'bond_price': np.random.uniform(80, 150, n_bonds),
            'conversion_price': np.random.uniform(10, 50, n_bonds),
            'stock_price': np.random.uniform(8, 60, n_bonds),
            'remaining_years': np.random.uniform(1, 5, n_bonds),
            'issue_size': np.random.uniform(5, 50, n_bonds),
            'coupon_rate': np.random.uniform(0.01, 0.05, n_bonds),
            'ytm': np.random.uniform(0.02, 0.08, n_bonds)
        })
        bond_data.set_index('bond_code', inplace=True)
        
        # 模拟市场数据
        dates = pd.date_range('2023-01-01', periods=n_days, freq='D')
        market_data = pd.DataFrame({
            'cb_index_price': 100 * np.cumprod(1 + np.random.normal(0.0005, 0.015, n_days)),
            'vix': np.random.uniform(15, 35, n_days),
            'cb_discount_rate': np.random.uniform(-0.05, 0.15, n_days),
            'stock_yield': np.random.uniform(0.03, 0.08, n_days),
            'bond_yield': np.random.uniform(0.025, 0.045, n_days),
            'cb_volume_ratio': np.random.uniform(0.8, 1.5, n_days),
            'new_cb_count': np.random.poisson(2, n_days)
        }, index=dates)
        
        # 模拟正股数据
        stock_data = pd.DataFrame({
            'revenue_growth': np.random.uniform(-0.1, 0.3, n_bonds)
        }, index=[f'{i+1:06d}' for i in range(n_bonds)])
        
        # 模拟价格数据
        price_data = pd.DataFrame(
            np.random.uniform(0.98, 1.02, (n_days, n_bonds)),
            index=dates,
            columns=[f'CB{i:03d}' for i in range(n_bonds)]
        )
        
        # 创建策略实例
        strategy = create_convertible_bond_strategy()
        
        # 回测策略
        backtest_results = strategy.backtest_strategy(bond_data, market_data, stock_data, price_data)
        
        print(f"✓ 可转债投资策略测试成功")
        print(f"  总收益率: {backtest_results['total_return']:.2%}")
        print(f"  年化波动率: {backtest_results['volatility']:.2%}")
        print(f"  夏普比率: {backtest_results['sharpe_ratio']:.2f}")
        print(f"  最大回撤: {backtest_results['max_drawdown']:.2%}")
        
        # 获取当前建议
        recommendation = strategy.get_current_recommendation(bond_data, market_data, stock_data)
        print(f"  当前择时: {recommendation['market_timing']}")
        print(f"  建议仓位: {recommendation['total_position']:.1%}")
        print(f"  推荐转债数量: {recommendation['bond_count']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 可转债投资策略测试失败: {str(e)}")
        return False

def test_integration_summary():
    """测试集成总结"""
    print("\n=== Outer PDF 集成总结 ===")
    
    integration_summary = {
        'analyzed_pdfs': [
            '2025-05-10_国盛证券_2025中期策略：景气全面复苏，聚焦AI+国产化.pdf',
            '2025-05-16_信达证券_24&25Q1消费板块综述：新消费方向崛起.pdf',
            '2025-05-16_华福证券_基于放量与缩量的量价择时策略研究.pdf',
            '2025-05-19_国泰君安期货_股指策略系列七：基于日频和分钟频特征的LSTM模型.pdf',
            '2025-05-26_艾德证券_美股策略周报：无经济衰退，波动创造更舒适的介入机会.pdf',
            '2025-06-02_艾德证券_美股策略周报：信心触底反弹，标普500历史新高可期.pdf',
            '2025-06-05_东吴证券_具身智能数据：AI时代的石油.pdf',
            '2025-06-05_东吴证券_创新药行业专题研究报告：创新突破，出海拓疆.pdf',
            '2025-06-09_国金证券_可转债量化择时、轮动、选券策略年中献礼.pdf'
        ],
        'implemented_strategies': [
            {
                'name': '量价择时策略',
                'source': '华福证券量价择时策略研究',
                'priority': 0.95,
                'module': 'strategy_autodev/Timing/volume_timing_strategy.py',
                'features': ['放量突破', '缩量整理', '量价背离', '动态仓位管理']
            },
            {
                'name': 'AI创新能力因子',
                'source': '国盛证券AI+国产化 + 东吴证券具身智能',
                'priority': 0.92,
                'module': 'factor_analyze\ai_ml_factors\ai_innovation_factor.py',
                'features': ['专利评分', '研发强度', '人才密度', '技术成熟度', '数据资产价值']
            },
            {
                'name': '可转债投资策略',
                'source': '国金证券可转债策略',
                'priority': 0.89,
                'module': 'strategy_autodev/convertible_bond_investment_strategy.py',
                'features': ['择时信号', '行业轮动', '双低选券', '期权价值分析']
            }
        ],
        'planned_strategies': [
            {
                'name': 'LSTM深度学习择时',
                'source': '国泰君安期货LSTM模型',
                'priority': 0.93,
                'status': '待实现'
            },
            {
                'name': '消费板块轮动策略',
                'source': '信达证券消费板块研究',
                'priority': 0.88,
                'status': '待实现'
            },
            {
                'name': '美股情绪择时策略',
                'source': '艾德证券美股策略周报',
                'priority': 0.87,
                'status': '待实现'
            },
            {
                'name': '创新药投资策略',
                'source': '东吴证券创新药研究',
                'priority': 0.86,
                'status': '待实现'
            }
        ]
    }
    
    print(f"分析PDF文件数量: {len(integration_summary['analyzed_pdfs'])}")
    print(f"已实现策略数量: {len(integration_summary['implemented_strategies'])}")
    print(f"计划实现策略数量: {len(integration_summary['planned_strategies'])}")
    
    print(f"\n已实现的策略:")
    for strategy in integration_summary['implemented_strategies']:
        print(f"  ✓ {strategy['name']} (优先级: {strategy['priority']})")
        print(f"    来源: {strategy['source']}")
        print(f"    模块: {strategy['module']}")
        print(f"    功能: {', '.join(strategy['features'])}")
    
    print(f"\n计划实现的策略:")
    for strategy in integration_summary['planned_strategies']:
        print(f"  ○ {strategy['name']} (优先级: {strategy['priority']})")
        print(f"    来源: {strategy['source']}")
        print(f"    状态: {strategy['status']}")
    
    return integration_summary

def main():
    """主函数"""
    print("=" * 80)
    print("Outer PDF 集成测试")
    print("基于 outer 目录中券商研究报告的策略集成验证")
    print("=" * 80)
    
    test_results = []
    
    # 测试各个组件
    test_results.append(("量价择时策略", test_volume_timing_strategy()))
    test_results.append(("AI创新能力因子", test_ai_innovation_factor()))
    test_results.append(("可转债投资策略", test_convertible_bond_strategy()))
    
    # 显示集成总结
    integration_summary = test_integration_summary()
    
    # 总结测试结果
    print(f"\n{'='*80}")
    print("测试结果总结")
    print(f"{'='*80}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！Outer PDF 集成成功完成！")
        print("\n核心成果:")
        print("1. ✓ 量价择时策略 - 基于华福证券研究，实现放量突破和量价背离识别")
        print("2. ✓ AI创新能力因子 - 基于国盛证券和东吴证券研究，多维度评估AI公司")
        print("3. ✓ 可转债投资策略 - 基于国金证券研究，实现择时轮动选券三维框架")
        print("\n下一步计划:")
        print("1. 实现LSTM深度学习择时策略")
        print("2. 开发消费板块轮动策略")
        print("3. 构建美股情绪择时系统")
        print("4. 完善创新药投资策略")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，需要进一步调试")
    
    print(f"\n{'='*80}")

if __name__ == "__main__":
    main()

 