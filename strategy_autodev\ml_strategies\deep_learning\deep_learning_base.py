# -*- coding: utf-8 -*-
"""
深度学习基础策略类
Deep Learning Base Strategy

为所有深度学习策略提供统一的基础框架和接口
基于Chapter 17的深度学习最佳实践
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 导入基础策略类
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from base_ml_strategy import BaseMLStrategy
except ImportError:
    try:
        from ..base_ml_strategy import BaseMLStrategy
    except ImportError:
        from strategy_autodev.ml_strategies.base_ml_strategy import BaseMLStrategy

# 导入数据管道
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from data_pipeline.data_adapter_compatibility import get_data_pipeline
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        warnings.warn("数据适配器不可用，将使用模拟数据")

logger = logging.getLogger(__name__)

class DeepLearningBaseStrategy(BaseMLStrategy):
    """
    深度学习基础策略类
    
    提供深度学习策略的通用功能：
    - 数据预处理和特征工程
    - 模型训练和验证框架
    - 超参数优化接口
    - 风险管理和回测功能
    """
    
    def __init__(self, name: str, **kwargs):
        # 将name和kwargs合并为config传递给父类
        config = kwargs.copy()
        config['name'] = name
        super().__init__(config=config)
        
        # 深度学习特定参数
        self.name = name
        self.model = None
        self.model_type = kwargs.get('model_type', 'feedforward')
        self.input_dim = kwargs.get('input_dim', 10)
        self.output_dim = kwargs.get('output_dim', 1)
        self.hidden_layers = kwargs.get('hidden_layers', [64, 32])
        self.activation = kwargs.get('activation', 'relu')
        self.dropout_rate = kwargs.get('dropout_rate', 0.2)
        self.learning_rate = kwargs.get('learning_rate', 0.001)
        self.batch_size = kwargs.get('batch_size', 32)
        self.epochs = kwargs.get('epochs', 100)
        self.validation_split = kwargs.get('validation_split', 0.2)
        
        # 数据处理参数
        self.lookback_window = kwargs.get('lookback_window', 20)
        self.prediction_horizon = kwargs.get('prediction_horizon', 1)
        self.feature_columns = kwargs.get('feature_columns', [])
        self.target_column = kwargs.get('target_column', 'returns')
        
        # 训练状态
        self.is_trained = False
        self.training_history = {}
        self.feature_scaler = None
        self.target_scaler = None
        
        logger.info(f"深度学习策略 {name} 初始化完成")
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备深度学习模型的特征和标签
        
        Args:
            data: 原始数据
            
        Returns:
            X: 特征矩阵
            y: 标签向量
        """
        try:
            # 计算技术指标
            features_df = self._calculate_technical_indicators(data)
            
            # 创建滞后特征
            features_df = self._create_lagged_features(features_df)
            
            # 计算目标变量
            if self.target_column == 'returns':
                features_df['returns'] = data['close'].pct_change(self.prediction_horizon).shift(-self.prediction_horizon)
            elif self.target_column == 'price_direction':
                features_df['price_direction'] = (data['close'].shift(-self.prediction_horizon) > data['close']).astype(int)
            else:
                features_df[self.target_column] = data[self.target_column]
            
            # 删除包含NaN的行
            features_df = features_df.dropna()
            
            if len(features_df) == 0:
                logger.warning("特征准备后数据为空")
                return np.array([]), np.array([])
            
            # 分离特征和标签
            feature_cols = [col for col in features_df.columns if col != self.target_column]
            X = features_df[feature_cols].values
            y = features_df[self.target_column].values
            
            # 数据标准化
            X = self._scale_features(X)
            y = self._scale_targets(y)
            
            logger.info(f"特征准备完成: X shape={X.shape}, y shape={y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"特征准备失败: {e}")
            return np.array([]), np.array([])
    
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        """
        df = data.copy()
        
        # 价格相关指标
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_momentum'] = df['close'] / df['close'].shift(5) - 1
        
        # 移动平均线
        for window in [5, 10, 20, 50]:
            df[f'ma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ma_ratio_{window}'] = df['close'] / df[f'ma_{window}'] - 1
        
        # 波动率指标
        df['volatility_5'] = df['returns'].rolling(window=5).std()
        df['volatility_20'] = df['returns'].rolling(window=20).std()
        
        # 成交量指标
        if 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        # RSI指标
        df['rsi'] = self._calculate_rsi(df['close'])
        
        # MACD指标
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        计算RSI指标
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """
        计算MACD指标
        """
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def _create_lagged_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        创建滞后特征
        """
        df = data.copy()
        
        # 为主要特征创建滞后版本
        lag_features = ['returns', 'volatility_5', 'rsi', 'macd']
        
        for feature in lag_features:
            if feature in df.columns:
                for lag in range(1, min(6, self.lookback_window + 1)):
                    df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)
        
        return df
    
    def _scale_features(self, X: np.ndarray) -> np.ndarray:
        """
        特征标准化
        """
        try:
            from sklearn.preprocessing import StandardScaler
            
            if self.feature_scaler is None:
                self.feature_scaler = StandardScaler()
                X_scaled = self.feature_scaler.fit_transform(X)
            else:
                X_scaled = self.feature_scaler.transform(X)
            
            return X_scaled
            
        except ImportError:
            logger.warning("sklearn不可用，跳过特征标准化")
            return X
    
    def _scale_targets(self, y: np.ndarray) -> np.ndarray:
        """
        目标变量标准化
        """
        if self.target_column in ['price_direction']:
            # 分类任务不需要标准化
            return y
        
        try:
            from sklearn.preprocessing import StandardScaler
            
            if self.target_scaler is None:
                self.target_scaler = StandardScaler()
                y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
            else:
                y_scaled = self.target_scaler.transform(y.reshape(-1, 1)).flatten()
            
            return y_scaled
            
        except ImportError:
            logger.warning("sklearn不可用，跳过目标变量标准化")
            return y
    
    @abstractmethod
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备特征数据
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    def _build_model(self) -> Any:
        """
        构建深度学习模型
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        模型预测
        子类必须实现此方法
        """
        pass
    
    def fit(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练策略模型
        """
        try:
            logger.info(f"开始训练深度学习策略: {self.name}")
            
            # 准备特征和标签
            X, y = self.prepare_features(data)
            
            if len(X) == 0 or len(y) == 0:
                logger.error("训练数据为空")
                return {'success': False, 'error': '训练数据为空'}
            
            # 构建模型
            self.model = self._build_model()
            
            # 训练模型
            training_result = self._train_model(X, y)
            
            self.is_trained = True
            self.training_history = training_result
            
            logger.info(f"模型训练完成: {self.name}")
            return {'success': True, 'training_result': training_result}
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        """
        try:
            if not self.is_trained or self.model is None:
                logger.warning("模型未训练，无法生成信号")
                return pd.DataFrame()
            
            # 准备特征
            X, _ = self.prepare_features(data)
            
            if len(X) == 0:
                logger.warning("特征数据为空")
                return pd.DataFrame()
            
            # 模型预测
            predictions = self._predict_model(X)
            
            # 生成信号
            signals_df = data.copy()
            
            if self.target_column == 'price_direction':
                # 分类任务：预测价格方向
                signals_df['prediction'] = 0
                signals_df.iloc[-len(predictions):, signals_df.columns.get_loc('prediction')] = predictions
                signals_df['signal'] = np.where(signals_df['prediction'] > 0.5, 1, -1)
            else:
                # 回归任务：预测收益率
                signals_df['prediction'] = 0
                signals_df.iloc[-len(predictions):, signals_df.columns.get_loc('prediction')] = predictions
                
                # 基于预测收益率生成信号
                threshold = np.std(predictions) * 0.5
                signals_df['signal'] = np.where(
                    signals_df['prediction'] > threshold, 1,
                    np.where(signals_df['prediction'] < -threshold, -1, 0)
                )
            
            logger.info(f"生成交易信号完成，信号数量: {len(signals_df)}")
            return signals_df
            
        except Exception as e:
            logger.error(f"生成信号失败: {e}")
            return pd.DataFrame()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        """
        return {
            'name': self.name,
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'input_dim': self.input_dim,
            'output_dim': self.output_dim,
            'hidden_layers': self.hidden_layers,
            'activation': self.activation,
            'dropout_rate': self.dropout_rate,
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'epochs': self.epochs,
            'lookback_window': self.lookback_window,
            'prediction_horizon': self.prediction_horizon,
            'target_column': self.target_column,
            'training_history': self.training_history
        }