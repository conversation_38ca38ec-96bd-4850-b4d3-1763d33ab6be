# -*- coding: utf-8 -*-
"""
导入修复补丁
解决循环导入和配置冲突问题
"""

import sys
import os
from pathlib import Path

def fix_import_paths():
    """修复导入路径"""
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 确保config模块优先级
    config_path = project_root / "config"
    if config_path.exists() and str(config_path) not in sys.path:
        sys.path.insert(0, str(config_path))

def safe_import(module_name, fallback=None):
    """安全导入模块"""
    try:
        return __import__(module_name)
    except ImportError as e:
        print(f"⚠️ 导入警告: {module_name} - {e}")
        return fallback

# 应用修复
fix_import_paths()

# 安全导入常用模块
DATA_DIR = safe_import('config').DATA_DIR if safe_import('config') else Path(__file__).parent / 'data_storage'
BaseDataAdapter = safe_import('adapters.base_data_adapter').BaseDataAdapter if safe_import('adapters.base_data_adapter') else None
