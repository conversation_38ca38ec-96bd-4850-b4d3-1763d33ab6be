#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
æµ‹è¯•å¯¼å…¥åŠŸèƒ½
"""

import sys
import os

# æ·»åŠ é¡¹ç›®æ ¹ç›®å½•åˆ°ç³»ç»Ÿè·¯å¾„
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_imports():
    """æµ‹è¯•å„ç§å¯¼å…¥"""
    try:
        # æµ‹è¯•TushareUtilå¯¼å…¥
        from Utils.TushareUtil import get_tushare_instance
        print("âœ?æˆåŠŸå¯¼å…¥ get_tushare_instance")
        
        # æµ‹è¯•å®ä¾‹åŒ?
        ts_instance = get_tushare_instance()
        if ts_instance:
            print("âœ?æˆåŠŸè·å–Tushareå®ä¾‹")
        else:
            print("âœ?æ— æ³•è·å–Tushareå®ä¾‹")
            
    except ImportError as e:
        print(f"âœ?å¯¼å…¥å¤±è´¥: {e}")
    except Exception as e:
        print(f"âœ?å…¶ä»–é”™è¯¯: {e}")

if __name__ == "__main__":
    test_imports()
