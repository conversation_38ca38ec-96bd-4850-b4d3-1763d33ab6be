# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准化季度营收意外因子演示脚本
展示因子的使用方法和计算结果
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def demo_basic_usage():
    """演示基础使用方法"""
    print("=== 标准化季度营收意外因子演示 ===")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 导入因子库
        from factor_analyze.mock.mock_standardized_unexpected_revenue_factor import (
            MockStandardizedUnexpectedRevenueFactor,
            StockStandardizedUnexpectedRevenueFactor,
            IndustryStandardizedUnexpectedRevenueFactor
        )
        print("✅ 因子模块导入成功")
        
        # 1. 个股因子演示
        print("\n=== 1. 个股因子演示 ===")
        stock_factor = StockStandardizedUnexpectedRevenueFactor()
        
        # 获取因子信息
        factor_info = stock_factor.get_factor_info()
        print(f"因子名称: {factor_info['name']}")
        print(f"因子类型: {factor_info['category']}")
        print(f"计算公式: {factor_info['formula']}")
        print(f"适用标的: {factor_info['target_type']}")
        
        # 计算因子值
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
        stock_result = stock_factor.calculate_factor(
            instruments=test_stocks,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        print(f"\n个股因子计算结果:")
        print(f"数据形状: {stock_result.shape}")
        print(f"数据列: {list(stock_result.columns)}")
        
        if not stock_result.empty:
            print("\n前几行数据:")
            print(stock_result.head())
            
            # 统计信息
            print("\n因子值统计:")
            print(stock_result['standardized_surprise'].describe())
        
        # 2. 行业因子演示
        print("\n=== 2. 行业因子演示 ===")
        industry_factor = IndustryStandardizedUnexpectedRevenueFactor()
        
        industry_result = industry_factor.calculate_factor(
            instruments=test_stocks,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        print(f"行业因子计算结果:")
        print(f"数据形状: {industry_result.shape}")
        
        if not industry_result.empty:
            print("\n前几行数据:")
            print(industry_result.head())
        
        # 3. 因子分析
        print("\n=== 3. 因子分析 ===")
        if not stock_result.empty:
            # 因子分布
            factor_values = stock_result['standardized_surprise']
            print(f"因子值范围: [{factor_values.min():.3f}, {factor_values.max():.3f}]")
            print(f"因子值均值: {factor_values.mean():.3f}")
            print(f"因子值标准差: {factor_values.std():.3f}")
            
            # 正负值分布
            positive_count = (factor_values > 0).sum()
            negative_count = (factor_values < 0).sum()
            zero_count = (factor_values == 0).sum()
            
            print(f"\n因子值分布:")
            print(f"正值数量: {positive_count} ({positive_count/len(factor_values)*100:.1f}%)")
            print(f"负值数量: {negative_count} ({negative_count/len(factor_values)*100:.1f}%)")
            print(f"零值数量: {zero_count} ({zero_count/len(factor_values)*100:.1f}%)")
            
            # 按股票分组统计
            print("\n按股票分组统计:")
            stock_stats = stock_result.groupby('security')['standardized_surprise'].agg([
                'count', 'mean', 'std', 'min', 'max'
            ]).round(3)
            print(stock_stats)
        
        # 4. 配置演示
        print("\n=== 4. 自定义配置演示 ===")
        custom_config = {
            'lookback_quarters': 12,  # 更长的回看期
            'min_quarters': 6,        # 更高的最小要求
            'outlier_threshold': 2.5  # 更严格的异常值过滤
        }
        
        custom_factor = StockStandardizedUnexpectedRevenueFactor(custom_config)
        print(f"自定义配置: {custom_factor.config}")
        
        custom_result = custom_factor.calculate_factor(
            instruments=['000001.SZ', '000002.SZ'],
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        print(f"自定义配置结果形状: {custom_result.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_factor_interpretation():
    """演示因子解释和应用"""
    print("\n=== 因子解释和应用指南 ===")
    
    print("\n📊 因子含义:")
    print("✅ 标准化季度营收意外因子衡量公司实际营收相对于市场预期的标准化偏差")
    print("✅ 正值: 实际营收超出预期，可能引发正面市场反应")
    print("✅ 负值: 实际营收低于预期，可能引发负面市场反应")
    print("✅ 绝对值越大: 意外程度越高，市场反应可能越强烈")
    
    print("\n🎯 适用场景:")
    print("✅ 个股选择: 寻找营收超预期的公司")
    print("✅ 行业轮动: 识别营收表现突出的行业")
    print("✅ 事件驱动: 财报发布前后的投资机会")
    print("✅ 风险管理: 识别营收不及预期的风险")
    
    print("\n⚙️ 技术特点:")
    print("✅ 使用模拟数据（因为需要分析师预期数据）")
    print("✅ 支持个股和行业两种类型")
    print("✅ 可配置回看期和质量过滤参数")
    print("✅ 集成到factor_mining模块中")
    
    print("\n📈 使用建议:")
    print("✅ 结合其他基本面因子使用")
    print("✅ 关注因子的持续性和稳定性")
    print("✅ 考虑行业和市场环境的影响")
    print("✅ 定期更新和验证因子有效性")

def main():
    """主函数"""
    print("🚀 开始标准化季度营收意外因子演示")
    
    # 基础使用演示
    success = demo_basic_usage()
    
    if success:
        # 因子解释
        demo_factor_interpretation()
        
        print("\n✅ 演示完成!")
        print("\n📝 总结:")
        print("1. 标准化季度营收意外因子已成功实现")
        print("2. 支持个股和行业两种应用场景")
        print("3. 使用模拟数据确保功能完整性")
        print("4. 可通过factor_mining模块调用")
        print("5. 因子计算逻辑符合原始定义")
        
    else:
        print("\n❌ 演示过程中出现问题。")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
