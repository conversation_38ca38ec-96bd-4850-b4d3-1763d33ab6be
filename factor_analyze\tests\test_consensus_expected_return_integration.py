#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ä¸€è‡´é¢„æœŸæ”¶ç›Šç‡å› å­é›†æˆæµ‹è¯•

æµ‹è¯•å› å­ä¸factor_miningæ¨¡å—çš„é›†æˆæƒ…å†?

ä½œè€? AI Assistant
åˆ›å»ºæ—¶é—´: 2025-01-27
"""

import os
import sys
import pandas as pd
from datetime import datetime

# æ·»åŠ é¡¹ç›®è·¯å¾„
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def test_factor_import():
    """
    æµ‹è¯•å› å­å¯¼å…¥
    """
    print("=== æµ‹è¯•å› å­å¯¼å…¥ ===")
    
    try:
        from factor_analyze.consensus_expected_return_factor import (
            ConsensusExpectedReturnFactor,
            StockConsensusExpectedReturnFactor,
            IndexConsensusExpectedReturnFactor
        )
        print("âœ?å› å­ç±»å¯¼å…¥æˆåŠ?)
        return True
    except Exception as e:
        print(f"âœ?å› å­ç±»å¯¼å…¥å¤±è´? {e}")
        return False

def test_factor_mining_integration():
    """
    æµ‹è¯•ä¸factor_miningæ¨¡å—çš„é›†æˆ?
    """
    print("\n=== æµ‹è¯•factor_miningé›†æˆ ===")
    
    try:
        # å°è¯•å¯¼å…¥factor_miningç›¸å…³æ¨¡å—
        try:
            from factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
            print("âœ?FactorAnalyzeRegistryå¯¼å…¥æˆåŠŸ")
            
            # åˆ›å»ºæ³¨å†Œè¡¨å®ä¾?
            registry = FactorAnalyzeRegistry()
            print("âœ?æ³¨å†Œè¡¨å®ä¾‹åˆ›å»ºæˆåŠ?)
            
            # æ£€æŸ¥å¯ç”¨å› å­?
            available_factors = registry.get_available_factors()
            print(f"âœ?å¯ç”¨å› å­æ•°é‡: {len(available_factors)}")
            
            return True
            
        except ImportError as e:
            print(f"âš?factor_miningæ¨¡å—æœªæ‰¾åˆ? {e}")
            print("  è¿™å¯èƒ½æ˜¯æ­£å¸¸çš„ï¼Œå¦‚æœfactor_miningæ¨¡å—å°šæœªå®Œå…¨é…ç½®")
            return True
            
    except Exception as e:
        print(f"âœ?factor_miningé›†æˆæµ‹è¯•å¤±è´¥: {e}")
        return False

def test_factor_functionality():
    """
    æµ‹è¯•å› å­åŸºç¡€åŠŸèƒ½
    """
    print("\n=== æµ‹è¯•å› å­åŸºç¡€åŠŸèƒ½ ===")
    
    try:
        from factor_analyze.consensus_expected_return_factor import (
            StockConsensusExpectedReturnFactor,
            IndexConsensusExpectedReturnFactor
        )
        
        # æµ‹è¯•ä¸ªè‚¡å› å­
        print("\n1. ä¸ªè‚¡å› å­æµ‹è¯•")
        stock_factor = StockConsensusExpectedReturnFactor()
        
        # è·å–å› å­ä¿¡æ¯
        stock_info = stock_factor.get_factor_info()
        print(f"  å› å­åç§°: {stock_info['factor_name']}")
        print(f"  å› å­æè¿°: {stock_info['factor_description']}")
        print(f"  é€‚ç”¨æ ‡çš„: {stock_info['applicable_targets']}")
        
        # æµ‹è¯•å› å­è®¡ç®—
        test_result = stock_factor.calculate_factor(
            securities=['000001.SZ'],
            start_date='2024-01-01',
            end_date='2024-02-29'
        )
        
        if isinstance(test_result, pd.DataFrame):
            print(f"  âœ?å› å­è®¡ç®—æˆåŠŸ: {len(test_result)} æ¡è®°å½?)
            if not test_result.empty:
                print(f"    - è¾“å‡ºå­—æ®µ: {list(test_result.columns)}")
                print(f"    - æ•°æ®æ ·ä¾‹:")
                print(test_result.head(2).to_string(index=False))
        else:
            print("  âš?å› å­è®¡ç®—è¿”å›éDataFrameç±»å‹")
        
        # æµ‹è¯•æŒ‡æ•°å› å­
        print("\n2. æŒ‡æ•°å› å­æµ‹è¯•")
        index_factor = IndexConsensusExpectedReturnFactor()
        
        index_info = index_factor.get_factor_info()
        print(f"  å› å­åç§°: {index_info['factor_name']}")
        print(f"  é€‚ç”¨æ ‡çš„: {index_info['applicable_targets']}")
        
        index_result = index_factor.calculate_factor(
            securities=['000300.SH'],
            start_date='2024-01-01',
            end_date='2024-02-29'
        )
        
        if isinstance(index_result, pd.DataFrame):
            print(f"  âœ?æŒ‡æ•°å› å­è®¡ç®—æˆåŠŸ: {len(index_result)} æ¡è®°å½?)
        
        return True
        
    except Exception as e:
        print(f"âœ?å› å­åŠŸèƒ½æµ‹è¯•å¤±è´¥: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_factor_configuration():
    """
    æµ‹è¯•å› å­é…ç½®
    """
    print("\n=== æµ‹è¯•å› å­é…ç½® ===")
    
    try:
        from factor_analyze.consensus_expected_return_factor import ConsensusExpectedReturnFactor
        
        # æµ‹è¯•é»˜è®¤é…ç½®
        factor = ConsensusExpectedReturnFactor()
        default_config = factor.config
        
        required_config_keys = [
            'factor_name', 'factor_description', 'calculation_frequency',
            'time_decay_days', 'min_analyst_count', 'outlier_threshold'
        ]
        
        for key in required_config_keys:
            if key in default_config:
                print(f"  âœ?{key}: {default_config[key]}")
            else:
                print(f"  âœ?ç¼ºå°‘é…ç½®é¡? {key}")
                return False
        
        # æµ‹è¯•è‡ªå®šä¹‰é…ç½?
        custom_config = {
            'factor_name': 'custom_consensus_return',
            'min_analyst_count': 5,
            'time_decay_days': 60
        }
        
        custom_factor = ConsensusExpectedReturnFactor(custom_config)
        print(f"  âœ?è‡ªå®šä¹‰é…ç½®æµ‹è¯•æˆåŠ?)
        print(f"    - è‡ªå®šä¹‰å› å­åç§? {custom_factor.config['factor_name']}")
        print(f"    - è‡ªå®šä¹‰åˆ†æå¸ˆæ•°é‡: {custom_factor.config['min_analyst_count']}")
        
        return True
        
    except Exception as e:
        print(f"âœ?å› å­é…ç½®æµ‹è¯•å¤±è´¥: {e}")
        return False

def test_data_pipeline_integration():
    """
    æµ‹è¯•æ•°æ®ç®¡é“é›†æˆ
    """
    print("\n=== æµ‹è¯•æ•°æ®ç®¡é“é›†æˆ ===")
    
    try:
        from factor_analyze.consensus_expected_return_factor import ConsensusExpectedReturnFactor
        
        factor = ConsensusExpectedReturnFactor()
        
        # æ£€æŸ¥æ•°æ®é€‚é…å™¨çŠ¶æ€?
        if hasattr(factor, 'analyst_adapter'):
            if factor.analyst_adapter is not None:
                print("  âœ?åˆ†æå¸ˆæ•°æ®é€‚é…å™¨å·²è¿æ¥")
            else:
                print("  âš?åˆ†æå¸ˆæ•°æ®é€‚é…å™¨æœªè¿æ¥ï¼ˆå°†ä½¿ç”¨æ¨¡æ‹Ÿæ•°æ®ï¼?)
        
        if hasattr(factor, 'price_adapter'):
            if factor.price_adapter is not None:
                print("  âœ?ä»·æ ¼æ•°æ®é€‚é…å™¨å·²è¿æ¥")
            else:
                print("  âš?ä»·æ ¼æ•°æ®é€‚é…å™¨æœªè¿æ¥ï¼ˆå°†ä½¿ç”¨æ¨¡æ‹Ÿæ•°æ®ï¼?)
        
        # æµ‹è¯•æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆ
        mock_target_data = factor._generate_mock_target_price_data(
            '000001.SZ', '2024-01-01', '2024-01-31'
        )
        
        mock_price_data = factor._generate_mock_price_data(
            '000001.SZ', '2024-01-01', '2024-01-31'
        )
        
        if not mock_target_data.empty and not mock_price_data.empty:
            print(f"  âœ?æ¨¡æ‹Ÿæ•°æ®ç”ŸæˆæˆåŠŸ")
            print(f"    - ç›®æ ‡ä»·æ ¼æ•°æ®: {len(mock_target_data)} æ?)
            print(f"    - è‚¡ä»·æ•°æ®: {len(mock_price_data)} æ?)
        
        return True
        
    except Exception as e:
        print(f"âœ?æ•°æ®ç®¡é“é›†æˆæµ‹è¯•å¤±è´¥: {e}")
        return False

def main():
    """
    ä¸»æµ‹è¯•å‡½æ•?
    """
    print("ä¸€è‡´é¢„æœŸæ”¶ç›Šç‡å› å­é›†æˆæµ‹è¯•")
    print("=" * 60)
    print(f"æµ‹è¯•æ—¶é—´: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = []
    
    # è¿è¡Œå„é¡¹æµ‹è¯•
    test_functions = [
        ("å› å­å¯¼å…¥æµ‹è¯•", test_factor_import),
        ("factor_miningé›†æˆæµ‹è¯•", test_factor_mining_integration),
        ("å› å­åŠŸèƒ½æµ‹è¯•", test_factor_functionality),
        ("å› å­é…ç½®æµ‹è¯•", test_factor_configuration),
        ("æ•°æ®ç®¡é“é›†æˆæµ‹è¯•", test_data_pipeline_integration)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"âœ?{test_name}æ‰§è¡Œå¼‚å¸¸: {e}")
            test_results.append((test_name, False))
    
    # æµ‹è¯•ç»“æœæ±‡æ€?
    print("\n" + "=" * 60)
    print("æµ‹è¯•ç»“æœæ±‡æ€?)
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "âœ?é€šè¿‡" if result else "âœ?å¤±è´¥"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\næ€»ä½“æµ‹è¯•ç»“æœ: {passed_tests}/{total_tests} é€šè¿‡ ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\nğŸ‰ ä¸€è‡´é¢„æœŸæ”¶ç›Šç‡å› å­é›†æˆæµ‹è¯•åŸºæœ¬é€šè¿‡ï¼?)
        print("å› å­å·²å‡†å¤‡å¥½ä¸factor_miningæ¨¡å—é›†æˆä½¿ç”¨ã€?)
    else:
        print("\nâš ï¸ éƒ¨åˆ†æµ‹è¯•æœªé€šè¿‡ï¼Œè¯·æ£€æŸ¥ç›¸å…³é…ç½®ã€?)
    
    print("\n" + "=" * 60)
    print("é›†æˆæµ‹è¯•å®Œæˆ")
    print("=" * 60)

if __name__ == '__main__':
    main()
