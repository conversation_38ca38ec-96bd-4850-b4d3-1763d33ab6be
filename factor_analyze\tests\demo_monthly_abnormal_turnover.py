# -*- coding: utf-8 -*-
"""
æœˆåº¦ç›¸å¯¹æ¢æ‰‹ç‡æº¢å‡ºå› å­æ¼”ç¤ºæ–‡ä»?

æ¼”ç¤ºå¦‚ä½•ä½¿ç”¨æœˆåº¦ç›¸å¯¹æ¢æ‰‹ç‡æº¢å‡ºå› å­è¿›è¡Œè®¡ç®—å’Œåˆ†æ
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# æ·»åŠ é¡¹ç›®è·¯å¾„
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factor_analyze.monthly_abnormal_turnover_factor import (
    MonthlyAbnormalTurnoverFactor,
    StockMonthlyAbnormalTurnoverFactor,
    IndexMonthlyAbnormalTurnoverFactor,
    create_monthly_abnormal_turnover_factor
)

def demo_basic_usage():
    """
    æ¼”ç¤ºåŸºæœ¬ä½¿ç”¨æ–¹æ³•
    """
    print("=== æœˆåº¦ç›¸å¯¹æ¢æ‰‹ç‡æº¢å‡ºå› å­æ¼”ç¤?===")
    print()
    
    # 1. åˆ›å»ºå› å­å®ä¾‹
    print("1. åˆ›å»ºå› å­å®ä¾‹")
    
    # åŸºç¡€å› å­
    base_factor = MonthlyAbnormalTurnoverFactor()
    print(f"   åŸºç¡€å› å­: {base_factor.factor_name}")
    
    # ä¸ªè‚¡å› å­
    stock_factor = StockMonthlyAbnormalTurnoverFactor()
    print(f"   ä¸ªè‚¡å› å­: {stock_factor.factor_name}")
    
    # æŒ‡æ•°å› å­
    index_factor = IndexMonthlyAbnormalTurnoverFactor()
    print(f"   æŒ‡æ•°å› å­: {index_factor.factor_name}")
    
    # ä½¿ç”¨å·¥å‚å‡½æ•°
    factory_stock = create_monthly_abnormal_turnover_factor("stock")
    factory_index = create_monthly_abnormal_turnover_factor("index")
    print(f"   å·¥å‚åˆ›å»º: {factory_stock.target_type}, {factory_index.target_type}")
    print()
    
    # 2. æ˜¾ç¤ºå› å­ä¿¡æ¯
    print("2. å› å­åŸºæœ¬ä¿¡æ¯")
    print(f"   åç§°: {base_factor.factor_name}")
    print(f"   ç±»åˆ«: {base_factor.factor_category}")
    print(f"   æè¿°: {base_factor.factor_description}")
    print()
    
    # 3. æ˜¾ç¤ºé…ç½®å‚æ•°
    print("3. é…ç½®å‚æ•°")
    print(f"   çŸ­æœŸçª—å£: {base_factor.config.short_window} å¤?)
    print(f"   é•¿æœŸçª—å£: {base_factor.config.long_window} å¤?)
    print(f"   æœ€å°æ¢æ‰‹ç‡é˜ˆå€? {base_factor.config.min_turnover_threshold}")
    print(f"   æœ€å¤§æ¢æ‰‹ç‡é˜ˆå€? {base_factor.config.max_turnover_threshold}")
    print(f"   å¼‚å¸¸å€¼é˜ˆå€? {base_factor.config.outlier_threshold}")
    print()
    
    # 4. ä¸ªè‚¡å’ŒæŒ‡æ•°é…ç½®å·®å¼?
    print("4. ä¸ªè‚¡ä¸æŒ‡æ•°é…ç½®å·®å¼?)
    print(f"   ä¸ªè‚¡æœ€å¤§æ¢æ‰‹ç‡é˜ˆå€? {stock_factor.config.max_turnover_threshold}")
    print(f"   æŒ‡æ•°æœ€å¤§æ¢æ‰‹ç‡é˜ˆå€? {index_factor.config.max_turnover_threshold}")
    print(f"   ä¸ªè‚¡å¼‚å¸¸å€¼é˜ˆå€? {stock_factor.config.outlier_threshold}")
    print(f"   æŒ‡æ•°å¼‚å¸¸å€¼é˜ˆå€? {index_factor.config.outlier_threshold}")
    print()

def demo_custom_configuration():
    """
    æ¼”ç¤ºè‡ªå®šä¹‰é…ç½?
    """
    print("=== è‡ªå®šä¹‰é…ç½®æ¼”ç¤?===")
    print()
    
    # è‡ªå®šä¹‰é…ç½?
    custom_config = {
        'short_window': 15,          # ç¼©çŸ­çŸ­æœŸçª—å£
        'long_window': 200,          # ç¼©çŸ­é•¿æœŸçª—å£
        'outlier_threshold': 2.0,    # æ›´ä¸¥æ ¼çš„å¼‚å¸¸å€¼æ£€æµ?
        'standardize': False,        # ä¸è¿›è¡Œæ ‡å‡†åŒ–
        'winsorize': False          # ä¸è¿›è¡Œç¼©å°¾å¤„ç?
    }
    
    custom_factor = MonthlyAbnormalTurnoverFactor(custom_config)
    
    print("è‡ªå®šä¹‰é…ç½?")
    print(f"   çŸ­æœŸçª—å£: {custom_factor.config.short_window} å¤?)
    print(f"   é•¿æœŸçª—å£: {custom_factor.config.long_window} å¤?)
    print(f"   å¼‚å¸¸å€¼é˜ˆå€? {custom_factor.config.outlier_threshold}")
    print(f"   æ ‡å‡†åŒ? {custom_factor.config.standardize}")
    print(f"   ç¼©å°¾å¤„ç†: {custom_factor.config.winsorize}")
    print()

def demo_factor_calculation_example():
    """
    æ¼”ç¤ºå› å­è®¡ç®—ç¤ºä¾‹ï¼ˆä½¿ç”¨æ¨¡æ‹Ÿæ•°æ®ï¼‰
    """
    print("=== å› å­è®¡ç®—ç¤ºä¾‹ ===")
    print()
    
    # åˆ›å»ºå› å­å®ä¾‹
    factor = StockMonthlyAbnormalTurnoverFactor()
    
    print("è®¡ç®—ç¤ºä¾‹:")
    print("   è¯åˆ¸ä»£ç : 000001.SZ")
    print("   è®¡ç®—æœŸé—´: 2023-01-01 åˆ?2023-12-31")
    print("   æ•°æ®æ¥æº: çœŸå®æ•°æ®ï¼ˆå¦‚æœå¯ç”¨ï¼‰æˆ–æ¨¡æ‹Ÿæ•°æ?)
    print()
    
    # å°è¯•è®¡ç®—å› å­ï¼ˆè¿™é‡Œåªæ˜¯æ¼”ç¤ºè°ƒç”¨æ–¹å¼ï¼‰
    print("è°ƒç”¨æ–¹å¼:")
    print("   result = factor.calculate_factor(")
    print("       security='000001.SZ',")
    print("       start_date='2023-01-01',")
    print("       end_date='2023-12-31'")
    print("   )")
    print()
    
    print("è¿”å›ç»“æœåŒ…å«:")
    print("   - factor_value: æœ€æ–°å› å­å€?)
    print("   - data_quality_score: æ•°æ®è´¨é‡è¯„åˆ†")
    print("   - turnover_data: æ¢æ‰‹ç‡æ•°æ?)
    print("   - factor_series: å› å­æ—¶é—´åºåˆ—")
    print("   - statistics: ç»Ÿè®¡æŒ‡æ ‡")
    print("   - config: é…ç½®å‚æ•°")
    print()

def demo_factor_interpretation():
    """
    æ¼”ç¤ºå› å­è§£é‡Š
    """
    print("=== å› å­è§£é‡Šè¯´æ˜ ===")
    print()
    
    print("å› å­å…¬å¼:")
    print("   æœˆåº¦ç›¸å¯¹æ¢æ‰‹ç‡æº¢å‡?= mean(Turnover_{t-20:t}) / mean(Turnover_{t-250:t})")
    print("   å…¶ä¸­:")
    print("   - Turnover_{t-20:t}: è¿‡å»20ä¸ªäº¤æ˜“æ—¥çš„æ—¥æ¢æ‰‹ç?)
    print("   - Turnover_{t-250:t}: è¿‡å»250ä¸ªäº¤æ˜“æ—¥çš„æ—¥æ¢æ‰‹ç?)
    print()
    
    print("å› å­å«ä¹‰:")
    print("   > 1.0: çŸ­æœŸæ¢æ‰‹ç‡é«˜äºé•¿æœŸå¹³å‡æ°´å¹?)
    print("          â†?å¸‚åœºæƒ…ç»ªé«˜æ¶¨ï¼Œäº¤æ˜“æ´»è·?)
    print("          â†?å¯èƒ½å­˜åœ¨è¿‡åº¦äº¤æ˜“ï¼Œè‚¡ä»·è¢«é«˜ä¼°é£é™©")
    print()
    print("   < 1.0: çŸ­æœŸæ¢æ‰‹ç‡ä½äºé•¿æœŸå¹³å‡æ°´å¹?)
    print("          â†?å¸‚åœºæƒ…ç»ªä½è¿·ï¼Œäº¤æ˜“ä¸æ´»è·ƒ")
    print("          â†?å¯èƒ½å­˜åœ¨ä»·å€¼ä½ä¼°æœºä¼?)
    print()
    print("   = 1.0: çŸ­æœŸæ¢æ‰‹ç‡ç­‰äºé•¿æœŸå¹³å‡æ°´å¹?)
    print("          â†?å¸‚åœºå¤„äºæ­£å¸¸çŠ¶æ€?)
    print()
    
    print("åº”ç”¨åœºæ™¯:")
    print("   1. æƒ…ç»ªåˆ†æ: è¯†åˆ«å¸‚åœºè¿‡çƒ­æˆ–è¿‡å†·çŠ¶æ€?)
    print("   2. é€‰è‚¡ç­–ç•¥: å¯»æ‰¾è¢«ä½ä¼°æˆ–é¿å…è¢«é«˜ä¼°çš„è‚¡ç¥¨")
    print("   3. é£é™©ç®¡ç†: è¯„ä¼°æµåŠ¨æ€§é£é™?)
    print("   4. æ‹©æ—¶ç­–ç•¥: åˆ¤æ–­å¸‚åœºæƒ…ç»ªè½¬æ¢ç‚?)
    print()

def demo_integration_with_factor_mining():
    """
    æ¼”ç¤ºä¸factor_miningæ¨¡å—çš„é›†æˆ?
    """
    print("=== factor_miningæ¨¡å—é›†æˆ ===")
    print()
    
    factor = MonthlyAbnormalTurnoverFactor()
    
    print("å› å­æ³¨å†Œä¿¡æ¯:")
    factor_info = {
        'name': factor.factor_name,
        'category': factor.factor_category,
        'description': factor.factor_description,
        'class_name': factor.__class__.__name__,
        'module_path': factor.__class__.__module__,
        'target_types': ['ä¸ªè‚¡', 'æŒ‡æ•°'],
        'data_requirements': ['æ—¥åº¦æ¢æ‰‹ç?],
        'calculation_frequency': 'æ—¥åº¦',
        'lookback_period': f"{factor.config.long_window}ä¸ªäº¤æ˜“æ—¥"
    }
    
    for key, value in factor_info.items():
        print(f"   {key}: {value}")
    print()
    
    print("factor_miningè°ƒç”¨ç¤ºä¾‹:")
    print("   from factor_analyze.monthly_abnormal_turnover_factor import create_monthly_abnormal_turnover_factor")
    print("   ")
    print("   # åˆ›å»ºä¸ªè‚¡å› å­")
    print("   stock_factor = create_monthly_abnormal_turnover_factor('stock')")
    print("   ")
    print("   # æ‰¹é‡è®¡ç®—")
    print("   results = []")
    print("   for security in security_list:")
    print("       result = stock_factor.calculate_factor(security, start_date, end_date)")
    print("       results.append(result)")
    print()

def main():
    """
    ä¸»æ¼”ç¤ºå‡½æ•?
    """
    print("æœˆåº¦ç›¸å¯¹æ¢æ‰‹ç‡æº¢å‡ºå› å­?(Monthly Abnormal Turnover Factor)")
    print("=" * 60)
    print()
    
    try:
        demo_basic_usage()
        demo_custom_configuration()
        demo_factor_calculation_example()
        demo_factor_interpretation()
        demo_integration_with_factor_mining()
        
        print("=" * 60)
        print("âœ?æ¼”ç¤ºå®Œæˆï¼å› å­å·²æˆåŠŸå®ç°å¹¶å¯ä»¥ä½¿ç”¨ã€?)
        print()
        print("ä¸‹ä¸€æ­?")
        print("1. åœ¨çœŸå®ç¯å¢ƒä¸­æµ‹è¯•å› å­è®¡ç®—")
        print("2. è¿›è¡Œå› å­æœ‰æ•ˆæ€§å›æµ?)
        print("3. é›†æˆåˆ°factor_miningæ¨¡å—")
        print("4. åº”ç”¨åˆ°å®é™…æŠ•èµ„ç­–ç•¥ä¸­")
        
    except Exception as e:
        print(f"æ¼”ç¤ºè¿‡ç¨‹ä¸­å‡ºç°é”™è¯? {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
