# -*- coding: utf-8 -*-
"""
CNN策略统一系统集成
CNN Strategy Unified System Integration

将CNN时间序列策略集成到统一策略管理系统
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入CNN策略
from cnn_time_series_strategy import CNNTimeSeriesStrategy

# 导入统一系统组件
try:
    from unified_strategies.unified_system import get_unified_system, StrategyCategory
    UNIFIED_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from strategy_autodev.unified_strategies.unified_system import get_unified_system, StrategyCategory
        UNIFIED_SYSTEM_AVAILABLE = True
    except ImportError:
        UNIFIED_SYSTEM_AVAILABLE = False
        warnings.warn("统一策略系统不可用")

# 导入数据管道
try:
    from data_adapter import get_data_adapter_manager
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    try:
        from data_pipeline.data_adapter_compatibility import get_data_pipeline
        DATA_ADAPTER_AVAILABLE = True
    except ImportError:
        DATA_ADAPTER_AVAILABLE = False
        warnings.warn("数据适配器不可用")

logger = logging.getLogger(__name__)

class CNNStrategyWrapper:
    """
    CNN策略包装器，用于统一系统集成
    """
    
    def __init__(self, **kwargs):
        self.strategy = CNNTimeSeriesStrategy(**kwargs)
        self.name = "CNNTimeSeriesStrategy"
        self.category = StrategyCategory.ML if hasattr(StrategyCategory, 'ML') else "ML"
        self.description = "基于卷积神经网络的时间序列预测策略"
        
    def initialize(self, **params):
        """初始化策略参数"""
        return True
        
    def train(self, data: pd.DataFrame) -> bool:
        """训练策略"""
        return self.strategy.train(data)
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        return self.strategy.generate_signals(data)
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.strategy.get_model_summary()

def register_cnn_strategy():
    """
    注册CNN策略到统一系统
    """
    if not UNIFIED_SYSTEM_AVAILABLE:
        logger.error("统一策略系统不可用，无法注册CNN策略")
        return None
        
    try:
        # 获取统一系统
        system = get_unified_system()
        
        # 注册策略
        system.register_strategy(
            "CNNTimeSeriesStrategy", 
            CNNStrategyWrapper, 
            StrategyCategory.ML if hasattr(StrategyCategory, 'ML') else "ML"
        )
        
        logger.info("CNN策略已成功注册到统一系统")
        return system
        
    except Exception as e:
        logger.error(f"CNN策略注册失败: {e}")
        return None

def create_and_test_cnn_strategy():
    """
    创建并测试CNN策略
    """
    try:
        # 注册策略
        system = register_cnn_strategy()
        if system is None:
            logger.error("策略注册失败")
            return False
            
        # 创建策略实例
        strategy_params = {
            "filters": 32,
            "kernel_size": 3,
            "sequence_length": 24,
            "prediction_type": "classification",
            "learning_rate": 0.001,
            "batch_size": 32,
            "epochs": 20,
            "validation_split": 0.2
        }
        
        strategy = system.create_strategy("CNNTimeSeriesStrategy", strategy_params)
        
        if strategy is None:
            logger.error("策略创建失败")
            return False
            
        logger.info("CNN策略创建成功")
        
        # 获取真实数据进行测试
        test_data = get_real_market_data()
        
        if test_data is None or len(test_data) == 0:
            logger.warning("无法获取真实数据，使用模拟数据")
            test_data = generate_sample_data()
            
        # 训练策略
        logger.info("开始训练CNN策略...")
        train_success = strategy.train(test_data)
        
        if not train_success:
            logger.error("策略训练失败")
            return False
            
        logger.info("策略训练成功")
        
        # 生成信号
        signals = strategy.generate_signals(test_data)
        logger.info(f"生成交易信号: {len(signals)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"CNN策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_cnn_strategy_backtest():
    """
    运行CNN策略回测
    """
    try:
        # 注册策略
        system = register_cnn_strategy()
        if system is None:
            return None
            
        # 设置回测参数
        start_date = "2023-01-01"
        end_date = "2023-12-31"
        
        logger.info(f"开始CNN策略回测: {start_date} 到 {end_date}")
        
        # 运行回测
        results = system.run_strategy_backtest(
            "CNNTimeSeriesStrategy", 
            start_date, 
            end_date
        )
        
        if results is not None:
            logger.info("CNN策略回测完成")
            logger.info(f"回测结果: {results}")
        else:
            logger.warning("回测结果为空")
            
        return results
        
    except Exception as e:
        logger.error(f"CNN策略回测失败: {e}")
        return None

def get_real_market_data() -> Optional[pd.DataFrame]:
    """
    获取真实市场数据
    """
    if not DATA_ADAPTER_AVAILABLE:
        logger.warning("数据适配器不可用")
        return None
        
    try:
        # 尝试获取数据适配器
        data_manager = get_data_adapter_manager()
        
        # 获取股票数据（示例：获取某只股票的历史数据）
        symbol = "000001.SZ"  # 平安银行
        start_date = "2022-01-01"
        end_date = "2023-12-31"
        
        data = data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            fields=['open', 'high', 'low', 'close', 'volume']
        )
        
        if data is not None and len(data) > 0:
            logger.info(f"获取真实数据成功: {symbol}, {len(data)} 行")
            return data
        else:
            logger.warning("获取的真实数据为空")
            return None
            
    except Exception as e:
        logger.error(f"获取真实数据失败: {e}")
        return None

def generate_sample_data(n_days=500):
    """
    生成样本数据（备用）
    """
    np.random.seed(42)
    
    dates = pd.date_range(start='2022-01-01', periods=n_days, freq='D')
    
    # 生成价格数据
    returns = np.random.normal(0.001, 0.02, n_days)
    prices = [100]
    
    for i in range(1, n_days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    # 生成OHLC数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        open_price = price * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, price) * (1 + abs(np.random.normal(0, 0.01)))
        low_price = min(open_price, price) * (1 - abs(np.random.normal(0, 0.01)))
        close_price = price
        volume = np.random.lognormal(10, 0.5)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df

def main():
    """
    主函数：完整的CNN策略集成流程
    """
    logger.info("开始CNN策略统一系统集成")
    
    # 1. 注册策略
    logger.info("步骤1: 注册CNN策略")
    system = register_cnn_strategy()
    
    if system is None:
        logger.error("策略注册失败，退出")
        return False
    
    # 2. 创建策略
    logger.info("步骤2: 创建CNN策略实例")
    success = create_and_test_cnn_strategy()
    
    if not success:
        logger.error("策略创建和测试失败")
        return False
    
    # 3. 运行回测
    logger.info("步骤3: 运行CNN策略回测")
    results = run_cnn_strategy_backtest()
    
    if results is not None:
        logger.info("CNN策略集成完成")
        return True
    else:
        logger.warning("回测未成功，但策略集成基本完成")
        return True

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 运行主流程
    success = main()
    
    if success:
        logger.info("✓ CNN策略统一系统集成成功")
    else:
        logger.error("✗ CNN策略统一系统集成失败")