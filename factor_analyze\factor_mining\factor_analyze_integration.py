# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
factor_analyze 与factor_mining 协作集成接口
Factor Analyze and Factor Mining Integration Interface

实现两个模块之间的无缝集成和协作：
- factor_analyze 中的所有因子可以被factor_mining 引用和调用
- factor_mining 可以调用 factor_analyze 的因子作为基础组件
- 通过 factor_core 的统一接口实现无缝集成
"""

import sys
import os
from typing import Dict, List, Optional, Any, Union, Type
import logging
from datetime import datetime

# 添加路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入factor_analyze的所有因子
try:
    from factor_analyze import (
        NegativeReturnIlliquidityFactorStock,
        NegativeReturnIlliquidityFactorIndex,
        StockOperatingCashFlowToMarketValueFactor,
        IndexOperatingCashFlowToMarketValueFactor,
        OperatingCashFlowToMarketValueFactor
    )
    
    # 导入投资者关注度占比因子
    from factor_analyze.mock.mock_investor_attention_ratio_factor import (
        MockInvestorAttentionRatioForStocks,
        MockInvestorAttentionRatioForIndices,
        create_investor_attention_ratio_factor
    )
    
    # 导入短期债务占比因子
    from factor_analyze.fundamental_factors.current_debt_ratio_factor import (
        CurrentDebtRatioFactorForStocks,
        CurrentDebtRatioFactorForIndices,
        calculate_current_debt_ratio_factor
    )
    
    # 导入标准化存货变化因子
    from factor_analyze.fundamental_factors.standardized_inventory_change_factor import (
        StandardizedInventoryChangeFactor,
        StandardizedInventoryChangeFactorForIndex,
        create_standardized_inventory_change_factor
    )
    
    FACTOR_ANALYZE_AVAILABLE = True
    
except ImportError as e:
    logging.warning(f"无法导入factor_analyze模块: {e}")
    FACTOR_ANALYZE_AVAILABLE = False

# 导入factor_core基础接口
try:
    from factor_analyze.factor_core.base_interfaces import BaseFactorMiner
    from factor_analyze.factor_core.common_types import FactorData
except ImportError as e:
    logging.warning(f"无法导入factor_core模块: {e}")
    BaseFactorMiner = object
    FactorData = dict


class FactorAnalyzeRegistry:
    """
    factor_analyze 因子注册表
    管理所有可用的 factor_analyze 因子
    """
    
    def __init__(self):
        self._stock_factors = {}
        self._index_factors = {}
        self._factor_metadata = {}
        self._initialize_factors()
    
    def _initialize_factors(self):
        """初始化所有可用因子"""
        if not FACTOR_ANALYZE_AVAILABLE:
            logging.warning("factor_analyze模块不可用，跳过因子注册")
            return
        
        # 注册股票因子
        self.register_stock_factor(
            'negative_return_illiquidity',
            NegativeReturnIlliquidityFactorStock,
            {
                'name': '负收益流动性因子(股票)',
                'category': 'emotion',
                'description': '衡量股票在下跌时的流动性状况',
                'data_requirements': ['daily_price', 'volume'],
                'calculation_period': 'monthly'
            }
        )
        
        # 注册指数因子
        self.register_index_factor(
            'negative_return_illiquidity',
            NegativeReturnIlliquidityFactorIndex,
            {
                'name': '负收益流动性因子(指数)',
                'category': 'emotion',
                'description': '衡量指数在下跌时的流动性状况',
                'data_requirements': ['daily_price', 'volume'],
                'calculation_period': 'monthly'
            }
        )
        
        # 注册短期债务占比因子(股票)
        self.register_stock_factor(
            'current_debt_ratio',
            CurrentDebtRatioFactorForStocks,
            {
                'name': '短期债务占比因子(股票)',
                'category': 'fundamental',
                'description': '衡量企业对短期债务的依赖程度和短期财务风险',
                'data_requirements': ['balance_sheet', 'current_liabilities', 'total_liabilities'],
                'calculation_period': 'quarterly',
                'formula': '短期负债 / 总负债',
                'data_source': 'real_with_mock_fallback'
            }
        )
        
        # 注册短期债务占比因子(指数)
        self.register_index_factor(
            'current_debt_ratio',
            CurrentDebtRatioFactorForIndices,
            {
                'name': '短期债务占比因子(指数)',
                'category': 'fundamental',
                'description': '基于成分股短期债务占比计算的指数层面财务风险因子',
                'data_requirements': ['balance_sheet', 'current_liabilities', 'total_liabilities', 'index_weights'],
                'calculation_period': 'quarterly',
                'formula': '∑(指数权重i × 成分股短期债务占比i)',
                'data_source': 'real_with_mock_fallback',
                'aggregation_method': 'weighted_average'
            }
        )
        
        # 注册投资者关注度占比因子(股票)
        self.register_stock_factor(
            'investor_attention_ratio',
            MockInvestorAttentionRatioForStocks,
            {
                'name': '投资者关注度占比因子(股票)',
                'category': 'emotion',
                'description': '基于投资者自选股数据反映股票关注度占比',
                'data_requirements': ['user_watchlist', 'portfolio_holdings', 'social_media_mentions'],
                'calculation_period': 'daily',
                'formula': '关注度占比 = Ni / Nall',
                'data_source': 'mock_only'
            }
        )
        
        # 注册投资者关注度占比因子(指数)
        self.register_index_factor(
            'investor_attention_ratio',
            MockInvestorAttentionRatioForIndices,
            {
                'name': '投资者关注度占比因子(指数)',
                'category': 'emotion',
                'description': '基于投资者自选股数据反映指数关注度占比',
                'data_requirements': ['user_watchlist', 'portfolio_holdings', 'social_media_mentions'],
                'calculation_period': 'daily',
                'formula': '关注度占比 = Ni / Nall',
                'data_source': 'mock_only'
            }
        )
        
        # 注册标准化存货变化因子(股票)
        self.register_stock_factor(
            'standardized_inventory_change',
            StandardizedInventoryChangeFactor,
            {
                'name': '标准化存货变化因子(股票)',
                'category': 'fundamental',
                'description': '衡量公司存货变化相对于总资产的标准化程度',
                'data_requirements': ['balance_sheet', 'inventories', 'total_assets'],
                'calculation_period': 'quarterly',
                'formula': '(存货_t - 存货_t-1) / ((总资产_t + 总资产_t-1) / 2)',
                'data_source': 'real_with_mock_fallback'
            }
        )
        
        # 注册标准化存货变化因子(指数)
        self.register_index_factor(
            'standardized_inventory_change',
            StandardizedInventoryChangeFactorForIndex,
            {
                'name': '标准化存货变化因子(指数)',
                'category': 'fundamental',
                'description': '基于成分股加权计算的指数标准化存货变化',
                'data_requirements': ['balance_sheet', 'inventories', 'total_assets', 'index_weights'],
                'calculation_period': 'quarterly',
                'formula': '∑(权重i × 成分股标准化存货变化i)',
                'data_source': 'real_with_mock_fallback',
                'aggregation_method': 'weighted_average'
            }
        )
        
        # 注册经营性现金流市值比因子(股票)
        self.register_stock_factor(
            'operating_cash_flow_to_market_value',
            StockOperatingCashFlowToMarketValueFactor,
            {
                'name': '经营性现金流市值比因子(股票)',
                'category': 'fundamental',
                'description': '衡量公司经营现金流相对于市值的比例',
                'data_requirements': ['cash_flow_statement', 'market_cap'],
                'calculation_period': 'quarterly',
                'formula': '经营性现金流TTM / 市值',
                'data_source': 'real_with_mock_fallback'
            }
        )
        
        # 注册经营性现金流市值比因子(指数)
        self.register_index_factor(
            'operating_cash_flow_to_market_value',
            IndexOperatingCashFlowToMarketValueFactor,
            {
                'name': '经营性现金流市值比因子(指数)',
                'category': 'fundamental',
                'description': '基于成分股经营性现金流市值比计算的指数层面因子',
                'data_requirements': ['cash_flow_statement', 'market_cap', 'index_weights'],
                'calculation_period': 'quarterly',
                'formula': '∑(指数权重i × 成分股经营性现金流市值比i)',
                'data_source': 'real_with_mock_fallback',
                'aggregation_method': 'weighted_average'
            }
        )
        
        logging.info(f"成功注册 {len(self._stock_factors)} 个股票因子和 {len(self._index_factors)} 个指数因子")
    
    def register_stock_factor(self, factor_id: str, factor_class: Type, metadata: Dict[str, Any]):
        """注册股票因子"""
        self._stock_factors[factor_id] = factor_class
        self._factor_metadata[('stock', factor_id)] = metadata
        logging.debug(f"注册股票因子: {factor_id}")
    
    def register_index_factor(self, factor_id: str, factor_class: Type, metadata: Dict[str, Any]):
        """注册指数因子"""
        self._index_factors[factor_id] = factor_class
        self._factor_metadata[('index', factor_id)] = metadata
        logging.debug(f"注册指数因子: {factor_id}")
    
    def get_stock_factor(self, factor_id: str) -> Optional[Type]:
        """获取股票因子类"""
        return self._stock_factors.get(factor_id)
    
    def get_index_factor(self, factor_id: str) -> Optional[Type]:
        """获取指数因子类"""
        return self._index_factors.get(factor_id)
    
    def get_factor_metadata(self, factor_type: str, factor_id: str) -> Optional[Dict]:
        """获取因子元数据"""
        return self._factor_metadata.get((factor_type, factor_id))
    
    def list_stock_factors(self) -> List[str]:
        """列出所有股票因子ID"""
        return list(self._stock_factors.keys())
    
    def list_index_factors(self) -> List[str]:
        """列出所有指数因子ID"""
        return list(self._index_factors.keys())
    
    def get_all_factors(self) -> Dict[str, Dict[str, Type]]:
        """获取所有因子"""
        return {
            'stock_factors': self._stock_factors.copy(),
            'index_factors': self._index_factors.copy()
        }


class FactorAnalyzeIntegrationMiner(BaseFactorMiner):
    """
    factor_analyze集成挖掘器
    将factor_analyze的因子集成到factor_mining流水线中
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.registry = FactorAnalyzeRegistry()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def mine_factors(self, symbols: List[str], start_date: str, end_date: str, **kwargs) -> Dict[str, Any]:
        """
        挖掘因子数据
        
        Args:
            symbols: 股票/指数代码列表
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        Returns:
            挖掘结果字典
        """
        try:
            self.logger.info(f"开始挖掘因子数据: {len(symbols)} 个标的, {start_date} 至 {end_date}")
            
            results = {}
            
            # 获取资产类型
            asset_type = kwargs.get('asset_type', 'stock')
            
            # 获取要计算的因子列表
            factor_list = kwargs.get('factors', [])
            
            if not factor_list:
                # 如果没有指定因子，使用所有可用因子
                if asset_type == 'stock':
                    factor_list = self.registry.list_stock_factors()
                else:
                    factor_list = self.registry.list_index_factors()
            
            # 计算每个因子
            for factor_id in factor_list:
                try:
                    if asset_type == 'stock':
                        factor_class = self.registry.get_stock_factor(factor_id)
                    else:
                        factor_class = self.registry.get_index_factor(factor_id)
                    
                    if factor_class is None:
                        self.logger.warning(f"因子 {factor_id} 不存在")
                        continue
                    
                    # 创建因子实例
                    factor_config = self.config.get(factor_id, {})
                    factor_config.update({
                        'symbols': symbols,
                        'start_date': start_date,
                        'end_date': end_date
                    })
                    
                    factor_instance = factor_class(config=factor_config)
                    
                    # 计算因子值
                    factor_data = factor_instance.calculate_factor()
                    
                    if factor_data is not None:
                        results[factor_id] = factor_data
                        self.logger.info(f"成功计算因子 {factor_id}")
                    else:
                        self.logger.warning(f"因子 {factor_id} 计算结果为空")
                        
                except Exception as e:
                    self.logger.error(f"计算因子 {factor_id} 时出错: {e}")
                    continue
            
            return {
                'success': True,
                'factor_data': results,
                'metadata': {
                    'symbols': symbols,
                    'start_date': start_date,
                    'end_date': end_date,
                    'asset_type': asset_type,
                    'calculated_factors': list(results.keys())
                }
            }
            
        except Exception as e:
            self.logger.error(f"因子挖掘过程出错: {e}")
            return {
                'success': False,
                'error': str(e),
                'factor_data': {}
            }
    
    def generate_factor_combinations(self, base_factors: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        生成因子组合
        
        Args:
            base_factors: 基础因子列表
            **kwargs: 其他参数
            
        Returns:
            因子组合列表
        """
        combinations = []
        
        # 简单的因子组合生成逻辑
        for i, factor1 in enumerate(base_factors):
            for j, factor2 in enumerate(base_factors[i+1:], i+1):
                combination = {
                    'combination_id': f"{factor1}_{factor2}",
                    'factors': [factor1, factor2],
                    'combination_method': 'linear',
                    'weights': [0.5, 0.5]
                }
                combinations.append(combination)
        
        return combinations


class FactorMiningIntegrationManager:
    """
    factor_mining集成管理器
    管理factor_analyze与factor_mining的集成
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.factor_registry = FactorAnalyzeRegistry()
        self.integration_miner = FactorAnalyzeIntegrationMiner(config)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def create_factor_pipeline(self, pipeline_config: Dict) -> Any:
        """
        创建因子流水线
        
        Args:
            pipeline_config: 流水线配置
            
        Returns:
            流水线对象
        """
        try:
            from factor_analyze.factor_mining.pipeline import FactorMiningPipeline
            
            # 将factor_analyze因子集成到流水线中
            pipeline_config['factor_sources'] = pipeline_config.get('factor_sources', [])
            pipeline_config['factor_sources'].append({
                'source_type': 'factor_analyze',
                'miner': self.integration_miner
            })
            
            pipeline = FactorMiningPipeline(pipeline_config)
            self.logger.info("成功创建集成因子流水线")
            return pipeline
            
        except ImportError:
            self.logger.warning("factor_mining.pipeline模块不可用，返回简化流水线")
            return {
                'config': pipeline_config,
                'miner': self.integration_miner,
                'status': 'simplified'
            }
    
    def get_integration_status(self) -> Dict[str, Any]:
        """
        获取集成状态
        
        Returns:
            集成状态字典
        """
        return {
            'factor_analyze_available': FACTOR_ANALYZE_AVAILABLE,
            'loaded_stock_factors': len(self.factor_registry.list_stock_factors()),
            'loaded_index_factors': len(self.factor_registry.list_index_factors()),
            'integration_time': datetime.now().isoformat(),
            'config': self.config
        }


# 全局集成管理器实例
_integration_manager = None


def get_integration_manager(config: Optional[Dict] = None) -> FactorMiningIntegrationManager:
    """
    获取集成管理器实例（单例模式）
    
    Args:
        config: 配置参数
        
    Returns:
        集成管理器实例
    """
    global _integration_manager
    if _integration_manager is None:
        _integration_manager = FactorMiningIntegrationManager(config)
    return _integration_manager


def create_integrated_pipeline(base_factors: List[Dict], **kwargs) -> Any:
    """
    创建集成流水线
    
    Args:
        base_factors: 基础因子配置列表
        **kwargs: 其他参数
        
    Returns:
        集成流水线对象
    """
    manager = get_integration_manager()
    
    pipeline_config = {
        'base_factors': base_factors,
        'pipeline_name': kwargs.get('pipeline_name', 'integrated_pipeline'),
        'enable_factor_analyze': True
    }
    pipeline_config.update(kwargs)
    
    return manager.create_factor_pipeline(pipeline_config)


def list_available_base_factors() -> Dict[str, List[str]]:
    """
    列出所有可用的基础因子
    
    Returns:
        可用因子字典
    """
    manager = get_integration_manager()
    registry = manager.factor_registry
    
    return {
        'stock_factors': registry.list_stock_factors(),
        'index_factors': registry.list_index_factors()
    }


# 导出主要接口
__all__ = [
    'FactorAnalyzeRegistry',
    'FactorAnalyzeIntegrationMiner', 
    'FactorMiningIntegrationManager',
    'get_integration_manager',
    'create_integrated_pipeline',
    'list_available_base_factors'
]


if __name__ == "__main__":
    # 测试集成功能
    print("=" * 80)
    print("factor_analyze与factor_mining集成测试")
    print("=" * 80)
    
    # 获取集成管理器
    manager = get_integration_manager()
    
    # 显示集成状态
    status = manager.get_integration_status()
    print("\n集成状态:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # 列出可用因子
    factors = list_available_base_factors()
    print(f"\n可用股票因子: {factors['stock_factors']}")
    print(f"可用指数因子: {factors['index_factors']}")
    
    # 测试因子挖掘
    print("\n测试因子挖掘:")
    test_symbols = ['000001.SZ', '000002.SZ']
    test_result = manager.integration_miner.mine_factors(
        symbols=test_symbols,
        start_date='2024-01-01',
        end_date='2024-01-10',
        asset_type='stock',
        factors=['current_debt_ratio']
    )
    
    if test_result['success']:
        print("✓ 因子挖掘测试成功")
        print(f"  计算的因子: {test_result['metadata']['calculated_factors']}")
    else:
        print("✗ 因子挖掘测试失败")
        print(f"  错误: {test_result.get('error', '未知错误')}")
    
    print("\n" + "=" * 80)
    print("集成测试完成")
    print("=" * 80)
