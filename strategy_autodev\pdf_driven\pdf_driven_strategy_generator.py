# -*- coding: utf-8 -*-
"""
基于PDF研究的策略自动生成器
整合来自研究报告的策略思路和实现模型

核心功能：
1. Barra多因子选股策略
2. 量价突破策略
3. 成长因子策略
4. 数据质量增强策略

集成来源：
- 方正证券《Barra模型》系列
- 海通证券《量价关系》研究
- 国盛证券《成长因子》分析
- 财通证券《数据质量》控制

Author: PDF Integration System
Date: 2025-01-28
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from factor_analyze.models.barra_multifactor_integration import create_barra_integrator
    from factor_analyze.volume_price_factors import create_volume_price_factors
    from research.quality_control import check_data_quality
    FACTOR_MODULES_AVAILABLE = True
except ImportError:
    FACTOR_MODULES_AVAILABLE = False


@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    strategy_type: str
    rebalance_frequency: str = "monthly"
    position_size: float = 0.95
    max_single_weight: float = 0.05
    enable_quality_check: bool = True


class BarraMultiFactorStrategy:
    """Barra多因子选股策略"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.factor_integrator = None
        if FACTOR_MODULES_AVAILABLE:
            self.factor_integrator = create_barra_integrator()
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成选股信号"""
        signals = pd.DataFrame(index=data.index)
        
        if self.factor_integrator is None:
            print("警告：Barra因子模块不可用，使用简化实现")
            return self._simple_multifactor_signals(data)
        
        print("使用Barra多因子模型生成信号...")
        
        # 计算Barra因子
        factor_results = self.factor_integrator.process_data(data)
        final_factors = factor_results.get('final_factors', pd.DataFrame())
        
        if final_factors.empty:
            return signals
        
        # 计算因子综合得分
        factor_scores = self._calculate_factor_scores(final_factors)
        
        # 生成选股信号
        signals['factor_score'] = factor_scores
        signals['signal'] = 0
        
        # 按得分排名选股
        daily_ranks = factor_scores.groupby(level=0).rank(pct=True, ascending=False)
        signals['signal'] = (daily_ranks <= 0.2).astype(int)  # 选20%
        
        return signals
    
    def _simple_multifactor_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """简化的多因子信号"""
        signals = pd.DataFrame(index=data.index)
        
        if 'close' not in data.columns:
            return signals
        
        # 简单的多因子评价
        score = 0
        
        # 动量因子
        if 'close' in data.columns:
            momentum = data['close'].pct_change(20)
            score += momentum.rank(pct=True) - 0.5
        
        # 价值因子
        if 'pe_ratio' in data.columns:
            value = 1 / data['pe_ratio']
            score += value.rank(pct=True) - 0.5
        
        # 质量因子
        if 'roe' in data.columns:
            quality = data['roe']
            score += quality.rank(pct=True) - 0.5
        
        signals['factor_score'] = score
        signals['signal'] = (score.rank(pct=True) > 0.8).astype(int)
        
        return signals
    
    def _calculate_factor_scores(self, factors: pd.DataFrame) -> pd.Series:
        """计算因子综合得分"""
        if factors.empty:
            return pd.Series()
        
        # 简单等权重评分
        factor_scores = factors.mean(axis=1)
        return factor_scores


class VolumePriceBreakoutStrategy:
    """量价突破策略"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成量价突破信号"""
        signals = pd.DataFrame(index=data.index)
        
        if 'close' not in data.columns or 'volume' not in data.columns:
            print("警告：缺少价格或成交量数据")
            return signals
        
        print("生成量价突破信号...")
        
        # 价格突破
        price_ma20 = data['close'].rolling(20).mean()
        price_breakout = data['close'] > price_ma20 * 1.02
        
        # 成交量突破
        volume_ma20 = data['volume'].rolling(20).mean()
        volume_breakout = data['volume'] > volume_ma20 * 1.5
        
        # 量价配合突破
        signals['price_breakout'] = price_breakout.astype(int)
        signals['volume_breakout'] = volume_breakout.astype(int)
        signals['signal'] = (price_breakout & volume_breakout).astype(int)
        
        # 计算强度
        price_strength = (data['close'] / price_ma20) - 1
        volume_strength = (data['volume'] / volume_ma20) - 1
        signals['breakout_strength'] = price_strength * volume_strength
        
        return signals


class GrowthFactorStrategy:
    """成长因子策略"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成成长因子信号"""
        signals = pd.DataFrame(index=data.index)
        
        print("生成成长因子信号...")
        
        # 成长评分
        growth_score = 0
        
        # 营收增长
        if 'revenue' in data.columns:
            revenue_growth = data['revenue'].pct_change(4)  # 同比增长
            growth_score += revenue_growth.rank(pct=True) - 0.5
        
        # 盈利增长
        if 'eps' in data.columns:
            eps_growth = data['eps'].pct_change(4)
            growth_score += eps_growth.rank(pct=True) - 0.5
        
        # ROE改善
        if 'roe' in data.columns:
            roe_change = data['roe'].diff(4)
            growth_score += roe_change.rank(pct=True) - 0.5
        
        # GARP (Growth at Reasonable Price)
        if 'pe_ratio' in data.columns and 'eps' in data.columns:
            eps_growth = data['eps'].pct_change(4)
            garp = eps_growth / data['pe_ratio']
            growth_score += garp.rank(pct=True) - 0.5
        
        signals['growth_score'] = growth_score
        signals['signal'] = (growth_score.rank(pct=True) > 0.8).astype(int)
        
        return signals


class PDFDrivenStrategyGenerator:
    """PDF驱动的策略生成器"""
    
    def __init__(self):
        self.available_strategies = {
            'barra_multifactor': BarraMultiFactorStrategy,
            'volume_price_breakout': VolumePriceBreakoutStrategy,
            'growth_factor': GrowthFactorStrategy
        }
        
    def create_strategy(self, strategy_type: str, config: Optional[Dict] = None) -> Any:
        """创建策略实例"""
        
        if strategy_type not in self.available_strategies:
            raise ValueError(f"不支持的策略类型: {strategy_type}")
        
        # 创建配置
        if config is None:
            config = {}
        
        strategy_config = StrategyConfig(
            name=config.get('name', f'{strategy_type}_strategy'),
            strategy_type=strategy_type,
            **{k: v for k, v in config.items() if k != 'name'}
        )
        
        # 创建策略实例
        strategy_class = self.available_strategies[strategy_type]
        return strategy_class(strategy_config)
    
    def generate_integrated_strategy(self, data: pd.DataFrame,
                                   strategy_types: List[str]) -> pd.DataFrame:
        """生成集成策略信号"""
        
        all_signals = pd.DataFrame(index=data.index)
        
        # 数据质量检查
        if FACTOR_MODULES_AVAILABLE:
            print("执行数据质量检查...")
            quality_report = check_data_quality(data)
            print(f"数据质量评分: {quality_report['quality_score']:.3f}")
            
            if quality_report['quality_score'] < 0.7:
                print("⚠️ 数据质量较低，策略结果可能不可靠")
        
        # 生成各策略信号
        strategy_signals = {}
        
        for strategy_type in strategy_types:
            try:
                strategy = self.create_strategy(strategy_type)
                signals = strategy.generate_signals(data)
                strategy_signals[strategy_type] = signals
                print(f"✅ {strategy_type} 策略信号生成完成")
            except Exception as e:
                print(f"❌ {strategy_type} 策略生成失败: {e}")
        
        # 整合信号
        if strategy_signals:
            all_signals = self._integrate_signals(strategy_signals)
        
        return all_signals
    
    def _integrate_signals(self, strategy_signals: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """整合多个策略信号"""
        
        integrated = pd.DataFrame()
        
        # 收集所有信号
        signal_columns = []
        for strategy_name, signals in strategy_signals.items():
            if 'signal' in signals.columns:
                signal_col = f'{strategy_name}_signal'
                integrated[signal_col] = signals['signal']
                signal_columns.append(signal_col)
        
        # 计算综合信号
        if signal_columns:
            integrated['combined_signal'] = integrated[signal_columns].mean(axis=1)
            integrated['strong_signal'] = (integrated['combined_signal'] > 0.6).astype(int)
        
        return integrated
    
    def get_strategy_templates(self) -> Dict[str, Dict]:
        """获取策略模板信息"""
        
        templates = {
            'barra_multifactor': {
                'name': 'Barra多因子选股策略',
                'description': '基于Barra风格因子和行业因子的多因子选股',
                'factors': ['规模', '价值', '成长', '盈利', '杠杆', '流动性', '动量', '波动率'],
                'rebalance': '月度',
                'expected_ir': 0.8,
                'complexity': 'high'
            },
            'volume_price_breakout': {
                'name': '量价突破策略',
                'description': '基于量价配合的突破信号进行交易',
                'factors': ['价格突破', '成交量确认', '量价一致性'],
                'rebalance': '周度',
                'expected_ir': 0.6,
                'complexity': 'medium'
            },
            'growth_factor': {
                'name': '成长因子策略',
                'description': '基于多维度成长因子的选股策略',
                'factors': ['营收增长', '盈利增长', 'ROE改善', 'GARP'],
                'rebalance': '季度',
                'expected_ir': 0.7,
                'complexity': 'medium'
            }
        }
        
        return templates


# 便捷函数
def create_strategy_generator() -> PDFDrivenStrategyGenerator:
    """创建策略生成器"""
    return PDFDrivenStrategyGenerator()


def quick_strategy_test(data: pd.DataFrame, strategy_type: str) -> Dict[str, Any]:
    """快速策略测试"""
    
    generator = create_strategy_generator()
    strategy = generator.create_strategy(strategy_type)
    signals = strategy.generate_signals(data)
    
    # 简单回测统计
    if 'signal' in signals.columns and 'close' in data.columns:
        returns = data['close'].pct_change()
        strategy_returns = returns[signals['signal'] == 1]
        
        stats = {
            'total_signals': signals['signal'].sum(),
            'signal_rate': signals['signal'].mean(),
            'avg_return': strategy_returns.mean() if len(strategy_returns) > 0 else 0,
            'win_rate': (strategy_returns > 0).mean() if len(strategy_returns) > 0 else 0,
            'volatility': strategy_returns.std() if len(strategy_returns) > 0 else 0
        }
    else:
        stats = {'error': '无法计算统计指标'}
    
    return {
        'signals': signals,
        'statistics': stats,
        'strategy': strategy
    }


if __name__ == "__main__":
    # 测试示例
    print("=== PDF驱动策略生成器测试===")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'close': 100 + np.random.normal(0, 2, len(dates)).cumsum(),
        'volume': np.random.lognormal(15, 0.5, len(dates)),
        'pe_ratio': np.random.normal(15, 5, len(dates)),
        'roe': np.random.normal(0.12, 0.05, len(dates)),
        'revenue': np.random.normal(1000, 100, len(dates)).cumsum(),
        'eps': np.random.normal(2, 0.5, len(dates))
    }, index=dates)
    
    # 创建策略生成器
    generator = create_strategy_generator()
    
    # 显示可用策略模板
    templates = generator.get_strategy_templates()
    print("\n📋 可用策略模板:")
    for name, info in templates.items():
        print(f"  - {info['name']}: {info['description']}")
        print(f"    因子: {info['factors'][:3]}... | 复杂度: {info['complexity']}")
    
    # 测试单个策略
    print("\n🧪 测试量价突破策略:")
    test_result = quick_strategy_test(test_data, 'volume_price_breakout')
    
    stats = test_result['statistics']
    if 'error' not in stats:
        print(f"  信号数量: {stats['total_signals']}")
        print(f"  信号比例: {stats['signal_rate']:.1%}")
        print(f"  平均收益: {stats['avg_return']:.3f}")
        print(f"  胜率: {stats['win_rate']:.1%}")
    
    # 测试集成策略
    print("\n🔄 测试集成策略:")
    integrated_signals = generator.generate_integrated_strategy(
        test_data, 
        ['barra_multifactor', 'volume_price_breakout', 'growth_factor']
    )
    
    if not integrated_signals.empty:
        print(f"  集成信号生成完成，列数: {len(integrated_signals.columns)}")
        if 'strong_signal' in integrated_signals.columns:
            strong_signal_rate = integrated_signals['strong_signal'].mean()
            print(f"  强信号比例: {strong_signal_rate:.1%}")
    
    print("\n✅ PDF驱动策略生成器集成完成!") 
