# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析师预期收益率加权平均因子与factor_mining模块的集成
Test Weighted Target Return Factor Integration with Factor Mining Module

验证新创建的因子能够被factor_mining模块正确调用和使用
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'factor_mining'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'factor_analyze'))

def test_factor_mining_integration():
    """
    测试factor_mining模块对分析师预期收益率加权平均因子的集成
    """
    print("=" * 60)
    print("测试分析师预期收益率加权平均因子与factor_mining模块集成")
    print("=" * 60)
    
    try:
        # 导入factor_mining集成模块
        from factor_analyze.factor_mining.factor_analyze_integration import (
            FactorAnalyzeRegistry,
            IntegratedFactorMiner
        )
        
        print("✔ 成功导入factor_mining集成模块")
        
        # 创建因子注册器
        registry = FactorAnalyzeRegistry()
        print("✔ 成功创建因子注册器")
        
        # 列出所有可用因子
        available_factors = registry.list_available_factors()
        print(f"\n可用股票因子: {available_factors['stock_factors']}")
        print(f"可用指数因子: {available_factors['index_factors']}")
        
        # 验证分析师预期收益率加权平均因子是否已注册
        if 'weighted_target_return' in available_factors['stock_factors']:
            print("✔ 股票分析师预期收益率加权平均因子已成功注册")
        else:
            print("❌ 股票分析师预期收益率加权平均因子未找到")
            
        if 'weighted_target_return' in available_factors['index_factors']:
            print("✔ 指数分析师预期收益率加权平均因子已成功注册")
        else:
            print("❌ 指数分析师预期收益率加权平均因子未找到")
        
        # 获取因子类
        stock_factor_class = registry.get_stock_factor('weighted_target_return')
        index_factor_class = registry.get_index_factor('weighted_target_return')
        
        if stock_factor_class:
            print(f"✔ 成功获取股票因子类: {stock_factor_class.__name__}")
        else:
            print("❌ 无法获取股票因子类")
            
        if index_factor_class:
            print(f"✔ 成功获取指数因子类: {index_factor_class.__name__}")
        else:
            print("❌ 无法获取指数因子类")
        
        # 获取因子元数据
        stock_metadata = registry.get_factor_metadata('stock', 'weighted_target_return')
        index_metadata = registry.get_factor_metadata('index', 'weighted_target_return')
        
        if stock_metadata:
            print(f"\n股票因子元数据:")
            for key, value in stock_metadata.items():
                print(f"  {key}: {value}")
        
        if index_metadata:
            print(f"\n指数因子元数据:")
            for key, value in index_metadata.items():
                print(f"  {key}: {value}")
        
        # 测试集成因子挖掘器
        print("\n" + "-" * 40)
        print("测试集成因子挖掘器")
        print("-" * 40)
        
        miner = IntegratedFactorMiner()
        print("✔ 成功创建集成因子挖掘器")
        
        # 配置因子加载
        factor_configs = [
            {
                'factor_type': 'stock',
                'factor_id': 'weighted_target_return',
                'params': {
                    'lookback_days': 30,
                    'min_analysts': 3,
                    'weight_method': 'rating'
                }
            },
            {
                'factor_type': 'index',
                'factor_id': 'weighted_target_return',
                'params': {
                    'lookback_days': 30,
                    'min_analysts': 2,
                    'weight_method': 'combined'
                }
            }
        ]
        
        # 加载基础因子
        loaded_factors = miner.load_base_factors(factor_configs)
        print(f"✔ 成功加载 {len(loaded_factors)} 个基础因子")
        
        for i, factor in enumerate(loaded_factors):
            if factor:
                print(f"  因子 {i+1}: {factor.__class__.__name__}")
        
        print("\n" + "=" * 60)
        print("集成测试完成！分析师预期收益率加权平均因子已成功集成到factor_mining模块")
        print("=" * 60)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_factor_creation():
    """
    测试直接创建因子实例
    """
    print("\n" + "=" * 60)
    print("测试直接创建因子实例")
    print("=" * 60)
    
    try:
        # 直接导入因子类
        from factor_analyze.fundamental_factors.weighted_target_return_factor import (
            StockWeightedTargetReturnFactor,
            IndexWeightedTargetReturnFactor,
            create_stock_weighted_target_return_factor,
            create_index_weighted_target_return_factor
        )
        
        print("✔ 成功导入因子类")
        
        # 测试工厂函数
        stock_factor = create_stock_weighted_target_return_factor()
        index_factor = create_index_weighted_target_return_factor()
        
        print(f"✔ 成功创建股票因子实例: {stock_factor.__class__.__name__}")
        print(f"✔ 成功创建指数因子实例: {index_factor.__class__.__name__}")
        
        # 测试因子配置
        print(f"\n股票因子配置:")
        print(f"  适用标的: {stock_factor.target_type}")
        print(f"  回看天数: {stock_factor.lookback_days}")
        print(f"  最小分析师数: {stock_factor.min_analysts}")
        print(f"  权重方法: {stock_factor.weight_method}")
        
        print(f"\n指数因子配置:")
        print(f"  适用标的: {index_factor.target_type}")
        print(f"  回看天数: {index_factor.lookback_days}")
        print(f"  最小分析师数: {index_factor.min_analysts}")
        print(f"  权重方法: {index_factor.weight_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建因子实例时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试分析师预期收益率加权平均因子集成...")
    print(f"测试时间: {datetime.now()}")
    
    # 运行集成测试
    integration_success = test_factor_mining_integration()
    
    # 运行因子创建测试
    creation_success = test_factor_creation()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"factor_mining集成测试: {'✔ 通过' if integration_success else '❌ 失败'}")
    print(f"因子创建测试: {'✔ 通过' if creation_success else '❌ 失败'}")
    
    if integration_success and creation_success:
        print("\n🎉 所有测试通过！分析师预期收益率加权平均因子已成功实现并集成！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
