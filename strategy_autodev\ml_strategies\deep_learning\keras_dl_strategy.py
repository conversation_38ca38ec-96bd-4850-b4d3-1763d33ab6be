import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union
import logging
from abc import ABC, abstractmethod

# PyTorch imports
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    torch = None
    nn = None
    optim = None
    F = None
    DataLoader = None
    TensorDataset = None

from ..base_strategy import BaseMLStrategy

logger = logging.getLogger(__name__)

class MLPModel(nn.Module):
    """多层感知机模型"""
    
    def __init__(self, input_dim: int, hidden_layers: List[int], output_dim: int, 
                 activation: str = 'relu', dropout_rate: float = 0.2, 
                 batch_normalization: bool = False):
        super(MLPModel, self).__init__()
        
        self.layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList() if batch_normalization else None
        self.dropout = nn.Dropout(dropout_rate) if dropout_rate > 0 else None
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        elif activation == 'sigmoid':
            self.activation = nn.Sigmoid()
        else:
            self.activation = nn.ReLU()
        
        # 构建层
        prev_dim = input_dim
        for hidden_dim in hidden_layers:
            self.layers.append(nn.Linear(prev_dim, hidden_dim))
            if batch_normalization:
                self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
            prev_dim = hidden_dim
        
        # 输出层
        self.output_layer = nn.Linear(prev_dim, output_dim)
    
    def forward(self, x):
        for i, layer in enumerate(self.layers):
            x = layer(x)
            if self.batch_norms:
                x = self.batch_norms[i](x)
            x = self.activation(x)
            if self.dropout:
                x = self.dropout(x)
        
        x = self.output_layer(x)
        return x

class CNNModel(nn.Module):
    """卷积神经网络模型"""
    
    def __init__(self, input_dim: int, sequence_length: int, features_per_step: int,
                 conv_filters: List[int], kernel_sizes: List[int], pool_sizes: List[int],
                 hidden_layers: List[int], output_dim: int, activation: str = 'relu',
                 dropout_rate: float = 0.2, batch_normalization: bool = False):
        super(CNNModel, self).__init__()
        
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.batch_norms_conv = nn.ModuleList() if batch_normalization else None
        
        # 卷积层
        in_channels = features_per_step
        for i, (filters, kernel_size, pool_size) in enumerate(zip(conv_filters, kernel_sizes, pool_sizes)):
            self.conv_layers.append(nn.Conv1d(in_channels, filters, kernel_size, padding=kernel_size//2))
            self.pool_layers.append(nn.MaxPool1d(pool_size))
            if batch_normalization:
                self.batch_norms_conv.append(nn.BatchNorm1d(filters))
            in_channels = filters
        
        # 计算展平后的维度
        self.flatten_dim = self._calculate_flatten_dim(sequence_length, conv_filters, pool_sizes)
        
        # 全连接层
        self.fc_layers = nn.ModuleList()
        self.batch_norms_fc = nn.ModuleList() if batch_normalization else None
        
        prev_dim = self.flatten_dim
        for hidden_dim in hidden_layers:
            self.fc_layers.append(nn.Linear(prev_dim, hidden_dim))
            if batch_normalization:
                self.batch_norms_fc.append(nn.BatchNorm1d(hidden_dim))
            prev_dim = hidden_dim
        
        self.output_layer = nn.Linear(prev_dim, output_dim)
        self.dropout = nn.Dropout(dropout_rate) if dropout_rate > 0 else None
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            self.activation = nn.ReLU()
    
    def _calculate_flatten_dim(self, sequence_length: int, conv_filters: List[int], pool_sizes: List[int]):
        """计算展平后的维度"""
        length = sequence_length
        for pool_size in pool_sizes:
            length = length // pool_size
        return length * conv_filters[-1]
    
    def forward(self, x):
        # x shape: (batch_size, sequence_length, features_per_step)
        x = x.transpose(1, 2)  # 转换为 (batch_size, features_per_step, sequence_length)
        
        # 卷积层
        for i, (conv, pool) in enumerate(zip(self.conv_layers, self.pool_layers)):
            x = conv(x)
            if self.batch_norms_conv:
                x = self.batch_norms_conv[i](x)
            x = self.activation(x)
            x = pool(x)
            if self.dropout:
                x = self.dropout(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        for i, fc in enumerate(self.fc_layers):
            x = fc(x)
            if self.batch_norms_fc:
                x = self.batch_norms_fc[i](x)
            x = self.activation(x)
            if self.dropout:
                x = self.dropout(x)
        
        x = self.output_layer(x)
        return x

class LSTMModel(nn.Module):
    """LSTM模型"""
    
    def __init__(self, input_dim: int, sequence_length: int, features_per_step: int,
                 lstm_units: List[int], hidden_layers: List[int], output_dim: int,
                 activation: str = 'relu', dropout_rate: float = 0.2,
                 batch_normalization: bool = False, return_sequences: bool = False):
        super(LSTMModel, self).__init__()
        
        self.lstm_layers = nn.ModuleList()
        self.batch_norms_lstm = nn.ModuleList() if batch_normalization else None
        
        # LSTM层
        input_size = features_per_step
        for i, units in enumerate(lstm_units):
            self.lstm_layers.append(nn.LSTM(input_size, units, batch_first=True, dropout=dropout_rate if i < len(lstm_units) - 1 else 0))
            if batch_normalization:
                self.batch_norms_lstm.append(nn.BatchNorm1d(units))
            input_size = units
        
        # 全连接层
        self.fc_layers = nn.ModuleList()
        self.batch_norms_fc = nn.ModuleList() if batch_normalization else None
        
        prev_dim = lstm_units[-1]
        for hidden_dim in hidden_layers:
            self.fc_layers.append(nn.Linear(prev_dim, hidden_dim))
            if batch_normalization:
                self.batch_norms_fc.append(nn.BatchNorm1d(hidden_dim))
            prev_dim = hidden_dim
        
        self.output_layer = nn.Linear(prev_dim, output_dim)
        self.dropout = nn.Dropout(dropout_rate) if dropout_rate > 0 else None
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            self.activation = nn.ReLU()
    
    def forward(self, x):
        # x shape: (batch_size, sequence_length, features_per_step)
        
        # LSTM层
        for i, lstm in enumerate(self.lstm_layers):
            x, _ = lstm(x)
            if self.batch_norms_lstm and x.size(0) > 1:  # 批量归一化需要batch_size > 1
                x = x.transpose(1, 2)
                x = self.batch_norms_lstm[i](x)
                x = x.transpose(1, 2)
            if self.dropout:
                x = self.dropout(x)
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]
        
        # 全连接层
        for i, fc in enumerate(self.fc_layers):
            x = fc(x)
            if self.batch_norms_fc:
                x = self.batch_norms_fc[i](x)
            x = self.activation(x)
            if self.dropout:
                x = self.dropout(x)
        
        x = self.output_layer(x)
        return x

class KerasDLStrategy(BaseMLStrategy):
    """
    基于PyTorch的深度学习策略（原Keras策略的PyTorch版本）
    支持MLP、CNN、LSTM等网络架构
    """
    
    def __init__(self, 
                 network_type: str = 'mlp',
                 hidden_layers: List[int] = [64, 32],
                 lstm_units: List[int] = [50, 25],
                 conv_filters: List[int] = [32, 64],
                 kernel_sizes: List[int] = [3, 3],
                 pool_sizes: List[int] = [2, 2],
                 activation: str = 'relu',
                 optimizer_name: str = 'adam',
                 learning_rate: float = 0.001,
                 loss_function: str = 'auto',
                 metrics: List[str] = ['mae'],
                 epochs: int = 100,
                 batch_size: int = 32,
                 validation_split: float = 0.2,
                 early_stopping_patience: int = 10,
                 reduce_lr_patience: int = 5,
                 reduce_lr_factor: float = 0.5,
                 min_lr: float = 1e-7,
                 dropout_rate: float = 0.2,
                 batch_normalization: bool = False,
                 l1_reg: float = 0.0,
                 l2_reg: float = 0.0,
                 return_sequences: bool = False,
                 lookback_window: int = 20,
                 **kwargs):
        
        super().__init__(**kwargs)
        
        # 网络配置
        self.network_type = network_type
        self.hidden_layers = hidden_layers
        self.lstm_units = lstm_units
        self.conv_filters = conv_filters
        self.kernel_sizes = kernel_sizes
        self.pool_sizes = pool_sizes
        self.activation = activation
        self.return_sequences = return_sequences
        self.lookback_window = lookback_window
        
        # 训练配置
        self.optimizer_name = optimizer_name
        self.learning_rate = learning_rate
        self.loss_function = loss_function
        self.metrics = metrics
        self.epochs = epochs
        self.batch_size = batch_size
        self.validation_split = validation_split
        
        # 正则化和优化
        self.early_stopping_patience = early_stopping_patience
        self.reduce_lr_patience = reduce_lr_patience
        self.reduce_lr_factor = reduce_lr_factor
        self.min_lr = min_lr
        self.dropout_rate = dropout_rate
        self.batch_normalization = batch_normalization
        self.l1_reg = l1_reg
        self.l2_reg = l2_reg
        
        # 模型相关
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu') if PYTORCH_AVAILABLE else None
        self.input_dim = None
        self.output_dim = 1
        self.history = {'train_loss': [], 'val_loss': []}
        
        logger.info(f"初始化PyTorch深度学习策略，网络类型: {self.network_type}")
    
    def _build_model(self) -> nn.Module:
        """
        构建PyTorch模型
        """
        if not PYTORCH_AVAILABLE:
            raise ImportError("PyTorch不可用，请安装torch")
        
        if self.network_type == 'mlp':
            return self._build_mlp_model()
        elif self.network_type == 'cnn':
            return self._build_cnn_model()
        elif self.network_type == 'lstm':
            return self._build_lstm_model()
        else:
            raise ValueError(f"不支持的网络类型: {self.network_type}")
    
    def _build_mlp_model(self) -> MLPModel:
        """
        构建MLP模型
        """
        return MLPModel(
            input_dim=self.input_dim,
            hidden_layers=self.hidden_layers,
            output_dim=self.output_dim,
            activation=self.activation,
            dropout_rate=self.dropout_rate,
            batch_normalization=self.batch_normalization
        )
    
    def _build_cnn_model(self) -> CNNModel:
        """
        构建CNN模型
        """
        sequence_length = min(self.lookback_window, 20)
        features_per_step = self.input_dim // sequence_length
        
        return CNNModel(
            input_dim=self.input_dim,
            sequence_length=sequence_length,
            features_per_step=features_per_step,
            conv_filters=self.conv_filters,
            kernel_sizes=self.kernel_sizes,
            pool_sizes=self.pool_sizes,
            hidden_layers=self.hidden_layers,
            output_dim=self.output_dim,
            activation=self.activation,
            dropout_rate=self.dropout_rate,
            batch_normalization=self.batch_normalization
        )
    
    def _build_lstm_model(self) -> LSTMModel:
        """
        构建LSTM模型
        """
        sequence_length = min(self.lookback_window, 20)
        features_per_step = self.input_dim // sequence_length
        
        return LSTMModel(
            input_dim=self.input_dim,
            sequence_length=sequence_length,
            features_per_step=features_per_step,
            lstm_units=self.lstm_units,
            hidden_layers=self.hidden_layers,
            output_dim=self.output_dim,
            activation=self.activation,
            dropout_rate=self.dropout_rate,
            batch_normalization=self.batch_normalization,
            return_sequences=self.return_sequences
        )
    
    def _get_optimizer(self, model: nn.Module) -> optim.Optimizer:
        """
        获取优化器
        """
        # 添加L1和L2正则化
        weight_decay = self.l2_reg
        
        if self.optimizer_name == 'adam':
            return optim.Adam(model.parameters(), lr=self.learning_rate, weight_decay=weight_decay)
        elif self.optimizer_name == 'sgd':
            return optim.SGD(model.parameters(), lr=self.learning_rate, momentum=0.9, weight_decay=weight_decay)
        elif self.optimizer_name == 'rmsprop':
            return optim.RMSprop(model.parameters(), lr=self.learning_rate, weight_decay=weight_decay)
        else:
            return optim.Adam(model.parameters(), lr=self.learning_rate, weight_decay=weight_decay)
    
    def _get_criterion(self):
        """
        获取损失函数
        """
        if self.loss_function == 'auto':
            if self.target_column == 'price_direction':
                return nn.BCEWithLogitsLoss()
            else:
                return nn.MSELoss()
        elif self.loss_function == 'mse':
            return nn.MSELoss()
        elif self.loss_function == 'mae':
            return nn.L1Loss()
        elif self.loss_function == 'binary_crossentropy':
            return nn.BCEWithLogitsLoss()
        else:
            return nn.MSELoss()
    
    def _prepare_data_for_network(self, X: np.ndarray) -> np.ndarray:
        """
        为特定网络类型准备数据
        """
        if self.network_type == 'mlp':
            return X
        
        if self.network_type in ['cnn', 'lstm']:
            # 重塑为序列数据
            sequence_length = min(self.lookback_window, 20)
            features_per_step = X.shape[1] // sequence_length
            
            # 确保可以整除
            total_features = sequence_length * features_per_step
            X_reshaped = X[:, :total_features]
            
            return X_reshaped.reshape(-1, sequence_length, features_per_step)
        
        return X
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练PyTorch模型
        """
        try:
            if not PYTORCH_AVAILABLE:
                return {'success': False, 'message': 'PyTorch不可用'}
            
            # 更新输入维度
            self.input_dim = X.shape[1]
            
            # 构建模型
            self.model = self._build_model()
            self.model.to(self.device)
            
            # 获取优化器和损失函数
            self.optimizer = self._get_optimizer(self.model)
            self.criterion = self._get_criterion()
            
            # 准备数据
            X_prepared = self._prepare_data_for_network(X)
            
            # 转换为张量
            X_tensor = torch.FloatTensor(X_prepared).to(self.device)
            y_tensor = torch.FloatTensor(y.reshape(-1, 1)).to(self.device)
            
            # 分割训练和验证数据
            val_size = int(len(X_tensor) * self.validation_split)
            train_size = len(X_tensor) - val_size
            
            X_train, X_val = X_tensor[:train_size], X_tensor[train_size:]
            y_train, y_val = y_tensor[:train_size], y_tensor[train_size:]
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            
            val_dataset = TensorDataset(X_val, y_val)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
            
            # 早停和学习率调度
            best_val_loss = float('inf')
            patience_counter = 0
            lr_patience_counter = 0
            
            logger.info(f"开始训练PyTorch模型，数据形状: {X_prepared.shape}")
            
            # 训练循环
            for epoch in range(self.epochs):
                # 训练阶段
                self.model.train()
                train_loss = 0.0
                
                for batch_X, batch_y in train_loader:
                    self.optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    
                    # L1正则化
                    l1_loss = 0
                    if self.l1_reg > 0:
                        for param in self.model.parameters():
                            l1_loss += torch.sum(torch.abs(param))
                    
                    loss = self.criterion(outputs, batch_y) + self.l1_reg * l1_loss
                    loss.backward()
                    self.optimizer.step()
                    
                    train_loss += loss.item()
                
                train_loss /= len(train_loader)
                
                # 验证阶段
                self.model.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = self.model(batch_X)
                        loss = self.criterion(outputs, batch_y)
                        val_loss += loss.item()
                
                val_loss /= len(val_loader)
                
                # 记录历史
                self.history['train_loss'].append(train_loss)
                self.history['val_loss'].append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    lr_patience_counter = 0
                    # 保存最佳模型
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    lr_patience_counter += 1
                
                # 学习率调度
                if lr_patience_counter >= self.reduce_lr_patience:
                    for param_group in self.optimizer.param_groups:
                        old_lr = param_group['lr']
                        new_lr = max(old_lr * self.reduce_lr_factor, self.min_lr)
                        param_group['lr'] = new_lr
                        if new_lr != old_lr:
                            logger.info(f"学习率从 {old_lr:.6f} 降低到 {new_lr:.6f}")
                    lr_patience_counter = 0
                
                # 早停
                if patience_counter >= self.early_stopping_patience:
                    logger.info(f"早停在第 {epoch+1} 轮")
                    break
                
                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 恢复最佳模型
            if hasattr(self, 'best_model_state'):
                self.model.load_state_dict(self.best_model_state)
            
            # 训练结果
            result = {
                'model_summary': self._get_model_summary(),
                'history': self.history,
                'final_loss': self.history['train_loss'][-1],
                'final_val_loss': self.history['val_loss'][-1],
                'epochs_trained': len(self.history['train_loss']),
                'best_val_loss': best_val_loss
            }
            
            logger.info(f"PyTorch模型训练完成，最佳验证损失: {best_val_loss:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"PyTorch模型训练失败: {e}")
            raise
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        PyTorch模型预测
        """
        try:
            if not PYTORCH_AVAILABLE or self.model is None:
                # 简化预测
                return np.random.random(len(X)) * 0.1
            
            # 准备数据
            X_prepared = self._prepare_data_for_network(X)
            X_tensor = torch.FloatTensor(X_prepared).to(self.device)
            
            # 预测
            self.model.eval()
            with torch.no_grad():
                predictions = self.model(X_tensor)
                predictions = predictions.cpu().numpy().flatten()
            
            return predictions
            
        except Exception as e:
            logger.error(f"PyTorch预测失败: {e}")
            raise
    
    def _get_model_summary(self) -> str:
        """
        获取模型摘要
        """
        if not PYTORCH_AVAILABLE or self.model is None:
            return "模型摘要不可用"
        
        try:
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            summary = f"模型类型: {self.network_type}\n"
            summary += f"总参数数: {total_params:,}\n"
            summary += f"可训练参数数: {trainable_params:,}\n"
            summary += f"模型结构:\n{str(self.model)}"
            
            return summary
        except:
            return "无法生成模型摘要"
    
    def save_model(self, filepath: str):
        """
        保存模型
        """
        if PYTORCH_AVAILABLE and self.model is not None:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'model_config': {
                    'network_type': self.network_type,
                    'input_dim': self.input_dim,
                    'output_dim': self.output_dim,
                    'hidden_layers': self.hidden_layers,
                    'lstm_units': self.lstm_units,
                    'conv_filters': self.conv_filters,
                    'kernel_sizes': self.kernel_sizes,
                    'pool_sizes': self.pool_sizes,
                    'activation': self.activation,
                    'dropout_rate': self.dropout_rate,
                    'batch_normalization': self.batch_normalization,
                    'return_sequences': self.return_sequences,
                    'lookback_window': self.lookback_window
                },
                'history': self.history
            }, filepath)
            logger.info(f"模型已保存到: {filepath}")
        else:
            logger.warning("无法保存模型：PyTorch不可用或模型未训练")
    
    def load_model(self, filepath: str):
        """
        加载模型
        """
        if PYTORCH_AVAILABLE:
            checkpoint = torch.load(filepath, map_location=self.device)
            
            # 恢复配置
            config = checkpoint['model_config']
            self.network_type = config['network_type']
            self.input_dim = config['input_dim']
            self.output_dim = config['output_dim']
            self.hidden_layers = config['hidden_layers']
            self.lstm_units = config['lstm_units']
            self.conv_filters = config['conv_filters']
            self.kernel_sizes = config['kernel_sizes']
            self.pool_sizes = config['pool_sizes']
            self.activation = config['activation']
            self.dropout_rate = config['dropout_rate']
            self.batch_normalization = config['batch_normalization']
            self.return_sequences = config['return_sequences']
            self.lookback_window = config['lookback_window']
            
            # 重建模型
            self.model = self._build_model()
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            
            # 恢复历史
            self.history = checkpoint.get('history', {'train_loss': [], 'val_loss': []})
            
            logger.info(f"模型已从 {filepath} 加载")
        else:
            logger.warning("无法加载模型：PyTorch不可用")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        """
        base_info = super().get_model_info()
        
        pytorch_info = {
            'network_type': self.network_type,
            'optimizer': self.optimizer_name,
            'loss_function': self.loss_function,
            'batch_normalization': self.batch_normalization,
            'l1_reg': self.l1_reg,
            'l2_reg': self.l2_reg,
            'pytorch_available': PYTORCH_AVAILABLE,
            'device': str(self.device) if self.device else None
        }
        
        if PYTORCH_AVAILABLE and self.model is not None:
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            pytorch_info.update({
                'total_params': total_params,
                'trainable_params': trainable_params,
                'model_summary': self._get_model_summary()
            })
        
        base_info.update(pytorch_info)
        return base_info