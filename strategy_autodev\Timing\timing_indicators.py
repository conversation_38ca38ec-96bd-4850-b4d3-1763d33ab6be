# -*- coding: utf-8 -*-
"""
择时指标工具库
提供各种技术分析指标和择时信号生成工具
包含Fisher Transform择时策略的完整实现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import warnings
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adapters.stock_data_adapter import StockDataAdapter
    from Utils.logger import logger
except ImportError:
    # 如果导入失败，提供简单的替代实现
    class StockDataAdapter:
        def get_stock_data(self, symbol, start_date, end_date, adjust='qfq'):
            print(f"警告：无法导入StockDataAdapter，请检查adapters目录")
            return pd.DataFrame()
    
    from loguru import logger

warnings.filterwarnings('ignore')


class TimingIndicators:
    """
    择时指标工具库
    提供各种技术分析指标的计算方法
    """
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """
        计算RSI相对强弱指数
        """
        delta = data.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=window).mean()
        avg_loss = loss.rolling(window=window).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        Args:
            data: 价格序列
            fast: 快线参数
            slow: 慢线参数
            signal: 信号线参数
            
        Returns:
            包含MACD、Signal、Hist的字典
        """
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            'MACD': macd_line,
            'Signal': signal_line,
            'Histogram': histogram
        }
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        计算布林带指标
        
        Args:
            data: 价格序列
            window: 移动平均窗口
            std_dev: 标准差倍数
            
        Returns:
            包含上轨、中轨、下轨的字典
        """
        middle = data.rolling(window=window).mean()
        std = data.rolling(window=window).std()
        
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'Upper': upper,
            'Middle': middle,
            'Lower': lower
        }
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_window: int = 14, d_window: int = 3) -> Dict[str, pd.Series]:
        """
        计算随机指标KD
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_window: K值计算窗口
            d_window: D值计算窗口
            
        Returns:
            包含%K、D的字典
        """
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window).mean()
        
        return {
            'K': k_percent,
            'D': d_percent
        }
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        计算威廉指标%R
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算窗口
            
        Returns:
            威廉指标序列
        """
        highest_high = high.rolling(window=window).max()
        lowest_low = low.rolling(window=window).min()
        
        wr = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return wr
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        计算平均真实波幅ATR
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算窗口
            
        Returns:
            ATR指标序列
        """
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = tr.rolling(window=window).mean()
        
        return atr
    
    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
        """
        计算商品渠道指标CCI
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 计算窗口
            
        Returns:
            CCI指标序列
        """
        typical_price = (high + low + close) / 3
        sma = typical_price.rolling(window=window).mean()
        mad = typical_price.rolling(window=window).apply(lambda x: np.abs(x - x.mean()).mean())
        
        cci = (typical_price - sma) / (0.015 * mad)
        
        return cci
    
    @staticmethod
    def fisher_transform(high: pd.Series, low: pd.Series, window: int = 10) -> Dict[str, pd.Series]:
        """
        计算Fisher Transform指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            window: 计算窗口
            
        Returns:
            包含Fisher和Signal的字典
        """
        # 计算中间价（最高与最低价的均值）
        median = (high + low) / 2
        
        # 计算中间价的N日最大值和最小值
        max_median = median.rolling(window).max()
        min_median = median.rolling(window).min()
        
        # 标准化价格到[-1,1]区间
        norm = pd.Series(0.0, index=median.index)
        mask = (max_median != min_median)
        norm[mask] = 2 * ((median - min_median) / (max_median - min_median) - 0.5)
        
        # 限制范围避免极端值
        norm = norm.clip(-0.999, 0.999)
        
        # Fisher变换（核心公式）
        fisher = 0.5 * np.log((1 + norm) / (1 - norm))
        
        # 添加信号线（Fisher 1日滞后作为信号线）
        signal = fisher.shift(1)
        
        return {
            'Fisher': fisher,
            'Signal': signal
        }


class TimingSignals:
    """
    择时信号生成器
    基于技术指标生成买卖信号
    """
    
    def __init__(self):
        self.indicators = TimingIndicators()
    
    def rsi_signals(self, data: pd.Series, window: int = 14, 
                   oversold: float = 30, overbought: float = 70) -> pd.Series:
        """
        基于RSI生成择时信号
        
        Args:
            data: 价格序列
            window: RSI计算窗口
            oversold: 超卖阈值
            overbought: 超买阈值
            
        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无操作)
        """
        rsi = self.indicators.rsi(data, window)
        
        signals = pd.Series(0, index=data.index)
        signals[rsi < oversold] = 1  # 超卖买入
        signals[rsi > overbought] = -1  # 超买卖出
        
        return signals
    
    def macd_signals(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """
        基于MACD生成择时信号
        
        Args:
            data: 价格序列
            fast: 快线参数
            slow: 慢线参数
            signal: 信号线参数
            
        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无操作)
        """
        macd_data = self.indicators.macd(data, fast, slow, signal)
        macd_line = macd_data['MACD']
        signal_line = macd_data['Signal']
        
        signals = pd.Series(0, index=data.index)
        
        # MACD金叉买入，死叉卖�?
        macd_cross = macd_line - signal_line
        signals[(macd_cross > 0) & (macd_cross.shift(1) <= 0)] = 1
        signals[(macd_cross < 0) & (macd_cross.shift(1) >= 0)] = -1
        
        return signals
    
    def bollinger_signals(self, data: pd.Series, window: int = 20, std_dev: float = 2.0) -> pd.Series:
        """
        基于布林带生成择时信号
        
        Args:
            data: 价格序列
            window: 移动平均窗口
            std_dev: 标准差倍数
            
        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无操作)
        """
        bb = self.indicators.bollinger_bands(data, window, std_dev)
        
        signals = pd.Series(0, index=data.index)
        
        # 价格触及下轨买入，触及上轨卖?
        signals[data <= bb['Lower']] = 1
        signals[data >= bb['Upper']] = -1
        
        return signals
    
    def stochastic_signals(self, high: pd.Series, low: pd.Series, close: pd.Series,
                          k_window: int = 14, d_window: int = 3,
                          oversold: float = 20, overbought: float = 80) -> pd.Series:
        """
        基于随机指标生成择时信号
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_window: K值计算窗口
            d_window: D值计算窗口
            oversold: 超卖阈值
            overbought: 超买阈值
            
        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无操作)
        """
        stoch = self.indicators.stochastic(high, low, close, k_window, d_window)
        k_line = stoch['K']
        d_line = stoch['D']
        
        signals = pd.Series(0, index=close.index)
        
        # K线上穿D线且在超卖区买入
        signals[(k_line > d_line) & (k_line.shift(1) <= d_line.shift(1)) & (k_line < oversold)] = 1
        
        # K线下穿D线且在超买区卖出
        signals[(k_line < d_line) & (k_line.shift(1) >= d_line.shift(1)) & (k_line > overbought)] = -1
        
        return signals
    
    def fisher_transform_signals(self, high: pd.Series, low: pd.Series, window: int = 10,
                                   overbought: float = 2.5, oversold: float = -2.5) -> pd.Series:
        """
        基于Fisher Transform生成择时信号
        
        Args:
            high: 最高价序列
            low: 最低价序列
            window: Fisher Transform计算窗口
            overbought: 超买阈值
            oversold: 超卖阈值
            
        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无操作)
        """
        fisher_data = self.indicators.fisher_transform(high, low, window)
        fisher_line = fisher_data['Fisher']
        signal_line = fisher_data['Signal']
        
        signals = pd.Series(0, index=high.index)
        
        # Fisher线上穿信号线买入，下穿卖�?
        cross_up = (fisher_line > signal_line) & (fisher_line.shift(1) <= signal_line.shift(1))
        cross_down = (fisher_line < signal_line) & (fisher_line.shift(1) >= signal_line.shift(1))
        
        signals[cross_up] = 1
        signals[cross_down] = -1
        
        # 在极值区域信号增�?
        extreme_buy = cross_up & (fisher_line < oversold)
        extreme_sell = cross_down & (fisher_line > overbought)
        
        signals[extreme_buy] = 2  # 强买入信号
        signals[extreme_sell] = -2  # 强卖出信号
        
        return signals
    
    def multiple_signals_consensus(self, signals_dict: Dict[str, pd.Series], 
                                 threshold: float = 0.5) -> pd.Series:
        """
        多信号一致性判断
        
        Args:
            signals_dict: 多个信号序列的字典
            threshold: 一致性阈值
            
        Returns:
            综合信号序列
        """
        if not signals_dict:
            return pd.Series()
        
        # 将所有信号对齐
        all_signals = pd.DataFrame(signals_dict)
        
        # 计算买入和卖出信号的比例
        buy_ratio = (all_signals == 1).sum(axis=1) / len(signals_dict)
        sell_ratio = (all_signals == -1).sum(axis=1) / len(signals_dict)
        
        # 生成综合信号
        consensus_signals = pd.Series(0, index=all_signals.index)
        consensus_signals[buy_ratio >= threshold] = 1
        consensus_signals[sell_ratio >= threshold] = -1
        
        return consensus_signals


class MarketRegimeDetector:
    """
    市场状态检测器
    识别不同的市场环境（牛市、熊市、震荡市）
    """
    
    @staticmethod
    def trend_strength(data: pd.Series, window: int = 20) -> pd.Series:
        """
        计算趋势强度
        
        Args:
            data: 价格序列
            window: 计算窗口
            
        Returns:
            趋势强度序列 (0-1之间，越接近1趋势越强)
        """
        returns = data.pct_change()
        
        # 计算价格方向一致性
        direction_consistency = returns.rolling(window=window).apply(
            lambda x: np.abs(np.sum(np.sign(x))) / len(x)
        )
        
        return direction_consistency
    
    @staticmethod
    def volatility_regime(data: pd.Series, window: int = 20, 
                         low_vol_threshold: float = 0.01, 
                         high_vol_threshold: float = 0.03) -> pd.Series:
        """
        识别波动率状态
        
        Args:
            data: 价格序列
            window: 计算窗口
            low_vol_threshold: 低波动阈值
            high_vol_threshold: 高波动阈值
            
        Returns:
            波动率状态序列 ('low', 'medium', 'high')
        """
        returns = data.pct_change()
        volatility = returns.rolling(window=window).std()
        
        regime = pd.Series('medium', index=data.index)
        regime[volatility < low_vol_threshold] = 'low'
        regime[volatility > high_vol_threshold] = 'high'
        
        return regime
    
    @staticmethod 
    def market_regime(data: pd.Series, short_window: int = 50, long_window: int = 200) -> pd.Series:
        """
        识别市场大趋势状态
        
        Args:
            data: 价格序列
            short_window: 短期均线周期
            long_window: 长期均线周期
            
        Returns:
            市场状态序列 ('bull', 'bear', 'sideways')
        """
        ma_short = data.rolling(window=short_window).mean()
        ma_long = data.rolling(window=long_window).mean()
        
        regime = pd.Series('sideways', index=data.index)
        
        # 短期均线在长期均线之上为牛市
        regime[ma_short > ma_long] = 'bull'
        
        # 短期均线在长期均线之下为熊市
        regime[ma_short < ma_long] = 'bear'
        
        return regime


class FisherTransformTimer:
    """
    Fisher Transform择时策略
    基于Fisher Transform指标生成买卖信号的择时策略
    融合了完整的数据获取、信号分析和可视化功能
    """
    
    def __init__(self, window: int = 10, overbought_threshold: float = 2.5, 
                 oversold_threshold: float = -2.5):
        """
        初始化Fisher Transform择时策略
        
        Args:
            window: Fisher Transform计算窗口
            overbought_threshold: 超买阈值
            oversold_threshold: 超卖阈值
        """
        self.window = window
        self.overbought_threshold = overbought_threshold
        self.oversold_threshold = oversold_threshold
        self.indicators = TimingIndicators()
        self.data_adapter = StockDataAdapter()
        
    def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取股票数据，替代聚宽的get_price函数
        
        Args:
            stock_code: 股票代码 (如'600519.XSHG')
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            包含OHLC数据的DataFrame
        """
        try:
            # 处理股票代码中的交易所标识
            if '.XSHG' in stock_code:
                ts_code = stock_code.replace('.XSHG', '.SH')
            elif '.XSHE' in stock_code:
                ts_code = stock_code.replace('.XSHE', '.SZ')
            else:
                ts_code = stock_code
            
            # 调用数据适配器获取数据
            data = self.data_adapter.get_stock_data(
                symbol=ts_code,
                start_date=start_date,
                end_date=end_date,
                adjust='qfq'  # 调整方式
            )
            
            if data.empty:
                logger.warning(f"未能获取股票数据: {stock_code}")
                return pd.DataFrame()
            
            # 确保列名小写
            data.columns = data.columns.str.lower()
            
            return data
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_fisher_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算Fisher Transform信号
        
        Args:
            data: 包含OHLC数据的DataFrame
            
        Returns:
            包含Fisher Transform指标和信号的DataFrame
        """
        if data.empty:
            return pd.DataFrame()
        
        # 使用TimingIndicators中的fisher_transform方法
        fisher_data = self.indicators.fisher_transform(data['high'], data['low'], self.window)
        
        # 将结果合并到原数据中
        result = data.copy()
        result['fisher'] = fisher_data['Fisher']
        result['signal'] = fisher_data['Signal']
        
        # 生成交易信号
        result['buy_signal'] = 0
        result['sell_signal'] = 0
        result['position_signal'] = 0
        
        # Fisher线上穿信号线 -> 买入信号
        cross_up = ((result['fisher'] > result['signal']) & 
                   (result['fisher'].shift(1) <= result['signal'].shift(1)))
        
        # Fisher线下穿信号线 -> 卖出信号
        cross_down = ((result['fisher'] < result['signal']) & 
                     (result['fisher'].shift(1) >= result['signal'].shift(1)))
        
        # 标记信号点
        result.loc[cross_up, 'buy_signal'] = 1
        result.loc[cross_down, 'sell_signal'] = 1
        
        # 生成持仓信号 (1: 买入, -1: 卖出, 0: 无操作)
        result.loc[cross_up, 'position_signal'] = 1
        result.loc[cross_down, 'position_signal'] = -1
        
        # 极值区域信号增强
        extreme_buy = (cross_up & (result['fisher'] < self.oversold_threshold))
        extreme_sell = (cross_down & (result['fisher'] > self.overbought_threshold))
        
        result.loc[extreme_buy, 'position_signal'] = 2  # 强买入信号
        result.loc[extreme_sell, 'position_signal'] = -2  # 强卖出信号
        
        return result
    
    def analyze_signals(self, result: pd.DataFrame) -> Dict:
        """
        分析交易信号
        
        Args:
            result: 包含信号的DataFrame
            
        Returns:
            信号分析结果字典
        """
        if result.empty:
            return {}
        
        # 找出所有信号点
        buy_signals = result[result['buy_signal'] == 1]
        sell_signals = result[result['sell_signal'] == 1]
        
        analysis = {
            'total_buy_signals': len(buy_signals),
            'total_sell_signals': len(sell_signals),
            'buy_signal_dates': buy_signals.index.tolist(),
            'sell_signal_dates': sell_signals.index.tolist(),
            'buy_signal_details': [],
            'sell_signal_details': []
        }
        
        # 买入信号详情
        for date in buy_signals.index:
            analysis['buy_signal_details'].append({
                'date': date,
                'fisher_value': result.loc[date, 'fisher'],
                'price': result.loc[date, 'close'],
                'signal_strength': '???' if result.loc[date, 'position_signal'] == 2 else ""
            })
        
        # 卖出信号详情
        for date in sell_signals.index:
            analysis['sell_signal_details'].append({
                'date': date,
                'fisher_value': result.loc[date, 'fisher'],
                'price': result.loc[date, 'close'],
                'signal_strength': '???' if result.loc[date, 'position_signal'] == -2 else ""
            })
        
        return analysis
    
    def plot_fisher_analysis(self, result: pd.DataFrame, stock_code: str, 
                           save_path: Optional[str] = None) -> None:
        """
        绘制Fisher Transform分析图表
        
        Args:
            result: 包含Fisher Transform数据的DataFrame
            stock_code: 股票代码
            save_path: 图表保存路径
        """
        if result.empty:
            logger.warning("未能绘制Fisher Transform分析图表")
            return
        
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        
        # 绘制Fisher Transform指标
        ax1.plot(result.index, result['fisher'], 
                label=f'Fisher Transform (窗口={self.window})', color='blue', linewidth=1.5)
        ax1.plot(result.index, result['signal'], 
                label='???', color='orange', linestyle='--', linewidth=1.5)
        ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        ax1.axhline(y=self.overbought_threshold, color='red', linestyle=':', alpha=0.7, label='超买')
        ax1.axhline(y=self.oversold_threshold, color='green', linestyle=':', alpha=0.7, label='超卖')
        
        # 标记极端值区域
        ax1.fill_between(result.index, self.overbought_threshold, 3, 
                        color='red', alpha=0.1, label='超买区域')
        ax1.fill_between(result.index, self.oversold_threshold, -3, 
                        color='green', alpha=0.1, label='超卖区域')
        
        # 标记信号交叉点
        buy_points = result[result['buy_signal'] == 1]
        sell_points = result[result['sell_signal'] == 1]
        
        if not buy_points.empty:
            ax1.scatter(buy_points.index, buy_points['fisher'], 
                       marker='^', color='green', s=100, label='买入信号', zorder=5)
        
        if not sell_points.empty:
            ax1.scatter(sell_points.index, sell_points['fisher'], 
                       marker='v', color='red', s=100, label='卖出信号', zorder=5)
        
        ax1.set_title(f'{stock_code} Fisher Transform 分析')
        ax1.set_ylabel('Fisher值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制股价对比
        ax2.plot(result.index, result['close'], label='收盘价', color='black', linewidth=1.5)
        
        # 在价格图上标记信号点
        if not buy_points.empty:
            ax2.scatter(buy_points.index, buy_points['close'], 
                       marker='^', color='green', s=100, label='买入信号', zorder=5)
        
        if not sell_points.empty:
            ax2.scatter(sell_points.index, sell_points['close'], 
                       marker='v', color='red', s=100, label='卖出信号', zorder=5)
        
        ax2.set_title('股价走势')
        ax2.set_ylabel('价格')
        ax2.set_xlabel('日期')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def run_strategy(self, stock_code: str, start_date: str, end_date: str, 
                    plot: bool = True, save_path: Optional[str] = None) -> Dict:
        """
        运行Fisher Transform择时策略
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            plot: 是否绘制图表
            save_path: 图表保存路径
            
        Returns:
            策略运行结果
        """
        logger.info(f"开始运行Fisher Transform择时策略: {stock_code}")
        
        # 获取股票数据
        data = self.get_stock_data(stock_code, start_date, end_date)
        if data.empty:
            return {'error': '无法获取股票数据'}
        
        # 计算Fisher Transform信号
        result = self.calculate_fisher_signals(data)
        if result.empty:
            return {'error': '无法计算Fisher Transform信号'}
        
        # 分析信号
        analysis = self.analyze_signals(result)
        
        # 绘制图表
        if plot:
            self.plot_fisher_analysis(result, stock_code, save_path)
        
        # 打印信号分析
        self._print_signal_analysis(analysis)
        
        return {
            'stock_code': stock_code,
            'period': f"{start_date} 至 {end_date}",
            'data': result,
            'analysis': analysis,
            'strategy_params': {
                'window': self.window,
                'overbought_threshold': self.overbought_threshold,
                'oversold_threshold': self.oversold_threshold
            }
        }
    
    def _print_signal_analysis(self, analysis: Dict) -> None:
        """
        打印信号分析结果
        
        Args:
            analysis: 信号分析结果
        """
        print("\n" + "="*60)
        print("Fisher Transform 择时策略分析")
        print("="*60)
        print(f"买入信号总数: {analysis.get('total_buy_signals', 0)}")
        print(f"卖出信号总数: {analysis.get('total_sell_signals', 0)}")
        
        print("\n买入信号详情:")
        print("-"*40)
        for signal in analysis.get('buy_signal_details', []):
            date_str = signal['date'].strftime('%Y-%m-%d') if hasattr(signal['date'], 'strftime') else str(signal['date'])
            strength = "???" if signal['signal_strength'] == '???' else ""
            print(f"{strength}买入信号 @ {date_str} | Fisher值: {signal['fisher_value']:.2f} | 价格: {signal['price']:.2f}")
        
        print("\n卖出信号详情:")
        print("-"*40)
        for signal in analysis.get('sell_signal_details', []):
            date_str = signal['date'].strftime('%Y-%m-%d') if hasattr(signal['date'], 'strftime') else str(signal['date'])
            strength = "???" if signal['signal_strength'] == '???' else ""
            print(f"{strength}卖出信号 @ {date_str} | Fisher值: {signal['fisher_value']:.2f} | 价格: {signal['price']:.2f}")
        
        print("\n" + "="*60)
        print(f"超买阈值: {self.overbought_threshold} | 超卖阈值: {self.oversold_threshold}")
        print("="*60)


if __name__ == "__main__":
    # 测试示例
    print("=== 择时指标工具库测试 ===")
    
    # 生成示例数据
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=252, freq='D')
    prices = 100 * np.cumprod(1 + np.random.normal(0.001, 0.02, 252))
    high = prices * (1 + np.abs(np.random.normal(0, 0.01, 252)))
    low = prices * (1 - np.abs(np.random.normal(0, 0.01, 252)))
    
    data = pd.Series(prices, index=dates)
    high_data = pd.Series(high, index=dates)
    low_data = pd.Series(low, index=dates)
    
    # 创建测试DataFrame
    test_data = pd.DataFrame({
        'high': high,
        'low': low,
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 252)
    }, index=dates)
    
    # 测试指标计算
    indicators = TimingIndicators()
    signals = TimingSignals()
    regime = MarketRegimeDetector()
    
    print("RSI指标:", indicators.rsi(data).tail(5))
    print("MACD指标:", list(indicators.macd(data)['MACD'].tail(3)))
    print("RSI信号:", signals.rsi_signals(data).value_counts())
    print("市场状态:", regime.market_regime(data).value_counts())
    
    print("\n=== Fisher Transform择时策略测试 ===")
    
    # 测试Fisher Transform择时策略
    try:
        fisher_timer = FisherTransformTimer(window=10, overbought_threshold=2.0, oversold_threshold=-2.0)
        
        # 使用测试数据进行信号计算
        result = fisher_timer.calculate_fisher_signals(test_data)
        if not result.empty:
            analysis = fisher_timer.analyze_signals(result)
            print(f"买入信号数: {analysis.get('total_buy_signals', 0)}")
            print(f"卖出信号数: {analysis.get('total_sell_signals', 0)}")
            
            # 显示最新的Fisher值
            latest_fisher = result['fisher'].iloc[-1]
            latest_signal = result['signal'].iloc[-1]
            print(f"最新Fisher值: {latest_fisher:.4f}")
            print(f"最新信号线: {latest_signal:.4f}")
        else:
            print("无法计算Fisher Transform信号")
            
    except Exception as e:
        print(f"Fisher Transform择时策略测试失败: {e}")
        print("请检查adapters目录下是否存在StockDataAdapter实现")
    
    print("\n使用说明:")
    print("1. 对于实际股票数据，请使用 FisherTransformTimer.run_strategy() 方法")
    print("2. 示例: fisher_timer.run_strategy('600519.XSHG', '2023-01-01', '2023-12-31')")
    print("3. 确保adapters目录下有正确的StockDataAdapter实现")
