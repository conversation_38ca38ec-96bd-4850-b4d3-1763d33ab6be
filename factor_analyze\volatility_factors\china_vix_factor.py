# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国波动率指数(CVIX)计算器 - 增强版
本文件实现了针对中国市场的波动率指数（CVIX）的计算。

核心功能
============
CVIX，即中国波动率指数，旨在衡量市场对未来30天波动率的预期。

1. 数据来源
   - 基于主要A股ETF期权（如沪深300ETF、上证50ETF等）的真实交易数据。
   - 采用多数据源融合，包括Tushare等，确保数据覆盖面和准确性。
   - 动态选择流动性最好的合约，以替代固定的近月和次近月合约，更贴近VIX编制思想。

2. 与传统VIX的区别
   - VIX指数基于无风险利率和未来30天预期股息率。
   - CVIX在计算中，使用SHIBOR利率作为无风险利率的近似，并对数据质量进行严格清洗。

3. 计算逻辑
   - 筛选核心ETF期权标的，如50ETF、300ETF、500ETF、1000ETF等。
   - 获取期权日行情和基本信息。
   - 进行数据预处理、清洗和验证。
   - 分别计算各成分期权的引伸波幅，并加权合成最终的CVIX指数。

4. 产出与应用
   - 生成代表全市场情绪的综合CVIX指数。
   - 提供各主要ETF的独立VIX指数，用于不同板块的风险评估。
   - 可用于量化策略，作为市场择时、风险管理的输入因子。

Author: FinGPT Team
Date: 2024
============
"""

import time
import sys
import os
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings
from abc import ABC, abstractmethod

# 确保项目根目录在Python解释器的搜索路径中
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import numpy as np
import pandas as pd
from scipy.interpolate import interp1d
from scipy import stats

# 动态导入
try:
    from ..base_factor import BaseFactor
except ImportError:
    from base_factor import BaseFactor

# 动态导入数据适配器
try:
    from Utils.TushareUtil import get_trade_dates_by_count
    from adapters.option_data_adapter import OptionDataAdapter
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    from adapters.futures_data_adapter import FuturesDataAdapter
except ImportError as e:
    print(f"依赖导入失败: {e}")
    # 如果相关模块导入失败，定义临时的占位符类
    class OptionDataAdapter: pass
    from adapters import StockDataAdapter
    from adapters import MarketDataAdapter
    class FuturesDataAdapter: pass

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class CVIXConfig:
    """
    CVIX计算的配置类
    定义了数据质量控制和目标策略的参数
    
    这个类作为所有可调参数的中心来源，
    使得修改和维护更加容易。
    """
    
    # 数据质量配置 - 用于清洗和过滤期权数据
    DATA_QUALITY_CONFIG = {
        'time_to_expiry': {
            'min_days': 3,            # 过滤剩余到期日过短的期权
            'max_days': 60,           # 过滤剩余到期日过长的期权
            'target_days': [30, 60]   # VIX计算的目标到期日范围
        },
        'moneyness': {
            'min_ratio': 0.8,         # 过滤深度价内/价外的期权
            'max_ratio': 1.2,         # 过滤深度价内/价外的期权
            'otm_threshold': 0.05     # 虚值程度阈值
        },
        'price_bounds': {
            'min_price': 0.001,       # 过滤异常低价的期权
            'max_price': 1000,        # 过滤异常高价的期权
            'max_price_change': 0.5   # 过滤日内价格变动过大的期权
        },
        'volume_filters': {
            'min_daily_volume': 10,   # 过滤日成交量过低的合约
            'min_avg_volume': 50,     # 过滤平均成交量过低的合约 
            'volume_percentile': 0.1  # 交易量分位数过滤
        },
        'outlier_detection': {
            'z_score_threshold': 3,   # Z-score异常值检测阈值
            'iqr_multiplier': 1.5,    # IQR异常值检测乘数
            'rolling_window': 5       # 滚动窗口
        }
    }
    
    # 目标策略配置 - 定义不同的CVIX计算组合
    TARGET_STRATEGIES = {
        # 策略一：核心市场指数 - 覆盖主要A股指数
        'core_market': {
            'description': '核心A股市场波动率指数',
            'targets': {
                '300ETF': {
                    'weight': 0.4,
                    'description': '沪深300ETF - 代表大盘蓝筹',
                    'min_volume': 1000,
                    'min_oi': 5000,
                    'quality_weight': 1.0,
                    'search_keywords': ['300ETF', '510300'],
                    'underlying_code': '510300',
                    'exchange': 'SSE'
                },
                '500ETF': {
                    'weight': 0.3,
                    'description': '中证500ETF - 代表中盘成长',
                    'min_volume': 500,
                    'min_oi': 2000,
                    'quality_weight': 0.8,
                    'search_keywords': ['500ETF', '510500'],
                    'underlying_code': '510500',
                    'exchange': 'SSE'
                },
                '50ETF': {
                    'weight': 0.2,
                    'description': '上证50ETF - 代表超大盘蓝筹',
                    'min_volume': 800,
                    'min_oi': 3000,
                    'quality_weight': 0.9,
                    'search_keywords': ['50ETF', '510050'],
                    'underlying_code': '510050',
                    'exchange': 'SSE'
                },
                '1000ETF': {
                    'weight': 0.1,
                    'description': '中证1000ETF - 代表小盘成长',
                    'min_volume': 200,
                    'min_oi': 1000,
                    'quality_weight': 0.6,
                    'search_keywords': ['1000ETF', '512100'],
                    'underlying_code': '512100',
                    'exchange': 'SSE'
                }
            }
        },
        
        # 策略二：单一沪深300指数
        'single_300': {
            'description': '单一沪深300波动率指数',
            'targets': {
                '300ETF': {
                    'weight': 1.0,
                    'description': '沪深300ETF',
                    'min_volume': 1000,
                    'min_oi': 5000,
                    'quality_weight': 1.0,
                    'search_keywords': ['300ETF', '510300'],
                    'underlying_code': '510300',
                    'exchange': 'SSE'
                }
            }
        }
    }
    
    @classmethod
    def get_strategy_config(cls, strategy_name: str) -> Dict:
        """获取指定策略的配置"""
        return cls.TARGET_STRATEGIES.get(strategy_name, cls.TARGET_STRATEGIES['core_market'])
    
    @classmethod
    def get_all_strategies(cls) -> List[str]:
        """获取所有可用的策略名称"""
        return list(cls.TARGET_STRATEGIES.keys())
    
    @classmethod
    def get_data_quality_config(cls) -> Dict:
        """获取数据质量配置"""
        return cls.DATA_QUALITY_CONFIG.copy()


class DataQualityValidator:
    """
    期权数据质量验证器
    用于在计算VIX之前，对原始数据进行清洗和过滤
    """
    
    def __init__(self, config: Dict = None):
        """初始化数据质量验证器"""
        self.config = config or CVIXConfig.get_data_quality_config()

    def validate_time_to_expiry(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """验证期权到期时间"""
        report = {
            'original_count': len(df),
            'issues': [],
            'filtered_count': 0,
            'filter_ratio': 0.0
        }
        
        if df.empty or 'T' not in df.columns:
            report['issues'].append('数据为空或缺少到期时间(T)列')
            return df, report
        
        # 获取配置
        min_days = self.config['time_to_expiry']['min_days'] / 365
        max_days = self.config['time_to_expiry']['max_days'] / 365
        
        before_count = len(df)
        df = df[(df['T'] >= min_days) & (df['T'] <= max_days)]
        after_count = len(df)
        
        if before_count > after_count:
            filtered = before_count - after_count
            report['issues'].append(f'到期时间过滤: {filtered} 条记录被移除')
        
        report['filtered_count'] = len(df)
        if report['original_count'] > 0:
            report['filter_ratio'] = (report['original_count'] - report['filtered_count']) / report['original_count']
        else:
            report['filter_ratio'] = 0.0
        
        return df, report
    
    def validate_price_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """验证价格数据"""
        report = {
            'original_count': len(df),
            'issues': [],
            'filtered_count': 0,
            'filter_ratio': 0.0
        }
        
        if df.empty:
            return df, report
        
        # 定义价格列
        price_cols = ['close', 'call', 'put']
        valid_price_cols = [col for col in price_cols if col in df.columns]
        
        if not valid_price_cols:
            report['issues'].append('缺少价格列')
            return df, report
        
        # 获取配置
        min_price = self.config['price_bounds']['min_price']
        max_price = self.config['price_bounds']['max_price']
        
        for col in valid_price_cols:
            before_count = len(df)
            df = df[(df[col] >= min_price) & (df[col] <= max_price)]
            after_count = len(df)
            
            if before_count > after_count:
                filtered = before_count - after_count
                report['issues'].append(f'{col}价格越界过滤: {filtered} 条记录被移除')
        
        report['filtered_count'] = len(df)
        report['filter_ratio'] = (report['original_count'] - report['filtered_count']) / report['original_count']
        
        return df, report
    
    def validate_volume_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """验证交易量数据"""
        report = {
            'original_count': len(df),
            'issues': [],
            'filtered_count': 0,
            'filter_ratio': 0.0
        }
        
        if df.empty or 'vol' not in df.columns:
            report['issues'].append('缺少交易量(vol)列')
            return df, report
        
        # 获取配置
        min_volume = self.config['volume_filters']['min_daily_volume']
        before_count = len(df)
        df = df[df['vol'] >= min_volume]
        after_count = len(df)
        
        if before_count > after_count:
            filtered = before_count - after_count
            report['issues'].append(f'低交易量过滤: {filtered} 条记录被移除')
        
        report['filtered_count'] = len(df)
        report['filter_ratio'] = (report['original_count'] - report['filtered_count']) / report['original_count']
        
        return df, report
    
    def detect_price_outliers(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """检测价格异常值"""
        report = {
            'original_count': len(df),
            'outliers_detected': 0,
            'outlier_methods': [],
            'filtered_count': 0
        }
        
        if df.empty:
            return df, report
        
        price_cols = ['close', 'call', 'put']
        valid_price_cols = [col for col in price_cols if col in df.columns]
        
        outlier_indices = set()
        
        for col in valid_price_cols:
            if col not in df.columns:
                continue
            
            # Z-score方法
            try:
                clean_data = df[col].dropna()
                if len(clean_data) > 1:
                    z_scores = np.abs(stats.zscore(clean_data, nan_policy='omit'))
                    z_threshold = self.config['outlier_detection']['z_score_threshold']
                    z_outliers = clean_data[z_scores > z_threshold].index
                    outlier_indices.update(z_outliers)
                    
                    if len(z_outliers) > 0:
                        report['outlier_methods'].append(f'{col}_zscore')
            except Exception as e:
                logger.warning(f"Z-score异常值检测失败于 {col}: {e}")
        
        # 移除异常值
        if outlier_indices:
            df = df[~df.index.isin(outlier_indices)]
            report['outliers_detected'] = len(outlier_indices)
        
        report['filtered_count'] = len(df)
        
        return df, report
    
    def comprehensive_validation(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """执行全面的数据验证"""
        validation_steps = []
        overall_report = {
            'original_count': len(df),
            'validation_steps': [],
            'overall_filter_ratio': 0.0,
            'quality_score': 0.0,
            'recommendations': []
        }
        
        if df.empty:
            overall_report['quality_score'] = 0.0
            return df, overall_report
        
        current_df = df.copy()
        
        # 1. 到期时间验证
        current_df, tte_report = self.validate_time_to_expiry(current_df)
        validation_steps.append(('time_to_expiry', tte_report))
        
        # 2. 价格数据验证
        current_df, price_report = self.validate_price_data(current_df)
        validation_steps.append(('price_validation', price_report))
        
        # 3. 交易量验证
        current_df, volume_report = self.validate_volume_data(current_df)
        validation_steps.append(('volume_validation', volume_report))
        
        # 4. 异常值检测
        current_df, outlier_report = self.detect_price_outliers(current_df)
        validation_steps.append(('outlier_detection', outlier_report))
        
        # 生成总报告
        overall_report['validation_steps'] = validation_steps
        overall_report['final_count'] = len(current_df)
        overall_report['overall_filter_ratio'] = (
            overall_report['original_count'] - overall_report['final_count']
        ) / overall_report['original_count']
        
        # 计算质量得分
        data_retention_score = 1 - overall_report['overall_filter_ratio']
        completeness_score = 1.0 if not current_df.empty else 0.0
        overall_report['quality_score'] = (data_retention_score + completeness_score) / 2
        
        # 生成建议
        if overall_report['overall_filter_ratio'] > 0.5:
            overall_report['recommendations'].append('数据过滤比例过高，建议检查数据源或放宽过滤条件。')
        if overall_report['quality_score'] < 0.7:
            overall_report['recommendations'].append('数据质量得分较低，请关注验证报告中的问题项。')
        
        return current_df, overall_report


class OptionDataProcessor:
    """
    期权数据处理器
    负责获取、处理和验证期权相关数据
    """
    
    def __init__(self, option_adapter: OptionDataAdapter, validator: DataQualityValidator = None):
        """初始化期权数据处理器"""
        self.option_adapter = option_adapter
        self.validator = validator or DataQualityValidator()
        self.cache = {}
    
    def get_option_basic_data(self, exchange: str, search_keywords: List[str]) -> pd.DataFrame:
        """获取期权基本信息"""
        opt_basic_list = []
        
        for keyword in search_keywords:
            try:
                basic_df = self.option_adapter.get_opt_basic(
                    exchange=exchange,
                    fields='ts_code,name,call_put,exercise_price,delist_date'
                )
                
                if not basic_df.empty:
                    # 根据关键词筛选
                    relevant_df = basic_df[basic_df['name'].str.contains(keyword, na=False)]
                    if not relevant_df.empty:
                        opt_basic_list.append(relevant_df)
                        
            except Exception as e:
                logger.warning(f"获取{keyword}期权基本信息失败: {e}")
                continue
        
        if not opt_basic_list:
            return pd.DataFrame()
        
        return pd.concat(opt_basic_list, ignore_index=True).drop_duplicates(subset=['ts_code'])
    
    def get_option_daily_data(self, start_date: str, end_date: str, exchange: str, 
                             ts_codes: List[str]) -> pd.DataFrame:
        """获取期权日线数据"""
        try:
            # 批量获取
            daily_df = self.option_adapter.get_opt_daily(
                start_date=start_date,
                end_date=end_date,
                exchange=exchange,
                fields='ts_code,trade_date,close,vol,amount,pre_settle'
            )
            
            if not daily_df.empty:
                # 筛选相关合约
                relevant_df = daily_df[daily_df['ts_code'].isin(ts_codes)]
                return relevant_df
            
        except Exception as e:
            logger.error(f"批量获取期权日线数据失败: {e}，尝试分日获取。")
            
            # 降级为分日获取
            date_range = pd.date_range(start=start_date, end=end_date, freq='B')
            dates = date_range.strftime('%Y%m%d').tolist()
            
            opt_daily_list = []
            for i, date in enumerate(dates[:10]):  # 限制最多获取10天
                try:
                    daily_df = self.option_adapter.get_opt_daily(
                        trade_date=date, 
                        exchange=exchange,
                        fields='ts_code,trade_date,close,vol,amount,pre_settle'
                    )
                    
                    if not daily_df.empty:
                        relevant_df = daily_df[daily_df['ts_code'].isin(ts_codes)]
                        if not relevant_df.empty:
                            opt_daily_list.append(relevant_df)
                            
                except Exception as date_e:
                    logger.debug(f"获取日期 {date} 数据失败: {date_e}")
                    continue
                  
                time.sleep(0.1)
            
            if opt_daily_list:
                return pd.concat(opt_daily_list, ignore_index=True)
        
        return pd.DataFrame()
    
    def process_option_data(self, symbol_code: str, target_config: Dict, 
                           start_date: str, end_date: str) -> pd.DataFrame:
        """处理指定标的期权数据"""
        cache_key = f"{symbol_code}_{start_date}_{end_date}"
        
        if cache_key in self.cache:
            logger.info(f"从缓存中加载{symbol_code}期权数据")
            return self.cache[cache_key]
        
        try:
            logger.info(f"开始处理{symbol_code}期权数据...")
            
            # 获取期权基本信息
            exchange = target_config.get('exchange', 'SSE')
            search_keywords = target_config.get('search_keywords', [symbol_code])
            
            opt_basic = self.get_option_basic_data(exchange, search_keywords)
            
            if opt_basic.empty:
                logger.warning(f"{symbol_code} 未找到相关的期权合约")
                return pd.DataFrame()
            
            logger.info(f"找到 {len(opt_basic)} 个{symbol_code}相关期权合约")
            
            # 获取期权日线数据
            opt_daily = self.get_option_daily_data(
                start_date, end_date, exchange, opt_basic['ts_code'].tolist()
            )
            
            if opt_daily.empty:
                logger.warning(f"{symbol_code} 未找到相关的期权日线数据")
                return pd.DataFrame()
            
            # 合并基础信息和日线数据
            merged_df = pd.merge(opt_basic, opt_daily, on='ts_code', how='inner')
            
            if merged_df.empty:
                logger.warning(f"{symbol_code} 合并数据后为空")
                return pd.DataFrame()
            
            # 预处理数据
            processed_df = self._preprocess_option_data(merged_df)
            
            # 验证数据质量
            validated_df = self._validate_option_data(processed_df, target_config)
            
            # 加入缓存
            self.cache[cache_key] = validated_df
            
            logger.info(f"{symbol_code} 数据处理完成，得到: {len(validated_df)} 条有效记录")
            
            return validated_df
            
        except Exception as e:
            logger.error(f"处理{symbol_code}期权数据时发生错误: {e}")
            return pd.DataFrame()
    
    def _preprocess_option_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        # 转换日期格式
        df['delist_date'] = pd.to_datetime(df['delist_date'])
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 计算剩余到期时间(年)
        df['T'] = (df['delist_date'] - df['trade_date']).dt.days / 365.0
        
        # 过滤已到期或无效数据
        df = df[df['T'] > 0]
        
        # 将数据透视，合并认购和认沽期权
        try:
            pivot_df = df.pivot_table(
                index=['trade_date', 'exercise_price', 'T'],
                columns='call_put',
                values='close',
                aggfunc='mean'
            )
            
            # 重命名列
            column_mapping = {'C': 'call', 'P': 'put'}
            pivot_df = pivot_df.rename(columns=column_mapping)
            
            # 移除缺少任一类型期权的数据
            pivot_df = pivot_df.dropna(subset=['call', 'put'])
            
            # 重置索引
            pivot_df = pivot_df.reset_index()
            
            return pivot_df
            
        except Exception as e:
            logger.error(f"数据透视操作失败: {e}")
            return pd.DataFrame()
    
    def _validate_option_data(self, df: pd.DataFrame, target_config: Dict) -> pd.DataFrame:
        """验证期权数据"""
        if df.empty:
            return df
        
        # 价格范围过滤
        mask = (
            (df['call'] > 0) & 
            (df['put'] > 0) & 
            (df['call'] <= 1000) & 
            (df['put'] <= 1000)
        )
        
        df = df[mask]
        
        # 交易量过滤
        min_volume = target_config.get('min_volume', 10)
        if 'vol' in df.columns:
            df = df[df['vol'] >= min_volume]
        
        return df


class ShiborDataProcessor:
    """
    SHIBOR数据处理器
    用于获取和处理作为无风险利率的SHIBOR数据
    """
    
    def __init__(self, option_adapter: OptionDataAdapter):
        """初始化SHIBOR数据处理器"""
        self.option_adapter = option_adapter
        self.cache = None
    
    def get_shibor_data(self, start_date: str, end_date: str) -> pd.Series:
        """获取并处理SHIBOR利率数据"""
        if self.cache is not None:
            logger.info("从缓存中加载SHIBOR数据")
            return self.cache
        
        try:
            logger.info("开始获取SHIBOR利率数据...")
            
            shibor_df = self.option_adapter.get_shibor(
                start_date=start_date,
                end_date=end_date
            )
            
            if shibor_df.empty:
                logger.warning("未能获取SHIBOR数据，将使用默认利率。")
                return self._get_default_rates()
            
            # 处理SHIBOR数据
            processed_rates = self._process_shibor_data(shibor_df)
            
            # 加入缓存
            self.cache = processed_rates
            
            return processed_rates
            
        except Exception as e:
            logger.error(f"获取SHIBOR数据时发生错误: {e}，将使用默认利率。")
            return self._get_default_rates()
    
    def _process_shibor_data(self, shibor_df: pd.DataFrame) -> pd.Series:
        """处理SHIBOR数据"""
        try:
            # 定义常用的利率期限
            rate_columns = ['1w', '2w', '1m', '3m', '6m', '9m', '1y']
            available_columns = [col for col in rate_columns if col in shibor_df.columns]
            
            if not available_columns:
                logger.warning("未找到可用的SHIBOR利率列。")
                return self._get_default_rates()
            
            # 计算平均利率
            shibor_df['avg_rate'] = shibor_df[available_columns].mean(axis=1, skipna=True)
            
            # 计算整个期间的平均利率
            avg_rate = shibor_df['avg_rate'].mean() / 100
            
            # 构建一个简化的利率曲线
            periods = [7, 14, 30, 60, 90, 180, 365]
            rates = [avg_rate] * len(periods)
            
            return pd.Series(rates, index=periods, name='shibor_rates')
            
        except Exception as e:
            logger.error(f"处理SHIBOR数据失败: {e}")
            return self._get_default_rates()
    
    def _get_default_rates(self) -> pd.Series:
        """提供默认的利率曲线"""
        periods = [7, 14, 30, 60, 90, 180, 365]
        default_rate = 0.03  # 3%的默认无风险利率
        
        rates_data = {period: default_rate for period in periods}
        
        return pd.Series(rates_data, name='default_rates')


class VIXCalculator:
    """
    VIX计算器
    封装了VIX指数的核心计算逻辑
    """
    
    def __init__(self):
        """初始化VIX计算器"""
        pass
    
    def calculate_single_vix(self, option_data: pd.DataFrame, rates: pd.Series) -> pd.Series:
        """计算单个标的物的VIX指数"""
        if option_data.empty:
            logger.warning("期权数据为空，无法计算VIX")
            return pd.Series(dtype=float)
        
        try:
            vix_results = []
            
            # 按交易日分组计算
            for trade_date, day_data in option_data.groupby('trade_date'):
                try:
                    vix_value = self._calculate_daily_vix(day_data, rates)
                    if not np.isnan(vix_value):
                        vix_results.append((trade_date, vix_value))
                except Exception as e:
                    logger.debug(f"计算 {trade_date} 的VIX失败: {e}")
                    continue
            
            if not vix_results:
                logger.warning("没有可用的VIX计算结果")
                return pd.Series(dtype=float)
            
            # 转换为时间序列
            dates, values = zip(*vix_results)
            vix_series = pd.Series(values, index=pd.to_datetime(dates))
            vix_series = vix_series.sort_index()
            
            return vix_series
            
        except Exception as e:
            logger.error(f"计算VIX时发生错误: {e}")
            return pd.Series(dtype=float)
    
    def _calculate_daily_vix(self, day_data: pd.DataFrame, rates: pd.Series) -> float:
        """计算单日的VIX值"""
        try:
            if day_data.empty:
                return np.nan
            
            # 初始化方差和权重
            variance_sum = 0
            weight_sum = 0
            
            for T, group in day_data.groupby('T'):
                if T <= 0:
                    continue
                
                # 获取对应的无风险利率
                days = int(T * 365)
                if days in rates.index:
                    r = rates[days]
                else:
                    r = 0.03  # 默认利率
                
                # 计算方差贡献
                variance_contrib = self._calculate_variance_contribution(group, r, T)
                
                if not np.isnan(variance_contrib):
                    # 加权求和
                    weight = 1 / T
                    variance_sum += variance_contrib * weight
                    weight_sum += weight
            
            if weight_sum > 0:
                # 计算VIX (年化百分比)
                variance = variance_sum / weight_sum
                vix = np.sqrt(variance) * 100
                return vix
            else:
                return np.nan
                
        except Exception as e:
            logger.warning(f"计算日度VIX失败: {e}")
            return np.nan
    
    def _calculate_variance_contribution(self, group: pd.DataFrame, r: float, T: float) -> float:
        """计算单个到期日的方差贡献"""
        try:
            if group.empty or len(group) < 2:
                return np.nan
            
            variance_sum = 0
            count = 0
            
            for _, row in group.iterrows():
                call_price = row.get('call', 0)
                put_price = row.get('put', 0)
                strike = row.get('exercise_price', 0)
                
                if call_price > 0 and put_price > 0 and strike > 0:
                    # 使用认购和认沽价格的平均值作为期权金
                    option_value = (call_price + put_price) / 2
                    if option_value > 0:
                        # 简化的隐含方差计算
                        implied_var = (option_value / strike) ** 2 / T
                        variance_sum += implied_var
                        count += 1
            
            if count > 0:
                return variance_sum / count
            else:
                return np.nan
                
        except Exception as e:
            logger.warning(f"计算方差贡献失败: {e}")
            return np.nan
    
    def calculate_composite_vix(self, vix_results: Dict[str, pd.Series], 
                               weights: Dict[str, float]) -> pd.Series:
        """计算加权合成的VIX指数"""
        if not vix_results:
            logger.error("无VIX成分数据，无法合成")
            return pd.Series(dtype=float)
        
        try:
            # 寻找所有成分的共同交易日
            common_dates = None
            for vix_series in vix_results.values():
                if common_dates is None:
                    common_dates = set(vix_series.index)
                else:
                    common_dates = common_dates.intersection(set(vix_series.index))
            
            if not common_dates:
                logger.warning("VIX成分之间无共同交易日")
                return pd.Series(dtype=float)
            
            common_dates = sorted(list(common_dates))
            
            composite_values = []
            for date in common_dates:
                weighted_sum = 0
                total_weight = 0
                
                for symbol_code, vix_series in vix_results.items():
                    if date in vix_series.index and symbol_code in weights:
                        weight = weights[symbol_code]
                        weighted_sum += vix_series[date] * weight
                        total_weight += weight
                
                if total_weight > 0:
                    composite_values.append(weighted_sum / total_weight)
                else:
                    composite_values.append(np.nan)
            
            composite_vix = pd.Series(
                composite_values, 
                index=pd.to_datetime(common_dates)
            )
            
            logger.info(f"合成VIX计算完成: 共 {len(composite_vix)} 个交易日")
            
            return composite_vix
            
        except Exception as e:
            logger.error(f"计算合成VIX时发生错误: {e}")
            return pd.Series(dtype=float)


class ChinaVIXFactor(BaseFactor):
    """
    CVIX因子生成器 - 增强版
    封装了从数据获取到最终因子计算的全过程
    
    主要功能:
    - 根据不同策略配置，计算多种CVIX
    - 可选的数据质量检查流程
    - 提供了丰富的衍生因子计算
    """
    
    def __init__(self, start_date: str, end_date: str, strategy: str = 'core_market', 
                 use_quality_check: bool = True, tushare_token: Optional[str] = None):
        """
        初始化CVIX因子生成器
        
        Args:
            start_date: 开始日期 'YYYYMMDD'
            end_date: 结束日期 'YYYYMMDD'
            strategy: 计算策略
            use_quality_check: 是否启用数据质量检查
            tushare_token: Tushare API token
        """
        # 调用父类构造函数
        super().__init__()
        
        self.start_date = start_date
        self.end_date = end_date
        self.strategy = strategy
        self.use_quality_check = use_quality_check
        
        # 获取策略配置
        self.config = CVIXConfig.get_strategy_config(strategy)
        
        # 初始化数据适配器
        self._init_adapters(tushare_token)
        
        # 初始化处理器
        self._init_processors()
        
        logger.info(f"CVIX因子已初始化，策略: {self.config['description']}")
    
    def _init_adapters(self, tushare_token: Optional[str]):
        """初始化数据适配器"""
        try:
            self.option_adapter = OptionDataAdapter()
            self.stock_adapter = StockDataAdapter(tushare_token)
            self.market_adapter = MarketDataAdapter(tushare_token)
            self.futures_adapter = FuturesDataAdapter(tushare_token)
            logger.info("数据适配器初始化成功")
        except Exception as e:
            logger.error(f"数据适配器初始化失败: {e}")
            # 降级处理
            self.option_adapter = OptionDataAdapter()
            self.stock_adapter = StockDataAdapter()
            self.market_adapter = MarketDataAdapter()
            self.futures_adapter = None
    
    def _init_processors(self):
        """初始化数据处理器"""
        # 数据质量验证器
        if self.use_quality_check:
            self.validator = DataQualityValidator()
        else:
            self.validator = None
        
        # 期权数据处理器
        self.option_processor = OptionDataProcessor(self.option_adapter, self.validator)
        
        # SHIBOR数据处理器
        self.shibor_processor = ShiborDataProcessor(self.option_adapter)
        
        # VIX计算器
        self.vix_calculator = VIXCalculator()
    
    def get_option_data(self, symbol_code: str) -> pd.DataFrame:
        """获取并处理单个标的的期权数据"""
        target_config = self.config['targets'].get(symbol_code, {})
        return self.option_processor.process_option_data(
            symbol_code, target_config, self.start_date, self.end_date
        )
    
    def get_shibor_data(self) -> pd.Series:
        """获取SHIBOR数据作为无风险利率"""
        return self.shibor_processor.get_shibor_data(self.start_date, self.end_date)
    
    def calculate_single_vix(self, symbol_code: str) -> pd.Series:
        """计算单个标的物的VIX指数"""
        option_data = self.get_option_data(symbol_code)
        rates = self.get_shibor_data()
        return self.vix_calculator.calculate_single_vix(option_data, rates)
    
    def calculate_composite_vix(self) -> Dict[str, pd.Series]:
        """计算合成的VIX指数及各成分"""
        logger.info(f"开始计算合成VIX指数: {self.config['description']}")
        
        targets = self.config['targets']
        vix_results = {}
        
        # 计算各成分的VIX
        for symbol_code, target_config in targets.items():
            try:
                vix_series = self.calculate_single_vix(symbol_code)
                if not vix_series.empty:
                    vix_results[symbol_code] = vix_series
                else:
                    logger.warning(f"{symbol_code} VIX计算结果为空")
            except Exception as e:
                logger.error(f"计算{symbol_code} VIX时出错: {e}")
                continue
        
        if not vix_results:
            logger.error("所有成分VIX均计算失败")
            return {}
        
        # 计算加权合成的VIX
        weights = {symbol: config['weight'] for symbol, config in targets.items()}
        composite_vix = self.vix_calculator.calculate_composite_vix(vix_results, weights)
        
        if not composite_vix.empty:
            vix_results['composite'] = composite_vix
        
        return vix_results
    
    def get_factor_data(self) -> pd.DataFrame:
        """获取最终的因子数据DataFrame"""
        logger.info("开始生成CVIX因子数据...")
        
        # 计算VIX
        vix_results = self.calculate_composite_vix()
        
        if not vix_results or 'composite' not in vix_results:
            logger.error("CVIX因子计算失败，无合成结果")
            return pd.DataFrame()
        
        # 获取合成VIX
        composite_vix = vix_results['composite']
        
        if composite_vix.empty:
            logger.error("合成的VIX指数为空")
            return pd.DataFrame()
        
        # 构建因子DataFrame
        factor_df = self._build_factor_dataframe(composite_vix, vix_results)
        
        logger.info(f"CVIX因子生成完毕: {len(factor_df)} 条记录")
        
        return factor_df
    
    def _build_factor_dataframe(self, composite_vix: pd.Series, 
                               vix_results: Dict[str, pd.Series]) -> pd.DataFrame:
        """构建最终的因子DataFrame"""
        # 基础列
        factor_df = pd.DataFrame({
            'date': composite_vix.index,
            'cvix': composite_vix.values
        })
        
        # 添加各成分VIX作为辅助列
        for symbol_code, vix_series in vix_results.items():
            if symbol_code != 'composite' and not vix_series.empty:
                # 将成分VIX对齐到合成VIX的日期
                aligned_vix = vix_series.reindex(composite_vix.index)
                factor_df[f'cvix_{symbol_code.lower()}'] = aligned_vix.values
        
        # 计算衍生因子
        factor_df = self._calculate_derived_factors(factor_df)
        
        return factor_df
    
    def _calculate_derived_factors(self, factor_df: pd.DataFrame) -> pd.DataFrame:
        """计算衍生因子"""
        # 移动平均线
        factor_df['cvix_ma5'] = factor_df['cvix'].rolling(window=5).mean()
        factor_df['cvix_ma20'] = factor_df['cvix'].rolling(window=20).mean()
        
        # 波动率
        factor_df['cvix_std20'] = factor_df['cvix'].rolling(window=20).std()
        
        # VIX历史分位数 - 衡量当前VIX在历史上的位置
        # 使用expanding window以处理不同长度的数据
        data_length = len(factor_df)
        if data_length >= 252:
            # 优先使用1年滚动窗口
            factor_df['cvix_percentile'] = factor_df['cvix'].rolling(window=252, min_periods=20).rank(pct=True)
        elif data_length >= 60:
            # 数据不足1年时使用3个月窗口
            factor_df['cvix_percentile'] = factor_df['cvix'].rolling(window=60, min_periods=10).rank(pct=True)
        elif data_length >= 20:
            # 数据过少时使用扩展窗口
            factor_df['cvix_percentile'] = factor_df['cvix'].expanding(min_periods=10).rank(pct=True)
        else:
            # 数据极少时直接计算分位数
            factor_df['cvix_percentile'] = factor_df['cvix'].rank(pct=True)
        
        # VIX变化率
        factor_df['cvix_pct_change'] = factor_df['cvix'].pct_change()
        
        # VIX趋势强度
        factor_df['cvix_trend'] = (factor_df['cvix_ma5'] / factor_df['cvix_ma20'] - 1) * 100
        
        return factor_df
    
    def save_results(self, results: Dict[str, pd.Series], output_dir: str = None) -> None:
        """保存计算结果到CSV文件"""
        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cvix_results')
        
        os.makedirs(output_dir, exist_ok=True)
        
        for vix_name, vix_series in results.items():
            if not vix_series.empty:
                filename = f"cvix_{self.strategy}_{vix_name}_{self.start_date}_{self.end_date}.csv"
                filepath = os.path.join(output_dir, filename)
                
                df = vix_series.reset_index()
                df.columns = ['date', 'vix_value']
                df.to_csv(filepath, index=False)
                
                logger.info(f"结果已保存至: {filepath}")
    
    def get_strategy_info(self) -> Dict:
        """获取当前策略的配置信息"""
        return {
            'strategy': self.strategy,
            'description': self.config['description'],
            'targets': list(self.config['targets'].keys()),
            'start_date': self.start_date,
            'end_date': self.end_date,
            'use_quality_check': self.use_quality_check
        }
    
    @staticmethod
    def get_available_strategies() -> List[str]:
        """获取所有可用的计算策略"""
        return CVIXConfig.get_all_strategies()
    
    @staticmethod
    def create_factor(start_date: str, end_date: str, strategy: str = 'core_market', 
                     **kwargs) -> 'ChinaVIXFactor':
        """工厂方法，用于创建CVIX因子实例"""
        return ChinaVIXFactor(start_date, end_date, strategy, **kwargs)


# 辅助函数
def calculate_cvix_factor(start_date: str, end_date: str, strategy: str = 'core_market', 
                         **kwargs) -> pd.DataFrame:
    """
    便捷函数，用于直接计算CVIX因子
    
    Args:
        start_date: 开始日期 'YYYYMMDD'
        end_date: 结束日期 'YYYYMMDD'
        strategy: 计算策略
        **kwargs: 其他参数
    
    Returns:
        pd.DataFrame: 包含CVIX因子的DataFrame
    """
    cvix_factor = ChinaVIXFactor(start_date, end_date, strategy, **kwargs)
    return cvix_factor.get_factor_data()


def get_cvix_strategies() -> List[str]:
    """
    获取所有可用的CVIX计算策略
    
    Returns:
        List[str]: 策略名称列表
    """
    return ChinaVIXFactor.get_available_strategies()


# 示例代码
def run_example():
    """运行示例"""
    print("=== CVIX波动率因子计算示例 ===")
    
    # 获取最近30个交易日
    from datetime import datetime
    today = datetime.now().strftime('%Y%m%d')
    
    try:
        stock_adapter = StockDataAdapter()
        trade_cal = stock_adapter.get_trade_cal(start_date='20240101', end_date=today)
        if not trade_cal.empty:
            # 获取已开市的交易日并排序
            trade_cal_sorted = trade_cal[trade_cal['is_open'] == 1].sort_values('cal_date')
            trade_dates = trade_cal_sorted['cal_date'].tail(30).tolist()
            start_date = trade_dates[0]  # 第一个交易日
            end_date = trade_dates[-1]   # 最后一个交易日
        else:
            import pandas as pd
            end_date = pd.to_datetime(today)
            start_date = end_date - pd.Timedelta(days=45)
            start_date = start_date.strftime('%Y%m%d')
            end_date = end_date.strftime('%Y%m%d')
    except Exception as e:
        print(f"获取交易日历失败: {e}，将使用默认日期范围。")
        import pandas as pd
        end_date = pd.to_datetime(today)
        start_date = end_date - pd.Timedelta(days=45)
        start_date = start_date.strftime('%Y%m%d')
        end_date = end_date.strftime('%Y%m%d')
    
    print(f"计算周期: {start_date} - {end_date}")
    
    try:
        # 示例1：使用便捷函数计算
        print("\n=== 示例1: 便捷函数计算 ===")
        factor_data = calculate_cvix_factor(start_date, end_date, 'core_market')
        
        if not factor_data.empty:
            print(f"计算得到: {len(factor_data)} 条记录")
            print(f"CVIX范围: {factor_data['cvix'].min():.2f} - {factor_data['cvix'].max():.2f}")
            print(f"CVIX均值: {factor_data['cvix'].mean():.2f}")
        
        # 示例2：使用类进行更灵活的计算
        print("\n=== 示例2: 使用类进行计算 ===")
        cvix_factor = ChinaVIXFactor.create_factor(start_date, end_date, 'single_300')
        
        # 获取策略信息
        strategy_info = cvix_factor.get_strategy_info()
        print(f"当前策略信息: {strategy_info}")
        
        # 计算因子
        factor_data2 = cvix_factor.get_factor_data()
        
        if not factor_data2.empty:
            print(f"\n最近5条因子数据:")
            print(factor_data2.tail().to_string(index=False))
        
        # 获取所有可用策略
        strategies = get_cvix_strategies()
        print(f"\n所有可用策略: {strategies}")
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("=== 中国波动率指数(CVIX)计算器 - 增强版 ===")
    print("本脚本用于计算和分析中国市场的波动率指数。")
    
    # 运行示例
    run_example()
