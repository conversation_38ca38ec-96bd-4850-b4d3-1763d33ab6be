#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理重复定义的数据适配器脚本
"""

import os
import re
from pathlib import Path

def find_duplicate_adapters():
    """查找重复定义的适配器"""
    project_root = Path(".")
    target_adapters = ["StockDataAdapter", "MarketDataAdapter", "FundamentalDataAdapter", "FactorDataAdapter"]
    
    results = {}
    for adapter_name in target_adapters:
        results[adapter_name] = []
        
        for py_file in project_root.rglob("*.py"):
            if "adapters" in str(py_file) or "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                pattern = rf"class\s+{adapter_name}\s*[:\(]"
                if re.search(pattern, content):
                    results[adapter_name].append(str(py_file))
                    
            except Exception as e:
                print(f"读取文件 {py_file} 时出错: {e}")
    
    return results

def main():
    print("🔍 查找重复定义的数据适配器...")
    duplicates = find_duplicate_adapters()
    
    print("\n📊 重复定义统计:")
    for adapter_name, files in duplicates.items():
        print(f"  {adapter_name}: {len(files)} 个重复定义")
        for file_path in files:
            print(f"    - {file_path}")
    
    if not any(duplicates.values()):
        print("✅ 未发现重复定义")
        return
    
    print("\n💡 建议:")
    print("1. 删除重复定义，统一使用 adapters/ 目录下的标准适配器")
    print("2. 在需要的地方添加: from adapters import StockDataAdapter, MarketDataAdapter, etc.")
    print("3. 这样可以消除警告并提高代码一致性")

if __name__ == "__main__":
    main() 