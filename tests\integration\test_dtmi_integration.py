# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证DTMI因子是否能被factor_mining模块发现和使用
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_dtmi_integration():
    """测试DTMI因子集成"""
    print("=" * 60)
    print("测试DTMI因子与factor_mining的集成")
    print("=" * 60)
    
    # 1. 测试DTMI因子模块导入
    print("\n1. 测试DTMI因子模块导入...")
    try:
        from factor_analyze.momentum_factors.dynamic_trend_momentum_indicator_factor import (
            create_stock_dtmi_factor, create_index_dtmi_factor
        )
        print("✅ DTMI因子模块导入成功")
    except Exception as e:
        print(f"❌ DTMI因子模块导入失败: {e}")
        return
    
    # 2. 测试注册系统
    print("\n2. 测试DTMI因子注册系统...")
    try:
        from factor_analyze.technical_indicators.dtmi_factor_registry import dtmi_registry, get_available_dtmi_factors
        print("✅ DTMI注册系统导入成功")
        
        factors = get_available_dtmi_factors()
        print(f"✅ 发现 {len(factors)} 个DTMI因子变种:")
        for name, desc in factors.items():
            print(f"    - {name}: {desc}")
            
    except Exception as e:
        print(f"❌ DTMI注册系统测试失败: {e}")
        return
    
    # 3. 测试因子创建和计算
    print("\n3. 测试因子创建和计算...")
    try:
        # 创建个股DTMI因子
        stock_factor = create_stock_dtmi_factor(use_mock=True)
        
        # 测试计算
        result = stock_factor.calculate_factor(
            securities=['000001.SZ'],
            start_date='2024-01-01',
            end_date='2024-01-31'
        )
        
        if result and '000001.SZ' in result:
            data = result['000001.SZ']
            print(f"✅ DTMI因子计算成功，生成 {len(data)} 条记录")
            print(f"    DTMI范围: [{data['dtmi'].min():.4f}, {data['dtmi'].max():.4f}]")
            print(f"    信号分布: {data['signal'].value_counts().to_dict()}")
        else:
            print("❌ DTMI因子计算失败，无有效结果")
            
    except Exception as e:
        print(f"❌ DTMI因子计算测试失败: {e}")
        return
    
    # 4. 测试factor_mining集成
    print("\n4. 测试factor_mining集成...")
    try:
        from research.factor_mining import factor_analyze.factor_mining
        print("✅ factor_mining模块导入成功")
        
        # 检查factor_mining是否有注册机制
        if hasattr(factor_mining, 'register_factor'):
            print("✅ factor_mining具有因子注册功能")
        else:
            print("! factor_mining暂无因子注册功能，但模块可用")
            
        # 检查技术因子生成功能
        if hasattr(factor_mining, 'generate_technical_factors'):
            print("✅ factor_mining具有技术因子生成功能")
        else:
            print("! factor_mining无技术因子生成功能")
            
    except ImportError:
        print("❌ factor_mining模块不可用")
    except Exception as e:
        print(f"❌ factor_mining集成测试失败: {e}")
    
    # 5. 验证因子能被发现
    print("\n5. 验证DTMI因子可发现性...")
    try:
        import factor_analyze.dynamic_trend_momentum_indicator_factor as dtmi_module
        
        # 检查模块是否在factor_analyze目录下
        module_path = dtmi_module.__file__
        print(f"✅ DTMI因子模块位置: {module_path}")
        
        # 检查是否可以通过文件名发现
        import glob
        factor_files = glob.glob("factor_analyze/*dtmi*.py")
        if factor_files:
            print(f"✅ 可通过文件名模式发现DTMI因子: {factor_files}")
        else:
            print("! 无法通过文件名模式发现DTMI因子")
            
    except Exception as e:
        print(f"❌ 因子可发现性验证失败: {e}")
    
    print("\n" + "=" * 60)
    print("DTMI因子集成测试完成")

if __name__ == "__main__":
    test_dtmi_integration()
