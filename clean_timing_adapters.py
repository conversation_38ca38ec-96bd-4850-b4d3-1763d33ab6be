#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量清理 Timing 目录下的重复适配器定义
"""

import os
import re
from pathlib import Path

def clean_timing_file(file_path):
    """清理单个 Timing 文件中的重复适配器定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换重复的适配器定义
        patterns_to_replace = [
            # StockDataAdapter 定义
            (r'class StockDataAdapter:\s*\n\s*def get_stock_data\(self, symbol, start_date, end_date, adjust=\'qfq\'\):\s*\n\s*print\(f"警告：无法导入StockDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)', 
             'from adapters import StockDataAdapter'),
            
            # MarketDataAdapter 定义
            (r'class MarketDataAdapter:\s*\n\s*def get_market_data\(self, \*args, \*\*kwargs\):\s*\n\s*print\(f"警告：无法导入MarketDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)',
             'from adapters import MarketDataAdapter'),
            
            # FundamentalDataAdapter 定义
            (r'class FundamentalDataAdapter:\s*\n\s*def get_fundamental_data\(self, \*args, \*\*kwargs\):\s*\n\s*print\(f"警告：无法导入FundamentalDataAdapter"\)\s*\n\s*return pd\.DataFrame\(\)',
             'from adapters import FundamentalDataAdapter'),
            
            # 简单的 logger 定义
            (r'class logger:\s*\n\s*@staticmethod\s*\n\s*def info\(msg\): print\(f"INFO: \{msg\}"\)\s*\n\s*@staticmethod\s*\n\s*def warning\(msg\): print\(f"WARNING: \{msg\}"\)\s*\n\s*@staticmethod\s*\n\s*def error\(msg\): print\(f"ERROR: \{msg\}"\)',
             'from loguru import logger')
        ]
        
        modified = False
        for pattern, replacement in patterns_to_replace:
            if re.search(pattern, content, re.MULTILINE):
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                modified = True
        
        if modified:
            # 备份原文件
            backup_path = str(file_path) + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 写回修改后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已清理: {file_path}")
            return True
        else:
            print(f"⏭️ 无需清理: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    timing_dir = Path("strategy_autodev/Timing")
    
    if not timing_dir.exists():
        print(f"❌ 目录不存在: {timing_dir}")
        return
    
    print(f"🔍 开始清理 {timing_dir} 目录下的重复适配器定义...")
    
    cleaned_count = 0
    total_count = 0
    
    for py_file in timing_dir.glob("*.py"):
        if py_file.name.endswith('.py'):
            total_count += 1
            if clean_timing_file(py_file):
                cleaned_count += 1
    
    print(f"\n📊 清理完成:")
    print(f"  总文件数: {total_count}")
    print(f"  已清理: {cleaned_count}")
    print(f"  无需清理: {total_count - cleaned_count}")

if __name__ == "__main__":
    main() 