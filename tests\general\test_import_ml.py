#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
æµ‹è¯•MLå¢å¼ºç­–ç•¥æ¨¡å—å¯¼å…¥
"""

import sys
import os

# æ·»åŠ è·¯å¾„
sys.path.append(os.path.join(os.path.dirname(__file__), 'strategy_autodev', 'industry_stg'))

try:
    from ml_enhanced_industry_strategy import MLEnhancedIndustryEngine
    print("âœ?MLEnhancedIndustryEngine å¯¼å…¥æˆåŠŸ")
    
    # æµ‹è¯•åˆ›å»ºå®ä¾‹
    engine = MLEnhancedIndustryEngine()
    print("âœ?MLEnhancedIndustryEngine å®ä¾‹åˆ›å»ºæˆåŠŸ")
    
except ImportError as e:
    print(f"âœ?å¯¼å…¥å¤±è´¥: {e}")
except Exception as e:
    print(f"âœ?åˆ›å»ºå®ä¾‹å¤±è´¥: {e}")

try:
    from ml_enhanced_backtest import MLEnhancedBacktestEngine
    print("âœ?MLEnhancedBacktestEngine å¯¼å…¥æˆåŠŸ")
except ImportError as e:
    print(f"âœ?MLEnhancedBacktestEngine å¯¼å…¥å¤±è´¥: {e}")
