# -*- coding: utf-8 -*-
"""
自动编码器策略
基于Chapter20的自动编码器技术，用于特征提取和降维
适用于金融时间序列数据的无监督学习
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple, Union
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架
try:
    from keras.layers import Input, Dense, Conv1D, MaxPooling1D, UpSampling1D, Flatten, Reshape
    from keras.models import Model, Sequential
    from keras.optimizers import Adam
    from keras.callbacks import EarlyStopping, ModelCheckpoint
    from keras import regularizers
    from keras import backend as K
    KERAS_AVAILABLE = True
except ImportError:
    KERAS_AVAILABLE = False
    logging.warning("Keras不可用，自动编码器策略将使用简化实现")

# 数据管道集成
try:
    from data_pipeline.data_adapter import get_adapter
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    DATA_PIPELINE_AVAILABLE = False
    logging.warning("数据管道不可用，将使用模拟数据")

# 策略基类
try:
    from ..base_ml_strategy import BaseMLStrategy
except ImportError:
    from strategy_autodev.ml_strategies.base_ml_strategy import BaseMLStrategy

# 统一系统集成
try:
    from strategy_autodev.core.unified_system import get_unified_system
    UNIFIED_SYSTEM_AVAILABLE = True
except ImportError:
    UNIFIED_SYSTEM_AVAILABLE = False

logger = logging.getLogger(__name__)


class AutoencoderStrategy(BaseMLStrategy):
    """
    自动编码器策略
    
    功能:
    1. 使用自动编码器进行特征提取和降维
    2. 学习金融时间序列的潜在表示
    3. 基于重构误差进行异常检测
    4. 生成新的特征用于预测
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化自动编码器策略
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        
        # 策略配置
        self.config = config or {}
        self.encoding_dim = self.config.get('encoding_dim', 32)
        self.sequence_length = self.config.get('sequence_length', 20)
        self.batch_size = self.config.get('batch_size', 32)
        self.epochs = self.config.get('epochs', 50)
        self.validation_split = self.config.get('validation_split', 0.2)
        
        # 模型组件
        self.autoencoder = None
        self.encoder = None
        self.decoder = None
        
        # 数据管道
        self.data_adapter = None
        self._init_data_pipeline()
        
        # 策略状态
        self.is_trained = False
        self.feature_columns = []
        self.scaler = None
        
        logger.info(f"自动编码器策略初始化完成，编码维度: {self.encoding_dim}")
    
    def _build_model(self) -> Any:
        """构建自动编码器模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，返回简化模型")
            return None
        
        # 这里会在训练时根据数据维度动态构建
        return None
    
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        # 选择数值特征列
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        exclude_cols = ['target', 'date', 'symbol']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        if not feature_cols:
            raise ValueError("没有找到可用的特征列")
        
        self.feature_columns = feature_cols
        return data[feature_cols]
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练自动编码器模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，使用简化训练")
            return {'status': 'simplified_training', 'loss': 0.0}
        
        try:
            # 构建模型
            input_dim = X.shape[1]
            self.autoencoder, self.encoder, self.decoder = self._build_autoencoder(input_dim)
            
            # 训练模型
            history = self.autoencoder.fit(
                X, X,  # 自动编码器的目标是重构输入
                epochs=self.epochs,
                batch_size=self.batch_size,
                validation_split=self.validation_split,
                verbose=0
            )
            
            # 计算异常检测阈值
            reconstructed = self.autoencoder.predict(X)
            mse = np.mean(np.square(X - reconstructed), axis=1)
            self.anomaly_threshold = np.percentile(mse, 95)
            
            return {
                'final_loss': history.history['loss'][-1],
                'val_loss': history.history.get('val_loss', [0])[-1],
                'anomaly_threshold': self.anomaly_threshold,
                'epochs_trained': len(history.history['loss'])
            }
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """使用自动编码器进行预测"""
        if not KERAS_AVAILABLE or self.autoencoder is None:
            logger.warning("模型不可用，返回随机预测")
            return np.random.random(len(X))
        
        try:
            # 重构数据
            reconstructed = self.autoencoder.predict(X)
            
            # 计算重构误差
            mse = np.mean(np.square(X - reconstructed), axis=1)
            
            # 返回异常分数（归一化的重构误差）
            if hasattr(self, 'anomaly_threshold'):
                anomaly_scores = mse / self.anomaly_threshold
            else:
                anomaly_scores = mse / np.max(mse)
            
            return anomaly_scores
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.zeros(len(X))
    
    def _init_data_pipeline(self):
        """初始化数据管道"""
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.data_adapter = get_adapter()
                logger.info("✅ 数据管道连接成功")
            except Exception as e:
                logger.warning(f"数据管道连接失败: {e}")
                self.data_adapter = None
        else:
            logger.warning("数据管道不可用，将使用模拟数据")
    
    def _build_autoencoder(self, input_dim: int) -> Tuple[Any, Any, Any]:
        """
        构建自动编码器模型
        
        Args:
            input_dim: 输入维度
            
        Returns:
            autoencoder, encoder, decoder 模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建自动编码器")
        
        # 输入层
        input_layer = Input(shape=(input_dim,), name='Input')
        
        # 编码器
        encoded = Dense(128, activation='relu')(input_layer)
        encoded = Dense(64, activation='relu')(encoded)
        encoded = Dense(self.encoding_dim, activation='relu', name='Encoder')(encoded)
        
        # 解码器
        decoded = Dense(64, activation='relu')(encoded)
        decoded = Dense(128, activation='relu')(decoded)
        decoded = Dense(input_dim, activation='sigmoid', name='Decoder')(decoded)
        
        # 自动编码器模型
        autoencoder = Model(inputs=input_layer, outputs=decoded, name='Autoencoder')
        
        # 编码器模型
        encoder = Model(inputs=input_layer, outputs=encoded, name='Encoder')
        
        # 解码器模型
        encoded_input = Input(shape=(self.encoding_dim,), name='Decoder_Input')
        decoder_layer = autoencoder.layers[-3](encoded_input)
        decoder_layer = autoencoder.layers[-2](decoder_layer)
        decoder_layer = autoencoder.layers[-1](decoder_layer)
        decoder = Model(inputs=encoded_input, outputs=decoder_layer, name='Decoder')
        
        # 编译模型
        autoencoder.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        logger.info("自动编码器模型构建完成")
        return autoencoder, encoder, decoder
    
    def _build_convolutional_autoencoder(self, input_shape: Tuple[int, int]) -> Tuple[Any, Any, Any]:
        """
        构建卷积自动编码器（用于时间序列）
        
        Args:
            input_shape: 输入形状 (sequence_length, features)
            
        Returns:
            autoencoder, encoder, decoder 模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建卷积自动编码器")
        
        # 输入层
        input_layer = Input(shape=input_shape, name='Input')
        
        # 编码器
        x = Conv1D(32, 3, activation='relu', padding='same')(input_layer)
        x = MaxPooling1D(2, padding='same')(x)
        x = Conv1D(16, 3, activation='relu', padding='same')(x)
        encoded = MaxPooling1D(2, padding='same')(x)
        
        # 解码器
        x = Conv1D(16, 3, activation='relu', padding='same')(encoded)
        x = UpSampling1D(2)(x)
        x = Conv1D(32, 3, activation='relu', padding='same')(x)
        x = UpSampling1D(2)(x)
        decoded = Conv1D(input_shape[1], 3, activation='sigmoid', padding='same')(x)
        
        # 自动编码器模型
        autoencoder = Model(inputs=input_layer, outputs=decoded, name='ConvAutoencoder')
        
        # 编码器模型
        encoder = Model(inputs=input_layer, outputs=encoded, name='ConvEncoder')
        
        # 编译模型
        autoencoder.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        logger.info("卷积自动编码器模型构建完成")
        return autoencoder, encoder, None
    
    def _prepare_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            data: 原始数据
            
        Returns:
            训练数据和测试数据
        """
        # 选择特征列
        feature_cols = [col for col in data.columns if col not in ['date', 'symbol', 'target']]
        self.feature_columns = feature_cols
        
        # 提取特征
        features = data[feature_cols].values
        
        # 数据标准化
        from sklearn.preprocessing import MinMaxScaler
        self.scaler = MinMaxScaler()
        features_scaled = self.scaler.fit_transform(features)
        
        # 创建序列数据（用于卷积自动编码器）
        sequences = []
        for i in range(len(features_scaled) - self.sequence_length + 1):
            sequences.append(features_scaled[i:i + self.sequence_length])
        
        sequences = np.array(sequences)
        
        # 分割训练和测试数据
        split_idx = int(len(sequences) * (1 - self.validation_split))
        train_data = sequences[:split_idx]
        test_data = sequences[split_idx:]
        
        logger.info(f"数据准备完成，训练样本: {len(train_data)}, 测试样本: {len(test_data)}")
        return train_data, test_data
    
    def train(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练自动编码器模型
        
        Args:
            data: 训练数据
            
        Returns:
            训练结果
        """
        try:
            logger.info("开始训练自动编码器模型")
            
            # 准备数据
            train_data, test_data = self._prepare_data(data)
            
            # 构建模型
            if len(train_data.shape) == 3:  # 序列数据，使用卷积自动编码器
                input_shape = train_data.shape[1:]
                self.autoencoder, self.encoder, self.decoder = self._build_convolutional_autoencoder(input_shape)
            else:  # 普通数据，使用全连接自动编码器
                input_dim = train_data.shape[1]
                self.autoencoder, self.encoder, self.decoder = self._build_autoencoder(input_dim)
            
            # 设置回调
            callbacks = [
                EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
                ModelCheckpoint('autoencoder_best.h5', save_best_only=True, monitor='val_loss')
            ]
            
            # 训练模型
            history = self.autoencoder.fit(
                train_data, train_data,
                epochs=self.epochs,
                batch_size=self.batch_size,
                validation_data=(test_data, test_data),
                callbacks=callbacks,
                verbose=1
            )
            
            self.is_trained = True
            
            # 计算重构误差
            train_pred = self.autoencoder.predict(train_data)
            train_mse = np.mean(np.square(train_data - train_pred), axis=(1, 2) if len(train_data.shape) == 3 else 1)
            
            test_pred = self.autoencoder.predict(test_data)
            test_mse = np.mean(np.square(test_data - test_pred), axis=(1, 2) if len(test_data.shape) == 3 else 1)
            
            # 设置异常检测阈值
            self.anomaly_threshold = np.percentile(train_mse, 95)
            
            results = {
                'train_loss': history.history['loss'][-1],
                'val_loss': history.history['val_loss'][-1],
                'train_mse_mean': np.mean(train_mse),
                'test_mse_mean': np.mean(test_mse),
                'anomaly_threshold': self.anomaly_threshold,
                'encoding_dim': self.encoding_dim
            }
            
            logger.info(f"自动编码器训练完成，验证损失: {results['val_loss']:.6f}")
            return results
            
        except Exception as e:
            logger.error(f"自动编码器训练失败: {e}")
            return {'error': str(e)}
    
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        使用自动编码器进行预测
        
        Args:
            data: 预测数据
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 准备数据
            features = data[self.feature_columns].values
            features_scaled = self.scaler.transform(features)
            
            # 创建序列数据
            sequences = []
            for i in range(len(features_scaled) - self.sequence_length + 1):
                sequences.append(features_scaled[i:i + self.sequence_length])
            
            if not sequences:
                return pd.DataFrame()
            
            sequences = np.array(sequences)
            
            # 编码特征
            encoded_features = self.encoder.predict(sequences)
            
            # 重构数据
            reconstructed = self.autoencoder.predict(sequences)
            
            # 计算重构误差
            mse = np.mean(np.square(sequences - reconstructed), axis=(1, 2) if len(sequences.shape) == 3 else 1)
            
            # 异常检测
            anomalies = mse > self.anomaly_threshold
            
            # 构建结果
            results = pd.DataFrame({
                'date': data['date'].iloc[self.sequence_length-1:].reset_index(drop=True),
                'reconstruction_error': mse,
                'is_anomaly': anomalies,
                'anomaly_score': mse / self.anomaly_threshold
            })
            
            # 添加编码特征
            for i in range(encoded_features.shape[1]):
                results[f'encoded_feature_{i}'] = encoded_features[:, i]
            
            logger.info(f"自动编码器预测完成，检测到 {np.sum(anomalies)} 个异常")
            return results
            
        except Exception as e:
            logger.error(f"自动编码器预测失败: {e}")
            return pd.DataFrame()
    
    def get_encoded_features(self, data: pd.DataFrame) -> np.ndarray:
        """
        获取编码后的特征
        
        Args:
            data: 输入数据
            
        Returns:
            编码特征
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        features = data[self.feature_columns].values
        features_scaled = self.scaler.transform(features)
        
        # 创建序列数据
        sequences = []
        for i in range(len(features_scaled) - self.sequence_length + 1):
            sequences.append(features_scaled[i:i + self.sequence_length])
        
        if not sequences:
            return np.array([])
        
        sequences = np.array(sequences)
        return self.encoder.predict(sequences)
    
    def detect_anomalies(self, data: pd.DataFrame, threshold_percentile: float = 95) -> pd.DataFrame:
        """
        异常检测
        
        Args:
            data: 检测数据
            threshold_percentile: 阈值百分位数
            
        Returns:
            异常检测结果
        """
        predictions = self.predict(data)
        if predictions.empty:
            return pd.DataFrame()
        
        # 动态调整阈值
        dynamic_threshold = np.percentile(predictions['reconstruction_error'], threshold_percentile)
        predictions['is_anomaly_dynamic'] = predictions['reconstruction_error'] > dynamic_threshold
        
        return predictions[['date', 'reconstruction_error', 'is_anomaly', 'is_anomaly_dynamic', 'anomaly_score']]
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': 'AutoencoderStrategy',
            'description': '基于自动编码器的特征提取和异常检测策略',
            'category': 'DEEP_LEARNING',
            'encoding_dim': self.encoding_dim,
            'sequence_length': self.sequence_length,
            'is_trained': self.is_trained,
            'features': self.feature_columns,
            'capabilities': [
                'feature_extraction',
                'dimensionality_reduction', 
                'anomaly_detection',
                'unsupervised_learning'
            ]
        }


def register_autoencoder_strategy():
    """
    注册自动编码器策略到统一系统
    """
    if UNIFIED_SYSTEM_AVAILABLE:
        try:
            system = get_unified_system()
            system.register_strategy(
                "AutoencoderStrategy",
                AutoencoderStrategy,
                "DEEP_LEARNING",
                description="基于自动编码器的特征提取和异常检测策略",
                author="Strategy AutoDev",
                version="1.0.0",
                capabilities=[
                    "feature_extraction",
                    "dimensionality_reduction",
                    "anomaly_detection",
                    "unsupervised_learning"
                ]
            )
            logger.info("✅ AutoencoderStrategy 注册成功")
            return True
        except Exception as e:
            logger.error(f"❌ AutoencoderStrategy 注册失败: {e}")
            return False
    else:
        logger.warning("统一系统不可用，跳过策略注册")
        return False


if __name__ == "__main__":
    # 注册策略
    register_autoencoder_strategy()
    
    # 示例使用
    if DATA_PIPELINE_AVAILABLE:
        try:
            # 创建策略实例
            config = {
                'encoding_dim': 32,
                'sequence_length': 20,
                'epochs': 50
            }
            
            strategy = AutoencoderStrategy(config)
            
            # 获取数据
            adapter = get_adapter()
            data = adapter.get_stock_data(['000001.SZ'], '2023-01-01', '2023-12-31')
            
            if not data.empty:
                # 训练模型
                results = strategy.train(data)
                print(f"训练结果: {results}")
                
                # 进行预测
                predictions = strategy.predict(data)
                print(f"预测结果: {predictions.head()}")
                
                # 异常检测
                anomalies = strategy.detect_anomalies(data)
                print(f"异常检测: {anomalies.head()}")
            
        except Exception as e:
            logger.error(f"示例运行失败: {e}")
    else:
        logger.info("数据管道不可用，跳过示例")