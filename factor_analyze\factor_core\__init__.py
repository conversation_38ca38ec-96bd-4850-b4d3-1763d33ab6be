# -*- coding: utf-8 -*-
"""
因子核心模块
Factor Core Module

提供统一的基础接口和共享组件
- 基础接口定义
- 公共工具函数
- 数据适配器集合
- 配置管理

支持factor_analyze和factor_mining模块的继承和扩展
"""

from .base_interfaces import (
    FactorInterface,
    BaseFactorMiner,
    TechnicalFactorMiner,
    FundamentalFactorMiner,
    AlphaFactorMiner,
    FactorAdapterInterface,
    factor_registration,
    performance_tracking,
    data_validation,
    BaseHypothesisEngine,
    BaseFactorEvaluator,
    BaseFactorManager,
    BaseMiningPipeline
)
from .shared_components import (
    DataAdapterManager,
    ConfigManager,
    FactorUtils,
    ValidationUtils,
    get_adapter_manager,
    get_adapter,
    get_data_pipeline,
    create_mock_data_pipeline,
    get_enhanced_qlib_pipeline
)
from .common_types import (
    FactorData,
    FactorResult,
    HypothesisResult,
    EvaluationResult,
    MiningConfig,
    PerformanceMetrics,
    RegistrationInfo,
    FactorCategory,
    DataFrequency,
    DataRequirements,
    FactorConfig,
    SystemStatus,
    create_factor_data,
    create_factor_result,
    validate_factor_data,
    validate_factor_result
)
from .factor_registry import FactorRegistry
from .data_interface import DefaultFactorDataAdapter
from .performance_tracker import FactorPerformanceTracker
from .coordination_center import FactorCoordinationCenter

# 导入基础模块
try:
    from .base import (
        BaseFactor,
        BaseDataAdapter,
        BaseConfig,
        BaseFactorMiner,
        BasePerformanceTracker,
        create_sample_factor_data,
        validate_factor_result,
        calculate_basic_metrics
    )
    BASE_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 基础模块不可用: {e}")
    BASE_MODULE_AVAILABLE = False

# 导入六维因子系统
try:
    from .six_dimension_config import SixDimensionFactorConfig
    from .six_dimension_core import (
        SixDimensionFactorWeights,
        SixDimensionFactorSystem,
        BaseSixDimensionFactor,
        LiquidityFactor,
        LongExpectationFactor,
        ShortExpectationFactor,
        QualityFactor,
        TechnicalFactor,
        ValuationFactor,
        MomentumFactor
    )
    SIX_DIMENSION_FACTORS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 六维因子系统不可用: {e}")
    SIX_DIMENSION_FACTORS_AVAILABLE = False

# 导入RDAgent集成组件
try:
    from .factor_adapter import (
        RDAgentFactorAdapter,
        RDAgentFactorMiner,
        adapt_rdagent_miner
    )
    from .rdagent_integration_bridge import (
        RDAgentIntegrationBridge,
        get_rdagent_integration_bridge
    )
except ImportError:
    # 如果RDAgent相关模块不可用，提供占位符
    RDAgentFactorAdapter = None
    RDAgentFactorMiner = None
    RDAgentIntegrationBridge = None
    adapt_rdagent_miner = None
    get_rdagent_integration_bridge = None

__version__ = "1.0.0"
__author__ = "RDAgent Core Team"

# 智能因子筛选系统
try:
    from .intelligent_factor_selector import (
        IntelligentFactorSelector,
        FactorSelectionCriteria,
        FactorEvaluationMetrics,
        create_sample_data
    )
    from .factor_selection_manager import FactorSelectionManager
    from .factor_selection_integration import (
        FactorSelectionIntegration,
        create_integrated_factor_selector,
        quick_factor_selection
    )
    FACTOR_SELECTION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 智能因子筛选系统不可用: {e}")
    FACTOR_SELECTION_AVAILABLE = False

# 因子失效预警系统
try:
    from .factor_failure_warning_system import (
        FactorFailureWarningSystem,
        WarningConfig,
        WarningLevel,
        FailureType,
        WarningSignal,
        FactorHealthMetrics,
        create_default_warning_system,
        setup_email_alerts,
        setup_auto_factor_removal
    )
    from .integrated_factor_manager import (
        IntegratedFactorManager,
        FactorLifecycleConfig,
        create_integrated_manager
    )
    FACTOR_WARNING_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 因子失效预警系统不可用: {e}")
    FACTOR_WARNING_AVAILABLE = False

__all__ = [
    # 基础接口
    'FactorInterface',
    'BaseFactorMiner',
    'TechnicalFactorMiner',
    'FundamentalFactorMiner',
    'AlphaFactorMiner',
    'FactorAdapterInterface',
    'factor_registration',
    'performance_tracking',
    'data_validation',
    'BaseHypothesisEngine',
    'BaseFactorEvaluator',
    'BaseFactorManager',
    'BaseMiningPipeline',

    # 共享组件
    'DataAdapterManager',
    'ConfigManager',
    'FactorUtils',
    'ValidationUtils',
    'get_adapter_manager',
    'get_adapter',
    'get_data_pipeline',
    'create_mock_data_pipeline',
    'get_enhanced_qlib_pipeline',

    # 通用类型
    'FactorData',
    'FactorResult',
    'HypothesisResult',
    'EvaluationResult',
    'MiningConfig',
    'PerformanceMetrics',
    'RegistrationInfo',
    'FactorCategory',
    'DataFrequency',
    'DataRequirements',
    'FactorConfig',
    'SystemStatus',
    'create_factor_data',
    'create_factor_result',
    'validate_factor_data',
    'validate_factor_result',

    # 因子注册中心
    'FactorRegistry',

    # 数据接口
    'DefaultFactorDataAdapter',

    # 性能跟踪
    'FactorPerformanceTracker',

    # 协调中心
    'FactorCoordinationCenter',

    # RDAgent集成组件
    'RDAgentFactorAdapter',
    'RDAgentFactorMiner',
    'RDAgentIntegrationBridge',
    'adapt_rdagent_miner',
    'get_rdagent_integration_bridge'
]

# 基础模块
if BASE_MODULE_AVAILABLE:
    __all__.extend([
        'BaseFactor',
        'BaseDataAdapter',
        'BaseConfig',
        'BaseFactorMiner',
        'BasePerformanceTracker',
        'create_sample_factor_data',
        'validate_factor_result',
        'calculate_basic_metrics'
    ])

# 六维因子系统
if SIX_DIMENSION_FACTORS_AVAILABLE:
    __all__.extend([
        'SixDimensionFactorConfig',
        'SixDimensionFactorWeights',
        'SixDimensionFactorSystem',
        'BaseSixDimensionFactor',
        'LiquidityFactor',
        'LongExpectationFactor',
        'ShortExpectationFactor',
        'QualityFactor',
        'TechnicalFactor',
        'ValuationFactor',
        'MomentumFactor'
    ])

# 智能因子筛选系统
if FACTOR_SELECTION_AVAILABLE:
    __all__.extend([
        'IntelligentFactorSelector',
        'FactorSelectionCriteria',
        'FactorEvaluationMetrics',
        'FactorSelectionManager',
        'FactorSelectionIntegration',
        'create_integrated_factor_selector',
        'quick_factor_selection',
        'create_sample_data'
    ])

# 因子失效预警系统
if FACTOR_WARNING_AVAILABLE:
    __all__.extend([
        'FactorFailureWarningSystem',
        'WarningConfig',
        'WarningLevel',
        'FailureType',
        'WarningSignal',
        'FactorHealthMetrics',
        'create_default_warning_system',
        'setup_email_alerts',
        'setup_auto_factor_removal',
        'IntegratedFactorManager',
        'FactorLifecycleConfig',
        'create_integrated_manager'
    ])
