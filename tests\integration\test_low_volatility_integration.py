# -*- coding: utf-8 -*-
"""
ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥é›†æˆæµ‹è¯•å’Œæ¼”ç¤º

æµ‹è¯•å®Œæ•´çš„ç­–ç•¥é›†æˆæµç¨‹ï¼š
1. ç­–ç•¥æ¨¡æ¿åŠ è½½ 
2. å‚æ•°ä¼˜åŒ–
3. å›æµ‹éªŒè¯
4. å˜ç§ç­–ç•¥ç”Ÿæˆ
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# å¯¼å…¥é¡¹ç›®æ¨¡å—
from strategy_autodev.strategy_automation import StrategyAutomationEngine
from strategy_autodev.low_volatility_variants import LowVolatilityVariants
from strategy_autodev.low_volatility_optimizer import LowVolatilityOptimizer
from strategy_autodev.low_volatility_backtest import LowVolatilityBacktester

# è®¾ç½®æ—¥å¿—
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_strategy_template_integration():
    """æµ‹è¯•ç­–ç•¥æ¨¡æ¿é›†æˆ"""
    logger.info("=" * 60)
    logger.info("æµ‹è¯•1: ç­–ç•¥æ¨¡æ¿é›†æˆ")
    logger.info("=" * 60)
    
    # åˆ›å»ºç­–ç•¥è‡ªåŠ¨åŒ–å¼•æ“?
    engine = StrategyAutomationEngine()
    
    # æ£€æŸ¥ä½æ³¢åŠ¨ç­–ç•¥æ¨¡æ¿æ˜¯å¦å·²é›†æˆ?
    templates = engine.get_strategy_templates()
    
    if 'low_volatility_value' in templates:
        logger.info("âœ?ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥æ¨¡æ¿å·²æˆåŠŸé›†æˆ")
        template = templates['low_volatility_value']
        
        logger.info(f"ç­–ç•¥åç§°: {template.name}")
        logger.info(f"ç­–ç•¥ç±»å‹: {template.template_type}")
        logger.info(f"é£é™©ç­‰çº§: {template.risk_level}")
        logger.info(f"æè¿°: {template.description}")
        logger.info(f"æ‰€éœ€ç‰¹å¾: {template.required_features}")
        logger.info(f"é»˜è®¤å‚æ•°: {template.default_params}")
        
        return True
    else:
        logger.error("â?ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥æ¨¡æ¿æœªæ‰¾åˆ°")
        return False

def test_strategy_generation():
    """æµ‹è¯•ç­–ç•¥ç”Ÿæˆ"""
    logger.info("=" * 60)
    logger.info("æµ‹è¯•2: ç­–ç•¥è‡ªåŠ¨ç”Ÿæˆ")
    logger.info("=" * 60)
    
    try:
        # åˆ›å»ºå¼•æ“
        engine = StrategyAutomationEngine()
        
        # ç”Ÿæˆä½æ³¢åŠ¨ç­–ç•?
        result = engine.generate_strategy(
            strategy_type='low_volatility_value',
            risk_tolerance='low',
            data_sources=['stock', 'market']
        )
        
        logger.info("âœ?ç­–ç•¥ç”ŸæˆæˆåŠŸ")
        logger.info(f"ç­–ç•¥ID: {result['strategy_id']}")
        logger.info(f"æ¨¡æ¿ç±»å‹: {result['template'].template_type}")
        logger.info(f"ä¼˜åŒ–å‚æ•°: {result['optimized_params']}")
        logger.info(f"æ€§èƒ½æŒ‡æ ‡: {result['quality_metrics']}")
        
        return True
        
    except Exception as e:
        logger.error(f"â?ç­–ç•¥ç”Ÿæˆå¤±è´¥: {e}")
        return False

def test_strategy_variants():
    """æµ‹è¯•ç­–ç•¥å˜ç§"""
    logger.info("=" * 60)
    logger.info("æµ‹è¯•3: ç­–ç•¥å˜ç§ç”Ÿæˆ")
    logger.info("=" * 60)
    
    try:
        # è·å–æ‰€æœ‰å˜ç§?
        variants = LowVolatilityVariants.get_all_variants()
        
        logger.info(f"âœ?æˆåŠŸåŠ è½½ {len(variants)} ä¸ªç­–ç•¥å˜ç§?")
        for variant_name, template in variants.items():
            logger.info(f"  - {variant_name}: {template.name} (é£é™©ç­‰çº§: {template.risk_level})")
        
        # æµ‹è¯•åˆ›å»ºå®šåˆ¶å˜ç§
        custom_variant = LowVolatilityVariants.create_variant_strategy(
            'low_vol_multifactor',
            {'volatility_weight': 0.5, 'quality_weight': 0.3}
        )
        
        logger.info("âœ?å®šåˆ¶å˜ç§åˆ›å»ºæˆåŠŸ")
        logger.info(f"å®šåˆ¶å‚æ•°: volatility_weight={custom_variant.default_params['volatility_weight']}")
        
        return True
        
    except Exception as e:
        logger.error(f"â?ç­–ç•¥å˜ç§æµ‹è¯•å¤±è´¥: {e}")
        return False

def test_parameter_optimization():
    """æµ‹è¯•å‚æ•°ä¼˜åŒ–"""
    logger.info("=" * 60)
    logger.info("æµ‹è¯•4: å‚æ•°ä¼˜åŒ–")
    logger.info("=" * 60)
    
    try:
        # åˆ›å»ºä¼˜åŒ–å™?
        optimizer = LowVolatilityOptimizer()
        
        # åˆ›å»ºæ¨¡æ‹Ÿæ•°æ®
        data = pd.DataFrame({
            'close': np.random.randn(100) * 0.02 + 1,
            'high': np.random.randn(100) * 0.02 + 1.01,
            'low': np.random.randn(100) * 0.02 + 0.99,
            'volume': np.random.randint(100000, 1000000, 100),
            'return': np.random.randn(100) * 0.02
        })
        
        # æ‰§è¡Œä¼˜åŒ–
        optimization_result = optimizer.optimize_base_strategy(data, n_trials=20)
        
        logger.info("âœ?å‚æ•°ä¼˜åŒ–æˆåŠŸ")
        logger.info(f"æœ€ä¼˜å‚æ•? {optimization_result['best_params']}")
        logger.info(f"æ€§èƒ½æŒ‡æ ‡: {optimization_result['performance_metrics']}")
        
        return True
        
    except Exception as e:
        logger.error(f"â?å‚æ•°ä¼˜åŒ–å¤±è´¥: {e}")
        return False

def test_backtest_validation():
    """æµ‹è¯•å›æµ‹éªŒè¯"""
    logger.info("=" * 60)
    logger.info("æµ‹è¯•5: å›æµ‹éªŒè¯")
    logger.info("=" * 60)
    
    try:
        # åˆ›å»ºå›æµ‹å™?
        backtester = LowVolatilityBacktester()
        
        # å®šä¹‰ç­–ç•¥å‚æ•°
        strategy_params = {
            'volatility_lookback': 30,
            'volume_lookback': 30,
            'first_screen_count': 50,
            'final_select_count': 25,
            'rebalance_freq': 20,
            'stop_loss_ratio': 0.10
        }
        
        # è¿è¡Œå›æµ‹
        backtest_result = backtester.run_backtest(strategy_params)
        
        logger.info("âœ?å›æµ‹éªŒè¯æˆåŠŸ")
        logger.info(f"å¹´åŒ–æ”¶ç›Šç? {backtest_result['annual_return']:.2%}")
        logger.info(f"å¤æ™®æ¯”ç‡: {backtest_result['sharpe_ratio']:.3f}")
        logger.info(f"æœ€å¤§å›æ’? {backtest_result['max_drawdown']:.2%}")
        logger.info(f"èƒœç‡: {backtest_result['win_rate']:.2%}")
        
        return True
        
    except Exception as e:
        logger.error(f"â?å›æµ‹éªŒè¯å¤±è´¥: {e}")
        return False

def run_comprehensive_demo():
    """è¿è¡Œç»¼åˆæ¼”ç¤º"""
    logger.info("=" * 80)
    logger.info("ä½æ³¢åŠ¨ä»·å€¼ç­–ç•?- ç»¼åˆé›†æˆæ¼”ç¤º")
    logger.info("=" * 80)
    
    # è¿è¡Œæ‰€æœ‰æµ‹è¯?
    tests = [
        ("ç­–ç•¥æ¨¡æ¿é›†æˆ", test_strategy_template_integration),
        ("ç­–ç•¥è‡ªåŠ¨ç”Ÿæˆ", test_strategy_generation),
        ("ç­–ç•¥å˜ç§æµ‹è¯•", test_strategy_variants),
        ("å‚æ•°ä¼˜åŒ–æµ‹è¯•", test_parameter_optimization),
        ("å›æµ‹éªŒè¯æµ‹è¯•", test_backtest_validation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"æµ‹è¯• {test_name} å‡ºç°å¼‚å¸¸: {e}")
            results[test_name] = False
    
    # è¾“å‡ºæ€»ç»“
    logger.info("=" * 80)
    logger.info("é›†æˆæµ‹è¯•æ€»ç»“")
    logger.info("=" * 80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "âœ?é€šè¿‡" if result else "â?å¤±è´¥"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\næ€»ä½“ç»“æœ: {passed}/{total} é¡¹æµ‹è¯•é€šè¿‡")
    
    if passed == total:
        logger.info("ğŸ‰ æ‰€æœ‰æµ‹è¯•é€šè¿‡ï¼ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥å·²æˆåŠŸé›†æˆåˆ°é¡¹ç›®ä¸­")
        logger.info("\né›†æˆå®Œæˆæ¸…å•:")
        logger.info("âœ?ç­–ç•¥æ¨¡æ¿å·²åŠ å…¥ä¸»æ¨¡æ¿åº?)
        logger.info("âœ?å‚æ•°ä¼˜åŒ–æ”¯æŒå·²æ·»åŠ?)
        logger.info("âœ?ä¿¡å·é€»è¾‘ç”Ÿæˆå·²å®ç?)
        logger.info("âœ?ç­–ç•¥å˜ç§åº“å·²åˆ›å»º")
        logger.info("âœ?ä¸“ç”¨ä¼˜åŒ–å™¨å·²å¼€å?)
        logger.info("âœ?å›æµ‹éªŒè¯ç³»ç»Ÿå·²æ„å»?)
        
        logger.info("\nåç»­ä½¿ç”¨å»ºè®®:")
        logger.info("1. ä½¿ç”¨ StrategyAutomationEngine ç”ŸæˆåŸºç¡€ä½æ³¢åŠ¨ç­–ç•?)
        logger.info("2. é€šè¿‡ LowVolatilityVariants æ¢ç´¢ä¸åŒå˜ç§")
        logger.info("3. ä½¿ç”¨ LowVolatilityOptimizer è¿›è¡Œå‚æ•°è°ƒä¼˜")
        logger.info("4. é€šè¿‡ LowVolatilityBacktester éªŒè¯ç­–ç•¥æ•ˆæœ")
        
    else:
        logger.warning(f"âš ï¸ æœ?{total - passed} é¡¹æµ‹è¯•å¤±è´¥ï¼Œè¯·æ£€æŸ¥ç›¸å…³æ¨¡å?)
    
    return passed == total

def demonstrate_strategy_usage():
    """æ¼”ç¤ºç­–ç•¥ä½¿ç”¨æ–¹æ³•"""
    logger.info("=" * 80)
    logger.info("ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥ä½¿ç”¨æ¼”ç¤?)
    logger.info("=" * 80)
    
    logger.info("1. åŸºç¡€ç­–ç•¥ç”Ÿæˆ:")
    logger.info("   from strategy_autodev.strategy_automation import StrategyAutomationEngine")
    logger.info("   engine = StrategyAutomationEngine()")
    logger.info("   result = engine.generate_strategy(strategy_type='low_volatility_value')")
    
    logger.info("\n2. ç­–ç•¥å˜ç§ä½¿ç”¨:")
    logger.info("   from strategy_autodev.low_volatility_variants import LowVolatilityVariants")
    logger.info("   variants = LowVolatilityVariants.get_all_variants()")
    logger.info("   ml_variant = variants['low_vol_ml']")
    
    logger.info("\n3. å‚æ•°ä¼˜åŒ–:")
    logger.info("   from strategy_autodev.low_volatility_optimizer import LowVolatilityOptimizer")
    logger.info("   optimizer = LowVolatilityOptimizer()")
    logger.info("   optimized = optimizer.optimize_base_strategy(data)")
    
    logger.info("\n4. å›æµ‹éªŒè¯:")
    logger.info("   from strategy_autodev.low_volatility_backtest import LowVolatilityBacktester")
    logger.info("   backtester = LowVolatilityBacktester()")
    logger.info("   results = backtester.run_comprehensive_backtest(params)")

if __name__ == "__main__":
    # è¿è¡Œç»¼åˆæ¼”ç¤º
    success = run_comprehensive_demo()
    
    # æ¼”ç¤ºä½¿ç”¨æ–¹æ³•
    demonstrate_strategy_usage()
    
    # è¾“å‡ºæœ€ç»ˆçŠ¶æ€?
    if success:
        print("\nğŸ‰ ä½æ³¢åŠ¨ä»·å€¼ç­–ç•¥é›†æˆå®Œæˆï¼")
        print("ç­–ç•¥å·²æˆåŠŸåŠ å…¥é¡¹ç›®çš„ç­–ç•¥è‡ªåŠ¨å¼€å‘åº“")
    else:
        print("\nâš ï¸ é›†æˆè¿‡ç¨‹ä¸­å‘ç°é—®é¢˜ï¼Œè¯·æ£€æŸ¥ç›¸å…³æ¨¡å?) 
