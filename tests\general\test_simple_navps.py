# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的每股净资产同比增速因子测试"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
import traceback

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_import():
    """
    测试导入
    """
    print("=== 测试导入 ===")
    try:
        from factor_analyze.fundamental_factors.net_asset_value_per_share_yoy_growth_factor import (
            StockNetAssetValuePerShareYoYGrowthFactor,
            IndexNetAssetValuePerShareYoYGrowthFactor
        )
        print("✔ 导入成功")
        return True, (StockNetAssetValuePerShareYoYGrowthFactor, IndexNetAssetValuePerShareYoYGrowthFactor)
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False, None

def test_initialization(classes):
    """
    测试初始化
    """
    print("\n=== 测试初始化 ===")
    try:
        StockFactor, IndexFactor = classes
        
        # 测试个股因子初始化
        stock_factor = StockFactor()
        print(f"✔ 个股因子初始化成功: {stock_factor.factor_name}")
        
        # 测试指数因子初始化
        index_factor = IndexFactor()
        print(f"✔ 指数因子初始化成功: {index_factor.factor_name}")
        
        return True, (stock_factor, index_factor)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        traceback.print_exc()
        return False, None

def test_calculate_method(factors):
    """
    测试calculate方法
    """
    print("\n=== 测试calculate方法 ===")
    try:
        stock_factor, index_factor = factors
        
        # 检查方法是否存在
        if not hasattr(stock_factor, 'calculate'):
            print("❌ 个股因子缺少calculate方法")
            return False
        
        if not hasattr(index_factor, 'calculate'):
            print("❌ 指数因子缺少calculate方法")
            return False
        
        print("✔ calculate方法存在")
        
        # 尝试调用（使用简单参数）
        symbol = "000001.SZ"
        start_date = "20240101"
        end_date = "20241231"
        
        print(f"测试个股计算: {symbol}")
        result = stock_factor.calculate(symbol, start_date, end_date)
        print(f"✔ 个股计算完成，结果类型: {type(result)}, 记录数: {len(result) if hasattr(result, '__len__') else 'N/A'}")
        
        # 测试指数计算
        index_code = "000300.SH"
        print(f"测试指数计算: {index_code}")
        index_result = index_factor.calculate(index_code, start_date, end_date)
        print(f"✔ 指数计算完成，结果类型: {type(index_result)}, 记录数: {len(index_result) if hasattr(index_result, '__len__') else 'N/A'}")
        
        return True
    except Exception as e:
        print(f"❌ calculate方法测试失败: {e}")
        traceback.print_exc()
        return False

def test_base_methods(factors):
    """
    测试基础方法
    """
    print("\n=== 测试基础方法 ===")
    try:
        stock_factor, index_factor = factors
        
        # 测试必要方法是否存在
        required_methods = ['mine_factors', 'generate_factor_combinations']
        
        for method_name in required_methods:
            if hasattr(stock_factor, method_name):
                print(f"✔ 个股因子有方法: {method_name}")
            else:
                print(f"❌ 个股因子缺少方法: {method_name}")
                return False
        
        # 简单调用测试
        symbols = ["000001.SZ"]
        start_date = "20240101"
        end_date = "20241231"
        
        factors_result = stock_factor.mine_factors(symbols, start_date, end_date)
        print(f"✔ mine_factors调用成功，结果类型: {type(factors_result)}")
        
        combinations = stock_factor.generate_factor_combinations(factors_result)
        print(f"✔ generate_factor_combinations调用成功，结果类型: {type(combinations)}")
        
        return True
    except Exception as e:
        print(f"❌ 基础方法测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("简化的每股净资产同比增速因子测试")
    print("=" * 50)
    
    # 测试导入
    success, classes = test_import()
    if not success:
        print("\n❌ 导入测试失败，终止测试")
        return False
    
    # 测试初始化
    success, factors = test_initialization(classes)
    if not success:
        print("\n❌ 初始化测试失败，终止测试")
        return False
    
    # 测试calculate方法
    success = test_calculate_method(factors)
    if not success:
        print("\n❌ calculate方法测试失败")
        return False
    
    # 测试基础方法
    success = test_base_methods(factors)
    if not success:
        print("\n❌ 基础方法测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
