# 深度学习策略模块

基于《Hands-On Machine Learning for Algorithmic Trading》Chapter 17的深度学习算法交易策略实现。

## 模块概述

本模块实现了多种深度学习策略，用于金融时间序列预测和算法交易：

- **DeepLearningBaseStrategy**: 深度学习基础策略类，提供统一的框架和接口
- **FeedforwardNNStrategy**: 前馈神经网络策略，使用反向传播算法训练
- **KerasDLStrategy**: PyTorch深度学习策略，支持多种网络架构（MLP、CNN、LSTM）
- **NNOptimizationStrategy**: 神经网络架构优化策略，支持超参数搜索和模型集成

## 核心特性

### 1. 统一的基础框架
- 标准化的数据预处理和特征工程
- 技术指标计算（RSI、MACD、移动平均等）
- 滞后特征创建
- 数据标准化和归一化

### 2. 多种网络架构
- **前馈神经网络**: 多层感知器，支持自定义隐藏层
- **卷积神经网络**: 1D CNN用于时间序列特征提取
- **循环神经网络**: LSTM用于序列建模

### 3. 高级优化功能
- 网格搜索超参数优化
- 随机搜索优化
- 贝叶斯优化（使用Optuna）
- 时间序列交叉验证
- 模型集成

### 4. 训练优化技术
- 早停机制
- 学习率调度
- 梯度裁剪
- 批量归一化
- Dropout正则化
- L1/L2权重正则化

## 使用示例

### 基础前馈神经网络策略

```python
from strategy_autodev.ml_strategies.deep_learning import FeedforwardNNStrategy
import pandas as pd

# 创建策略
strategy = FeedforwardNNStrategy(
    name="MyFeedforwardNN",
    hidden_layers=[64, 32, 16],
    learning_rate=0.001,
    dropout_rate=0.2,
    epochs=100,
    batch_size=32
)

# 准备数据
data = pd.read_csv('stock_data.csv')

# 训练策略
result = strategy.fit(data)
print(f"训练结果: {result}")

# 生成交易信号
signals = strategy.generate_signals(data)
print(signals.head())
```

### PyTorch深度学习策略

```python
from strategy_autodev.ml_strategies.deep_learning import KerasDLStrategy

# 创建LSTM策略
strategy = KerasDLStrategy(
    name="MyLSTMStrategy",
    network_type='lstm',
    lstm_units=[50, 50],
    hidden_layers=[32, 16],
    learning_rate=0.001,
    batch_size=64,
    epochs=50,
    early_stopping_patience=10
)

# 训练和使用
result = strategy.fit(data)
signals = strategy.generate_signals(data)
```

### 超参数优化策略

```python
from strategy_autodev.ml_strategies.deep_learning import NNOptimizationStrategy

# 创建优化策略
strategy = NNOptimizationStrategy(
    name="OptimizedNN",
    optimization_method='bayesian',  # 'grid_search', 'random_search', 'bayesian'
    n_trials=50,
    cv_folds=3,
    use_ensemble=True
)

# 自动优化和训练
result = strategy.fit(data)
print(f"最佳参数: {strategy.best_params}")
print(f"最佳分数: {strategy.best_score}")

# 获取优化报告
report = strategy.get_optimization_report()
print(report)
```

## 参数配置

### 通用参数
- `hidden_layers`: 隐藏层架构，如 [64, 32, 16]
- `learning_rate`: 学习率，默认 0.001
- `dropout_rate`: Dropout比率，默认 0.2
- `batch_size`: 批量大小，默认 32
- `epochs`: 训练轮数，默认 100
- `activation`: 激活函数，默认 'relu'
- `lookback_window`: 回望窗口，默认 20
- `prediction_horizon`: 预测时间范围，默认 1

### 前馈网络特定参数
- `momentum`: 动量因子，默认 0.9
- `weight_decay`: 权重衰减，默认 0.0001
- `gradient_clip`: 梯度裁剪阈值，默认 1.0

### PyTorch策略特定参数
- `network_type`: 网络类型 ('mlp', 'cnn', 'lstm')
- `optimizer`: 优化器 ('adam', 'sgd', 'rmsprop')
- `batch_normalization`: 是否使用批量归一化
- `l1_reg`, `l2_reg`: L1/L2正则化系数

### 优化策略特定参数
- `optimization_method`: 优化方法 ('grid_search', 'random_search', 'bayesian')
- `n_trials`: 贝叶斯优化试验次数
- `cv_folds`: 交叉验证折数
- `use_ensemble`: 是否使用模型集成

## 数据要求

输入数据应包含以下列：
- `close`: 收盘价（必需）
- `volume`: 成交量（可选，用于计算成交量指标）
- `high`, `low`, `open`: 高低开盘价（可选，用于计算更多技术指标）

## 输出信号

策略生成的信号DataFrame包含：
- `prediction`: 模型预测值
- `signal`: 交易信号 (1: 买入, -1: 卖出, 0: 持有)
- 原始数据的所有列

## 性能监控

### 训练监控
```python
# 获取训练历史
history = strategy.training_history
print(f"训练损失: {history.get('loss', [])}")
print(f"验证损失: {history.get('val_loss', [])}")

# 获取模型信息
model_info = strategy.get_model_info()
print(model_info)
```

### 特征重要性
```python
# 获取特征重要性（仅前馈网络）
if hasattr(strategy, 'get_feature_importance'):
    importance = strategy.get_feature_importance()
    print(importance)
```

## 依赖项

### 必需依赖
- numpy
- pandas
- scikit-learn

### 可选依赖
- pytorch (用于KerasDLStrategy)
- optuna (用于贝叶斯优化)

## 注意事项

1. **数据质量**: 确保输入数据质量良好，无缺失值或异常值
2. **计算资源**: 深度学习训练需要较多计算资源，建议使用GPU加速
3. **过拟合**: 注意监控验证损失，避免过拟合
4. **参数调优**: 根据具体数据集调整超参数
5. **时间序列特性**: 注意金融时间序列的非平稳性和概念漂移

## 扩展开发

要添加新的深度学习策略：

1. 继承 `DeepLearningBaseStrategy` 基类
2. 实现必需的抽象方法：
   - `build_model()`
   - `train_model()`
   - `predict()`
3. 添加到 `__init__.py` 的导入列表

```python
from .deep_learning_base import DeepLearningBaseStrategy

class MyCustomDLStrategy(DeepLearningBaseStrategy):
    def build_model(self):
        # 实现模型构建逻辑
        pass
    
    def train_model(self, X, y):
        # 实现训练逻辑
        pass
    
    def predict(self, X):
        # 实现预测逻辑
        pass
```

## 版本历史

- v1.0.0: 初始版本，包含基础深度学习策略实现