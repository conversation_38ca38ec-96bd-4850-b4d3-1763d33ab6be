# -*- coding: utf-8 -*-\n#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
每股净资产同比增速因子测试
测试内容：
1. 个股因子计算
2. 指数因子计算
3. BaseFactorMiner接口兼容性
4. factor_mining模块兼容性
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入因子
from factor_analyze.fundamental_factors.net_asset_value_per_share_yoy_growth_factor import (
    StockNetAssetValuePerShareYoYGrowthFactor,
    IndexNetAssetValuePerShareYoYGrowthFactor
)

def test_factor_basic_info():
    """
    测试因子基本信息
    """
    print("\n=== 测试因子基本信息 ===")
    
    # 个股因子
    stock_factor = StockNetAssetValuePerShareYoYGrowthFactor()
    print(f"个股因子名称: {stock_factor.factor_name}")
    print(f"个股因子代码: {stock_factor.factor_code}")
    print(f"个股因子描述: {stock_factor.factor_description}")
    
    # 指数因子
    index_factor = IndexNetAssetValuePerShareYoYGrowthFactor()
    print(f"指数因子名称: {index_factor.factor_name}")
    print(f"指数因子代码: {index_factor.factor_code}")
    print(f"指数因子描述: {index_factor.factor_description}")
    
    return True

def test_single_stock_calculation():
    """
    测试单只股票计算
    """
    print("\n=== 测试单只股票计算 ===")
    
    try:
        factor = StockNetAssetValuePerShareYoYGrowthFactor()
        
        # 测试参数
        symbol = "000001.SZ"
        start_date = "20220101"
        end_date = "20241231"
        
        print(f"计算股票: {symbol}")
        print(f"时间范围: {start_date} - {end_date}")
        
        # 计算因子
        result = factor.calculate(symbol, start_date, end_date)
        
        if result.empty:
            print("[WARNING] 计算结果为空")
            return False
        
        print(f"计算成功，共{len(result)}条记录")
        print("前5条记录:")
        print(result.head().to_string(index=False))
        
        # 检查必要列
        required_columns = ['ts_code', 'end_date', 'navps_yoy_growth']
        missing_columns = [col for col in required_columns if col not in result.columns]
        
        if missing_columns:
            print(f"[ERROR] 缺少必要列: {missing_columns}")
            return False
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"平均增速: {result['navps_yoy_growth'].mean():.4f}")
        print(f"增速标准差: {result['navps_yoy_growth'].std():.4f}")
        print(f"最大增速: {result['navps_yoy_growth'].max():.4f}")
        print(f"最小增速: {result['navps_yoy_growth'].min():.4f}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 单只股票计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base_factor_miner_compatibility():
    """
    测试BaseFactorMiner接口兼容性
    """
    print("\n=== 测试BaseFactorMiner接口兼容性 ===")
    
    try:
        factor = StockNetAssetValuePerShareYoYGrowthFactor()
        
        # 测试必要方法是否存在
        required_methods = ['mine_factors', 'generate_factor_combinations']
        
        for method_name in required_methods:
            if not hasattr(factor, method_name):
                print(f"[ERROR] 缺少方法: {method_name}")
                return False
            print(f"✅ 方法 {method_name} 存在")
        
        # 测试方法调用
        symbols = ["000001.SZ", "000002.SZ"]
        start_date = "20220101"
        end_date = "20241231"
        
        # 测试mine_factors方法
        factors = factor.mine_factors(symbols, start_date, end_date)
        print(f"✅ mine_factors方法调用成功，返回类型: {type(factors)}")
        
        # 测试generate_factor_combinations方法
        combinations = factor.generate_factor_combinations(factors)
        print(f"✅ generate_factor_combinations方法调用成功，返回类型: {type(combinations)}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] BaseFactorMiner兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_factor_calculation():
    """
    测试指数因子计算
    """
    print("\n=== 测试指数因子计算 ===")
    
    try:
        factor = IndexNetAssetValuePerShareYoYGrowthFactor()
        
        # 测试参数
        index_code = "000300.SH"  # 沪深300
        start_date = "20220101"
        end_date = "20241231"
        
        print(f"计算指数: {index_code}")
        print(f"时间范围: {start_date} - {end_date}")
        
        # 计算因子
        result = factor.calculate(index_code, start_date, end_date)
        
        if result.empty:
            print("[WARNING] 指数计算结果为空")
            return False
        
        print(f"计算成功，共{len(result)}条记录")
        print("前5条记录:")
        print(result.head().to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 指数因子计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_factor_mining_compatibility():
    """
    测试factor_mining模块兼容性
    """
    print("\n=== 测试factor_mining模块兼容性 ===")
    
    try:
        # 尝试导入factor_mining模块
        try:
            from factor_analyze.factor_mining import FactorMiner
            print("✅ factor_mining模块导入成功")
            
            # 测试因子是否能被factor_mining识别
            factor = StockNetAssetValuePerShareYoYGrowthFactor()
            
            # 检查是否有必要的属性和方法
            required_attrs = ['factor_name', 'factor_code', 'factor_description']
            for attr in required_attrs:
                if not hasattr(factor, attr):
                    print(f"[ERROR] 缺少属性: {attr}")
                    return False
                print(f"✅ 属性 {attr} 存在: {getattr(factor, attr)}")
            
            return True
            
        except ImportError:
            print("[WARNING] factor_mining模块未找到，跳过兼容性测试")
            return True
        
    except Exception as e:
        print(f"[ERROR] factor_mining兼容性测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("每股净资产同比增速因子测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("因子基本信息", test_factor_basic_info),
        ("单只股票计算", test_single_stock_calculation),
        ("BaseFactorMiner兼容性", test_base_factor_miner_compatibility),
        ("指数因子计算", test_index_factor_calculation),
        ("factor_mining兼容性", test_factor_mining_compatibility),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            test_results.append((test_name, False))
            print(f"\n{test_name}: ❌ 异常 - {e}")
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查日志")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
