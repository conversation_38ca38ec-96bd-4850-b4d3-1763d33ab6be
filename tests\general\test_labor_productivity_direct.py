# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试人均营收增长率因子功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_import():
    """直接导入测试"""
    print("=== 直接导入人均营收增长率因子 ===")
    try:
        from factor_analyze.mock.mock_labor_productivity_factor import (
            MockLaborProductivityFactor,
            StockLaborProductivityFactor,
            IndexLaborProductivityFactor,
            create_labor_productivity_factor
        )
        print("✓ 直接导入成功")
        return True, {
            'MockLaborProductivityFactor': MockLaborProductivityFactor,
            'StockLaborProductivityFactor': StockLaborProductivityFactor,
            'IndexLaborProductivityFactor': IndexLaborProductivityFactor,
            'create_labor_productivity_factor': create_labor_productivity_factor
        }
    except Exception as e:
        print(f"✗ 直接导入失败: {e}")
        return False, None

def test_factor_functionality(classes):
    """测试因子功能"""
    print("\n=== 测试因子功能 ===")
    try:
        # 测试创建函数
        create_func = classes['create_labor_productivity_factor']
        
        # 创建个股因子
        stock_factor = create_func(target_type='stock')
        print(f"✓ 个股因子创建: {type(stock_factor).__name__}")
        
        # 创建指数因子
        index_factor = create_func(target_type='index')
        print(f"✓ 指数因子创建: {type(index_factor).__name__}")
        
        # 测试因子描述
        description = stock_factor.get_factor_description()
        print(f"✓ 因子名称: {description['factor_name']}")
        print(f"✓ 因子代码: {description['factor_code']}")
        print(f"✓ 适用目标: {description['applicable_targets']}")
        
        return True
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

def test_factor_calculation(classes):
    """测试因子计算"""
    print("\n=== 测试因子计算 ===")
    try:
        from datetime import datetime
        
        # 创建个股因子
        create_func = classes['create_labor_productivity_factor']
        factor = create_func(target_type='stock')
        
        # 测试计算
        stocks = ['000001.SZ', '000002.SZ']
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        result = factor.calculate(stocks, start_date, end_date)
        
        if result is not None and len(result) > 0:
            print(f"✓ 计算成功: {len(result)}条记录)")
            print(f"✓ 数据列: {list(result.columns)}")
            return True
        else:
            print("✗ 计算结果为空")
            return False
            
    except Exception as e:
        print(f"✗ 计算测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始人均营收增长率因子直接测试")
    print("=" * 60)
    
    # 测试导入
    success, classes = test_direct_import()
    if not success:
        print("✗ 导入失败，无法继续测试")
        return
    
    # 测试功能
    tests = [
        lambda: test_factor_functionality(classes),
        lambda: test_factor_calculation(classes)
    ]
    
    passed = 0
    total = len(tests) + 1  # +1 for import test
    passed += 1  # import test passed
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("📝 因子可以通过以下方式使用:")
        print("   from factor_analyze.mock.mock_labor_productivity_factor import create_labor_productivity_factor")
        print("   factor = create_labor_productivity_factor(target_type='stock')")
        print("   result = factor.calculate(stocks, start_date, end_date)")
    else:
        print("✗ 部分测试失败")
    
    print("\n📋 因子信息:")
    if classes:
        try:
            factor = classes['create_labor_productivity_factor'](target_type='stock')
            desc = factor.get_factor_description()
            print(f"   名称: {desc['factor_name']}")
            print(f"   代码: {desc['factor_code']}")
            print(f"   公式: {desc['formula']}")
            print(f"   来源: {desc['data_source']}")
        except:
            pass

if __name__ == "__main__":
    main()
