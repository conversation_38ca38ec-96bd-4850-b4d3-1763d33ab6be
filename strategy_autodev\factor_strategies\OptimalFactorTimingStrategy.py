# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最优因子择时策略
================

基于高维环境下的最优因子择时信号，使用机器学习方法动态调整因子权重。
通过识别市场状态和因子regime，实现优于静态权重的动态配置。

来源：2025-06-12_华安证券_学海拾珠系列之二百三十八：高维环境下的最优因子择时
优先级：0.91
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 导入基类
from base_factor_strategy import BaseFactorStrategy, StrategySignal, logger

# 导入因子择时信号
try:
    from factor_analyze.tools.factor_timing_signal import FactorTimingSignal
    TIMING_SIGNAL_AVAILABLE = True
except ImportError:
    TIMING_SIGNAL_AVAILABLE = False
    logger.warning("因子择时信号模块未找到，使用模拟数据")


@dataclass
class FactorTimingPosition:
    """因子择时持仓"""
    date: pd.Timestamp
    factor_weights: Dict[str, float]  # 因子权重
    stock_weights: Dict[str, float]  # 股票权重
    market_regime: str  # 市场状态
    timing_signal: float  # 择时信号强度
    expected_return: float  # 预期收益
    risk_level: float  # 风险水平


class OptimalFactorTimingStrategy(BaseFactorStrategy):
    """最优因子择时策略"""

    def __init__(self, config: Optional[Dict] = None):
        super().__init__(config)
        
        # 初始化因子择时信号
        if TIMING_SIGNAL_AVAILABLE:
            self.timing_signal = FactorTimingSignal(
                lookback_window=self.config.extra_params.get('lookback_window', 60),
                ic_window=20,
                n_components=5,
                method='lasso'
            )
        else:
            self.timing_signal = None

        # 市场状态参数
        self.regime_params = {
            'bull_low_vol': {'risk_multiplier': 1.2, 'position_size': 1.0},
            'bull_high_vol': {'risk_multiplier': 0.8, 'position_size': 0.8},
            'bear_low_vol': {'risk_multiplier': 0.6, 'position_size': 0.6},
            'bear_high_vol': {'risk_multiplier': 0.4, 'position_size': 0.4},
            'neutral': {'risk_multiplier': 1.0, 'position_size': 0.8}
        }
        
        # 当前因子权重
        self.current_factor_weights = {}

    def _get_default_config(self) -> Dict[str, any]:
        """返回默认配置"""
        return {
            'n_stocks': 50,
            'rebalance_freq': 'monthly',
            'max_single_stock_weight': 0.05,
            'transaction_cost': 0.002,
            'extra_params': {
                'lookback_window': 60,
                'ic_threshold': 0.02,
                'min_factor_weight': 0.05,
                'max_factor_weight': 0.40,
                'leverage': 1.0
            }
        }

    def _calculate_scores(self, 
                         market_data: Dict[str, pd.DataFrame],
                         date: pd.Timestamp) -> pd.Series:
        """计算因子得分"""
        # 获取股票池
        stocks = list(market_data.keys())
        
        # 模拟因子数据
        factor_data = self._generate_factor_data(stocks, market_data, date)
        
        # 计算因子择时信号
        if self.timing_signal is not None:
            try:
                # 模拟收益率数据
                return_data = self._generate_return_data(market_data, date)
                stock_returns = self._generate_stock_returns(market_data, date)
                
                timing_result = self.timing_signal.calculate(
                    factor_data, return_data, date
                )
                
                factor_weights = timing_result.factor_weights
                market_regime = timing_result.market_regime
                
                # 过滤低IC因子
                filtered_weights = {}
                for factor, weight in factor_weights.items():
                    ic = timing_result.factor_ic.get(factor, 0)
                    if abs(ic) >= self.config.extra_params.get('ic_threshold', 0.02):
                        weight = np.clip(weight, 
                                       self.config.extra_params.get('min_factor_weight', 0.05), 
                                       self.config.extra_params.get('max_factor_weight', 0.40))
                        filtered_weights[factor] = weight
                
                # 重新归一化
                total_weight = sum(filtered_weights.values())
                if total_weight > 0:
                    factor_weights = {k: v/total_weight for k, v in filtered_weights.items()}
                else:
                    n_factors = len(factor_data.columns)
                    factor_weights = {col: 1.0/n_factors for col in factor_data.columns}
                
                self.current_factor_weights = factor_weights
                
            except Exception as e:
                logger.warning(f"择时信号计算失败: {e}")
                self.current_factor_weights = self._get_default_factor_weights(factor_data)
        else:
            self.current_factor_weights = self._get_default_factor_weights(factor_data)
        
        # 计算因子得分
        return self._calculate_factor_scores(factor_data, market_data, date)

    def _select_stocks(self, 
                      scores: pd.Series,
                      market_data: Dict[str, pd.DataFrame]) -> List[str]:
        """根据得分选择股票"""
        # 确定选股数量
        market_regime = self._detect_market_regime(market_data)
        n_stocks = min(self.config.n_stocks, 
                      int(30 * self.regime_params[market_regime]['position_size']))
        
        # 按得分排序选择
        selected_stocks = scores.nlargest(n_stocks).index.tolist()
        return selected_stocks

    def generate_signal(self, 
                       market_data: Dict[str, pd.DataFrame],
                       date: pd.Timestamp) -> StrategySignal:
        """生成策略信号"""
        # 计算得分
        scores = self._calculate_scores(market_data, date)
        
        # 选择股票
        selected_stocks = self._select_stocks(scores, market_data)
        
        # 计算权重
        target_weights = self._calculate_stock_weights(selected_stocks, scores, market_data)
        
        # 应用风险控制
        target_weights = self.apply_position_limits(target_weights)
        target_weights = self.normalize_weights(target_weights)
        
        # 计算信号强度
        signal_strength = scores.std() if len(scores) > 0 else 0.0
        
        # 检测市场状态
        market_regime = self._detect_market_regime(market_data)
        
        return StrategySignal(
            date=date,
            stock_weights=target_weights,
            signal_strength=signal_strength,
            metadata={
                'factor_weights': self.current_factor_weights,
                'market_regime': market_regime,
                'n_selected_stocks': len(selected_stocks)
            }
        )

    def _generate_factor_data(self, 
                            stocks: List[str], 
                            market_data: Dict[str, pd.DataFrame], 
                            date: pd.Timestamp) -> pd.DataFrame:
        """生成因子数据"""
        factor_names = ['value', 'momentum', 'quality', 'growth', 'low_vol']
        
        # 生成模拟因子数据
        factor_data = pd.DataFrame(
            np.random.randn(len(stocks), len(factor_names)),
            index=stocks,
            columns=factor_names
        )
        
        return factor_data

    def _generate_return_data(self, 
                            market_data: Dict[str, pd.DataFrame], 
                            date: pd.Timestamp) -> pd.Series:
        """生成基准收益率数据"""
        # 从市场数据中计算平均收益率
        all_returns = []
        for stock, data in market_data.items():
            if date in data.index:
                stock_data = data.loc[:date]
                if len(stock_data) > 1:
                    returns = stock_data['close'].pct_change().dropna()
                    all_returns.extend(returns.values)
        
        if all_returns:
            return pd.Series(all_returns, index=pd.date_range(end=date, periods=len(all_returns)))
        else:
            return pd.Series(np.random.randn(100) * 0.01, 
                           index=pd.date_range(end=date, periods=100))

    def _generate_stock_returns(self, 
                              market_data: Dict[str, pd.DataFrame], 
                              date: pd.Timestamp) -> pd.DataFrame:
        """生成股票收益率数据"""
        stock_returns = {}
        
        for stock, data in market_data.items():
            if date in data.index:
                stock_data = data.loc[:date]
                if len(stock_data) > 1:
                    returns = stock_data['close'].pct_change().dropna()
                    stock_returns[stock] = returns
        
        if stock_returns:
            return pd.DataFrame(stock_returns)
        else:
            # 模拟数据
            stocks = list(market_data.keys())
            dates = pd.date_range(end=date, periods=100)
            return pd.DataFrame(
                np.random.randn(len(dates), len(stocks)) * 0.02,
                index=dates,
                columns=stocks
            )

    def _get_default_factor_weights(self, factor_data: pd.DataFrame) -> Dict[str, float]:
        """获取默认因子权重"""
        n_factors = len(factor_data.columns)
        return {col: 1.0/n_factors for col in factor_data.columns}

    def _calculate_factor_scores(self, 
                               factor_data: pd.DataFrame,
                               market_data: Dict[str, pd.DataFrame],
                               date: pd.Timestamp) -> pd.Series:
        """计算因子综合得分"""
        # 标准化因子
        factor_zscore = (factor_data - factor_data.mean()) / factor_data.std()
        
        # 使用动态权重
        weights_series = pd.Series(
            [self.current_factor_weights.get(col, 0) for col in factor_zscore.columns],
            index=factor_zscore.columns
        )
        
        factor_scores = factor_zscore @ weights_series
        return factor_scores

    def _calculate_stock_weights(self, 
                               selected_stocks: List[str],
                               scores: pd.Series,
                               market_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """计算股票权重"""
        if not selected_stocks:
            return {}
        
        # 获取市场状态
        market_regime = self._detect_market_regime(market_data)
        
        # 获取选中股票的得分
        selected_scores = scores[selected_stocks]
        
        # 得分加权
        positive_scores = selected_scores.clip(lower=0)
        if positive_scores.sum() > 0:
            base_weights = positive_scores / positive_scores.sum()
        else:
            base_weights = pd.Series(1.0 / len(selected_stocks), index=selected_stocks)
        
        # 根据市场状态调整仓位
        regime_params = self.regime_params.get(market_regime, self.regime_params['neutral'])
        position_size = regime_params['position_size']
        leverage = self.config.extra_params.get('leverage', 1.0)
        
        # 应用仓位调整
        adjusted_weights = base_weights * position_size * leverage
        
        return adjusted_weights.to_dict()

    def _detect_market_regime(self, market_data: Dict[str, pd.DataFrame]) -> str:
        """检测市场状态"""
        # 简化的市场状态检测
        all_returns = []
        for stock, data in market_data.items():
            if len(data) > 20:
                returns = data['close'].pct_change().dropna()
                all_returns.extend(returns.tail(20).values)
        
        if all_returns:
            mean_return = np.mean(all_returns)
            volatility = np.std(all_returns)
            
            if mean_return > 0.001:
                if volatility < 0.02:
                    return 'bull_low_vol'
                else:
                    return 'bull_high_vol'
            elif mean_return < -0.001:
                if volatility < 0.02:
                    return 'bear_low_vol'
                else:
                    return 'bear_high_vol'
            else:
                return 'neutral'
        else:
            return 'neutral'

    def generate_position(self,
                        factor_data: pd.DataFrame,
                        return_data: pd.Series,
                        stock_returns: pd.DataFrame,
                        date: pd.Timestamp) -> FactorTimingPosition:
        """生成因子择时持仓（兼容旧接口）"""
        # 构建市场数据格式
        market_data = {}
        for stock in stock_returns.columns:
            market_data[stock] = pd.DataFrame({
                'close': stock_returns[stock] * 100 + 100,  # 模拟价格
                'volume': np.random.uniform(10000, 50000, len(stock_returns))
            }, index=stock_returns.index)
        
        # 生成信号
        signal = self.generate_signal(market_data, date)
        
        # 计算预期收益和风险
        if signal.stock_weights:
            selected_returns = stock_returns[list(signal.stock_weights.keys())].iloc[-20:]
            expected_return = selected_returns.mean().mean() * 252
            risk_level = selected_returns.std().mean() * np.sqrt(252)
        else:
            expected_return = 0.0
            risk_level = 0.0
        
        return FactorTimingPosition(
            date=date,
            factor_weights=signal.metadata['factor_weights'],
            stock_weights=signal.stock_weights,
            market_regime=signal.metadata['market_regime'],
            timing_signal=signal.signal_strength,
            expected_return=expected_return,
            risk_level=risk_level
        )

    def rebalance_portfolio(self,
                          current_position: FactorTimingPosition,
                          new_position: FactorTimingPosition) -> Dict[str, Dict[str, float]]:
        """调整投资组合"""
        current_stocks = set(current_position.stock_weights.keys())
        new_stocks = set(new_position.stock_weights.keys())

        # 需要卖出的股票
        stocks_to_sell = current_stocks - new_stocks
        sell_orders = {stock: 0.0 for stock in stocks_to_sell}

        # 需要买入的股票
        stocks_to_buy = new_stocks - current_stocks
        buy_orders = {stock: new_position.stock_weights[stock] for stock in stocks_to_buy}

        # 需要调整的股票
        stocks_to_adjust = current_stocks & new_stocks
        for stock in stocks_to_adjust:
            current_weight = current_position.stock_weights[stock]
            new_weight = new_position.stock_weights[stock]

            # 只有权重变化超过阈值才调整
            if abs(new_weight - current_weight) > 0.01:
                if new_weight > current_weight:
                    buy_orders[stock] = new_weight - current_weight
                else:
                    sell_orders[stock] = current_weight - new_weight

        return {
            'sell': sell_orders,
            'buy': buy_orders
        }


def demo():
    """演示因子择时策略"""
    print("=" * 60)
    print("最优因子择时策略演示")
    print("=" * 60)
    
    # 创建策略实例
    strategy = OptimalFactorTimingStrategy()
    
    # 生成模拟数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', '2024-01-01', freq='D')
    
    # 模拟因子数据
    factor_names = ['value', 'momentum', 'quality', 'growth', 'low_vol']
    factor_data = pd.DataFrame(
        np.random.randn(len(dates), len(factor_names)),
        index=dates,
        columns=factor_names
    )
    
    # 模拟基准收益率
    benchmark_returns = pd.Series(
        np.random.randn(len(dates)) * 0.01,
        index=dates
    )
    
    # 模拟个股收益率
    stock_codes = [f'stock_{i:03d}' for i in range(100)]
    stock_returns = pd.DataFrame(
        np.random.randn(len(dates), len(stock_codes)) * 0.02,
        index=dates,
        columns=stock_codes
    )
    
    # 构建市场数据
    market_data = {}
    for stock in stock_codes:
        prices = 100 * np.exp(np.cumsum(stock_returns[stock]))
        market_data[stock] = pd.DataFrame({
            'close': prices,
            'volume': np.random.uniform(10000, 50000, len(dates))
        }, index=dates)
    
    # 生成信号
    test_date = dates[100]
    signal = strategy.generate_signal(market_data, test_date)
    
    print(f"信号日期: {signal.date}")
    print(f"信号强度: {signal.signal_strength:.3f}")
    print(f"市场状态: {signal.metadata['market_regime']}")
    print(f"持仓股票数: {len(signal.stock_weights)}")
    
    print(f"\n因子权重:")
    for factor, weight in signal.metadata['factor_weights'].items():
        print(f"  {factor}: {weight:.3f}")
    
    print("\n前10大持仓:")
    sorted_stocks = sorted(signal.stock_weights.items(), key=lambda x: x[1], reverse=True)[:10]
    for stock, weight in sorted_stocks:
        print(f"  {stock}: {weight:.2%}")
    
    # 运行回测
    print("\n运行回测...")
    backtest_results = strategy.backtest(market_data)
    
    # 计算性能指标
    metrics = strategy.calculate_performance_metrics(backtest_results)
    
    print("\n策略性能:")
    print(f"年化收益率: {metrics.annual_return:.2%}")
    print(f"年化波动率: {metrics.annual_volatility:.2%}")
    print(f"夏普比率: {metrics.sharpe_ratio:.2f}")
    print(f"最大回撤: {metrics.max_drawdown:.2%}")
    print(f"胜率: {metrics.win_rate:.2%}")
    print(f"总交易次数: {metrics.total_trades}")


if __name__ == "__main__":
    demo() 
