# -*- coding: utf-8 -*-
"""
空头收益转化因子研究模块
基于海通选股因子系列研究16：选股因子空头收益的转化

核心研究内容：
1. 空头收益转化机制分析
2. 因子有效性衰减研究
3. 多空组合收益差异分析
4. 因子择时信号研究
5. 风险调整收益分析

作者: Research Enhancement System
版本: 1.0.0
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import spearmanr, pearsonr
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import warnings
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 导入项目模块
try:
    from data_pipeline.data_adapter import get_adapter
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    DATA_PIPELINE_AVAILABLE = False

try:
    from factor_analyze.tools.factor_evaluator import FactorEvaluator
    from factor_analyze.factor_engine import FactorEngine
    FACTOR_ANALYSIS_AVAILABLE = True
except ImportError:
    FACTOR_ANALYSIS_AVAILABLE = False

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


@dataclass
class ShortReturnConversionResearchConfig:
    """空头收益转化研究配置"""
    # 时间参数
    lookback_window: int = 20
    ic_window: int = 60
    effectiveness_window: int = 252
    
    # 组合参数
    long_quantile: float = 0.8
    short_quantile: float = 0.2
    rebalance_frequency: int = 5
    
    # 分析参数
    min_periods: int = 20
    significance_level: float = 0.05
    rolling_window: int = 60
    
    # 可视化参数
    plot_style: str = 'seaborn'
    figure_size: tuple = (12, 8)
    save_plots: bool = True


@dataclass
class ShortReturnConversionAnalysisResult:
    """空头收益转化分析结果"""
    # 基础信息
    analysis_date: datetime
    research_period: str
    sample_size: int
    
    # 转化因子结果
    conversion_factor_stats: Dict[str, float] = field(default_factory=dict)
    conversion_factor_ic: pd.Series = field(default_factory=pd.Series)
    conversion_factor_returns: pd.DataFrame = field(default_factory=pd.DataFrame)
    
    # 有效性衰减结果
    effectiveness_decay_analysis: Dict[str, Any] = field(default_factory=dict)
    decay_trend: pd.Series = field(default_factory=pd.Series)
    decay_periods: List[str] = field(default_factory=list)
    
    # 多空收益差异结果
    long_short_spread_analysis: Dict[str, Any] = field(default_factory=dict)
    spread_distribution: pd.DataFrame = field(default_factory=pd.DataFrame)
    spread_persistence: Dict[str, float] = field(default_factory=dict)
    
    # 择时信号结果
    timing_signal_performance: Dict[str, Any] = field(default_factory=dict)
    timing_accuracy: float = 0.0
    signal_strength: pd.Series = field(default_factory=pd.Series)
    
    # 风险调整收益结果
    risk_adjusted_metrics: Dict[str, float] = field(default_factory=dict)
    risk_return_profile: pd.DataFrame = field(default_factory=pd.DataFrame)
    
    # 综合评估
    overall_effectiveness: float = 0.0
    implementation_feasibility: str = "medium"
    research_insights: List[str] = field(default_factory=list)


class ShortReturnConversionResearcher:
    """空头收益转化研究器"""
    
    def __init__(self, config: Optional[ShortReturnConversionResearchConfig] = None):
        """初始化研究器"""
        self.config = config or ShortReturnConversionResearchConfig()
        self.data_adapter = None
        self.factor_evaluator = None
        self.factor_engine = None
        
        # 初始化组件
        self._initialize_components()
        
        # 设置绘图样式
        try:
            plt.style.use(self.config.plot_style)
        except OSError:
            # 如果指定样式不存在，使用默认样式
            plt.style.use('default')
        
        logger.info("空头收益转化研究器初始化完成")
    
    def _initialize_components(self):
        """初始化研究组件"""
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.data_adapter = get_adapter()
                logger.info("✅ 数据管道初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 数据管道初始化失败: {e}")
        
        if FACTOR_ANALYSIS_AVAILABLE:
            try:
                self.factor_evaluator = FactorEvaluator()
                self.factor_engine = FactorEngine()
                logger.info("✅ 因子分析工具初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 因子分析工具初始化失败: {e}")
    
    def calculate_short_return_conversion_factor(self, data: pd.DataFrame) -> pd.Series:
        """
        计算空头收益转化因子
        
        参数:
            data: 包含价格和收益数据的DataFrame
            
        返回:
            conversion_factor: 空头收益转化因子序列
        """
        try:
            # 提取收益率
            returns = data['returns'] if 'returns' in data.columns else data['close'].pct_change()
            
            # 计算多头和空头收益的滚动平均
            long_returns = returns.rolling(self.config.lookback_window).apply(
                lambda x: x[x > 0].mean() if len(x[x > 0]) > 0 else 0
            )
            
            short_returns = returns.rolling(self.config.lookback_window).apply(
                lambda x: x[x < 0].mean() if len(x[x < 0]) > 0 else 0
            )
            
            # 计算转化因子
            denominator = long_returns.abs() + short_returns.abs()
            conversion_factor = (short_returns.abs() - long_returns.abs()) / (denominator + 1e-8)
            
            # 处理异常值
            conversion_factor = conversion_factor.clip(-3, 3)
            
            return conversion_factor.fillna(0)
            
        except Exception as e:
            logger.error(f"计算空头收益转化因子失败: {e}")
            return pd.Series(0, index=data.index)
    
    def analyze_factor_effectiveness_decay(self, factor_values: pd.Series, 
                                         returns: pd.Series) -> Dict[str, Any]:
        """
        分析因子有效性衰减
        
        参数:
            factor_values: 因子值序列
            returns: 收益率序列
            
        返回:
            decay_analysis: 衰减分析结果
        """
        try:
            decay_analysis = {}
            
            # 计算滚动IC
            rolling_ic = pd.Series(index=factor_values.index, dtype=float)
            
            for i in range(self.config.ic_window, len(factor_values)):
                window_factor = factor_values.iloc[i-self.config.ic_window+1:i+1]
                window_returns = returns.iloc[i-self.config.ic_window+1:i+1]
                
                # 计算相关系数
                correlation, p_value = spearmanr(window_factor, window_returns)
                rolling_ic.iloc[i] = correlation if not np.isnan(correlation) else 0
            
            # 计算衰减趋势
            ic_trend = rolling_ic.dropna()
            if len(ic_trend) > 0:
                # 线性回归分析趋势
                x = np.arange(len(ic_trend))
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, ic_trend)
                
                decay_analysis['trend_slope'] = slope
                decay_analysis['trend_r_squared'] = r_value**2
                decay_analysis['trend_p_value'] = p_value
                decay_analysis['ic_mean'] = ic_trend.mean()
                decay_analysis['ic_std'] = ic_trend.std()
                decay_analysis['ic_trend'] = ic_trend
                
                # 识别衰减期
                ic_ma = ic_trend.rolling(self.config.rolling_window).mean()
                decay_periods = []
                
                for i in range(1, len(ic_ma)):
                    if ic_ma.iloc[i] < ic_ma.iloc[i-1] * 0.9:  # 10%的衰减
                        decay_periods.append(ic_ma.index[i])
                
                decay_analysis['decay_periods'] = decay_periods
                decay_analysis['decay_count'] = len(decay_periods)
                
            return decay_analysis
            
        except Exception as e:
            logger.error(f"分析因子有效性衰减失败: {e}")
            return {}
    
    def analyze_long_short_spread(self, factor_values: pd.Series, 
                                returns: pd.Series) -> Dict[str, Any]:
        """
        分析多空收益差异
        
        参数:
            factor_values: 因子值序列
            returns: 收益率序列
            
        返回:
            spread_analysis: 多空收益差异分析结果
        """
        try:
            spread_analysis = {}
            
            # 构建多空组合
            long_short_returns = []
            dates = []
            
            for date in factor_values.index:
                if date in returns.index:
                    factor_val = factor_values.loc[date]
                    return_val = returns.loc[date]
                    
                    if not pd.isna(factor_val) and not pd.isna(return_val):
                        # 简化的多空信号
                        if factor_val > np.quantile(factor_values.dropna(), self.config.long_quantile):
                            position = 1  # 多头
                        elif factor_val < np.quantile(factor_values.dropna(), self.config.short_quantile):
                            position = -1  # 空头
                        else:
                            position = 0  # 中性
                        
                        long_short_returns.append(position * return_val)
                        dates.append(date)
            
            if len(long_short_returns) > 0:
                ls_returns = pd.Series(long_short_returns, index=dates)
                
                # 计算多空收益统计
                spread_analysis['mean_return'] = ls_returns.mean()
                spread_analysis['std_return'] = ls_returns.std()
                spread_analysis['sharpe_ratio'] = ls_returns.mean() / (ls_returns.std() + 1e-8)
                spread_analysis['max_drawdown'] = self._calculate_max_drawdown(ls_returns)
                spread_analysis['win_rate'] = (ls_returns > 0).mean()
                
                # 收益分布分析
                spread_analysis['return_distribution'] = {
                    'skewness': stats.skew(ls_returns.dropna()),
                    'kurtosis': stats.kurtosis(ls_returns.dropna()),
                    'percentiles': {
                        '5%': np.percentile(ls_returns.dropna(), 5),
                        '25%': np.percentile(ls_returns.dropna(), 25),
                        '50%': np.percentile(ls_returns.dropna(), 50),
                        '75%': np.percentile(ls_returns.dropna(), 75),
                        '95%': np.percentile(ls_returns.dropna(), 95)
                    }
                }
                
                # 持续性分析
                spread_analysis['persistence'] = {
                    'autocorr_1': ls_returns.autocorr(lag=1),
                    'autocorr_5': ls_returns.autocorr(lag=5),
                    'autocorr_20': ls_returns.autocorr(lag=20)
                }
                
                spread_analysis['returns_series'] = ls_returns
            
            return spread_analysis
            
        except Exception as e:
            logger.error(f"分析多空收益差异失败: {e}")
            return {}
    
    def generate_timing_signals(self, factor_values: pd.Series) -> Dict[str, Any]:
        """
        生成因子择时信号
        
        参数:
            factor_values: 因子值序列
            
        返回:
            timing_analysis: 择时信号分析结果
        """
        try:
            timing_analysis = {}
            
            # 计算移动平均
            fast_ma = factor_values.rolling(self.config.lookback_window).mean()
            slow_ma = factor_values.rolling(self.config.lookback_window * 3).mean()
            
            # 生成信号
            signals = pd.Series(0, index=factor_values.index)
            signals[fast_ma > slow_ma] = 1
            signals[fast_ma < slow_ma] = -1
            
            # 信号强度
            signal_strength = abs(fast_ma - slow_ma) / (slow_ma + 1e-8)
            
            # 信号统计
            timing_analysis['signal_distribution'] = {
                'long_signals': (signals == 1).sum(),
                'short_signals': (signals == -1).sum(),
                'neutral_signals': (signals == 0).sum()
            }
            
            timing_analysis['signal_strength'] = {
                'mean': signal_strength.mean(),
                'std': signal_strength.std(),
                'max': signal_strength.max(),
                'min': signal_strength.min()
            }
            
            # 信号持续性
            signal_changes = (signals != signals.shift(1)).sum()
            timing_analysis['signal_stability'] = {
                'change_frequency': signal_changes / len(signals),
                'average_hold_period': len(signals) / signal_changes if signal_changes > 0 else np.inf
            }
            
            timing_analysis['signals'] = signals
            timing_analysis['signal_strength_series'] = signal_strength
            
            return timing_analysis
            
        except Exception as e:
            logger.error(f"生成择时信号失败: {e}")
            return {}
    
    def calculate_risk_adjusted_metrics(self, returns: pd.Series, 
                                      benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
        """
        计算风险调整收益指标
        
        参数:
            returns: 策略收益序列
            benchmark_returns: 基准收益序列（可选）
            
        返回:
            risk_metrics: 风险调整收益指标
        """
        try:
            risk_metrics = {}
            
            # 基础指标
            risk_metrics['total_return'] = returns.sum()
            risk_metrics['annualized_return'] = returns.mean() * 252
            risk_metrics['volatility'] = returns.std() * np.sqrt(252)
            risk_metrics['sharpe_ratio'] = (returns.mean() * 252) / (returns.std() * np.sqrt(252) + 1e-8)
            risk_metrics['max_drawdown'] = self._calculate_max_drawdown(returns)
            risk_metrics['calmar_ratio'] = (returns.mean() * 252) / (abs(risk_metrics['max_drawdown']) + 1e-8)
            
            # 如果有基准收益，计算相对指标
            if benchmark_returns is not None:
                excess_returns = returns - benchmark_returns
                risk_metrics['excess_return'] = excess_returns.mean() * 252
                risk_metrics['tracking_error'] = excess_returns.std() * np.sqrt(252)
                risk_metrics['information_ratio'] = (excess_returns.mean() * 252) / (excess_returns.std() * np.sqrt(252) + 1e-8)
                risk_metrics['beta'] = returns.cov(benchmark_returns) / (benchmark_returns.var() + 1e-8)
                risk_metrics['alpha'] = returns.mean() - risk_metrics['beta'] * benchmark_returns.mean()
            
            # 下行风险指标
            negative_returns = returns[returns < 0]
            if len(negative_returns) > 0:
                risk_metrics['downside_deviation'] = negative_returns.std() * np.sqrt(252)
                risk_metrics['sortino_ratio'] = (returns.mean() * 252) / (negative_returns.std() * np.sqrt(252) + 1e-8)
            
            # VaR和CVaR
            risk_metrics['var_95'] = np.percentile(returns.dropna(), 5)
            risk_metrics['cvar_95'] = returns[returns <= risk_metrics['var_95']].mean()
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"计算风险调整收益指标失败: {e}")
            return {}
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        try:
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            return drawdown.min()
        except:
            return 0.0
    
    def comprehensive_research(self, data: pd.DataFrame) -> ShortReturnConversionAnalysisResult:
        """
        综合研究分析
        
        参数:
            data: 包含价格和收益数据的DataFrame
            
        返回:
            analysis_result: 综合分析结果
        """
        try:
            logger.info("开始综合空头收益转化研究")
            
            # 初始化结果
            result = ShortReturnConversionAnalysisResult(
                analysis_date=datetime.now(),
                research_period=f"{data.index[0]} - {data.index[-1]}",
                sample_size=len(data)
            )
            
            # 计算收益率
            returns = data['returns'] if 'returns' in data.columns else data['close'].pct_change()
            
            # 1. 计算空头收益转化因子
            logger.info("计算空头收益转化因子...")
            conversion_factor = self.calculate_short_return_conversion_factor(data)
            
            # 因子统计
            result.conversion_factor_stats = {
                'mean': conversion_factor.mean(),
                'std': conversion_factor.std(),
                'min': conversion_factor.min(),
                'max': conversion_factor.max(),
                'skewness': stats.skew(conversion_factor.dropna()),
                'kurtosis': stats.kurtosis(conversion_factor.dropna())
            }
            
            # 2. 分析因子有效性衰减
            logger.info("分析因子有效性衰减...")
            result.effectiveness_decay_analysis = self.analyze_factor_effectiveness_decay(
                conversion_factor, returns
            )
            
            # 3. 分析多空收益差异
            logger.info("分析多空收益差异...")
            result.long_short_spread_analysis = self.analyze_long_short_spread(
                conversion_factor, returns
            )
            
            # 4. 生成择时信号
            logger.info("生成择时信号...")
            result.timing_signal_performance = self.generate_timing_signals(conversion_factor)
            
            # 5. 计算风险调整收益指标
            logger.info("计算风险调整收益指标...")
            if 'returns_series' in result.long_short_spread_analysis:
                result.risk_adjusted_metrics = self.calculate_risk_adjusted_metrics(
                    result.long_short_spread_analysis['returns_series']
                )
            
            # 6. 综合评估
            result.overall_effectiveness = self._calculate_overall_effectiveness(result)
            result.implementation_feasibility = self._assess_implementation_feasibility(result)
            result.research_insights = self._generate_research_insights(result)
            
            logger.info("综合研究分析完成")
            return result
            
        except Exception as e:
            logger.error(f"综合研究分析失败: {e}")
            raise
    
    def _calculate_overall_effectiveness(self, result: ShortReturnConversionAnalysisResult) -> float:
        """计算整体有效性评分"""
        try:
            effectiveness_score = 0.0
            
            # 因子质量评分 (30%)
            factor_quality = 0.0
            if result.conversion_factor_stats:
                # 标准化后的因子质量
                factor_quality = min(1.0, abs(result.conversion_factor_stats.get('mean', 0)) * 10)
            
            # 衰减分析评分 (25%)
            decay_score = 0.0
            if result.effectiveness_decay_analysis:
                ic_mean = result.effectiveness_decay_analysis.get('ic_mean', 0)
                decay_score = min(1.0, abs(ic_mean) * 20)
            
            # 多空收益评分 (30%)
            spread_score = 0.0
            if result.long_short_spread_analysis:
                sharpe = result.long_short_spread_analysis.get('sharpe_ratio', 0)
                spread_score = min(1.0, max(0, sharpe) / 2)
            
            # 择时信号评分 (15%)
            timing_score = 0.0
            if result.timing_signal_performance:
                signal_strength = result.timing_signal_performance.get('signal_strength', {})
                timing_score = min(1.0, signal_strength.get('mean', 0) * 2)
            
            # 加权平均
            effectiveness_score = (
                factor_quality * 0.3 +
                decay_score * 0.25 +
                spread_score * 0.3 +
                timing_score * 0.15
            )
            
            return effectiveness_score
            
        except Exception as e:
            logger.error(f"计算整体有效性评分失败: {e}")
            return 0.0
    
    def _assess_implementation_feasibility(self, result: ShortReturnConversionAnalysisResult) -> str:
        """评估实施可行性"""
        try:
            score = result.overall_effectiveness
            
            if score > 0.7:
                return "high"
            elif score > 0.4:
                return "medium"
            else:
                return "low"
                
        except Exception as e:
            logger.error(f"评估实施可行性失败: {e}")
            return "unknown"
    
    def _generate_research_insights(self, result: ShortReturnConversionAnalysisResult) -> List[str]:
        """生成研究洞察"""
        try:
            insights = []
            
            # 因子质量洞察
            if result.conversion_factor_stats:
                mean_val = result.conversion_factor_stats.get('mean', 0)
                if abs(mean_val) > 0.1:
                    insights.append(f"空头收益转化因子显示{('正向' if mean_val > 0 else '负向')}偏向，平均值为{mean_val:.3f}")
            
            # 衰减分析洞察
            if result.effectiveness_decay_analysis:
                trend_slope = result.effectiveness_decay_analysis.get('trend_slope', 0)
                if trend_slope < -0.001:
                    insights.append("因子有效性存在显著衰减趋势，需要动态调整")
                elif trend_slope > 0.001:
                    insights.append("因子有效性呈现增强趋势，可持续使用")
            
            # 多空收益洞察
            if result.long_short_spread_analysis:
                sharpe = result.long_short_spread_analysis.get('sharpe_ratio', 0)
                if sharpe > 1.0:
                    insights.append("多空策略表现优异，夏普比率超过1.0")
                elif sharpe > 0.5:
                    insights.append("多空策略表现良好，具有实用价值")
            
            # 择时信号洞察
            if result.timing_signal_performance:
                signal_dist = result.timing_signal_performance.get('signal_distribution', {})
                total_signals = sum(signal_dist.values())
                if total_signals > 0:
                    long_ratio = signal_dist.get('long_signals', 0) / total_signals
                    if long_ratio > 0.6:
                        insights.append("择时信号偏向多头，适合多头市场")
                    elif long_ratio < 0.4:
                        insights.append("择时信号偏向空头，适合空头市场")
            
            # 风险调整收益洞察
            if result.risk_adjusted_metrics:
                max_dd = result.risk_adjusted_metrics.get('max_drawdown', 0)
                if max_dd < -0.2:
                    insights.append("策略存在较大回撤风险，需要加强风险管理")
                elif max_dd > -0.1:
                    insights.append("策略回撤控制良好，风险相对较小")
            
            return insights
            
        except Exception as e:
            logger.error(f"生成研究洞察失败: {e}")
            return []
    
    def visualize_analysis_results(self, result: ShortReturnConversionAnalysisResult, 
                                 output_dir: str = "research_output"):
        """可视化分析结果"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 1. 因子分布图
            if result.conversion_factor_stats:
                plt.figure(figsize=self.config.figure_size)
                
                # 模拟因子分布数据
                np.random.seed(42)
                factor_data = np.random.normal(
                    result.conversion_factor_stats.get('mean', 0),
                    result.conversion_factor_stats.get('std', 1),
                    1000
                )
                
                plt.hist(factor_data, bins=50, alpha=0.7, edgecolor='black')
                plt.axvline(result.conversion_factor_stats.get('mean', 0), color='red', linestyle='--', label='均值')
                plt.title('空头收益转化因子分布', fontsize=14)
                plt.xlabel('因子值')
                plt.ylabel('频率')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                if self.config.save_plots:
                    plt.savefig(os.path.join(output_dir, 'factor_distribution.png'), dpi=300, bbox_inches='tight')
                plt.close()
            
            # 2. IC趋势图
            if result.effectiveness_decay_analysis and 'ic_trend' in result.effectiveness_decay_analysis:
                plt.figure(figsize=self.config.figure_size)
                
                ic_trend = result.effectiveness_decay_analysis['ic_trend']
                plt.plot(ic_trend.index, ic_trend.values, label='滚动IC', alpha=0.7)
                plt.axhline(y=0, color='red', linestyle='--', alpha=0.5)
                plt.title('因子有效性衰减趋势', fontsize=14)
                plt.xlabel('时间')
                plt.ylabel('IC值')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                if self.config.save_plots:
                    plt.savefig(os.path.join(output_dir, 'ic_trend.png'), dpi=300, bbox_inches='tight')
                plt.close()
            
            # 3. 多空收益图
            if result.long_short_spread_analysis and 'returns_series' in result.long_short_spread_analysis:
                plt.figure(figsize=self.config.figure_size)
                
                returns_series = result.long_short_spread_analysis['returns_series']
                cumulative_returns = (1 + returns_series).cumprod()
                
                plt.plot(cumulative_returns.index, cumulative_returns.values, label='多空策略累计收益')
                plt.title('多空策略收益表现', fontsize=14)
                plt.xlabel('时间')
                plt.ylabel('累计收益')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                if self.config.save_plots:
                    plt.savefig(os.path.join(output_dir, 'long_short_returns.png'), dpi=300, bbox_inches='tight')
                plt.close()
            
            # 4. 择时信号图
            if result.timing_signal_performance and 'signals' in result.timing_signal_performance:
                plt.figure(figsize=self.config.figure_size)
                
                signals = result.timing_signal_performance['signals']
                signal_strength = result.timing_signal_performance.get('signal_strength_series', signals)
                
                plt.subplot(2, 1, 1)
                plt.plot(signals.index, signals.values, label='择时信号', alpha=0.7)
                plt.title('择时信号', fontsize=12)
                plt.ylabel('信号值')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                plt.subplot(2, 1, 2)
                plt.plot(signal_strength.index, signal_strength.values, label='信号强度', alpha=0.7, color='orange')
                plt.title('信号强度', fontsize=12)
                plt.xlabel('时间')
                plt.ylabel('强度')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                plt.tight_layout()
                
                if self.config.save_plots:
                    plt.savefig(os.path.join(output_dir, 'timing_signals.png'), dpi=300, bbox_inches='tight')
                plt.close()
            
            logger.info(f"可视化结果已保存到: {output_dir}")
            
        except Exception as e:
            logger.error(f"可视化分析结果失败: {e}")
    
    def generate_research_report(self, result: ShortReturnConversionAnalysisResult, 
                               output_path: str = "research/research_output/short_return_conversion_report.md"):
        """生成研究报告"""
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# 空头收益转化因子研究报告\n\n")
                f.write(f"**分析时间**: {result.analysis_date}\n")
                f.write(f"**研究期间**: {result.research_period}\n")
                f.write(f"**样本量**: {result.sample_size}\n\n")
                
                f.write("## 1. 执行摘要\n\n")
                f.write(f"- **整体有效性评分**: {result.overall_effectiveness:.3f}\n")
                f.write(f"- **实施可行性**: {result.implementation_feasibility}\n")
                f.write(f"- **主要发现**: {len(result.research_insights)} 项重要洞察\n\n")
                
                f.write("## 2. 因子基础统计\n\n")
                if result.conversion_factor_stats:
                    f.write("| 统计指标 | 值 |\n")
                    f.write("|---------|----|\n")
                    for key, value in result.conversion_factor_stats.items():
                        f.write(f"| {key} | {value:.4f} |\n")
                    f.write("\n")
                
                f.write("## 3. 因子有效性衰减分析\n\n")
                if result.effectiveness_decay_analysis:
                    f.write("### 3.1 衰减趋势分析\n\n")
                    decay_info = result.effectiveness_decay_analysis
                    f.write(f"- **趋势斜率**: {decay_info.get('trend_slope', 0):.6f}\n")
                    f.write(f"- **趋势R²**: {decay_info.get('trend_r_squared', 0):.4f}\n")
                    f.write(f"- **IC均值**: {decay_info.get('ic_mean', 0):.4f}\n")
                    f.write(f"- **IC标准差**: {decay_info.get('ic_std', 0):.4f}\n")
                    f.write(f"- **衰减期数量**: {decay_info.get('decay_count', 0)}\n\n")
                
                f.write("## 4. 多空收益差异分析\n\n")
                if result.long_short_spread_analysis:
                    f.write("### 4.1 策略表现指标\n\n")
                    spread_info = result.long_short_spread_analysis
                    f.write(f"- **平均收益**: {spread_info.get('mean_return', 0):.4f}\n")
                    f.write(f"- **收益标准差**: {spread_info.get('std_return', 0):.4f}\n")
                    f.write(f"- **夏普比率**: {spread_info.get('sharpe_ratio', 0):.4f}\n")
                    f.write(f"- **最大回撤**: {spread_info.get('max_drawdown', 0):.4f}\n")
                    f.write(f"- **胜率**: {spread_info.get('win_rate', 0):.4f}\n\n")
                
                f.write("## 5. 择时信号分析\n\n")
                if result.timing_signal_performance:
                    f.write("### 5.1 信号分布\n\n")
                    signal_dist = result.timing_signal_performance.get('signal_distribution', {})
                    f.write(f"- **多头信号**: {signal_dist.get('long_signals', 0)}\n")
                    f.write(f"- **空头信号**: {signal_dist.get('short_signals', 0)}\n")
                    f.write(f"- **中性信号**: {signal_dist.get('neutral_signals', 0)}\n\n")
                
                f.write("## 6. 风险调整收益分析\n\n")
                if result.risk_adjusted_metrics:
                    f.write("### 6.1 风险收益指标\n\n")
                    f.write("| 指标 | 值 |\n")
                    f.write("|------|----|\n")
                    for key, value in result.risk_adjusted_metrics.items():
                        f.write(f"| {key} | {value:.4f} |\n")
                    f.write("\n")
                
                f.write("## 7. 研究洞察\n\n")
                for i, insight in enumerate(result.research_insights, 1):
                    f.write(f"{i}. {insight}\n")
                f.write("\n")
                
                f.write("## 8. 实施建议\n\n")
                f.write("基于以上分析，给出以下实施建议：\n\n")
                
                if result.overall_effectiveness > 0.7:
                    f.write("- ✅ **建议立即实施**：因子表现优异，具有很高的实用价值\n")
                elif result.overall_effectiveness > 0.4:
                    f.write("- ⚠️ **建议谨慎实施**：因子表现一般，需要进一步优化\n")
                else:
                    f.write("- ❌ **不建议实施**：因子表现较差，需要重新设计\n")
                
                f.write("- 定期监控因子有效性，及时调整参数\n")
                f.write("- 结合其他因子使用，避免单一因子风险\n")
                f.write("- 考虑市场环境变化，动态调整策略\n\n")
                
                f.write("---\n")
                f.write("*本报告由空头收益转化因子研究系统自动生成*\n")
            
            logger.info(f"研究报告已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"生成研究报告失败: {e}")
    
    def save_analysis_results(self, result: ShortReturnConversionAnalysisResult, 
                            output_path: str = "research/research_output/analysis_results.json"):
        """保存分析结果"""
        try:
            import json
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 准备要保存的数据
            save_data = {
                'analysis_date': result.analysis_date.isoformat(),
                'research_period': result.research_period,
                'sample_size': result.sample_size,
                'conversion_factor_stats': result.conversion_factor_stats,
                'effectiveness_decay_analysis': result.effectiveness_decay_analysis,
                'long_short_spread_analysis': result.long_short_spread_analysis,
                'timing_signal_performance': result.timing_signal_performance,
                'risk_adjusted_metrics': result.risk_adjusted_metrics,
                'overall_effectiveness': result.overall_effectiveness,
                'implementation_feasibility': result.implementation_feasibility,
                'research_insights': result.research_insights
            }
            
            # 保存到JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"分析结果已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")


def demo_research():
    """研究演示"""
    logger.info("🚀 启动空头收益转化因子研究演示")
    
    # 创建研究器
    researcher = ShortReturnConversionResearcher()
    
    # 生成模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    data = pd.DataFrame({
        'close': 100 + np.cumsum(np.random.normal(0, 1, len(dates))),
        'volume': np.random.exponential(1000000, len(dates)),
        'returns': np.random.normal(0, 0.02, len(dates))
    }, index=dates)
    
    # 执行研究
    result = researcher.comprehensive_research(data)
    
    # 生成可视化
    researcher.visualize_analysis_results(result)
    
    # 生成报告
    researcher.generate_research_report(result)
    
    # 保存结果
    researcher.save_analysis_results(result)
    
    print(f"\n✅ 研究演示完成，整体有效性评分: {result.overall_effectiveness:.3f}")
    print(f"✅ 实施可行性: {result.implementation_feasibility}")
    print(f"✅ 发现洞察: {len(result.research_insights)} 项")
    
    return result


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    demo_result = demo_research() 