#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CGOæ¨¡å—é›†æˆæµ‹è¯•è„šæœ¬

æµ‹è¯•CGOè¡Œä¸ºé‡‘èå­¦ç ”ç©¶æ¨¡å—çš„å®Œæ•´åŠŸèƒ½
åŒ…æ‹¬å› å­è®¡ç®—ã€ç­–ç•¥å›æµ‹ã€æŠ¥å‘Šç”Ÿæˆç­‰

ä½œè€…ï¼šTest Team
æ—¥æœŸï¼?025-06-26
"""

import numpy as np
import pandas as pd
import sys
from pathlib import Path
import logging
from datetime import datetime

# æ·»åŠ é¡¹ç›®è·¯å¾„
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# å¯¼å…¥CGOæ¨¡å—
from research.cgo_behavioral_finance_research import CGOBehavioralFinanceResearcher
from research.cgo_factor_integration import CGOFactorAPI, quick_cgo_analysis
from research.cgo_strategy_template import create_cgo_strategy, quick_cgo_backtest

# é…ç½®æ—¥å¿—
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_simulation_data(n_days: int = 500, n_stocks: int = 50, seed: int = 42) -> dict:
    """
    ç”Ÿæˆæ¨¡æ‹Ÿå¸‚åœºæ•°æ®ç”¨äºæµ‹è¯•
    
    Args:
        n_days: äº¤æ˜“æ—¥æ•°é‡?
        n_stocks: è‚¡ç¥¨æ•°é‡
        seed: éšæœºç§å­
        
    Returns:
        æ¨¡æ‹Ÿå¸‚åœºæ•°æ®å­—å…¸
    """
    np.random.seed(seed)
    
    # ç”Ÿæˆæ—¥æœŸåºåˆ—ï¼ˆå·¥ä½œæ—¥ï¼?
    dates = pd.date_range('2022-01-01', periods=n_days, freq='B')
    
    # ç”Ÿæˆè‚¡ç¥¨ä»£ç 
    symbols = [f'TEST_{i:03d}.SZ' for i in range(1, n_stocks + 1)]
    
    logger.info(f"ç”Ÿæˆæ¨¡æ‹Ÿæ•°æ®: {n_days}ä¸ªäº¤æ˜“æ—¥ Ã— {n_stocks}åªè‚¡ç¥?)
    
    # ç”Ÿæˆä»·æ ¼æ•°æ®ï¼ˆå‡ ä½•å¸ƒæœ—è¿åŠ¨ï¼‰
    price_returns = np.random.normal(0.0005, 0.02, (n_days, n_stocks))  # æ—¥æ”¶ç›Šç‡
    price_levels = np.exp(np.cumsum(price_returns, axis=0)) * 100  # ç´¯ç§¯ä»·æ ¼
    
    # æ·»åŠ ä¸€äº›è¶‹åŠ¿å’Œå‘¨æœŸæ€?
    trend = np.linspace(0.95, 1.05, n_days).reshape(-1, 1)
    seasonal = 1 + 0.1 * np.sin(2 * np.pi * np.arange(n_days) / 252).reshape(-1, 1)
    price_levels = price_levels * trend * seasonal
    
    close_prices = pd.DataFrame(price_levels, index=dates, columns=symbols)
    
    # ç”Ÿæˆæ¢æ‰‹ç‡æ•°æ®ï¼ˆæŒ‡æ•°åˆ†å¸ƒï¼?
    base_turnover = np.random.exponential(1.5, (n_days, n_stocks))
    # æ·»åŠ æ³¢åŠ¨ç‡èšç±»æ•ˆåº?
    volatility_clustering = np.random.beta(2, 5, (n_days, n_stocks)) * 5
    turnover_rates = base_turnover + volatility_clustering
    
    turnover_data = pd.DataFrame(turnover_rates, index=dates, columns=symbols)
    
    # ç”Ÿæˆæˆäº¤é‡æ•°æ®ï¼ˆå¯¹æ•°æ­£æ€åˆ†å¸ƒï¼‰
    base_volume = np.random.lognormal(13, 1, (n_days, n_stocks))  # çº?00ä¸‡è‚¡å¹³å‡
    # ä¸ä»·æ ¼å˜åŠ¨ç›¸å…³ï¼ˆä»·æ ¼å¤§å¹…å˜åŠ¨æ—¶æˆäº¤é‡å¢åŠ ï¼?
    price_change = np.abs(np.diff(price_levels, axis=0, prepend=price_levels[0:1]))
    volume_multiplier = 1 + price_change / price_levels * 2
    volumes = base_volume * volume_multiplier
    
    volume_data = pd.DataFrame(volumes, index=dates, columns=symbols)
    
    # ç”Ÿæˆæˆäº¤é¢æ•°æ?
    amount_data = close_prices * volume_data
    
    # æ·»åŠ ä¸€äº›å¼‚å¸¸å€¼å’Œç¼ºå¤±å€¼æ¥æ¨¡æ‹ŸçœŸå®æƒ…å†µ
    if n_days > 100:
        # éšæœºé€‰æ‹©ä¸€äº›ç‚¹è®¾ä¸ºå¼‚å¸¸å€?
        anomaly_mask = np.random.random((n_days, n_stocks)) < 0.01  # 1%çš„å¼‚å¸¸å€?
        turnover_data[anomaly_mask] = turnover_data[anomaly_mask] * 5  # å¼‚å¸¸é«˜æ¢æ‰‹ç‡
        
        # éšæœºè®¾ç½®ä¸€äº›ç¼ºå¤±å€?
        missing_mask = np.random.random((n_days, n_stocks)) < 0.005  # 0.5%çš„ç¼ºå¤±å€?
        volume_data[missing_mask] = np.nan
    
    market_data = {
        'close': close_prices,
        'turnover': turnover_data,
        'volume': volume_data,
        'amount': amount_data
    }
    
    logger.info("æ¨¡æ‹Ÿæ•°æ®ç”Ÿæˆå®Œæˆ")
    return market_data

def test_cgo_factor_calculation():
    """æµ‹è¯•CGOå› å­è®¡ç®—åŠŸèƒ½"""
    logger.info("=== æµ‹è¯•CGOå› å­è®¡ç®— ===")
    
    try:
        # ç”Ÿæˆæµ‹è¯•æ•°æ®
        market_data = generate_simulation_data(n_days=300, n_stocks=30)
        
        # åˆ›å»ºç ”ç©¶å™?
        researcher = CGOBehavioralFinanceResearcher(window=100, data_path="./tests/tests/test_output")
        
        # æ‰§è¡Œç ”ç©¶æµæ°´çº?
        results = researcher.research_pipeline(
            price_data=market_data['close'],
            turnover_data=market_data['turnover'],
            volume_data=market_data['volume'],
            amount_data=market_data['amount'],
            analysis_suffix="test_run"
        )
        
        if results and 'cgo_factor' in results:
            cgo_factor = results['cgo_factor']
            logger.info(f"âœ?CGOå› å­è®¡ç®—æˆåŠŸ")
            logger.info(f"   - å› å­æ•°æ®å½¢çŠ¶: {cgo_factor.shape}")
            logger.info(f"   - æ—¶é—´è·¨åº¦: {cgo_factor.index[0]} åˆ?{cgo_factor.index[-1]}")
            logger.info(f"   - è‚¡ç¥¨æ•°é‡: {len(cgo_factor.columns)}")
            logger.info(f"   - æœ‰æ•ˆå€¼æ¯”ä¾? {cgo_factor.notna().sum().sum() / cgo_factor.size:.2%}")
            
            # è¾“å‡ºä¸€äº›åŸºæœ¬ç»Ÿè®?
            flat_cgo = cgo_factor.stack().dropna()
            if len(flat_cgo) > 0:
                logger.info(f"   - CGOå‡å€? {flat_cgo.mean():.4f}")
                logger.info(f"   - CGOæ ‡å‡†å·? {flat_cgo.std():.4f}")
                logger.info(f"   - CGOååº¦: {flat_cgo.skew():.4f}")
            
            return True, results
        else:
            logger.error("â?CGOå› å­è®¡ç®—å¤±è´¥")
            return False, {}
            
    except Exception as e:
        logger.error(f"â?CGOå› å­è®¡ç®—æµ‹è¯•å‡ºé”™: {e}")
        return False, {}

def test_factor_integration():
    """æµ‹è¯•å› å­é›†æˆåŠŸèƒ½"""
    logger.info("=== æµ‹è¯•å› å­é›†æˆ ===")
    
    try:
        # ç”Ÿæˆæµ‹è¯•æ•°æ®
        market_data = generate_simulation_data(n_days=200, n_stocks=25)
        
        # ä½¿ç”¨å¿«é€Ÿåˆ†ææ¥å?
        results = quick_cgo_analysis(
            market_data['close'],
            market_data['turnover'],
            market_data['volume'],
            market_data['amount'],
            config={'cgo_window': 50}  # è¾ƒçŸ­çª—å£æœŸç”¨äºæµ‹è¯?
        )
        
        if 'error' not in results:
            logger.info("âœ?å› å­é›†æˆæµ‹è¯•æˆåŠŸ")
            logger.info(f"   - é›†æˆæ¥å£æ­£å¸¸å·¥ä½œ")
            logger.info(f"   - APIå¯¹è±¡åˆ›å»ºæˆåŠŸ: {type(results['api'])}")
            
            # æµ‹è¯•APIæ¥å£
            api = results['api']
            cgo_factor = results['cgo_factor']
            
            # æµ‹è¯•é€‰è‚¡åŠŸèƒ½
            if not cgo_factor.empty:
                latest_date = cgo_factor.index[-1].strftime('%Y-%m-%d')
                selected_stocks = api.select_stocks(cgo_factor, latest_date)
                logger.info(f"   - é€‰è‚¡æµ‹è¯•: é€‰ä¸­ {len(selected_stocks)} åªè‚¡ç¥?)
                
            return True, results
        else:
            logger.error(f"â?å› å­é›†æˆæµ‹è¯•å¤±è´¥: {results['error']}")
            return False, {}
            
    except Exception as e:
        logger.error(f"â?å› å­é›†æˆæµ‹è¯•å‡ºé”™: {e}")
        return False, {}

def test_strategy_backtest():
    """æµ‹è¯•ç­–ç•¥å›æµ‹åŠŸèƒ½"""
    logger.info("=== æµ‹è¯•ç­–ç•¥å›æµ‹ ===")
    
    try:
        # ç”Ÿæˆæµ‹è¯•æ•°æ®
        market_data = generate_simulation_data(n_days=250, n_stocks=40)
        
        # æµ‹è¯•ä¸åŒç­–ç•¥ç±»å‹
        strategy_types = ['basic', 'conservative', 'aggressive']
        
        all_results = {}
        
        for strategy_type in strategy_types:
            logger.info(f"æµ‹è¯•{strategy_type}ç­–ç•¥...")
            
            # æ‰§è¡Œå›æµ‹
            backtest_results = quick_cgo_backtest(
                market_data, 
                '2022-06-01', '2022-12-31',  # ä½¿ç”¨éƒ¨åˆ†æ•°æ®è¿›è¡Œå›æµ‹
                strategy_type
            )
            
            if 'error' not in backtest_results:
                perf = backtest_results['performance']
                logger.info(f"   âœ?{strategy_type}ç­–ç•¥å›æµ‹æˆåŠŸ")
                logger.info(f"      - æ€»æ”¶ç›Šç‡: {perf.get('total_return', 0):.2%}")
                logger.info(f"      - å¹´åŒ–æ”¶ç›Š: {perf.get('annual_return', 0):.2%}")
                logger.info(f"      - æœ€å¤§å›æ’? {perf.get('max_drawdown', 0):.2%}")
                logger.info(f"      - å¤æ™®æ¯”ç‡: {perf.get('sharpe_ratio', 0):.3f}")
                logger.info(f"      - èƒœç‡: {perf.get('win_rate', 0):.2%}")
                
                all_results[strategy_type] = backtest_results
            else:
                logger.error(f"   â?{strategy_type}ç­–ç•¥å›æµ‹å¤±è´¥: {backtest_results['error']}")
        
        if all_results:
            logger.info("âœ?ç­–ç•¥å›æµ‹åŠŸèƒ½æµ‹è¯•é€šè¿‡")
            return True, all_results
        else:
            logger.error("â?æ‰€æœ‰ç­–ç•¥å›æµ‹å‡å¤±è´¥")
            return False, {}
            
    except Exception as e:
        logger.error(f"â?ç­–ç•¥å›æµ‹æµ‹è¯•å‡ºé”™: {e}")
        return False, {}

def test_custom_strategy():
    """æµ‹è¯•è‡ªå®šä¹‰ç­–ç•¥é…ç½?""
    logger.info("=== æµ‹è¯•è‡ªå®šä¹‰ç­–ç•?===")
    
    try:
        # ç”Ÿæˆæµ‹è¯•æ•°æ®
        market_data = generate_simulation_data(n_days=150, n_stocks=20)
        
        # åˆ›å»ºè‡ªå®šä¹‰ç­–ç•¥é…ç½?
        custom_config = {
            'strategy': {
                'rebalance_freq': 'W',
                'max_stocks': 15,
                'min_stocks': 5,
                'position_limit': 0.15,
                'turnover_limit': 0.8
            },
            'risk_management': {
                'stop_loss': -0.08,
                'take_profit': 0.12,
                'volatility_target': 0.18
            },
            'cgo_config': {
                'cgo_window': 60,
                'lambda_threshold': 0.05,
                'risk_adjustment': True
            }
        }
        
        # åˆ›å»ºè‡ªå®šä¹‰ç­–ç•?
        strategy = create_cgo_strategy('basic', **custom_config)
        
        # æ‰§è¡Œç­–ç•¥
        results = strategy.execute_strategy(
            market_data, '2022-03-01', '2022-11-30'
        )
        
        if 'error' not in results:
            logger.info("âœ?è‡ªå®šä¹‰ç­–ç•¥æµ‹è¯•æˆåŠ?)
            logger.info(f"   - è‡ªå®šä¹‰é…ç½®åº”ç”¨æˆåŠ?)
            logger.info(f"   - ç­–ç•¥æ‰§è¡Œæ­£å¸¸")
            
            if 'performance' in results:
                perf = results['performance']
                logger.info(f"   - æ€»æ”¶ç›Šç‡: {perf.get('total_return', 0):.2%}")
                logger.info(f"   - å¤æ™®æ¯”ç‡: {perf.get('sharpe_ratio', 0):.3f}")
            
            return True, results
        else:
            logger.error(f"â?è‡ªå®šä¹‰ç­–ç•¥æµ‹è¯•å¤±è´? {results['error']}")
            return False, {}
            
    except Exception as e:
        logger.error(f"â?è‡ªå®šä¹‰ç­–ç•¥æµ‹è¯•å‡ºé”? {e}")
        return False, {}

def test_edge_cases():
    """æµ‹è¯•è¾¹ç•Œæƒ…å†µå’Œå¼‚å¸¸å¤„ç?""
    logger.info("=== æµ‹è¯•è¾¹ç•Œæƒ…å†µ ===")
    
    try:
        # æµ‹è¯•1ï¼šæ•°æ®é‡ä¸è¶³
        logger.info("æµ‹è¯•æ•°æ®é‡ä¸è¶³çš„æƒ…å†µ...")
        small_data = generate_simulation_data(n_days=50, n_stocks=10)
        
        researcher = CGOBehavioralFinanceResearcher(window=100)  # çª—å£æœŸå¤§äºæ•°æ®é•¿åº?
        results = researcher.research_pipeline(
            small_data['close'], small_data['turnover'], 
            small_data['volume'], small_data['amount']
        )
        
        if not results or results.get('cgo_factor', pd.DataFrame()).empty:
            logger.info("   âœ?æ­£ç¡®å¤„ç†äº†æ•°æ®ä¸è¶³çš„æƒ…å†µ")
        else:
            logger.warning("   âš ï¸ æ•°æ®ä¸è¶³æ—¶ä»ç„¶è®¡ç®—äº†å› å­")
        
        # æµ‹è¯•2ï¼šåŒ…å«å¤§é‡ç¼ºå¤±å€¼çš„æ•°æ®
        logger.info("æµ‹è¯•å¤§é‡ç¼ºå¤±å€¼çš„æƒ…å†µ...")
        missing_data = generate_simulation_data(n_days=200, n_stocks=15)
        
        # äººä¸ºæ·»åŠ å¤§é‡ç¼ºå¤±å€?
        for key in missing_data:
            mask = np.random.random(missing_data[key].shape) < 0.3  # 30%ç¼ºå¤±
            missing_data[key][mask] = np.nan
        
        api = CGOFactorAPI()
        cgo_factor = api.compute_factor(
            missing_data['close'], missing_data['turnover'],
            missing_data['volume'], missing_data['amount']
        )
        
        if cgo_factor.empty:
            logger.info("   âœ?æ­£ç¡®å¤„ç†äº†å¤§é‡ç¼ºå¤±å€¼çš„æƒ…å†µ")
        else:
            valid_ratio = cgo_factor.notna().sum().sum() / cgo_factor.size
            logger.info(f"   âœ?å¤„ç†ç¼ºå¤±å€¼åä»æœ‰ {valid_ratio:.1%} æœ‰æ•ˆæ•°æ®")
        
        # æµ‹è¯•3ï¼šæç«¯å€¼å¤„ç?
        logger.info("æµ‹è¯•æç«¯å€¼å¤„ç?..")
        extreme_data = generate_simulation_data(n_days=100, n_stocks=20)
        
        # æ·»åŠ æç«¯å€?
        extreme_data['turnover'].iloc[10:15, 5:10] = 100  # æç«¯é«˜æ¢æ‰‹ç‡
        extreme_data['close'].iloc[20:25, 0:5] *= 10      # æç«¯ä»·æ ¼è·³è·ƒ
        
        results = quick_cgo_analysis(
            extreme_data['close'], extreme_data['turnover'],
            extreme_data['volume'], extreme_data['amount']
        )
        
        if 'error' not in results:
            logger.info("   âœ?æ­£ç¡®å¤„ç†äº†æç«¯å€?)
        else:
            logger.info(f"   âš ï¸ æç«¯å€¼å¤„ç†å¤±è´? {results['error']}")
        
        logger.info("âœ?è¾¹ç•Œæƒ…å†µæµ‹è¯•å®Œæˆ")
        return True
        
    except Exception as e:
        logger.error(f"â?è¾¹ç•Œæƒ…å†µæµ‹è¯•å‡ºé”™: {e}")
        return False

def run_comprehensive_test():
    """è¿è¡Œç»¼åˆæµ‹è¯•"""
    logger.info("ğŸš€ å¼€å§‹CGOæ¨¡å—ç»¼åˆæµ‹è¯•")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 1. å› å­è®¡ç®—æµ‹è¯•
    success, results = test_cgo_factor_calculation()
    test_results['factor_calculation'] = success
    
    # 2. å› å­é›†æˆæµ‹è¯•
    success, results = test_factor_integration()
    test_results['factor_integration'] = success
    
    # 3. ç­–ç•¥å›æµ‹æµ‹è¯•
    success, results = test_strategy_backtest()
    test_results['strategy_backtest'] = success
    
    # 4. è‡ªå®šä¹‰ç­–ç•¥æµ‹è¯?
    success, results = test_custom_strategy()
    test_results['custom_strategy'] = success
    
    # 5. è¾¹ç•Œæƒ…å†µæµ‹è¯•
    success = test_edge_cases()
    test_results['edge_cases'] = success
    
    # æ±‡æ€»æµ‹è¯•ç»“æ?
    logger.info("=" * 60)
    logger.info("ğŸ“Š æµ‹è¯•ç»“æœæ±‡æ€?")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, passed in test_results.items():
        status = "âœ?é€šè¿‡" if passed else "â?å¤±è´¥"
        logger.info(f"   {test_name}: {status}")
        if passed:
            passed_tests += 1
    
    logger.info(f"\nğŸ¯ æµ‹è¯•é€šè¿‡ç? {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        logger.info("ğŸ‰ æ‰€æœ‰æµ‹è¯•é€šè¿‡ï¼CGOæ¨¡å—åŠŸèƒ½æ­£å¸¸")
        return True
    else:
        logger.warning(f"âš ï¸ æœ?{total_tests - passed_tests} ä¸ªæµ‹è¯•å¤±è´¥ï¼Œè¯·æ£€æŸ¥ç›¸å…³åŠŸèƒ?)
        return False

if __name__ == "__main__":
    # è®¾ç½®è¾“å‡ºç›®å½•
    output_dir = Path("./tests/tests/test_output")
    output_dir.mkdir(exist_ok=True)
    
    print("""
    â•”â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—
    â•?                 CGOæ¨¡å—é›†æˆæµ‹è¯•                             â•?
    â•?                                                             â•?
    â•? æµ‹è¯•å†…å®¹ï¼?                                                 â•?
    â•? 1. CGOå› å­è®¡ç®—åŠŸèƒ½                                          â•?
    â•? 2. å› å­é›†æˆæ¥å£                                             â•?
    â•? 3. ç­–ç•¥å›æµ‹åŠŸèƒ½                                             â•?
    â•? 4. è‡ªå®šä¹‰ç­–ç•¥é…ç½?                                          â•?
    â•? 5. è¾¹ç•Œæƒ…å†µå¤„ç†                                             â•?
    â•?                                                             â•?
    â•? é¢„è®¡æ‰§è¡Œæ—¶é—´: 2-3åˆ†é’Ÿ                                       â•?
    â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•
    """)
    
    # è¿è¡Œç»¼åˆæµ‹è¯•
    test_passed = run_comprehensive_test()
    
    if test_passed:
        print("\nğŸ‰ CGOæ¨¡å—æµ‹è¯•å…¨éƒ¨é€šè¿‡ï¼æ¨¡å—å·²æˆåŠŸé›†æˆåˆ°ç ”ç©¶æ¡†æ¶ä¸­ã€?)
        print(f"ğŸ“ æµ‹è¯•è¾“å‡ºæ–‡ä»¶ä¿å­˜åœ? {output_dir.absolute()}")
        print("\nğŸ“– ä½¿ç”¨è¯´æ˜è¯·å‚è€? research/README_CGO.md")
    else:
        print("\nâš ï¸ éƒ¨åˆ†æµ‹è¯•æœªé€šè¿‡ï¼Œè¯·æ£€æŸ¥æ—¥å¿—ä¿¡æ¯å¹¶ä¿®å¤ç›¸å…³é—®é¢˜ã€?)
    
    print("\n" + "=" * 60)
    print("æµ‹è¯•å®Œæˆæ—¶é—´:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
