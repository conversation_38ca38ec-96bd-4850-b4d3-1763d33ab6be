# -*- coding: utf-8 -*-
"""
Word2Vec金融文档分析策略
基于Hands-On-Machine-Learning-for-Algorithmic-Trading Chapter15的word2vec技术
实现SEC文件和财经新闻的语义分析驱动的量化交易策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings
import logging
from pathlib import Path
import pickle
import json

# Word2Vec相关库
try:
    from gensim.models import Word2Vec
    from gensim.models.doc2vec import Doc2Vec, TaggedDocument
    from gensim.utils import simple_preprocess
    from gensim.parsing.preprocessing import STOPWORDS
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    from sklearn.decomposition import PCA
    import nltk
    WORD2VEC_AVAILABLE = True
except ImportError:
    WORD2VEC_AVAILABLE = False
    warnings.warn("Word2Vec库不可用，策略将使用简化模式")

# 数据管道集成
try:
    from data_adapter import get_data_adapter
    DATA_ADAPTER_AVAILABLE = True
except ImportError:
    DATA_ADAPTER_AVAILABLE = False
    warnings.warn("数据适配器不可用，将使用模拟数据")

# 策略基类
try:
    from ..base_ml_strategy import BaseMLStrategy, BaseMLConfig
    from ...core.interfaces import StrategyCategory
except ImportError:
    try:
        # 尝试从核心模块导入
        from strategy_autodev.core.base_strategy import BaseStrategy as BaseMLStrategy
        from strategy_autodev.core.interfaces import StrategyCategory
        class BaseMLConfig:
            def __init__(self):
                pass
    except ImportError:
        # 兼容性导入
        warnings.warn("基础策略类不可用，使用简化实现")
    
    class BaseMLStrategy:
        def __init__(self, config=None):
            self.config = config or {}
        
        def generate_signals(self, data):
            return {}
        
        def calculate_positions(self, signals, data):
            return {}
    
    class BaseMLConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class StrategyCategory:
        ML = "ML"
        NLP = "NLP"


class Word2VecFinancialConfig(BaseMLConfig):
    """Word2Vec金融策略配置"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Word2Vec模型参数
        self.vector_size = kwargs.get('vector_size', 100)
        self.window = kwargs.get('window', 5)
        self.min_count = kwargs.get('min_count', 5)
        self.workers = kwargs.get('workers', 4)
        self.sg = kwargs.get('sg', 1)  # 1 for skip-gram, 0 for CBOW
        self.hs = kwargs.get('hs', 0)  # 1 for hierarchical softmax
        self.negative = kwargs.get('negative', 5)
        self.epochs = kwargs.get('epochs', 10)
        
        # 文档处理参数
        self.max_vocab_size = kwargs.get('max_vocab_size', 50000)
        self.min_doc_length = kwargs.get('min_doc_length', 10)
        self.max_doc_length = kwargs.get('max_doc_length', 1000)
        
        # 策略参数
        self.similarity_threshold = kwargs.get('similarity_threshold', 0.7)
        self.top_k_similar = kwargs.get('top_k_similar', 10)
        self.sentiment_weight = kwargs.get('sentiment_weight', 0.3)
        self.semantic_weight = kwargs.get('semantic_weight', 0.7)
        
        # 交易参数
        self.position_size = kwargs.get('position_size', 0.1)
        self.rebalance_freq = kwargs.get('rebalance_freq', 5)
        self.lookback_days = kwargs.get('lookback_days', 30)


class Word2VecFinancialStrategy(BaseMLStrategy):
    """
    Word2Vec金融文档分析策略
    
    基于Chapter15的word2vec技术实现：
    1. SEC文件和财经新闻的语义向量化
    2. 文档相似性分析和主题聚类
    3. 语义情感分析和市场预测
    4. 基于文档嵌入的交易信号生成
    """
    
    def __init__(self, config: Union[Word2VecFinancialConfig, dict] = None):
        if isinstance(config, dict):
            config = Word2VecFinancialConfig(**config)
        elif config is None:
            config = Word2VecFinancialConfig()
            
        super().__init__(config)
        
        self.strategy_name = "Word2Vec金融文档分析策略"
        self.category = StrategyCategory.ML
        
        # 初始化组件
        self.logger = logging.getLogger(__name__)
        self.word2vec_model = None
        self.doc_vectors = {}
        self.financial_vocabulary = set()
        self.document_clusters = {}
        
        # 金融领域特定词汇
        self.financial_terms = {
            'market_indicators': ['bull', 'bear', 'volatility', 'momentum', 'trend', 'resistance', 'support'],
            'financial_metrics': ['revenue', 'profit', 'earnings', 'ebitda', 'margin', 'growth', 'valuation'],
            'sentiment_words': ['positive', 'negative', 'optimistic', 'pessimistic', 'confident', 'uncertain'],
            'action_words': ['buy', 'sell', 'hold', 'upgrade', 'downgrade', 'outperform', 'underperform']
        }
        
        # 数据适配器
        if DATA_ADAPTER_AVAILABLE:
            try:
                self.data_adapter = get_data_adapter()
            except Exception as e:
                self.logger.warning(f"数据适配器初始化失败: {e}")
                self.data_adapter = None
        else:
            self.data_adapter = None
            
        self._initialize_word2vec_components()
    
    def _initialize_word2vec_components(self):
        """初始化Word2Vec组件"""
        if not WORD2VEC_AVAILABLE:
            self.logger.warning("Word2Vec库不可用，使用简化分析")
            return
            
        try:
            # 下载必要的NLTK数据
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
                
            self.logger.info("Word2Vec组件初始化成功")
            
        except Exception as e:
            self.logger.error(f"Word2Vec组件初始化失败: {e}")
    
    def preprocess_document(self, text: str) -> List[str]:
        """文档预处理"""
        if not isinstance(text, str) or len(text) < self.config.min_doc_length:
            return []
            
        # 使用gensim的简单预处理
        tokens = simple_preprocess(text, deacc=True, min_len=2, max_len=15)
        
        # 过滤停用词
        tokens = [token for token in tokens if token not in STOPWORDS]
        
        # 限制文档长度
        if len(tokens) > self.config.max_doc_length:
            tokens = tokens[:self.config.max_doc_length]
            
        return tokens
    
    def train_word2vec_model(self, documents: List[str]) -> bool:
        """训练Word2Vec模型"""
        if not WORD2VEC_AVAILABLE:
            self.logger.warning("Word2Vec不可用，跳过模型训练")
            return False
            
        try:
            # 预处理文档
            processed_docs = []
            for doc in documents:
                tokens = self.preprocess_document(doc)
                if tokens:
                    processed_docs.append(tokens)
            
            if len(processed_docs) < 10:
                self.logger.warning("文档数量不足，无法训练有效模型")
                return False
            
            # 训练Word2Vec模型
            self.word2vec_model = Word2Vec(
                sentences=processed_docs,
                vector_size=self.config.vector_size,
                window=self.config.window,
                min_count=self.config.min_count,
                workers=self.config.workers,
                sg=self.config.sg,
                hs=self.config.hs,
                negative=self.config.negative,
                epochs=self.config.epochs,
                seed=42
            )
            
            # 构建金融词汇表
            self._build_financial_vocabulary()
            
            self.logger.info(f"Word2Vec模型训练完成，词汇量: {len(self.word2vec_model.wv)}")
            return True
            
        except Exception as e:
            self.logger.error(f"Word2Vec模型训练失败: {e}")
            return False
    
    def _build_financial_vocabulary(self):
        """构建金融领域词汇表"""
        if not self.word2vec_model:
            return
            
        vocab = set(self.word2vec_model.wv.key_to_index.keys())
        
        # 添加金融特定词汇
        for category, terms in self.financial_terms.items():
            for term in terms:
                if term in vocab:
                    self.financial_vocabulary.add(term)
        
        self.logger.info(f"金融词汇表构建完成，包含 {len(self.financial_vocabulary)} 个词汇")
    
    def get_document_vector(self, text: str) -> np.ndarray:
        """获取文档向量"""
        if not self.word2vec_model:
            return np.zeros(self.config.vector_size)
            
        tokens = self.preprocess_document(text)
        if not tokens:
            return np.zeros(self.config.vector_size)
        
        # 计算文档向量（词向量的平均值）
        vectors = []
        for token in tokens:
            if token in self.word2vec_model.wv:
                vectors.append(self.word2vec_model.wv[token])
        
        if not vectors:
            return np.zeros(self.config.vector_size)
            
        return np.mean(vectors, axis=0)
    
    def calculate_document_similarity(self, doc1: str, doc2: str) -> float:
        """计算文档相似性"""
        vec1 = self.get_document_vector(doc1)
        vec2 = self.get_document_vector(doc2)
        
        # 计算余弦相似性
        similarity = cosine_similarity([vec1], [vec2])[0][0]
        return float(similarity)
    
    def analyze_semantic_sentiment(self, text: str) -> Dict[str, float]:
        """语义情感分析"""
        if not self.word2vec_model:
            return {'sentiment': 0.0, 'confidence': 0.0}
            
        tokens = self.preprocess_document(text)
        if not tokens:
            return {'sentiment': 0.0, 'confidence': 0.0}
        
        # 基于金融词汇的情感分析
        sentiment_scores = []
        
        for token in tokens:
            if token in self.word2vec_model.wv and token in self.financial_vocabulary:
                # 计算与正面/负面词汇的相似性
                pos_similarities = []
                neg_similarities = []
                
                for pos_word in ['profit', 'growth', 'positive', 'bull', 'upgrade']:
                    if pos_word in self.word2vec_model.wv:
                        sim = self.word2vec_model.wv.similarity(token, pos_word)
                        pos_similarities.append(sim)
                
                for neg_word in ['loss', 'decline', 'negative', 'bear', 'downgrade']:
                    if neg_word in self.word2vec_model.wv:
                        sim = self.word2vec_model.wv.similarity(token, neg_word)
                        neg_similarities.append(sim)
                
                if pos_similarities and neg_similarities:
                    pos_score = max(pos_similarities)
                    neg_score = max(neg_similarities)
                    sentiment_scores.append(pos_score - neg_score)
        
        if not sentiment_scores:
            return {'sentiment': 0.0, 'confidence': 0.0}
        
        sentiment = np.mean(sentiment_scores)
        confidence = 1.0 - np.std(sentiment_scores) if len(sentiment_scores) > 1 else 0.5
        
        return {
            'sentiment': float(sentiment),
            'confidence': float(confidence)
        }
    
    def cluster_documents(self, documents: List[str], n_clusters: int = 5) -> Dict[int, List[int]]:
        """文档聚类"""
        if not documents or not self.word2vec_model:
            return {}
            
        # 获取所有文档向量
        doc_vectors = []
        valid_indices = []
        
        for i, doc in enumerate(documents):
            vec = self.get_document_vector(doc)
            if np.any(vec):
                doc_vectors.append(vec)
                valid_indices.append(i)
        
        if len(doc_vectors) < n_clusters:
            return {}
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(doc_vectors)
        
        # 组织聚类结果
        clusters = {}
        for i, label in enumerate(cluster_labels):
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(valid_indices[i])
        
        self.document_clusters = clusters
        return clusters
    
    def get_financial_news_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """获取财经新闻数据"""
        if not self.data_adapter:
            # 返回模拟数据
            self.logger.warning("使用模拟财经新闻数据")
            dates = pd.date_range(start_date, end_date, freq='D')
            return pd.DataFrame({
                'date': dates,
                'title': [f'Financial News {i}' for i in range(len(dates))],
                'content': [f'Sample financial news content {i}' for i in range(len(dates))],
                'symbol': ['AAPL'] * len(dates)
            })
        
        try:
            # 使用真实数据适配器
            news_data = self.data_adapter.get_news_data(
                start_date=start_date,
                end_date=end_date,
                source='financial_news'
            )
            return news_data
            
        except Exception as e:
            self.logger.error(f"获取财经新闻数据失败: {e}")
            return pd.DataFrame()
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成交易信号"""
        if data.empty:
            return {}
        
        try:
            # 获取日期范围
            start_date = data.index.min().strftime('%Y-%m-%d')
            end_date = data.index.max().strftime('%Y-%m-%d')
            
            # 获取新闻数据
            news_data = self.get_financial_news_data(start_date, end_date)
            
            if news_data.empty:
                self.logger.warning("无新闻数据，返回空信号")
                return {}
            
            # 训练Word2Vec模型
            documents = news_data['content'].tolist()
            if not self.train_word2vec_model(documents):
                return {}
            
            # 生成信号
            signals = {}
            
            for symbol in data.columns:
                if symbol in news_data['symbol'].values:
                    symbol_news = news_data[news_data['symbol'] == symbol]
                    
                    # 分析最近新闻的语义情感
                    recent_news = symbol_news.tail(self.config.lookback_days)
                    
                    sentiment_scores = []
                    for _, row in recent_news.iterrows():
                        sentiment_result = self.analyze_semantic_sentiment(row['content'])
                        sentiment_scores.append(sentiment_result['sentiment'])
                    
                    if sentiment_scores:
                        avg_sentiment = np.mean(sentiment_scores)
                        confidence = np.std(sentiment_scores)
                        
                        # 生成交易信号
                        if avg_sentiment > self.config.similarity_threshold:
                            signal = 'BUY'
                        elif avg_sentiment < -self.config.similarity_threshold:
                            signal = 'SELL'
                        else:
                            signal = 'HOLD'
                        
                        signals[symbol] = {
                            'signal': signal,
                            'sentiment': avg_sentiment,
                            'confidence': 1.0 - confidence if confidence < 1.0 else 0.0,
                            'news_count': len(recent_news)
                        }
            
            return signals
            
        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return {}
    
    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        if not self.word2vec_model:
            return False
            
        try:
            model_data = {
                'word2vec_model': self.word2vec_model,
                'financial_vocabulary': self.financial_vocabulary,
                'document_clusters': self.document_clusters,
                'config': self.config.__dict__
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.word2vec_model = model_data['word2vec_model']
            self.financial_vocabulary = model_data['financial_vocabulary']
            self.document_clusters = model_data['document_clusters']
            
            self.logger.info(f"模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False


def create_word2vec_financial_strategy(config: Dict[str, Any] = None) -> Word2VecFinancialStrategy:
    """创建Word2Vec金融策略实例"""
    return Word2VecFinancialStrategy(config)


# 策略注册
def register_strategy():
    """注册策略到统一管理系统"""
    try:
        from ...core.unified_system import get_unified_system
        
        system = get_unified_system()
        
        success = system.register_strategy(
            name="Word2VecFinancialStrategy",
            strategy_class=Word2VecFinancialStrategy,
            category=StrategyCategory.ML,
            description="基于Word2Vec的金融文档分析策略",
            tags=["NLP", "Word2Vec", "文档分析", "语义分析"],
            risk_level="MEDIUM",
            data_requirements=["financial_news", "sec_filings"]
        )
        
        if success:
            print("✓ Word2Vec金融策略注册成功")
        else:
            print("✗ Word2Vec金融策略注册失败")
            
        return success
        
    except Exception as e:
        print(f"✗ 策略注册失败: {e}")
        return False


if __name__ == "__main__":
    # 测试策略
    config = Word2VecFinancialConfig(
        vector_size=100,
        window=5,
        min_count=3,
        epochs=10
    )
    
    strategy = Word2VecFinancialStrategy(config)
    print(f"策略创建成功: {strategy.strategy_name}")
    
    # 注册策略
    register_strategy()