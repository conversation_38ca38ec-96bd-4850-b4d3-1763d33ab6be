#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习策略注册模块

该模块负责将深度学习策略注册到统一策略管理系统中。
"""

import logging
from typing import Dict, Any

from strategy_autodev.core.unified_system import get_unified_system, StrategyCategory
from strategy_autodev.ml_strategies.deep_learning import (
    DeepLearningBaseStrategy,
    FeedforwardNNStrategy,
    KerasDLStrategy,
    NNOptimizationStrategy
)

logger = logging.getLogger(__name__)


def register_deep_learning_strategies() -> bool:
    """
    注册所有深度学习策略到统一系统
    
    Returns:
        bool: 注册是否成功
    """
    try:
        system = get_unified_system()
        
        # 注册前馈神经网络策略
        success1 = system.register_strategy(
            name="FeedforwardNN",
            strategy_class=FeedforwardNNStrategy,
            category=StrategyCategory.ML,
            description="基于反向传播算法的前馈神经网络策略",
            author="Deep Learning Module",
            version="1.0.0",
            tags=["neural_network", "feedforward", "backpropagation"],
            parameters={
                "hidden_layers": [64, 32],
                "learning_rate": 0.001,
                "epochs": 100,
                "batch_size": 32,
                "momentum": 0.9,
                "l2_reg": 0.01,
                "early_stopping_patience": 10
            }
        )
        
        # 注册PyTorch深度学习策略
        success2 = system.register_strategy(
            name="KerasDeepLearning",
            strategy_class=KerasDLStrategy,
            category=StrategyCategory.ML,
            description="基于PyTorch的深度学习策略，支持MLP、CNN、LSTM",
            author="Deep Learning Module",
            version="1.0.0",
            tags=["pytorch", "deep_learning", "mlp", "cnn", "lstm"],
            parameters={
                "model_type": "mlp",
                "hidden_layers": [128, 64, 32],
                "learning_rate": 0.001,
                "epochs": 100,
                "batch_size": 32,
                "dropout_rate": 0.2,
                "use_batch_norm": True,
                "early_stopping_patience": 15
            }
        )
        
        # 注册神经网络优化策略
        success3 = system.register_strategy(
            name="NNOptimization",
            strategy_class=NNOptimizationStrategy,
            category=StrategyCategory.ML,
            description="神经网络架构优化策略，支持超参数搜索和模型集成",
            author="Deep Learning Module",
            version="1.0.0",
            tags=["optimization", "hyperparameter_tuning", "ensemble", "optuna"],
            parameters={
                "optimization_method": "optuna",
                "n_trials": 50,
                "cv_folds": 5,
                "ensemble_size": 3,
                "feature_selection": True,
                "auto_feature_engineering": True
            }
        )
        
        if success1 and success2 and success3:
            logger.info("所有深度学习策略注册成功")
            return True
        else:
            logger.error("部分深度学习策略注册失败")
            return False
            
    except Exception as e:
        logger.error(f"深度学习策略注册失败: {e}")
        return False


def get_strategy_configs() -> Dict[str, Dict[str, Any]]:
    """
    获取深度学习策略的默认配置
    
    Returns:
        Dict[str, Dict[str, Any]]: 策略配置字典
    """
    return {
        "FeedforwardNN": {
            "hidden_layers": [64, 32],
            "learning_rate": 0.001,
            "epochs": 100,
            "batch_size": 32,
            "momentum": 0.9,
            "l2_reg": 0.01,
            "early_stopping_patience": 10,
            "lookback_period": 20,
            "prediction_horizon": 1,
            "features": ["close", "volume", "high", "low", "open"]
        },
        "KerasDeepLearning": {
            "model_type": "mlp",
            "hidden_layers": [128, 64, 32],
            "learning_rate": 0.001,
            "epochs": 100,
            "batch_size": 32,
            "dropout_rate": 0.2,
            "use_batch_norm": True,
            "early_stopping_patience": 15,
            "lookback_period": 20,
            "prediction_horizon": 1,
            "features": ["close", "volume", "high", "low", "open"]
        },
        "NNOptimization": {
            "optimization_method": "optuna",
            "n_trials": 50,
            "cv_folds": 5,
            "ensemble_size": 3,
            "feature_selection": True,
            "auto_feature_engineering": True,
            "lookback_period": 20,
            "prediction_horizon": 1,
            "features": ["close", "volume", "high", "low", "open"]
        }
    }


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 注册策略
    success = register_deep_learning_strategies()
    if success:
        print("深度学习策略注册成功！")
        
        # 显示系统状态
        system = get_unified_system()
        status = system.get_system_status()
        print(f"系统状态: {status}")
    else:
        print("深度学习策略注册失败！")