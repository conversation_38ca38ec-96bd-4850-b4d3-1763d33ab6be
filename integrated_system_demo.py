# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成系统演示
============

展示如何使用Phase 3集成的因子管理器、策略管理器和回测引擎。
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def demo_factor_management():
    """演示因子管理功能"""
    print("\n" + "="*60)
    print("1. 因子管理器演示")
    print("="*60)
    
    try:
        from factor_analyze.factor_core.integrated_factor_manager import IntegratedFactorManager
        
        # 创建因子管理器
        factor_manager = IntegratedFactorManager(
            cache_dir='demo_factor_cache',
            use_cache=True,
            parallel_workers=2,
            factor_config={
                'mda_sentiment': {'use_transformer': False},
                'text_similarity': {'n_clusters': 10},
                'fed_liquidity': {'smooth_window': 5}
            }
        )
        
        # 显示可用因子
        print("\n可用因子信息：")
        factor_info = factor_manager.get_factor_info()
        print(factor_info)
        
        # 准备演示数据
        dates = pd.date_range('2024-01-01', '2024-01-31', freq='D')
        
        # 模拟MD&A情绪数据
        mda_data = pd.DataFrame({
            'stock_code': ['000001.SZ', '000002.SZ', '000858.SZ'],
            'period': ['2024Q1'] * 3,
            'mda_text': [
                '公司业绩稳健增长，未来前景光明，管理层对发展充满信心',
                '面临市场挑战，但积极调整策略，预期下半年将有改善',
                '技术创新取得突破，新产品市场反响良好，收入大幅增长'
            ]
        })
        
        # 模拟文本相似度数据
        text_data = pd.DataFrame({
            'stock_code': ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ'],
            'text': [
                '新能源汽车电池技术创新',
                '光伏发电效率提升方案',
                '人工智能芯片研发突破',
                '云计算基础设施优化'
            ]
        })
        
        # 模拟流动性数据
        liquidity_data = pd.DataFrame({
            'fed_balance_sheet': np.random.randn(len(dates)) * 0.1 + 8.5,
            'repo_operations': np.random.randn(len(dates)) * 0.05,
            'reverse_repo': np.random.randn(len(dates)) * 0.1 + 2.0,
            'treasury_general_account': np.random.randn(len(dates)) * 0.05 + 0.5,
            'commercial_bank_reserves': np.random.randn(len(dates)) * 0.1 + 3.5
        }, index=dates).abs()
        
        # 准备数据字典
        data_dict = {
            'mda_sentiment_data': mda_data,
            'text_similarity_data': text_data,
            'fed_liquidity_data': liquidity_data
        }
        
        # 计算因子（如果可用）
        if factor_manager.factors:
            available_factors = list(factor_manager.factors.keys())[:2]  # 选择前两个可用因子
            if available_factors:
                print(f"\n计算因子: {available_factors}")
                factor_results = factor_manager.calculate_multiple_factors(
                    available_factors,
                    data_dict,
                    dates[-1],
                    parallel=False
                )
                
                print("\n因子计算结果：")
                for factor_name, result in factor_results.items():
                    print(f"\n{factor_name}:")
                    print(result.head())
        else:
            print("\n注意：因子模块未完全加载，跳过因子计算演示")
        
        return factor_manager
        
    except Exception as e:
        logger.error(f"因子管理器演示失败: {e}")
        return None


def demo_strategy_management():
    """演示策略管理功能"""
    print("\n" + "="*60)
    print("2. 策略管理器演示")
    print("="*60)
    
    try:
        from strategy_autodev.integrated_strategy_manager import IntegratedStrategyManager
        
        # 创建策略管理器
        strategy_manager = IntegratedStrategyManager(
            strategy_config={
                'ai_text_analysis': {
                    'sentiment_weight': 0.4,
                    'confidence_threshold': 0.7
                },
                'optimal_factor_timing': {
                    'lookback_period': 60,
                    'n_factors': 5
                }
            },
            risk_limit=0.02,
            position_limit=0.2
        )
        
        # 显示可用策略
        print("\n可用策略信息：")
        strategy_info = strategy_manager.get_strategy_info()
        print(strategy_info)
        
        # 准备市场数据
        dates = pd.date_range('2023-01-01', '2024-01-01', freq='D')
        market_data = pd.DataFrame({
            'close': 100 * (1 + np.random.randn(len(dates)).cumsum() * 0.01),
            'volume': np.random.randint(1000000, 5000000, len(dates)),
            'returns': np.random.randn(len(dates)) * 0.02
        }, index=dates)
        
        # 运行策略（如果可用）
        available_strategies = list(strategy_manager.strategies.keys())
        if available_strategies:
            strategy_name = available_strategies[0]
            print(f"\n运行策略: {strategy_name}")
            
            try:
                result = strategy_manager.run_strategy(strategy_name, market_data)
                print(f"\n策略性能:")
                for metric, value in result['performance'].items():
                    if isinstance(value, float):
                        print(f"  {metric}: {value:.4f}")
            except Exception as e:
                logger.error(f"策略运行失败: {e}")
        else:
            print("\n注意：策略模块未完全加载，跳过策略运行演示")
        
        # 演示策略组合优化
        if len(available_strategies) >= 2:
            print("\n\n策略组合优化演示...")
            weights = strategy_manager.optimize_portfolio(
                available_strategies[:2],
                market_data,
                optimization_method='mean_variance'
            )
            print("优化后的权重:")
            for strategy, weight in weights.items():
                print(f"  {strategy}: {weight:.2%}")
        
        return strategy_manager
        
    except Exception as e:
        logger.error(f"策略管理器演示失败: {e}")
        return None


def demo_integrated_backtest(factor_manager=None, strategy_manager=None):
    """演示集成回测功能"""
    print("\n" + "="*60)
    print("3. 集成回测演示")
    print("="*60)
    
    try:
        from strategy_autodev.backtest.integrated_backtest_engine import IntegratedBacktestEngine
        
        # 创建回测引擎
        backtest_engine = IntegratedBacktestEngine(
            factor_manager=factor_manager,
            strategy_manager=strategy_manager,
            initial_capital=1000000,
            commission_rate=0.001,
            slippage_rate=0.0005
        )
        
        # 准备回测数据
        print("\n准备回测数据...")
        universe = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ']
        
        # 使用更简单的数据结构避免多层索引问题
        dates = pd.date_range('2023-01-01', '2024-01-01', freq='D')
        
        # 生成单一DataFrame的价格数据
        price_data = pd.DataFrame()
        for stock in universe:
            np.random.seed(hash(stock) % 1000)
            price_data[stock] = 100 * (1 + np.random.randn(len(dates)).cumsum() * 0.01)
        price_data.index = dates
        
        # 添加一个简单的收盘价列（用第一只股票的价格）
        price_data['close'] = price_data[universe[0]]
        
        print(f"数据准备完成，包含 {len(universe)} 只股票，{len(dates)} 个交易日")
        
        # 运行简单回测
        print("\n运行回测...")
        result = backtest_engine.run_backtest('demo_strategy', price_data)
        
        # 显示回测结果
        print("\n回测结果:")
        metrics = result['metrics']
        print(f"年化收益率: {metrics['annual_return']:.2%}")
        print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
        print(f"最大回撤: {metrics['max_drawdown']:.2%}")
        print(f"总收益率: {metrics['total_return']:.2%}")
        
        # 生成报告
        output_dir = 'integrated_backtest_output'
        print(f"\n生成回测报告于: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存简单的报告
        report_content = f"""
# 集成回测报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 回测配置
- 初始资金: ¥1,000,000
- 手续费率: 0.1%
- 滑点率: 0.05%
- 回测期间: 2023-01-01 到 2024-01-01
- 股票池: {', '.join(universe)}

## 性能指标
- 年化收益率: {metrics['annual_return']:.2%}
- 夏普比率: {metrics['sharpe_ratio']:.3f}
- 最大回撤: {metrics['max_drawdown']:.2%}
- 总收益率: {metrics['total_return']:.2%}
"""
        
        with open(os.path.join(output_dir, 'report.md'), 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("报告生成完成。")
        
        return backtest_engine
        
    except Exception as e:
        logger.error(f"集成回测演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数：运行完整的集成系统演示"""
    print("="*60)
    print("Phase 3 集成系统演示")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 演示因子管理
    factor_manager = demo_factor_management()
    
    # 2. 演示策略管理
    strategy_manager = demo_strategy_management()
    
    # 3. 演示集成回测
    backtest_engine = demo_integrated_backtest(factor_manager, strategy_manager)
    
    # 总结
    print("\n" + "="*60)
    print("集成系统演示完成。")
    print("="*60)
    
    print("\n系统集成状态：")
    print(f"✅ 因子管理器: {'已加载' if factor_manager else '未加载'}")
    print(f"✅ 策略管理器: {'已加载' if strategy_manager else '未加载'}")
    print(f"✅ 回测引擎: {'已加载' if backtest_engine else '未加载'}")
    
    print("\n主要功能点：")
    print("1. 统一因子管理：支持多个新因子的计算、缓存和并行处理")
    print("2. 统一策略管理：支持多个新策略的运行、组合和优化")
    print("3. 集成回测引擎：支持因子驱动回测、多策略组合回测")
    
    print("\n下一步建议：")
    print("1. 完善因子和策略的具体实现")
    print("2. 集成真实的数据源（如Qlib、Tushare等）")
    print("3. 添加更多的风险管理功能。")
    print("4. 开发可视化界面")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main() 
