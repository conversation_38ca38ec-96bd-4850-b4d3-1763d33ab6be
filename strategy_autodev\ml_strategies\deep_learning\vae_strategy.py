# -*- coding: utf-8 -*-
"""
变分自动编码器策略
基于Chapter20的变分自动编码器技术，用于概率生成建模
适用于金融数据的概率分布学习和生成
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习框架
try:
    from keras.layers import Input, Dense, Lambda, Layer
    from keras.models import Model
    from keras.optimizers import Adam
    from keras.callbacks import EarlyStopping, ModelCheckpoint
    from keras import backend as K
    from keras.losses import mse, binary_crossentropy
    import tensorflow as tf
    KERAS_AVAILABLE = True
except ImportError:
    KERAS_AVAILABLE = False
    logging.warning("Keras不可用，VAE策略将使用简化实现")

# 数据管道集成
try:
    from data_pipeline.data_adapter import get_adapter
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    DATA_PIPELINE_AVAILABLE = False
    logging.warning("数据管道不可用，将使用模拟数据")

# 策略基类
try:
    from ..base_ml_strategy import BaseMLStrategy
except ImportError:
    from strategy_autodev.ml_strategies.base_ml_strategy import BaseMLStrategy

# 统一系统集成
try:
    from strategy_autodev.core.unified_system import get_unified_system
    UNIFIED_SYSTEM_AVAILABLE = True
except ImportError:
    UNIFIED_SYSTEM_AVAILABLE = False

logger = logging.getLogger(__name__)


if KERAS_AVAILABLE:
    class Sampling(Layer):
        """采样层，用于VAE的重参数化技巧"""
        
        def call(self, inputs):
            z_mean, z_log_var = inputs
            batch = tf.shape(z_mean)[0]
            dim = tf.shape(z_mean)[1]
            epsilon = tf.keras.backend.random_normal(shape=(batch, dim))
            return z_mean + tf.exp(0.5 * z_log_var) * epsilon
else:
    class Sampling:
        """采样层的简化实现（当Keras不可用时）"""
        
        def __init__(self):
            pass
        
        def call(self, inputs):
            # 简化实现
            return inputs[0]  # 返回均值


class VAEStrategy(BaseMLStrategy):
    """
    变分自动编码器策略
    
    功能:
    1. 学习金融数据的概率分布
    2. 生成符合分布的合成数据
    3. 进行概率推理和不确定性量化
    4. 异常检测和风险评估
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化VAE策略
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        
        # 策略配置
        self.config = config or {}
        self.latent_dim = self.config.get('latent_dim', 32)
        self.intermediate_dim = self.config.get('intermediate_dim', 64)
        self.batch_size = self.config.get('batch_size', 32)
        self.epochs = self.config.get('epochs', 50)
        self.beta = self.config.get('beta', 1.0)  # KL散度权重
        
        # 模型组件
        self.vae = None
        self.encoder = None
        self.decoder = None
        
        # 数据管道
        self.data_adapter = None
        self._init_data_pipeline()
        
        # 策略状态
        self.is_trained = False
        self.feature_columns = []
        self.scaler = None
        self.original_dim = None
        
        logger.info(f"VAE策略初始化完成，潜在维度: {self.latent_dim}")
    
    def _build_model(self) -> Any:
        """构建VAE模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，返回简化模型")
            return None
        
        # 这里会在训练时根据数据维度动态构建
        return None
    
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        # 选择数值特征列
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        exclude_cols = ['target', 'date', 'symbol']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        if not feature_cols:
            raise ValueError("没有找到可用的特征列")
        
        self.feature_columns = feature_cols
        return data[feature_cols]
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练VAE模型"""
        if not KERAS_AVAILABLE:
            logger.warning("Keras不可用，使用简化训练")
            return {'status': 'simplified_training', 'loss': 0.0}
        
        try:
            # 构建模型
            input_shape = (self.sequence_length, X.shape[1])
            self.encoder = self._build_encoder(input_shape)
            self.decoder = self._build_decoder()
            self.vae = self._build_vae()
            
            # 准备训练数据
            sequences = self._prepare_data(pd.DataFrame(X))
            
            # 训练VAE
            history = self.vae.fit(
                sequences, sequences,
                epochs=self.epochs,
                batch_size=self.batch_size,
                validation_split=0.2,
                verbose=0
            )
            
            # 计算重构误差阈值（用于异常检测）
            reconstructed = self.vae.predict(sequences)
            reconstruction_errors = np.mean(np.square(sequences - reconstructed), axis=(1, 2))
            self.anomaly_threshold = np.percentile(reconstruction_errors, 95)
            
            return {
                'final_loss': history.history['loss'][-1],
                'final_val_loss': history.history.get('val_loss', [0])[-1],
                'epochs_trained': len(history.history['loss']),
                'anomaly_threshold': self.anomaly_threshold
            }
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """使用VAE进行预测（异常检测）"""
        if not KERAS_AVAILABLE or self.vae is None:
            logger.warning("模型不可用，返回随机预测")
            return np.random.random(len(X))
        
        try:
            # 准备序列数据
            sequences = self._prepare_data(pd.DataFrame(X))
            
            # 重构数据
            reconstructed = self.vae.predict(sequences)
            
            # 计算重构误差
            reconstruction_errors = np.mean(np.square(sequences - reconstructed), axis=(1, 2))
            
            # 计算异常分数（重构误差越大，异常分数越高）
            if hasattr(self, 'anomaly_threshold') and self.anomaly_threshold > 0:
                anomaly_scores = reconstruction_errors / self.anomaly_threshold
            else:
                # 归一化到[0,1]
                min_error = np.min(reconstruction_errors)
                max_error = np.max(reconstruction_errors)
                anomaly_scores = (reconstruction_errors - min_error) / (max_error - min_error + 1e-8)
            
            return anomaly_scores
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.zeros(len(X))
    
    def _init_data_pipeline(self):
        """初始化数据管道"""
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.data_adapter = get_adapter()
                logger.info("✅ 数据管道连接成功")
            except Exception as e:
                logger.warning(f"数据管道连接失败: {e}")
                self.data_adapter = None
        else:
            logger.warning("数据管道不可用，将使用模拟数据")
    
    def _build_encoder(self, original_dim: int) -> Tuple[Any, Any, Any]:
        """
        构建编码器
        
        Args:
            original_dim: 原始数据维度
            
        Returns:
            编码器模型、均值模型、方差模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建编码器")
        
        # 输入层
        inputs = Input(shape=(original_dim,), name='encoder_input')
        
        # 隐藏层
        x = Dense(self.intermediate_dim, activation='relu')(inputs)
        
        # 潜在空间参数
        z_mean = Dense(self.latent_dim, name='z_mean')(x)
        z_log_var = Dense(self.latent_dim, name='z_log_var')(x)
        
        # 采样层
        z = Sampling()([z_mean, z_log_var])
        
        # 编码器模型
        encoder = Model(inputs, [z_mean, z_log_var, z], name='encoder')
        
        logger.info("编码器构建完成")
        return encoder, z_mean, z_log_var
    
    def _build_decoder(self, latent_dim: int, original_dim: int) -> Any:
        """
        构建解码器
        
        Args:
            latent_dim: 潜在空间维度
            original_dim: 原始数据维度
            
        Returns:
            解码器模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建解码器")
        
        # 潜在空间输入
        latent_inputs = Input(shape=(latent_dim,), name='decoder_input')
        
        # 隐藏层
        x = Dense(self.intermediate_dim, activation='relu')(latent_inputs)
        
        # 输出层
        outputs = Dense(original_dim, activation='sigmoid')(x)
        
        # 解码器模型
        decoder = Model(latent_inputs, outputs, name='decoder')
        
        logger.info("解码器构建完成")
        return decoder
    
    def _vae_loss(self, inputs, outputs, z_mean, z_log_var):
        """
        VAE损失函数
        
        Args:
            inputs: 输入数据
            outputs: 重构数据
            z_mean: 潜在空间均值
            z_log_var: 潜在空间对数方差
            
        Returns:
            VAE损失
        """
        # 重构损失
        reconstruction_loss = mse(inputs, outputs)
        reconstruction_loss *= self.original_dim
        
        # KL散度损失
        kl_loss = 1 + z_log_var - K.square(z_mean) - K.exp(z_log_var)
        kl_loss = K.sum(kl_loss, axis=-1)
        kl_loss *= -0.5 * self.beta
        
        # 总损失
        vae_loss = K.mean(reconstruction_loss + kl_loss)
        return vae_loss
    
    def _build_vae(self, original_dim: int) -> Any:
        """
        构建VAE模型
        
        Args:
            original_dim: 原始数据维度
            
        Returns:
            VAE模型
        """
        if not KERAS_AVAILABLE:
            raise ImportError("Keras不可用，无法构建VAE")
        
        # 构建编码器和解码器
        self.encoder, z_mean, z_log_var = self._build_encoder(original_dim)
        self.decoder = self._build_decoder(self.latent_dim, original_dim)
        
        # VAE输入
        inputs = Input(shape=(original_dim,), name='vae_input')
        
        # 编码
        z_mean_out, z_log_var_out, z = self.encoder(inputs)
        
        # 解码
        outputs = self.decoder(z)
        
        # VAE模型
        vae = Model(inputs, outputs, name='vae')
        
        # 添加损失
        vae.add_loss(self._vae_loss(inputs, outputs, z_mean_out, z_log_var_out))
        vae.compile(optimizer=Adam(learning_rate=0.001))
        
        logger.info("VAE模型构建完成")
        return vae
    
    def _prepare_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            data: 原始数据
            
        Returns:
            训练数据和测试数据
        """
        # 选择特征列
        feature_cols = [col for col in data.columns if col not in ['date', 'symbol', 'target']]
        self.feature_columns = feature_cols
        
        # 提取特征
        features = data[feature_cols].values
        self.original_dim = features.shape[1]
        
        # 数据标准化到[0, 1]范围（适合sigmoid激活函数）
        from sklearn.preprocessing import MinMaxScaler
        self.scaler = MinMaxScaler()
        features_scaled = self.scaler.fit_transform(features)
        
        # 分割训练和测试数据
        split_idx = int(len(features_scaled) * 0.8)
        train_data = features_scaled[:split_idx]
        test_data = features_scaled[split_idx:]
        
        logger.info(f"数据准备完成，训练样本: {len(train_data)}, 测试样本: {len(test_data)}")
        return train_data, test_data
    
    def train(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练VAE模型
        
        Args:
            data: 训练数据
            
        Returns:
            训练结果
        """
        try:
            logger.info("开始训练VAE模型")
            
            # 准备数据
            train_data, test_data = self._prepare_data(data)
            
            # 构建模型
            self.vae = self._build_vae(self.original_dim)
            
            # 设置回调
            callbacks = [
                EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
                ModelCheckpoint('vae_best.h5', save_best_only=True, monitor='val_loss')
            ]
            
            # 训练模型
            history = self.vae.fit(
                train_data, train_data,
                epochs=self.epochs,
                batch_size=self.batch_size,
                validation_data=(test_data, test_data),
                callbacks=callbacks,
                verbose=1
            )
            
            self.is_trained = True
            
            # 计算重构误差
            train_pred = self.vae.predict(train_data)
            train_mse = np.mean(np.square(train_data - train_pred), axis=1)
            
            test_pred = self.vae.predict(test_data)
            test_mse = np.mean(np.square(test_data - test_pred), axis=1)
            
            # 计算潜在空间表示
            z_mean, z_log_var, z = self.encoder.predict(train_data)
            
            results = {
                'train_loss': history.history['loss'][-1],
                'val_loss': history.history['val_loss'][-1],
                'train_mse_mean': np.mean(train_mse),
                'test_mse_mean': np.mean(test_mse),
                'latent_mean_std': np.std(z_mean),
                'latent_var_mean': np.mean(np.exp(z_log_var)),
                'latent_dim': self.latent_dim
            }
            
            logger.info(f"VAE训练完成，验证损失: {results['val_loss']:.6f}")
            return results
            
        except Exception as e:
            logger.error(f"VAE训练失败: {e}")
            return {'error': str(e)}
    
    def generate_data(self, num_samples: int = 100) -> np.ndarray:
        """
        生成合成数据
        
        Args:
            num_samples: 生成样本数量
            
        Returns:
            生成的数据
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 从标准正态分布采样
            z_sample = np.random.normal(size=(num_samples, self.latent_dim))
            
            # 解码生成数据
            generated_data = self.decoder.predict(z_sample, verbose=0)
            
            logger.info(f"生成 {num_samples} 个合成样本")
            return generated_data
            
        except Exception as e:
            logger.error(f"数据生成失败: {e}")
            return np.array([])
    
    def encode(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        编码数据到潜在空间
        
        Args:
            data: 输入数据
            
        Returns:
            潜在空间均值、方差、采样
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        features = data[self.feature_columns].values
        features_scaled = self.scaler.transform(features)
        
        z_mean, z_log_var, z = self.encoder.predict(features_scaled, verbose=0)
        return z_mean, z_log_var, z
    
    def decode(self, z: np.ndarray) -> np.ndarray:
        """
        从潜在空间解码数据
        
        Args:
            z: 潜在空间表示
            
        Returns:
            解码数据
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        decoded = self.decoder.predict(z, verbose=0)
        return self.scaler.inverse_transform(decoded)
    
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        使用VAE进行预测
        
        Args:
            data: 预测数据
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 编码到潜在空间
            z_mean, z_log_var, z = self.encode(data)
            
            # 重构数据
            features_scaled = self.scaler.transform(data[self.feature_columns].values)
            reconstructed_scaled = self.vae.predict(features_scaled, verbose=0)
            reconstructed = self.scaler.inverse_transform(reconstructed_scaled)
            
            # 计算重构误差
            mse = np.mean(np.square(data[self.feature_columns].values - reconstructed), axis=1)
            
            # 计算不确定性（基于潜在空间方差）
            uncertainty = np.mean(np.exp(z_log_var), axis=1)
            
            # 构建结果
            results = pd.DataFrame({
                'date': data['date'] if 'date' in data.columns else range(len(data)),
                'reconstruction_error': mse,
                'uncertainty': uncertainty,
                'log_likelihood': -mse  # 简化的对数似然
            })
            
            # 添加潜在空间特征
            for i in range(z_mean.shape[1]):
                results[f'latent_mean_{i}'] = z_mean[:, i]
                results[f'latent_var_{i}'] = np.exp(z_log_var[:, i])
            
            logger.info(f"VAE预测完成，平均重构误差: {np.mean(mse):.6f}")
            return results
            
        except Exception as e:
            logger.error(f"VAE预测失败: {e}")
            return pd.DataFrame()
    
    def anomaly_detection(self, data: pd.DataFrame, threshold_percentile: float = 95) -> pd.DataFrame:
        """
        基于重构误差的异常检测
        
        Args:
            data: 检测数据
            threshold_percentile: 阈值百分位数
            
        Returns:
            异常检测结果
        """
        predictions = self.predict(data)
        if predictions.empty:
            return pd.DataFrame()
        
        # 设置异常阈值
        threshold = np.percentile(predictions['reconstruction_error'], threshold_percentile)
        predictions['is_anomaly'] = predictions['reconstruction_error'] > threshold
        predictions['anomaly_score'] = predictions['reconstruction_error'] / threshold
        
        return predictions[['date', 'reconstruction_error', 'uncertainty', 'is_anomaly', 'anomaly_score']]
    
    def interpolate(self, data1: pd.DataFrame, data2: pd.DataFrame, num_steps: int = 10) -> List[np.ndarray]:
        """
        在潜在空间中插值
        
        Args:
            data1: 起始数据
            data2: 结束数据
            num_steps: 插值步数
            
        Returns:
            插值序列
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 编码到潜在空间
        z1_mean, _, _ = self.encode(data1)
        z2_mean, _, _ = self.encode(data2)
        
        # 在潜在空间中插值
        interpolations = []
        for i in range(num_steps):
            alpha = i / (num_steps - 1)
            z_interp = (1 - alpha) * z1_mean + alpha * z2_mean
            decoded = self.decode(z_interp)
            interpolations.append(decoded)
        
        return interpolations
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': 'VAEStrategy',
            'description': '基于变分自动编码器的概率生成建模策略',
            'category': 'DEEP_LEARNING',
            'latent_dim': self.latent_dim,
            'intermediate_dim': self.intermediate_dim,
            'is_trained': self.is_trained,
            'features': self.feature_columns,
            'capabilities': [
                'probabilistic_modeling',
                'data_generation',
                'uncertainty_quantification',
                'anomaly_detection',
                'latent_space_interpolation'
            ]
        }


def register_vae_strategy():
    """
    注册VAE策略到统一系统
    """
    if UNIFIED_SYSTEM_AVAILABLE:
        try:
            system = get_unified_system()
            system.register_strategy(
                "VAEStrategy",
                VAEStrategy,
                "DEEP_LEARNING",
                description="基于变分自动编码器的概率生成建模策略",
                author="Strategy AutoDev",
                version="1.0.0",
                capabilities=[
                    "probabilistic_modeling",
                    "data_generation",
                    "uncertainty_quantification",
                    "anomaly_detection",
                    "latent_space_interpolation"
                ]
            )
            logger.info("✅ VAEStrategy 注册成功")
            return True
        except Exception as e:
            logger.error(f"❌ VAEStrategy 注册失败: {e}")
            return False
    else:
        logger.warning("统一系统不可用，跳过策略注册")
        return False


if __name__ == "__main__":
    # 注册策略
    register_vae_strategy()
    
    # 示例使用
    if DATA_PIPELINE_AVAILABLE:
        try:
            # 创建策略实例
            config = {
                'latent_dim': 32,
                'intermediate_dim': 64,
                'epochs': 50
            }
            
            strategy = VAEStrategy(config)
            
            # 获取数据
            adapter = get_adapter()
            data = adapter.get_stock_data(['000001.SZ'], '2023-01-01', '2023-12-31')
            
            if not data.empty:
                # 训练模型
                results = strategy.train(data)
                print(f"训练结果: {results}")
                
                # 进行预测
                predictions = strategy.predict(data)
                print(f"预测结果: {predictions.head()}")
                
                # 生成数据
                synthetic_data = strategy.generate_data(50)
                print(f"生成数据形状: {synthetic_data.shape}")
                
                # 异常检测
                anomalies = strategy.anomaly_detection(data)
                print(f"异常检测: {anomalies.head()}")
            
        except Exception as e:
            logger.error(f"示例运行失败: {e}")
    else:
        logger.info("数据管道不可用，跳过示例")