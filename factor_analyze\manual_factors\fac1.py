#!/usr/bin/env python
# coding: utf-8

# # 浜嬩欢椹卞姩绛栫暐-楂橀佽浆浜嬩欢
# 
# - 鏈鏂囧皢浠ラ珮閫佽浆浜嬩欢涓轰緥锛屽垎浜涓涓绠鍗曠殑楂橀佽浆浜嬩欢椹卞姩绛栫暐鐨勪骇鐢熻繃绋嬶紝甯屾湜鑳界粰澶у跺甫鏉ヤ竴浜涘府鍔┿?
# (https://www.joinquant.com/view/community/detail/2a2d88bce0caa3c3c8069b581e1359db?type=1)

# ## 楂橀佽浆鍚涔
# - 瀵归珮閫佽浆鐞嗚В鐨勬繁搴﹀逛簬鐞嗚В鏈绡囨枃绔犵殑瑕佷箟褰卞搷骞朵笉澶э紝鐪嬪畬鏂囩珷鍚庡湪鍘荤爺绌堕珮閫佽浆鐨勭粏鑺備篃鍙浠ャ?
# - [鐧惧害鐧剧戝归珮閫佽浆鐨勮В閲婏細](https://baike.baidu.com/item/楂橀佽浆鑲＄エ/8253112)楂橀佽浆鑲＄エ锛堢畝绉帮細楂橀佽浆锛夌畝鍗曠悊瑙ｄ负锛氳偂鏁板炲姞锛岃偂浠峰彉灏忋備妇渚嬶細1寮?00鍏冿紝鎹㈡垚浜?寮?0鍏冿紝鏁伴噺澧炲姞鎬讳环鍊兼病鍙樺姩銆傛槸鎸囬佺孩鑲℃垨鑰呰浆澧炶偂绁ㄧ殑姣斾緥寰堝ぇ銆傚疄璐ㄦ槸鑲′笢鏉冪泭鐨勫唴閮ㄧ粨鏋勮皟鏁达紝瀵瑰噣璧勪骇鏀剁泭鐜囨病鏈夊奖鍝嶏紝瀵瑰叕鍙哥殑鐩堝埄鑳藉姏涔熷苟娌℃湁浠讳綍瀹炶川鎬у奖鍝嶃傗滈珮閫佽浆鈥濆悗锛屽叕鍙歌偂鏈鎬绘暟铏界劧鎵╁ぇ浜嗭紝浣嗗叕鍙哥殑鑲′笢鏉冪泭骞朵笉浼氬洜姝よ屽炲姞銆傝屼笖锛屽湪鍑鍒╂鼎涓嶅彉鐨勬儏鍐典笅锛岀敱浜庤偂鏈鎵╁ぇ锛岃祫鏈鍏绉閲戣浆澧炶偂鏈鎽婅杽姣忚偂鏀剁泭銆?鍦ㄥ叕鍙糕滈珮閫佽浆鈥濇柟妗堢殑瀹炴柦鏃ワ紝鍏鍙歌偂浠峰皢鍋氶櫎鏉冨勭悊锛屼篃灏辨槸璇达紝灏界♀滈珮閫佽浆鈥濇柟妗堜娇寰楁姇璧勮呮墜涓鐨勮偂绁ㄦ暟閲忓炲姞浜嗭紝浣嗚偂浠蜂篃灏嗚繘琛岀浉搴旂殑璋冩暣锛屾姇璧勮呮寔鑲℃瘮渚嬩笉鍙橈紝鎸佹湁鑲＄エ鐨勬讳环鍊间篃鏈鍙戠敓鍙樺寲銆?

# ## 鐮旂┒鎬濊矾
# - 鐚滄兂锛岃偂绁ㄥ叕鍙稿叕甯冭佽繘琛岄珮閫佽浆鍚庯紝鑲′环浼氫笂娑ㄣ?
# - 楠岃瘉鎬濊矾锛岃幏鍙栧巻鍙蹭笂杩涜岄珮閫佽浆鑲＄エ鐨勬暟鎹锛岃＄畻鍑鸿繖浜涜偂绁ㄥ叕甯冮珮閫佽浆涔嬪悗鐨勮偂浠峰彉鍖栵紝瀵瑰叾鍔犳诲钩鍧囷紝缁熻″嚭骞冲潎鐪嬫潵鑲＄エ楂橀佽浆鍚庤偂浠峰彉鍖栨儏鍐碉紝濡傛灉鏈変笂娑ㄨ秼鍔匡紝璁や负鐚滄兂鎴愮珛銆?

# ## 瀵煎叆搴撲笌鍑芥暟
# - 浠ヤ笅杩欓儴鍒嗗唴瀹规槸鍔犺浇鍚勭嶇敤鍒扮殑搴擄紝浠ュ強鑷瀹氫箟鐨勫嚱鏁帮紝闇瑕佸厛杩愯岃繃涓娆℃墠鑳芥ｇ‘杩愯屽悗缁鐨勫唴瀹

# In[1]:


from jqdata import *
import jqdata
import pandas as pd
import datetime 
import pickle

# 鍏ㄥ眬鍙橀噺
# 浜ゆ槗鏃ユ湡鍒楄〃
all_trade_days=[i.strftime('%Y-%m-%d') for i in list(get_all_trade_days())]

# 鑾峰彇鐩稿筨aseday鐨刬x涓浜ゆ槗鏃ラ噺浠锋定璺屽?
def get_pct_change(scu,ix,date,fqy='1d',field='close'):
    if ix<=0:
        df=get_price(security=scu,count=-ix+2,end_date=date,fields=[field],frequency=fqy)[field,:,:].pct_change().iloc[1:,:]

    else:
        df=get_price(security=scu,count=ix+2,end_date=all_trade_days[all_trade_days.index(date)+ix],fields=[field],frequency=fqy)[field,:,:].pct_change().iloc[1:,:]
    if df.index.empty==True:
        df=df.T
        df['0000-00-00']=[]
        df=df.T
    return df
# 鑾峰彇鐩稿筨aseday鐨刬x涓浜ゆ槗鏃ラ噺浠风疮璁℃定璺屽?
def get_pct_change_accum(scu,ix,date,fqy='1d',field='close'):
    if ix<=0:
        df=get_price(security=scu,count=-ix+2,end_date=date,fields=[field],frequency=fqy)[field,:,:].iloc[1:,:]

    else:
        df=get_price(security=scu,count=ix+2,end_date=all_trade_days[all_trade_days.index(date)+ix],fields=[field],frequency=fqy)[field,:,:].iloc[1:,:]
    if df.index.empty==True:
        df=df.T
        df['0000-00-00']=[]
        df=df.T
        
    return df/df.iloc[0]
# 绛涘幓娑ㄨ穼鍋滆偂
def tenper_stk_filter(scu,date,ix=0):
    if len(scu)==0:
        return []
    df=get_pct_change(scu,ix,date,fqy='1d',field='close').T
    df=df[df<0.099].dropna()
    df=df[df>-0.099].dropna()
    return list(df.index)


# ## 鑾峰彇浜嬩欢鏁版嵁
# - 鑾峰彇浜嬩欢鏁版嵁娌℃湁閫氭硶銆備笉鍚岀殑浜嬩欢鏁版嵁鏉ユ簮鍙鑳戒笉鍚岋紝瀵艰嚧鏍煎紡涓嶅悓锛岀敋鑷虫湁浜涙暟鎹闇瑕佽嚜宸辩敓鎴愪笌鏁寸悊锛屾墍浠ユ崲鎴愬叾浠栦簨浠舵椂锛屼富瑕佹敼鐨勫湴鏂逛篃灏辨槸杩欓噷銆?
# - 姝ゅ勪互楂橀佽浆浜嬩欢鏁版嵁鏉ユ簮鏄?[tushare 鍒嗛厤棰勬圿(https://www.joinquant.com/data/dict/tushare)銆?
# - 楂橀佽浆閲囧彇鐨勫畾涔夋槸锛屾瘡10鑲¤浆澧炲拰閫佽偂鏁板ぇ浜?锛屾墠绠?楂樷濋佽浆銆傛墍浠ヨ繘琛屼簡绗﹀悎楂橀佽浆瀹氫箟鐨勪簨浠剁殑绛涢夛紝骞跺皢鍏舵暣鐞嗘垚key涓烘棩鏈燂紝value涓鸿偂绁ㄤ唬鐮佺殑dict褰㈠紡銆?
# - 姝ゅ勪竴娆℃ф彁鍙栦簡2007鍒?018鐨勬暟鎹锛屾墍浠ヤ細鏈夌偣鎱銆?

# In[2]:


# 鍒濆嬪寲瀛樺偍浜嬩欢瑙﹀彂鎯呭喌鐨勫彉閲
totalLog=dict()
top_shares=pd.DataFrame()

# 瀵煎叆tushare搴?
import tushare as ts

# 鑾峰彇2007骞村埌2018骞寸殑鏁版嵁
years=range(2007,2018+1)
for i in years:
    # 鎵撳嵃褰撳墠姝ｅ湪鑾峰彇鐨勬暟鎹鐨勫勾浠斤紝鐢ㄤ互灞曠ず杩涘?
    print(i)
    t = ts.profit_data(year=i,top=4000)
    top_shares=pd.concat([top_shares,t],axis=0)

# 瀵规暟鎹杩涜屾寜鍏甯冩棩鏈熸帓搴
top_shares.sort_values(by=['report_date'],ascending=True,inplace=True)
# 閲嶇疆鏁版嵁index
top_shares.reset_index(inplace=True,drop=True)
# 杞鍖栬偂绁ㄤ唬鐮佷负鑱氬芥牸寮?
top_shares['code']=top_shares['code'].apply(normalize_code)
# 绛涢夊嚭姣?0鑲¤浆澧炲拰閫佽偂鏁板ぇ浜?鐨勬暟鎹?
top_shares=top_shares[top_shares['shares']>=8]


# In[3]:


# 鎶婂叕鍛婃棩鏈熶腑闈炰氦鏄撴棩鎹㈡垚鏈杩戠殑鍓嶄竴涓浜ゆ槗鏃
df=pd.DataFrame(all_trade_days)
f=lambda x:df[df<=x].dropna().iloc[-1].values[0]
top_shares['report_date']=top_shares['report_date'].apply(f)


# In[4]:


# 鏁版嵁灞曠ず
top_shares
# - code:鑲＄エ浠ｇ爜
# - name:鑲＄エ鍚嶇О
# - year:鍒嗛厤骞翠唤
# - report_date:鍏甯冩棩鏈
# - divi:鍒嗙孩閲戦濓紙姣10鑲★級
# - shares:杞澧炲拰閫佽偂鏁帮紙姣?0鑲★級


# In[5]:


# 灏嗘暟鎹鏁寸悊鎴恔ey涓烘棩鏈燂紝value涓鸿偂绁ㄤ唬鐮佺殑dict褰㈠紡
for i,j in zip(top_shares['report_date'],top_shares['code']):
#     print(i,j)
    if i in totalLog:
        totalLog[i]+=[j]
    else:
        totalLog[i]=[j]


# In[6]:


# 灞曠ずtotalLog锛屽嵆姣忓ぉ瑙﹀彂浜嬩欢鐨勮偂绁?
totalLog      


# ## 缁熻′簨浠惰Е鍙戝悗鑲′环娑ㄨ穼骞呭彉鍖栨儏鍐
# 
# - 涓嶇′綘鐨勪簨浠舵槸浠涔堬紝鍙瑕佷綘鐨勬暟鎹鏍煎紡澶勭悊鎴愬拰鍓嶆枃鐨則otalLog涓鏍峰氨鍙浠ュ楃敤锛屽嵆鏄痙ict绫诲瀷锛屽叾涓锛宬ey涓烘棩鏈燂紝绫诲瀷涓哄瓧绗︿覆锛寁alue涓鸿偂绁ㄤ唬鐮侊紝绫诲瀷涓鸿仛瀹藉钩鍙癆PI浣跨敤鐨勮偂绁ㄤ唬鐮併?

# In[3]:


# 閫夋嫨缁熻′簨浠跺嚭鐜版儏鍐电殑璧锋㈡椂闂?
start='2007-07-12'
end='2018-01-29'
# 鑾峰彇璧锋㈡椂闂村唴鐨勪氦鏄撴?
days=[i.strftime('%Y-%m-%d') for i in list(get_trade_days(start,end))]


# In[4]:


# 閫夋嫨鍩哄噯鎸囨暟
# 濡傛灉閫夋嫨浜嗗熀鍑嗘寚鏁板垯缁熻＄殑鑲＄エ娑ㄨ穼骞呮槸鍑忓幓浜嗚ユ寚鏁版定璺屽箙鐨勭浉瀵规定璺屽箙
# 濡傛灉鎯崇湅鍗曠函鑲＄エ娑ㄨ穼骞咃紝鍙浠ヨ祴鍊间负绌哄瓧绗︿覆''
baseIndex='000300.XSHG'

# 閫夋嫨瑕佺粺璁¤Е鍙戜簨浠跺悗澶氬皯澶╃殑鑲′环鍙樺寲鎯呭喌
n_limt=200
# 閫夋嫨瑕佷粠瑙﹀彂浜嬩欢鍓嶅氬皯澶╁紑濮嬬粺璁¤偂浠峰彉鍖栨儏鍐?
ahead_days=0

# 鎶婁簨浠舵暟鎹璧嬪肩粰stk
stk=totalLog
# 鍒濆嬪寲瀛樺偍缁熻＄粨鏋滅殑鍙橀噺
totalSum=pd.DataFrame()

# 寰鐜瑙﹀彂浜嗕簨浠剁殑姣忎竴澶?
for i in stk.keys():
    if i<start or i>end:
        continue
#     print(i)
    # 鑾峰緱褰撳ぉ瑙﹀彂浜嬩欢鐨勮偂绁?
    stk_list=stk[i]
    # 濡傛灉閫夋嫨浜嗗熀鍑嗘寚鏁?
    if baseIndex!='':
        # 鑾峰彇褰撳ぉ瑙﹀彂浜嬩欢鍚庤偂绁ㄧ浉瀵瑰熀鍑嗘寚鏁扮殑绱璁℃定璺屽?
        t=get_pct_change_accum([baseIndex]+stk_list,n_limt,all_trade_days[all_trade_days.index(i)-ahead_days],fqy='1d')
        t.reset_index(drop=True,inplace=True)
        t=t.sub(t[baseIndex],axis=0)
        del t[baseIndex]
    # 濡傛灉娌￠夊熀鍑嗘寚鏁?
    else:
        # 鑾峰彇褰撳ぉ瑙﹀彂浜嬩欢鍚庤偂绁ㄧ殑绱璁℃定璺屽?
        t=get_pct_change_accum(stk_list,n_limt,all_trade_days[all_trade_days.index(i)-ahead_days],fqy='1d')
        t.reset_index(drop=True,inplace=True)
    # 璁板綍褰撳ぉ瑙﹀彂浜嬩欢鍚庤偂绁ㄧ殑娑ㄨ穼骞咃紝瀛樺偍鍦╰otalSum
    totalSum=pd.concat([totalSum,t],axis=1)


# In[5]:


# 灞曠ず缁熻＄粨鏋
# 鏍煎紡涓篸ataframe锛屽瓧娈典负鑲＄エ浠ｇ爜锛岀储寮曚负浜嬩欢鍚庣鍑犲ぉ锛屽綋澶╀负0锛屽间负娑ㄨ穼骞?
totalSum


# ## 浜嬩欢瑙﹀彂鍚庤偂浠峰钩鍧囨定璺屽箙璧板娍鍥?
# - 涓轰簡鑳藉揩閫熷逛簨浠惰Е鍙戝悗鑲′环璧板娍鏈変釜鍒ゆ柇锛屾垜浠鐢诲嚭浜嬩欢瑙﹀彂鍚庤偂浠峰钩鍧囨定璺屽箙璧板娍鍥俱?
# - 浠ラ珮閫佽浆浜嬩欢涓轰緥锛屽彲瑙佸湪2007鑷?018鏈熼棿锛屽钩鍧囨潵鐪嬶紝鑲＄エ鍏甯冭繘琛岄珮閫佽浆鍚庯紝鑲′环浼氭寔缁涓婃定锛屽湪绗80澶╁乏鍙宠揪鍒?0%宸﹀彸鐨勮秴棰濇敹鐩婏紝闅忓悗鑲′环寮濮嬭蛋浣庛?

# In[6]:


# 璁剧疆浣滃浘澶у皬
figsize=(16,5)
# 鐢熸垚浜嬩欢瑙﹀彂鍚庤偂浠峰钩鍧囨定璺屽箙璧板娍鍥?
totalSum.T.mean().plot(figsize=figsize).grid()


# ## 杞鎴愮瓥鐣
# - 涓轰簡鎶婄粺璁″悗鐨勫彂鐜拌浆鎴愪竴涓浜嬩欢椹卞姩绛栫暐锛屾垜浠闇瑕佺‘瀹氫拱鍗栨柟娉曘?
# - 涔板崠鏂规硶娌℃湁閫氭硶锛屾ゅ勯噰鐢ㄧ畝鍗曠殑绛夎祫閲戦噺鐨勪拱鍏ユ柟娉曪紝涓庡埌鏈熸姏鍞鐨勫崠鍑烘柟娉曪紝浠ラ珮閫佽浆浜嬩欢涓轰緥鍏蜂綋濡備笅銆傞栧厛瀹氱殑鏄锛屼簨浠惰Е鍙戝悗绗?澶╀拱鍏ワ紝绗?0澶╁崠鍑恒傜劧鍚庯紝鍥犱负浜嬩欢鏄涓嶆柇瑙﹀彂鐨勶紝鎵浠ユ垜浠瑕佺粺璁″嚭鍦80澶╃殑鏃堕棿绐楀彛鍐呮渶澶氫細鏈夊氬皯涓鑲＄エ瑙﹀彂浜嬩欢銆備笅鏂囩粺璁″嚭鏄?12涓锛屾墍浠ユ垜浠鎶婃昏祫閲戝垎涓?12浠斤紝姣忔¤Е鍙戜簨浠讹紝鎷垮嚭涓浠藉幓涔板叆璇ヨ偂绁ㄣ?

# ## 缁熻′竴娈垫椂闂寸獥鍙ｅ唴瑙﹀彂浜嬩欢鐨勬渶澶ф℃?

# In[11]:


# 鎶婁簨浠舵暟鎹璧嬪肩粰stk
stk=totalLog
# 閫夋嫨鏃堕棿绐楀彛涓哄嚑澶?
period=80
# 鍒濆嬪寲瀛樺偍姣忓ぉ瑙﹀彂浜嬩欢娆℃暟鐨勫彉閲
stk_num={}
# 鍒濆嬪寲瀛樺偍鏃堕棿绐楀彛鍐呰Е鍙戜簨浠剁殑鏈澶ф℃暟鐨勫彉閲
max_stk_num=0

# 寰鐜缁熻¤捣姝㈡椂闂村唴鐨勪氦鏄撴?
for i in days:
    # 濡傛灉褰撳ぉ鏈変簨浠惰Е鍙?
    if stk.has_key(i):
        t=len(stk[i])
    # 濡傛灉褰撳ぉ娌℃湁浜嬩欢瑙﹀彂
    else:
        t=0
    # 璁板綍褰撳ぉ浜嬩欢瑙﹀彂鏁?
    stk_num[i]=t
#     # 璁＄畻褰撳墠鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵绘暟
#     t=sum(stk_num[-period:])
#     # 鏇存柊鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵渶澶ф绘暟
#     max_stk_num=max(max_stk_num,t)
# 灞曠ず缁撴灉
# max_stk_num
stk_num=pd.DataFrame.from_dict(data=stk_num,orient='index')
stk_num.sort_index(inplace=True)
stk_num=stk_num[0]


# In[12]:


# 灞曠ず姣忓ぉ瑙﹀彂浜嬩欢娆℃暟璧板娍鍥?
df=stk_num
df.plot(figsize=figsize).grid()


# In[13]:


# 灞曠ず鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵绘暟璧板娍鍥?
df=stk_num
df=df.rolling(window=period).sum()
df.plot(figsize=figsize).grid()
# 灞曠ず鍘嗗彶涓婃椂闂寸獥鍙ｅ唴瑙﹀彂浜嬩欢鎬绘暟鐨勬渶澶у?
df.max()


# ## 鏈浣宠祫閲戝垎閰嶄唤鏁颁及璁?
# - 浠庝笂鏂囩殑鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵绘暟璧板娍鍥惧彲浠ョ湅鍒帮紝鐩稿綋澶氱殑鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵暟涓嶈秴杩?0锛屽洜姝ゅ傛灉鎬昏祫閲戝垎涓?33浠斤紝姣忔¤Е鍙戜簨浠讹紝鎷垮嚭涓浠藉幓涔板叆璇ヨ偂绁ㄧ殑璇濓紝浼氬彂鐜板钩鏃跺ぇ閲忕殑璧勯噾閮芥槸闂茬疆鐨勩傚洜姝ゆ渶浣宠祫閲戝垎閰嶄唤鏁板簲璇ユ瘮333灏忎笉灏戙?
# - 濡傛灉姣忔￠珮閫佽浆浜嬩欢瑙﹀彂鍚庢寔鏈夌浉鍚屽ぉ鏁?0锛屽繀瀹氳幏鍙栫浉鍚岀殑姝ｆ敹鐩?0%锛岄偅涔堟渶浣宠祫閲戝垎閰嶄唤鏁板簲璇ユ槸1銆傚叾涓鐨勬暟瀛﹁В閲婃槸鏈浣宠祫閲戝垎閰嶄唤鏁癮锛屽簲浣垮緱褰搙=a鏃朵笅寮弝鍊间负鏈澶э紝鍏朵腑$m_i$涓虹琲涓鏃堕棿绐楀彛鍐呰Е鍙戜簨浠舵暟锛寈涓鸿祫閲戝垎閰嶄唤鏁帮紝x涓?鍒?33鐨勬暣鏁帮紝n涓烘诲叡鐨勬椂闂寸獥鍙ｆ暟銆備笉闅炬兂鍒皒瓒婂皬锛寉瓒婂ぇ銆備笉鐞嗚В娌″叧绯伙紝鏈夌粨璁恒?
# $$y=\sum_{i=1}^n \frac{min(m_i,x)}{x}$$
# - 涓嬫枃鐢诲嚭浜唝闅弜鐨勫彉鍖栧叧绯伙紝鍙鍙戠幇锛屽傛灉鈥滄瘡娆′簨浠惰Е鍙戝悗鎸佹湁鐩稿悓澶╂暟锛屽繀瀹氳幏鍙栫浉鍚岀殑姝ｆ敹鐩娾濊ュ墠鎻愭垚绔嬶紝鍒欐渶浣宠祫閲戝垎閰嶄唤鏁版槸1锛屽嵆姣忔￠兘婊′粨锛屽摢鎬曢敊杩囦竴浜涗簨浠剁殑瑙﹀彂銆傞棶棰樻槸鍓嶆彁鍙鍦ㄨ冻澶熷氱殑鏍锋湰缁熻＄粨鏋滀笅鎴愮珛锛屽疄闄呬笂瑙﹀彂楂橀佽浆鍚庡苟涓嶄竴瀹氱泩鍒╋紝鎵浠ュ疄闄呮渶浣宠祫閲戝垎閰嶄唤鏁颁笉鑳藉お灏忥紝閭ｆ牱浼氬艰嚧涔板崠鑲＄エ鏁拌繃灏戜娇寰楀墠鎻愪笉鎴愮珛锛屼粠鑰屼娇寰楁敹鐩婂彉寰楁瀬涓嶇ǔ瀹氾紝杈句笉鍒伴勬湡鐢氳嚦璐熸敹鐩娿?
# - 鏈鍚庢垜浠浜轰负浼拌＄殑閫夊彇50锛屼綔涓烘帴涓嬫潵绀轰緥绛栫暐閲囩敤鐨勮祫閲戝垎閰嶄唤鏁般?

# In[14]:


# 鐢诲嚭浜唝闅弜鐨勫彉鍖栧叧绯?
best_vp=pd.DataFrame(data=[0])
df=stk_num
for i in range(1,int(df.max())+1):
    
    f=lambda x:min(i,x)
    t=sum(df.apply(f).values)/i
    best_vp[i]=t

df=best_vp.T
df.plot(figsize=figsize).grid()
df.idxmax(axis=0).values


# ## 鏁版嵁瀛樺偍
# - 鎶婁簨浠惰Е鍙戞儏鍐电殑鏁版嵁锛坱otalLog锛夊瓨鍦ㄧ爺绌剁┖闂翠腑锛屼互澶囧湪绛栫暐鍥炴祴涓浣跨敤銆?

# In[29]:


# 鎶婁簨浠惰Е鍙戞儏鍐电殑鏁版嵁锛坱otalLog锛夊瓨鍦ㄧ爺绌剁┖闂翠腑锛屽瓨涓簍op_shares.json
import json
with open("top_shares.json", "w") as f:
    json.dump(obj=totalLog, fp=f)


# ## 绛栫暐鍥炴祴缁撴灉
# - 绛栫暐鐨勫洖娴嬬粨鏋滃傚浘銆傚洖娴嬫椂闂翠负2007-01-01鍒?018-11-30锛屽垵濮嬭祫閲?0000000锛?棰戝害姣忓ぉ銆傜疮璁℃敹鐩?12%锛屽勾鍖栨敹鐩婄巼6.7%锛屾渶澶у洖鎾ょ巼48%銆?
# 
# ![浜嬩欢椹卞姩.png][1]
# 
# 
#   [1]: https://image.joinquant.com/43d83a06ba2c5eef51c2e762cb1a1650

# # 闂棰樹笌鎬濊?
# - 鑰屼笖鐢ㄥ巻鍙叉暟鎹缁熻″嚭鐨勫弬鏁板仛瀵瑰簲鍘嗗彶鏃舵湡鐨勫洖娴嬶紝鏈夊弬鏁拌繃鎷熷悎闂棰橈紝搴旇冭檻鏍锋湰澶栨祴璇曘?
# - 浜嬩欢瑙﹀彂鍚庤偂浠峰钩鍧囨定璺屽箙璧板娍鍥剧粺璁′笂澶氬ぇ绋嬪害涓婂彲淇★紵鍙鑳藉彧鏄闅忔満娉㈠姩鐨勭粨鏋滐紝搴斿炲姞缁熻℃楠岀幆鑺傘?
# - 涔板叆鍗栧嚭鏂瑰紡杩囦簬绠鍗曪紝闄愬埗浜嗙泩鍒╄兘鍔涖?
