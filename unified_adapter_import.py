#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一数据适配器导入模块
解决重复定义问题，统一使用adapters目录下的标准实现
"""

import sys
from typing import Optional

def get_unified_adapters():
    """
    获取统一的数据适配器
    优先使用标准实现，失败时提供Mock版本
    """
    
    # 尝试导入标准实现
    try:
        from adapters.stock_data_adapter import StockDataAdapter
        from adapters.fundamental_data_adapter import FundamentalDataAdapter
        from adapters.market_data_adapter import MarketDataAdapter
        from adapters.factor_data_adapter import FactorDataAdapter
        
        print("[INFO] 使用标准数据适配器实现")
        return {
            'StockDataAdapter': StockDataAdapter,
            'FundamentalDataAdapter': FundamentalDataAdapter,
            'MarketDataAdapter': MarketDataAdapter,
            'FactorDataAdapter': FactorDataAdapter
        }
        
    except ImportError as e:
        print(f"[WARNING] 标准适配器导入失败: {e}")
        print("[INFO] 使用Mock数据适配器")
        
        # 提供Mock版本
        class MockStockDataAdapter:
            def __init__(self):
                self.name = "MockStockDataAdapter"
            
            def get_stock_basic(self, **kwargs):
                import pandas as pd
                return pd.DataFrame()
            
            def get_daily(self, **kwargs):
                import pandas as pd
                return pd.DataFrame()
        
        class MockFundamentalDataAdapter:
            def __init__(self):
                self.name = "MockFundamentalDataAdapter"
            
            def get_fundamental_data(self, **kwargs):
                import pandas as pd
                return pd.DataFrame()
        
        class MockMarketDataAdapter:
            def __init__(self):
                self.name = "MockMarketDataAdapter"
            
            def get_market_data(self, **kwargs):
                import pandas as pd
                return pd.DataFrame()
        
        class MockFactorDataAdapter:
            def __init__(self):
                self.name = "MockFactorDataAdapter"
            
            def get_factor_data(self, **kwargs):
                import pandas as pd
                return pd.DataFrame()
        
        return {
            'StockDataAdapter': MockStockDataAdapter,
            'FundamentalDataAdapter': MockFundamentalDataAdapter,
            'MarketDataAdapter': MockMarketDataAdapter,
            'FactorDataAdapter': MockFactorDataAdapter
        }

# 全局变量存储适配器
_ADAPTERS = None

def get_adapter(adapter_name: str):
    """
    获取指定的适配器类
    
    Args:
        adapter_name: 适配器名称
        
    Returns:
        适配器类
    """
    global _ADAPTERS
    if _ADAPTERS is None:
        _ADAPTERS = get_unified_adapters()
    
    return _ADAPTERS.get(adapter_name)

def get_all_adapters():
    """
    获取所有适配器
    
    Returns:
        包含所有适配器的字典
    """
    global _ADAPTERS
    if _ADAPTERS is None:
        _ADAPTERS = get_unified_adapters()
    
    return _ADAPTERS

# 为了方便使用，直接提供适配器类
try:
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from adapters.market_data_adapter import MarketDataAdapter
    from adapters.factor_data_adapter import FactorDataAdapter
    print("[INFO] 标准数据适配器可用")
except ImportError as e:
    print(f"[WARNING] 标准适配器不可用: {e}")
    print("[INFO] 将使用Mock版本")
    
    # 定义Mock版本
    class StockDataAdapter:
        def __init__(self):
            self.name = "MockStockDataAdapter"
        
        def get_stock_basic(self, **kwargs):
            import pandas as pd
            return pd.DataFrame()
        
        def get_daily(self, **kwargs):
            import pandas as pd
            return pd.DataFrame()
    
    class FundamentalDataAdapter:
        def __init__(self):
            self.name = "MockFundamentalDataAdapter"
        
        def get_fundamental_data(self, **kwargs):
            import pandas as pd
            return pd.DataFrame()
    
    class MarketDataAdapter:
        def __init__(self):
            self.name = "MockMarketDataAdapter"
        
        def get_market_data(self, **kwargs):
            import pandas as pd
            return pd.DataFrame()
    
    class FactorDataAdapter:
        def __init__(self):
            self.name = "MockFactorDataAdapter"
        
        def get_factor_data(self, **kwargs):
            import pandas as pd
            return pd.DataFrame()

# 导出所有适配器
__all__ = [
    'StockDataAdapter',
    'FundamentalDataAdapter', 
    'MarketDataAdapter',
    'FactorDataAdapter',
    'get_adapter',
    'get_all_adapters'
] 