# -*- coding: utf-8 -*-
"""
滚动扣非净资产收益率(Rolling Deducted Non-recurring ROE Factor)
该因子衡量了公司在扣除非经常性损益后，利用股东权益创造利润的能力。
计算公式：滚动扣非ROE = 扣非净利润(TTM) / 平均净资产

其中:
- 扣非净利润(TTM): 最近12个月（4个季度）的扣除非经常性损益后的净利润之和。
- 平均净资产: (期初归属母公司股东权益 + 期末归属母公司股东权益) / 2

数据来源:
- 扣非净利润：财务指标表中的 `profit_dedt` 字段。
- 归属母公司股东权益：资产负债表中的 `total_hldr_eqy_exc_min_int` 字段。

经济含义:
该因子反映了公司核心业务的盈利能力，排除了非经常性项目对利润的干扰。相较于传统的ROE，
扣非ROE更能持续地反映公司的价值创造能力。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
import warnings
from loguru import logger

try:
    from adapters.fundamental_data_adapter import FundamentalDataAdapter
    from factor_analyze.base_factor_miner import BaseFactorMiner
except ImportError as e:
    logger.warning(f"导入模块失败: {e}")
    # 为了测试和独立运行，定义备用基类
    class BaseFactorMiner:
        def __init__(self, config=None):
            self.config = config or {}
    
    # 已删除重复定义: FundamentalDataAdapter
warnings.filterwarnings('ignore')


class StockRollingDeductedROEFactor(BaseFactorMiner):
    """
    个股滚动扣非净资产收益率（ROE）因子计算器。
    
    计算流程:
    1. 获取个股的财务指标和资产负债表数据。
    2. 对财务数据进行合并和清洗。
    3. 计算TTM（过去12个月）的扣非净利润。
    4. 计算滚动扣非ROE。
    5. 对计算出的因子数据进行清洗和异常值处理。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化个股滚动扣非ROE因子计算器。
        
        Args:
            config: 配置字典。
        """
        super().__init__(config)
        
        # 因子基本信息
        self.factor_name = "rolling_deducted_roe"
        self.factor_description = "滚动扣非净资产收益率"
        self.factor_type = "fundamental"
        self.target_type = "stock"
        
        # 数据适配器
        try:
            self.data_adapter = FundamentalDataAdapter()
        except Exception as e:
            logger.warning(f"数据适配器初始化失败: {e}")
            self.data_adapter = None
        
        # 计算参数
        self.lookback_quarters = self.config.get('lookback_quarters', 4)  # TTM默认4个季度
        self.min_quarters = self.config.get('min_quarters', 4)  # 计算TTM所需最少季度数
        self.min_equity_threshold = self.config.get('min_equity_threshold', 1e6)  # 最小股东权益阈值
        
        # 因子数据清洗参数
        self.min_roe_threshold = self.config.get('min_roe_threshold', -1.0)  # ROE最小阈值
        self.max_roe_threshold = self.config.get('max_roe_threshold', 1.0)   # ROE最大阈值
        self.outlier_threshold = self.config.get('outlier_threshold', 3.0)   # 异常值标准差倍数
        
        logger.info("[INFO] 个股滚动扣非ROE因子初始化完成。")
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置。
        
        Returns:
            配置字典。
        """
        return {
            'lookback_quarters': 4,        # TTM计算回溯季度数
            'min_quarters': 4,             # 计算TTM所需最小季度数
            'min_equity_threshold': 1e6,   # 最小股东权益阈值
            'min_roe_threshold': -1.0,     # ROE最小阈值
            'max_roe_threshold': 1.0,      # ROE最大阈值
            'outlier_threshold': 3.0,      # 异常值过滤阈值
            'data_source': 'tushare',      # 数据源
            'calculation_method': 'ttm',   # 计算方法, 默认ttm
        }
    
    def calculate_factor(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        计算单个股票的滚动扣非ROE因子。
        
        Args:
            symbol: 股票代码。
            start_date: 开始日期。
            end_date: 结束日期。
            
        Returns:
            包含因子计算结果的字典。
        """
        try:
            logger.info(f"[INFO] 开始为 {symbol} 计算滚动扣非ROE因子。")
            
            # 获取财务数据
            financial_data = self._get_financial_data(symbol, start_date, end_date)
            
            if financial_data.empty:
                logger.warning(f"[WARNING] {symbol}: 无法获取财务数据。")
                return self._create_empty_result(symbol, "无财务数据")
            
            # 计算TTM扣非净利润
            ttm_data = self._calculate_ttm_deducted_profit(financial_data)
            
            if ttm_data.empty:
                logger.warning(f"[WARNING] {symbol}: TTM数据计算失败。")
                return self._create_empty_result(symbol, "TTM计算失败")
            
            # 计算滚动扣非ROE
            factor_data = self._calculate_rolling_deducted_roe(ttm_data)
            
            if factor_data.empty:
                logger.warning(f"[WARNING] {symbol}: 滚动ROE计算失败。")
                return self._create_empty_result(symbol, "ROE计算失败")
            
            # 清洗因子数据
            cleaned_data = self._clean_factor_data(factor_data)
            
            if cleaned_data.empty:
                logger.warning(f"[WARNING] {symbol}: 数据清洗后无有效数据。")
                return self._create_empty_result(symbol, "数据清洗后为空")
            
            # 准备最终结果
            latest_factor = cleaned_data.iloc[-1]['rolling_deducted_roe']
            
            result = {
                'symbol': symbol,
                'factor_name': self.factor_name,
                'factor_value': float(latest_factor),
                'calculation_date': cleaned_data.iloc[-1]['end_date'],
                'data_points': len(cleaned_data),
                'factor_data': cleaned_data,
                'ttm_deducted_profit': ttm_data['ttm_deducted_profit'],
                'avg_equity': ttm_data['avg_equity'],
                'status': 'success'
            }
            
            logger.info(f"[INFO] {symbol} 滚动扣非ROE因子计算成功，最新值为: {latest_factor:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] 计算 {symbol} 滚动扣非ROE因子时出错: {e}")
            return self._create_empty_result(symbol, str(e))
    
    def calculate_factor_batch(self, symbols: List[str], start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        批量计算多个股票的滚动扣非ROE因子。
        
        Args:
            symbols: 股票代码列表。
            start_date: 开始日期。
            end_date: 结束日期。
            
        Returns:
            包含所有股票计算结果的字典。
        """
        results = {}
        successful_count = 0
        
        logger.info(f"[INFO] 开始批量计算滚动扣非ROE因子，总数: {len(symbols)}")
        
        for symbol in symbols:
            try:
                result = self.calculate_factor(symbol, start_date, end_date)
                results[symbol] = result
                
                if result['status'] == 'success':
                    successful_count += 1
                    
            except Exception as e:
                logger.error(f"[ERROR] 批量计算 {symbol} 时失败: {e}")
                results[symbol] = self._create_empty_result(symbol, str(e))
        
        logger.info(f"[INFO] 批量计算完成，成功 {successful_count}/{len(symbols)}。")
        
        return {
            'results': results,
            'summary': {
                'total_symbols': len(symbols),
                'successful_count': successful_count,
                'success_rate': successful_count / len(symbols) if symbols else 0
            }
        }
    
    def _get_financial_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取并合并财务数据。
        
        Args:
            symbol: 股票代码。
            start_date: 开始日期。
            end_date: 结束日期。
            
        Returns:
            合并后的财务数据DataFrame。
        """
        try:
            if self.data_adapter is None:
                logger.warning("[WARNING] 数据适配器未初始化，将使用模拟数据。")
                return self._generate_mock_financial_data(symbol)
            
            # 获取财务指标数据（包含扣非净利润）
            fina_data = self.data_adapter.get_financial_indicators(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            # 获取资产负债表数据（包含股东权益）
            balance_data = self.data_adapter.get_balance_sheet(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if fina_data.empty or balance_data.empty:
                logger.warning(f"[WARNING] {symbol}: 财务指标或资产负债表数据为空。")
                return self._generate_mock_financial_data(symbol)
            
            # 合并两个数据源
            merged_data = self._merge_financial_data(fina_data, balance_data)
            
            return merged_data
            
        except Exception as e:
            logger.error(f"[ERROR] 为 {symbol} 获取财务数据时失败: {e}")
            return self._generate_mock_financial_data(symbol)
    
    def _merge_financial_data(self, fina_data: pd.DataFrame, balance_data: pd.DataFrame) -> pd.DataFrame:
        """
        合并财务指标和资产负债表数据。
        
        Args:
            fina_data: 财务指标数据。
            balance_data: 资产负债表数据。
            
        Returns:
            合并和清洗后的数据。
        """
        try:
            # 定义所需字段
            required_fina_fields = ['end_date', 'profit_dedt']  # 扣非净利润
            required_balance_fields = ['end_date', 'total_hldr_eqy_exc_min_int']  # 归母股东权益
            
            # 检查并补充利润字段
            available_fina_fields = [field for field in required_fina_fields if field in fina_data.columns]
            if 'profit_dedt' not in fina_data.columns:
                # 如果缺少`profit_dedt`，尝试使用其他净利润字段替代
                profit_fields = ['n_income', 'net_profit', 'netprofit']
                for field in profit_fields:
                    if field in fina_data.columns:
                        fina_data['profit_dedt'] = fina_data[field]
                        logger.warning(f"[WARNING] 使用 {field} 字段替代 profit_dedt。")
                        break
            
            # 检查并补充股东权益字段
            available_balance_fields = [field for field in required_balance_fields if field in balance_data.columns]
            if 'total_hldr_eqy_exc_min_int' not in balance_data.columns:
                # 如果缺少`total_hldr_eqy_exc_min_int`，尝试使用其他权益字段替代
                equity_fields = ['total_equity', 'total_hldr_eqy', 'equity']
                for field in equity_fields:
                    if field in balance_data.columns:
                        balance_data['total_hldr_eqy_exc_min_int'] = balance_data[field]
                        logger.warning(f"[WARNING] 使用 {field} 字段替代 total_hldr_eqy_exc_min_int。")
                        break
            
            # 基于end_date合并数据
            merged_data = pd.merge(
                fina_data[['end_date', 'profit_dedt']],
                balance_data[['end_date', 'total_hldr_eqy_exc_min_int']],
                on='end_date',
                how='inner'
            )
            
            # 数据预处理
            merged_data['end_date'] = pd.to_datetime(merged_data['end_date'])
            merged_data = merged_data.sort_values('end_date')
            
            # 移除空值
            merged_data = merged_data.dropna(subset=['profit_dedt', 'total_hldr_eqy_exc_min_int'])
            
            # 过滤不合理的股东权益
            equity_mask = merged_data['total_hldr_eqy_exc_min_int'] >= self.min_equity_threshold
            merged_data = merged_data[equity_mask]
            
            logger.info(f"[INFO] 财务数据合并清洗完成，有效记录数: {len(merged_data)}")
            return merged_data
            
        except Exception as e:
            logger.error(f"[ERROR] 合并财务数据时出错: {e}")
            return pd.DataFrame()
    
    def _calculate_ttm_deducted_profit(self, financial_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算TTM扣非净利润和平均股东权益。
        
        Args:
            financial_data: 财务数据。
            
        Returns:
            包含TTM数据的DataFrame。
        """
        try:
            data = financial_data.copy()
            data = data.sort_values('end_date')
            
            ttm_results = []
            
            for i in range(len(data)):
                if i < self.lookback_quarters - 1:
                    continue  # 数据不足以计算TTM
                
                # 截取最近4个季度的数据
                recent_data = data.iloc[i-self.lookback_quarters+1:i+1]
                
                if len(recent_data) < self.min_quarters:
                    continue
                
                # 计算TTM扣非净利润
                ttm_deducted_profit = recent_data['profit_dedt'].sum()
                
                # 计算平均股东权益（期初+期末）/2
                begin_equity = recent_data.iloc[0]['total_hldr_eqy_exc_min_int']
                end_equity = recent_data.iloc[-1]['total_hldr_eqy_exc_min_int']
                avg_equity = (begin_equity + end_equity) / 2
                
                ttm_results.append({
                    'end_date': data.iloc[i]['end_date'],
                    'ttm_deducted_profit': ttm_deducted_profit,
                    'avg_equity': avg_equity,
                    'begin_equity': begin_equity,
                    'end_equity': end_equity
                })
            
            result_df = pd.DataFrame(ttm_results)
            logger.info(f"[INFO] TTM数据计算完成，生成记录数: {len(result_df)}")
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] 计算TTM数据时出错: {e}")
            return pd.DataFrame()
    
    def _calculate_rolling_deducted_roe(self, ttm_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算滚动扣非ROE。
        
        Args:
            ttm_data: TTM数据。
            
        Returns:
            包含滚动ROE的DataFrame。
        """
        try:
            if ttm_data.empty:
                return pd.DataFrame()
            
            data = ttm_data.copy()
            
            # 计算滚动扣非ROE，避免分母为0
            data['rolling_deducted_roe'] = np.where(
                data['avg_equity'] > 0,
                data['ttm_deducted_profit'] / data['avg_equity'],
                np.nan
            )
            
            # 移除空值
            data = data.dropna(subset=['rolling_deducted_roe'])
            
            logger.info(f"[INFO] 滚动ROE计算完成，有效记录数: {len(data)}")
            return data
            
        except Exception as e:
            logger.error(f"[ERROR] 计算滚动ROE时出错: {e}")
            return pd.DataFrame()
    
    def _clean_factor_data(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        清洗因子数据，包括阈值过滤和异常值处理。
        
        Args:
            factor_data: 原始因子数据。
            
        Returns:
            清洗后的因子数据。
        """
        try:
            if factor_data.empty:
                return pd.DataFrame()
            
            cleaned_data = factor_data.copy()
            
            # 移除空值
            cleaned_data = cleaned_data.dropna(subset=['rolling_deducted_roe'])
            
            # ROE阈值过滤
            roe_mask = (
                (cleaned_data['rolling_deducted_roe'] >= self.min_roe_threshold) &
                (cleaned_data['rolling_deducted_roe'] <= self.max_roe_threshold)
            )
            cleaned_data = cleaned_data[roe_mask]
            
            # 异常值处理（3-sigma原则）
            if len(cleaned_data) > 2:
                roe_mean = cleaned_data['rolling_deducted_roe'].mean()
                roe_std = cleaned_data['rolling_deducted_roe'].std()
                if roe_std > 0:
                    outlier_mask = (
                        np.abs(cleaned_data['rolling_deducted_roe'] - roe_mean) <=
                        self.outlier_threshold * roe_std
                    )
                    cleaned_data = cleaned_data[outlier_mask]
            
            logger.info(f"[INFO] 因子数据清洗完成，剩余记录数: {len(cleaned_data)}")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"[ERROR] 清洗因子数据时出错: {e}")
            return pd.DataFrame()
    
    def _generate_mock_financial_data(self, symbol: str) -> pd.DataFrame:
        """
        生成模拟财务数据用于测试。
        
        Args:
            symbol: 股票代码。
            
        Returns:
            模拟财务数据的DataFrame。
        """
        try:
            # 生成最近8个季度的数据
            dates = pd.date_range(end=datetime.now(), periods=8, freq='Q')
            
            # 随机生成基准值
            base_equity = np.random.uniform(5e8, 20e8)  # 初始股东权益
            base_profit_margin = np.random.uniform(0.05, 0.20)  # 初始利润率
            
            mock_data = []
            for i, date in enumerate(dates):
                # 模拟权益增长
                equity_growth = np.random.uniform(0.95, 1.15)  # 季度增长率
                current_equity = base_equity * (equity_growth ** i)
                
                # 模拟季度利润
                quarterly_profit = current_equity * base_profit_margin * np.random.uniform(0.8, 1.2)
                
                mock_data.append({
                    'end_date': date,
                    'profit_dedt': quarterly_profit,
                    'total_hldr_eqy_exc_min_int': current_equity
                })
            
            result_df = pd.DataFrame(mock_data)
            logger.info(f"[INFO] 已生成模拟财务数据: {len(result_df)} 条记录 (用于测试)。")
            return result_df
            
        except Exception as e:
            logger.error(f"[ERROR] 生成模拟数据时出错: {e}")
            return pd.DataFrame()
    
    def _create_empty_result(self, symbol: str, error_msg: str) -> Dict[str, Any]:
        """
        创建空的或失败的计算结果。
        
        Args:
            symbol: 股票代码。
            error_msg: 错误信息。
            
        Returns:
            表示失败的字典。
        """
        return {
            'symbol': symbol,
            'factor_name': self.factor_name,
            'factor_value': np.nan,
            'calculation_date': None,
            'data_points': 0,
            'factor_data': pd.DataFrame(),
            'error': error_msg,
            'status': 'failed'
        }


class IndexRollingDeductedROEFactor(BaseFactorMiner):
    """
    指数滚动扣非净资产收益率因子。
    
    通过对其成分股的因子值进行加权平均来计算指数的整体因子值。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化指数滚动扣非ROE因子计算器。
        
        Args:
            config: 配置字典。
        """
        super().__init__(config)
        
        # 因子基本信息
        self.factor_name = "index_rolling_deducted_roe"
        self.factor_description = "指数滚动扣非净资产收益率"
        self.factor_type = "fundamental"
        self.target_type = "index"
        
        # 依赖个股因子计算器
        self.stock_factor = StockRollingDeductedROEFactor(config)
        
        # 指数计算参数
        self.weight_method = self.config.get('weight_method', 'market_cap')  # 权重方法
        self.min_constituent_ratio = self.config.get('min_constituent_ratio', 0.8)  # 最小有效成分股比例
        
        logger.info("[INFO] 指数滚动扣非ROE因子初始化完成。")
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置。
        
        Returns:
            配置字典。
        """
        base_config = self.stock_factor.get_default_config()
        base_config.update({
            'weight_method': 'market_cap',        # 权重方法
            'min_constituent_ratio': 0.8,        # 最小有效成分股比例
            'index_calculation_method': 'weighted_average'  # 指数计算方法
        })
        return base_config
    
    def calculate_factor(self, index_code: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        计算单个指数的滚动扣非ROE因子。
        
        Args:
            index_code: 指数代码。
            start_date: 开始日期。
            end_date: 结束日期。
            
        Returns:
            包含因子计算结果的字典。
        """
        try:
            logger.info(f"[INFO] 开始为指数 {index_code} 计算滚动扣非ROE因子。")
            
            # 获取指数成分股
            constituents = self._get_index_constituents(index_code)
            
            if not constituents:
                logger.warning(f"[WARNING] {index_code}: 无法获取成分股列表。")
                return self._create_empty_result(index_code, "无成分股数据")
            
            # 计算各成分股的因子值
            constituent_factors = {}
            successful_count = 0
            
            for symbol in constituents:
                try:
                    factor_result = self.stock_factor.calculate_factor(symbol, start_date, end_date)
                    if factor_result['status'] == 'success':
                        constituent_factors[symbol] = factor_result
                        successful_count += 1
                except Exception as e:
                    logger.warning(f"[WARNING] 计算成分股 {symbol} 因子时失败: {e}")
                    continue
            
            # 检查有效成分股比例
            success_ratio = successful_count / len(constituents) if constituents else 0
            if success_ratio < self.min_constituent_ratio:
                logger.warning(f"[WARNING] {index_code}: 有效成分股比例过低 ({success_ratio:.2%})。")
                return self._create_empty_result(index_code, f"有效成分股比例过低: {success_ratio:.2%}")
            
            # 计算指数因子值
            index_factor = self._calculate_index_factor(constituent_factors, index_code)
            
            result = {
                'index_code': index_code,
                'factor_name': self.factor_name,
                'factor_value': float(index_factor),
                'calculation_date': datetime.now().strftime('%Y-%m-%d'),
                'constituent_count': len(constituent_factors),
                'total_constituents': len(constituents),
                'success_ratio': success_ratio,
                'constituent_factors': constituent_factors,
                'status': 'success'
            }
            
            logger.info(f"[INFO] 指数 {index_code} 滚动扣非ROE因子计算成功，最新值为: {index_factor:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] 计算指数 {index_code} 滚动扣非ROE因子时出错: {e}")
            return self._create_empty_result(index_code, str(e))
    
    def _get_index_constituents(self, index_code: str) -> List[str]:
        """
        获取指数成分股列表。
        
        Args:
            index_code: 指数代码。
            
        Returns:
            成分股代码列表。
        """
        try:
            # 此处应调用真实的数据适配器来获取成分股
            # 为演示目的，使用模拟数据
            mock_constituents = [
                '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
                '000858.SZ', '002415.SZ', '600276.SH', '000725.SZ', '002594.SZ'
            ]
            
            logger.info(f"[INFO] 获取到指数 {index_code} 的成分股: {len(mock_constituents)} 支 (模拟数据)。")
            return mock_constituents
            
        except Exception as e:
            logger.error(f"[ERROR] 获取指数成分股失败: {e}")
            return []
    
    def _calculate_index_factor(self, constituent_factors: Dict[str, Dict], index_code: str) -> float:
        """
        计算指数的加权平均因子值。
        
        Args:
            constituent_factors: 成分股因子数据。
            index_code: 指数代码。
            
        Returns:
            指数的因子值。
        """
        try:
            if not constituent_factors:
                return np.nan
            
            # 提取因子值和权重
            factor_values = []
            weights = []
            
            for symbol, factor_data in constituent_factors.items():
                factor_value = factor_data.get('factor_value', np.nan)
                if not np.isnan(factor_value):
                    factor_values.append(factor_value)
                    # 此处应获取真实权重，如市值。为演示，使用等权重。
                    weights.append(1.0)
            
            if not factor_values:
                return np.nan
            
            # 计算加权平均
            weights = np.array(weights)
            weights = weights / weights.sum()  # 归一化
            
            index_factor = np.average(factor_values, weights=weights)
            
            return index_factor
            
        except Exception as e:
            logger.error(f"[ERROR] 计算指数因子值失败: {e}")
            return np.nan
    
    def _create_empty_result(self, index_code: str, error_msg: str) -> Dict[str, Any]:
        """
        创建空的或失败的计算结果。
        
        Args:
            index_code: 指数代码。
            error_msg: 错误信息。
            
        Returns:
            表示失败的字典。
        """
        return {
            'index_code': index_code,
            'factor_name': self.factor_name,
            'factor_value': np.nan,
            'calculation_date': None,
            'constituent_count': 0,
            'total_constituents': 0,
            'success_ratio': 0.0,
            'constituent_factors': {},
            'error': error_msg,
            'status': 'failed'
        }


class RollingDeductedROEFactor:
    """
    滚动扣非ROE因子的统一接口。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.stock_factor = StockRollingDeductedROEFactor(config)
        self.index_factor = IndexRollingDeductedROEFactor(config)
    
    def calculate(self, instrument: str, instrument_type: str = 'stock', **kwargs) -> Dict[str, Any]:
        """
        根据标的类型计算因子。
        
        Args:
            instrument: 标的代码 (股票或指数)。
            instrument_type: 标的类型 ('stock' 或 'index')。
            **kwargs: 其他计算参数。
            
        Returns:
            因子计算结果。
        """
        if instrument_type.lower() == 'stock':
            return self.stock_factor.calculate_factor(instrument, **kwargs)
        elif instrument_type.lower() == 'index':
            return self.index_factor.calculate_factor(instrument, **kwargs)
        else:
            raise ValueError(f"不支持的标的类型: {instrument_type}")


if __name__ == "__main__":
    # 示例代码
    print("=== 开始测试滚动扣非ROE因子 ===")
    
    # 1. 测试个股因子
    print("\n1. 测试个股因子")
    stock_factor = StockRollingDeductedROEFactor()
    stock_result = stock_factor.calculate_factor('000001.SZ')
    print(f"个股因子计算结果: {stock_result['factor_value']:.6f}")
    
    # 2. 测试指数因子
    print("\n2. 测试指数因子")
    index_factor = IndexRollingDeductedROEFactor()
    index_result = index_factor.calculate_factor('000300.SH')
    print(f"指数因子计算结果: {index_result['factor_value']:.6f}")
    
    # 3. 测试统一接口
    print("\n3. 测试统一接口")
    unified_factor = RollingDeductedROEFactor()
    unified_result = unified_factor.calculate('000001.SZ', 'stock')
    print(f"统一接口（个股）计算结果: {unified_result['factor_value']:.6f}")
    
    print("\n测试完成。")
