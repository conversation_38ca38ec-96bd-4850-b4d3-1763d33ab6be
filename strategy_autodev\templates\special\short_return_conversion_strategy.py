# -*- coding: utf-8 -*-
"""
空头收益转化策略模板
基于海通选股因子系列研究16：选股因子空头收益的转化

核心特点：
1. 空头收益转化机制
2. 因子有效性动态监控
3. 多空组合构建
4. 因子择时框架
5. 风险预算分配

作者: Strategy AutoDev System
版本: 1.0.0
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 导入数据管道
try:
    from data_pipeline.data_adapter import get_adapter
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    DATA_PIPELINE_AVAILABLE = False

# 导入因子分析工具
try:
    from factor_analyze.factor_engine import FactorEngine
    from factor_analyze.tools.factor_evaluator import FactorEvaluator
    FACTOR_ANALYSIS_AVAILABLE = True
except ImportError:
    FACTOR_ANALYSIS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ShortReturnConversionConfig:
    """空头收益转化策略配置"""
    # 基础参数
    lookback_window: int = 20
    rebalance_frequency: int = 5
    
    # 组合参数
    long_ratio: float = 0.3
    short_ratio: float = 0.3
    max_position_weight: float = 0.05
    
    # 因子参数
    factor_decay_threshold: float = 0.05
    ic_threshold: float = 0.03
    timing_fast_window: int = 20
    timing_slow_window: int = 60
    
    # 风险参数
    max_leverage: float = 2.0
    max_drawdown_threshold: float = 0.1
    volatility_target: float = 0.15
    
    # 调整参数
    transaction_cost: float = 0.002
    slippage: float = 0.001


class ShortReturnConversionStrategy:
    """空头收益转化策略"""
    
    def __init__(self, config: Optional[ShortReturnConversionConfig] = None):
        """初始化策略"""
        self.config = config or ShortReturnConversionConfig()
        self.factor_weights = {}
        self.portfolio_positions = {}
        self.performance_metrics = {}
        self.factor_ic_history = pd.DataFrame()
        self.position_history = pd.DataFrame()
        
        # 初始化组件
        self.data_adapter = None
        self.factor_engine = None
        self.factor_evaluator = None
        
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化组件"""
        if DATA_PIPELINE_AVAILABLE:
            try:
                self.data_adapter = get_adapter()
                logger.info("✅ 数据管道初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 数据管道初始化失败: {e}")
        
        if FACTOR_ANALYSIS_AVAILABLE:
            try:
                self.factor_engine = FactorEngine()
                self.factor_evaluator = FactorEvaluator()
                logger.info("✅ 因子分析工具初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 因子分析工具初始化失败: {e}")
    
    def calculate_short_return_conversion_factor(self, data: pd.DataFrame) -> pd.Series:
        """
        计算空头收益转化因子
        
        核心公式：(Short_Return - Long_Return) / (Long_Return + Short_Return)
        """
        try:
            # 计算多头和空头收益
            returns = data['returns'] if 'returns' in data.columns else data['close'].pct_change()
            
            # 分别计算多头和空头收益
            long_returns = returns.rolling(self.config.lookback_window).apply(
                lambda x: x[x > 0].mean() if len(x[x > 0]) > 0 else 0
            )
            
            short_returns = returns.rolling(self.config.lookback_window).apply(
                lambda x: x[x < 0].mean() if len(x[x < 0]) > 0 else 0
            )
            
            # 计算转化因子
            denominator = long_returns + short_returns
            denominator = denominator.replace(0, np.nan)
            
            conversion_factor = (short_returns - long_returns) / denominator
            
            return conversion_factor.fillna(0)
            
        except Exception as e:
            logger.error(f"计算空头收益转化因子失败: {e}")
            return pd.Series(0, index=data.index)
    
    def calculate_factor_effectiveness_decay(self, factor_ic: pd.Series) -> pd.Series:
        """
        计算因子有效性衰减
        
        核心公式：IC_current / IC_historical - 1
        """
        try:
            if len(factor_ic) < self.config.lookback_window * 2:
                return pd.Series(0, index=factor_ic.index)
            
            # 计算当前IC和历史IC
            current_ic = factor_ic.rolling(self.config.lookback_window).mean()
            historical_ic = factor_ic.rolling(self.config.lookback_window * 3).mean()
            
            # 计算衰减因子
            decay_factor = (current_ic / historical_ic - 1).fillna(0)
            
            return decay_factor
            
        except Exception as e:
            logger.error(f"计算因子有效性衰减失败: {e}")
            return pd.Series(0, index=factor_ic.index)
    
    def calculate_long_short_spread_factor(self, long_returns: pd.Series, 
                                         short_returns: pd.Series, 
                                         benchmark_returns: pd.Series) -> pd.Series:
        """
        计算多空收益差异因子
        
        核心公式：(Long_Portfolio_Return - Short_Portfolio_Return) / Benchmark_Return
        """
        try:
            # 计算多空收益差异
            spread = long_returns - short_returns
            
            # 标准化
            standardized_spread = spread / (benchmark_returns + 1e-8)
            
            return standardized_spread.fillna(0)
            
        except Exception as e:
            logger.error(f"计算多空收益差异因子失败: {e}")
            return pd.Series(0, index=long_returns.index)
    
    def generate_factor_timing_signal(self, factor_ic: pd.Series) -> pd.Series:
        """
        生成因子择时信号
        
        核心公式：MovingAverage(IC, 20) > MovingAverage(IC, 60)
        """
        try:
            # 计算快速和慢速移动平均
            fast_ma = factor_ic.rolling(self.config.timing_fast_window).mean()
            slow_ma = factor_ic.rolling(self.config.timing_slow_window).mean()
            
            # 生成信号
            timing_signal = pd.Series(0, index=factor_ic.index)
            timing_signal[fast_ma > slow_ma] = 1
            timing_signal[fast_ma < slow_ma] = -1
            
            # 平滑信号
            smoothed_signal = timing_signal.rolling(5).mean()
            
            return smoothed_signal.fillna(0)
            
        except Exception as e:
            logger.error(f"生成因子择时信号失败: {e}")
            return pd.Series(0, index=factor_ic.index)
    
    def calculate_risk_adjusted_return_factor(self, returns: pd.Series, 
                                            risk_free_rate: float = 0.03) -> pd.Series:
        """
        计算风险调整收益因子
        
        核心公式：(Portfolio_Return - Risk_Free_Rate) / Portfolio_Volatility
        """
        try:
            # 计算超额收益
            excess_returns = returns - risk_free_rate / 252
            
            # 计算滚动波动率
            rolling_volatility = returns.rolling(self.config.lookback_window).std()
            
            # 计算风险调整收益
            risk_adjusted_factor = excess_returns / (rolling_volatility + 1e-8)
            
            return risk_adjusted_factor.fillna(0)
            
        except Exception as e:
            logger.error(f"计算风险调整收益因子失败: {e}")
            return pd.Series(0, index=returns.index)
    
    def build_factor_composite_score(self, factor_data: Dict[str, pd.Series]) -> pd.Series:
        """构建因子综合评分"""
        try:
            # 因子权重
            factor_weights = {
                'conversion_factor': 0.35,
                'effectiveness_decay': 0.25,
                'timing_signal': 0.25,
                'risk_adjusted_return': 0.15
            }
            
            # 计算综合评分
            composite_score = pd.Series(0, index=factor_data['conversion_factor'].index)
            
            for factor_name, weight in factor_weights.items():
                if factor_name in factor_data:
                    # 标准化因子
                    factor_values = factor_data[factor_name]
                    standardized_factor = (factor_values - factor_values.mean()) / (factor_values.std() + 1e-8)
                    
                    # 加权累加
                    composite_score += weight * standardized_factor
            
            return composite_score.fillna(0)
            
        except Exception as e:
            logger.error(f"构建因子综合评分失败: {e}")
            return pd.Series(0, index=factor_data['conversion_factor'].index)
    
    def build_long_short_portfolio(self, factor_scores: pd.DataFrame, 
                                 universe: List[str]) -> Dict[str, pd.Series]:
        """构建多空组合"""
        try:
            portfolios = {}
            
            for date in factor_scores.index:
                if date not in factor_scores.index:
                    continue
                
                # 获取当日因子分数
                daily_scores = factor_scores.loc[date]
                
                # 过滤有效股票
                valid_stocks = daily_scores.dropna()
                
                if len(valid_stocks) < 20:
                    continue
                
                # 排序
                sorted_scores = valid_stocks.sort_values(ascending=False)
                
                # 构建多头组合
                long_size = max(1, int(len(sorted_scores) * self.config.long_ratio))
                long_stocks = sorted_scores.head(long_size)
                
                # 构建空头组合
                short_size = max(1, int(len(sorted_scores) * self.config.short_ratio))
                short_stocks = sorted_scores.tail(short_size)
                
                # 计算权重
                long_weights = pd.Series(1.0/len(long_stocks), index=long_stocks.index)
                short_weights = pd.Series(-1.0/len(short_stocks), index=short_stocks.index)
                
                # 限制单个股票权重
                long_weights = long_weights.clip(upper=self.config.max_position_weight)
                short_weights = short_weights.clip(lower=-self.config.max_position_weight)
                
                # 重新标准化
                long_weights = long_weights / long_weights.sum()
                short_weights = short_weights / abs(short_weights.sum())
                
                # 合并权重
                portfolio_weights = pd.concat([long_weights, short_weights])
                
                portfolios[date] = portfolio_weights
            
            return portfolios
            
        except Exception as e:
            logger.error(f"构建多空组合失败: {e}")
            return {}
    
    def calculate_portfolio_performance(self, portfolio_weights: Dict[str, pd.Series], 
                                     returns_data: pd.DataFrame) -> Dict[str, Any]:
        """计算组合业绩"""
        try:
            portfolio_returns = []
            portfolio_dates = []
            
            for date, weights in portfolio_weights.items():
                if date in returns_data.index:
                    # 计算组合收益
                    daily_return = 0
                    for stock, weight in weights.items():
                        if stock in returns_data.columns:
                            stock_return = returns_data.loc[date, stock]
                            if not pd.isna(stock_return):
                                daily_return += weight * stock_return
                    
                    portfolio_returns.append(daily_return)
                    portfolio_dates.append(date)
            
            # 转换为序列
            portfolio_returns = pd.Series(portfolio_returns, index=portfolio_dates)
            
            # 计算业绩指标
            performance_metrics = {
                'total_return': portfolio_returns.sum(),
                'annualized_return': portfolio_returns.mean() * 252,
                'volatility': portfolio_returns.std() * np.sqrt(252),
                'sharpe_ratio': (portfolio_returns.mean() * 252) / (portfolio_returns.std() * np.sqrt(252) + 1e-8),
                'max_drawdown': self._calculate_max_drawdown(portfolio_returns),
                'win_rate': (portfolio_returns > 0).mean(),
                'calmar_ratio': (portfolio_returns.mean() * 252) / (abs(self._calculate_max_drawdown(portfolio_returns)) + 1e-8)
            }
            
            return {
                'portfolio_returns': portfolio_returns,
                'performance_metrics': performance_metrics
            }
            
        except Exception as e:
            logger.error(f"计算组合业绩失败: {e}")
            return {}
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        try:
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            return drawdown.min()
        except:
            return 0.0
    
    def run_backtest(self, data: pd.DataFrame, start_date: str, end_date: str) -> Dict[str, Any]:
        """运行回测"""
        try:
            logger.info(f"开始回测，时间范围: {start_date} - {end_date}")
            
            # 过滤数据
            backtest_data = data.loc[start_date:end_date]
            
            if len(backtest_data) < self.config.lookback_window * 2:
                logger.error("数据长度不足")
                return {}
            
            # 计算收益率
            if 'returns' not in backtest_data.columns:
                backtest_data['returns'] = backtest_data['close'].pct_change()
            
            # 1. 计算各个因子
            conversion_factor = self.calculate_short_return_conversion_factor(backtest_data)
            
            # 模拟因子IC
            factor_ic = pd.Series(
                np.random.normal(0.05, 0.02, len(backtest_data)), 
                index=backtest_data.index
            )
            
            effectiveness_decay = self.calculate_factor_effectiveness_decay(factor_ic)
            timing_signal = self.generate_factor_timing_signal(factor_ic)
            risk_adjusted_return = self.calculate_risk_adjusted_return_factor(backtest_data['returns'])
            
            # 2. 构建因子数据
            factor_data = {
                'conversion_factor': conversion_factor,
                'effectiveness_decay': effectiveness_decay,
                'timing_signal': timing_signal,
                'risk_adjusted_return': risk_adjusted_return
            }
            
            # 3. 计算综合评分
            composite_score = self.build_factor_composite_score(factor_data)
            
            # 4. 构建组合
            factor_scores_df = pd.DataFrame({
                'stock_001': composite_score,
                'stock_002': composite_score * 0.8,
                'stock_003': composite_score * 1.2,
                'stock_004': composite_score * 0.9,
                'stock_005': composite_score * 1.1
            })
            
            portfolio_weights = self.build_long_short_portfolio(
                factor_scores_df, 
                list(factor_scores_df.columns)
            )
            
            # 5. 计算业绩
            returns_data = pd.DataFrame({
                'stock_001': backtest_data['returns'],
                'stock_002': backtest_data['returns'] * 0.9,
                'stock_003': backtest_data['returns'] * 1.1,
                'stock_004': backtest_data['returns'] * 0.95,
                'stock_005': backtest_data['returns'] * 1.05
            })
            
            performance_results = self.calculate_portfolio_performance(
                portfolio_weights, returns_data
            )
            
            # 6. 整理结果
            backtest_results = {
                'strategy_name': '空头收益转化策略',
                'backtest_period': f"{start_date} - {end_date}",
                'factor_data': factor_data,
                'composite_score': composite_score,
                'portfolio_weights': portfolio_weights,
                'performance_results': performance_results,
                'config': self.config.__dict__
            }
            
            logger.info("回测完成")
            return backtest_results
            
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {}
    
    def save_strategy_results(self, results: Dict[str, Any], output_path: str):
        """保存策略结果"""
        try:
            import json
            
            # 准备要保存的数据
            save_data = {
                'strategy_name': results.get('strategy_name', ''),
                'backtest_period': results.get('backtest_period', ''),
                'performance_metrics': results.get('performance_results', {}).get('performance_metrics', {}),
                'config': results.get('config', {})
            }
            
            # 保存到JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"策略结果已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存策略结果失败: {e}")
    
    def print_performance_summary(self, results: Dict[str, Any]):
        """打印业绩摘要"""
        if 'performance_results' not in results:
            print("❌ 无业绩结果")
            return
        
        metrics = results['performance_results'].get('performance_metrics', {})
        
        print("\n" + "="*50)
        print("📊 空头收益转化策略业绩摘要")
        print("="*50)
        print(f"策略名称: {results.get('strategy_name', 'N/A')}")
        print(f"回测期间: {results.get('backtest_period', 'N/A')}")
        print(f"总收益: {metrics.get('total_return', 0):.2%}")
        print(f"年化收益: {metrics.get('annualized_return', 0):.2%}")
        print(f"年化波动率: {metrics.get('volatility', 0):.2%}")
        print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
        print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
        print(f"胜率: {metrics.get('win_rate', 0):.2%}")
        print(f"卡玛比率: {metrics.get('calmar_ratio', 0):.2f}")
        print("="*50)


def demo_strategy():
    """策略演示"""
    logger.info("🚀 启动空头收益转化策略演示")
    
    # 创建策略实例
    strategy = ShortReturnConversionStrategy()
    
    # 生成模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    data = pd.DataFrame({
        'close': 100 + np.cumsum(np.random.normal(0, 1, len(dates))),
        'volume': np.random.exponential(1000000, len(dates))
    }, index=dates)
    
    # 运行回测
    results = strategy.run_backtest(data, '2023-01-01', '2023-12-31')
    
    # 打印结果
    strategy.print_performance_summary(results)
    
    # 保存结果
    output_path = os.path.join(project_root, "strategy_results", "short_return_conversion_demo.json")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    strategy.save_strategy_results(results, output_path)
    
    return results


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    demo_results = demo_strategy() 