# -*- coding: utf-8 -*-
"""
消费行业多因子投资策略
融合聚宽策略stg1.py的核心思路，适配本项目数据管理

基于消费行业（家电、食品饮料、零售、汽车）的多因子分析和投资策略
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import warnings
import logging
from scipy import stats
import matplotlib.pyplot as plt

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.append(project_root)

# 导入项目模块
try:
    from data_pipeline.adapter_manager import AdapterRegistry, DataSourceManager
    from adapters.stock_data_adapter import StockDataAdapter
    from adapters.industry_data_adapter import IndustryDataAdapter
    from Utils.TushareUtil import get_tushare_instance
    DATA_ADAPTERS_AVAILABLE = True
except ImportError:
    DATA_ADAPTERS_AVAILABLE = False
    warnings.warn("数据适配器模块不可用")

try:
    from factor_analyze.tools.factor_evaluator import FactorEvaluator
    from Utils.FactorUtil import FactorAnalyzer
    FACTOR_MODULES_AVAILABLE = True
except ImportError:
    FACTOR_MODULES_AVAILABLE = False
    warnings.warn("因子分析模块不可用")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConsumerIndustryFactorStrategy:
    """
    消费行业多因子投资策略
    
    核心功能：
    1. 消费行业股票筛选和分组
    2. 多因子数据获取和预处理
    3. 因子有效性分析（IC、IR、分组回测）
    4. 投资组合构建和优化
    5. 策略回测和性能评估
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化策略
        
        Args:
            config: 策略配置参数
        """
        # 默认配置
        self.default_config = {
            # 消费行业代码（申万一级）
            'consumer_industries': {
                '801110': '家用电器',  # 家用电器I
                '801120': '食品饮料',  # 食品饮料I
                '801200': '商业贸易',  # 一般零售II
                '801880': '汽车'       # 汽车整车II
            },
            
            # 因子配置
            'factor_list': [
                'total_asset_turnover_rate',    # 总资产周转率
                'roe_ttm',                      # 权益回报率TTM
                'account_receivable_turnover_rate', # 应收账款周转率
                'gross_income_ratio',           # 毛利率
                'inventory_turnover_rate',      # 存货周转率
                'operating_profit_growth_rate', # 营业利润增长率
                'operating_revenue_growth_rate', # 营业收入增长率
                'net_profit_growth_rate',       # 净利润增长率
                'pe_ratio',                     # 市盈率
                'roa_ttm',                      # 资产回报率TTM
                'market_cap',                   # 市值
                'turnover_rate',                # 换手率
                'volatility'                    # 波动率
            ],
            
            # 数据参数
            'start_date': '2020-01-01',
            'end_date': datetime.now().strftime('%Y-%m-%d'),
            'rebalance_frequency': 'monthly',  # 调仓频率
            'min_stock_count': 20,            # 最小股票数量
            
            # 因子筛选参数
            'min_ic': 0.02,                   # 最小IC阈值
            'min_ir': 0.5,                    # 最小IR阈值
            'min_hit_rate': 0.52,             # 最小命中率
            
            # 组合构建参数
            'top_n_factors': 5,               # 选择前N个因子
            'quantile_groups': 5,             # 分位数分组数
            'position_ratio': 0.9,            # 仓位比例
            
            # 风险控制
            'max_single_weight': 0.05,        # 单股最大权重
            'exclude_st_stocks': True,        # 排除ST股票
            
            # 其他参数
            'cache_enabled': True,            # 启用缓存
            'parallel_processing': True       # 并行处理
        }
        
        # 合并配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化数据适配器
        self._initialize_adapters()
        
        # 初始化因子分析器
        self._initialize_factor_analyzer()
        
        # 策略状态
        self.universe_stocks = []
        self.factor_data = pd.DataFrame()
        self.factor_scores = {}
        self.portfolio_weights = {}
        
        logger.info("消费行业多因子策略初始化完成")
    
    def _initialize_adapters(self):
        """初始化数据适配器"""
        global DATA_ADAPTERS_AVAILABLE
        if DATA_ADAPTERS_AVAILABLE:
            try:
                self.adapter_registry = AdapterRegistry()
                self.data_manager = DataSourceManager(self.adapter_registry)
                self.stock_adapter = self.adapter_registry.get_adapter('stock')
                self.industry_adapter = self.adapter_registry.get_adapter('industry')
                logger.info("数据适配器初始化成功")
            except Exception as e:
                logger.error(f"数据适配器初始化失败: {e}")
                DATA_ADAPTERS_AVAILABLE = False
    
    def _initialize_factor_analyzer(self):
        """初始化因子分析器"""
        global FACTOR_MODULES_AVAILABLE
        if FACTOR_MODULES_AVAILABLE:
            try:
                self.factor_evaluator = FactorEvaluator({
                    'min_ic': self.config['min_ic'],
                    'min_ir': self.config['min_ir'],
                    'significance_level': 0.05
                })
                logger.info("因子分析器初始化成功")
            except Exception as e:
                logger.error(f"因子分析器初始化失败: {e}")
                FACTOR_MODULES_AVAILABLE = False
    
    def get_consumer_industry_stocks(self, date: str = None) -> List[str]:
        """
        获取消费行业股票池
        
        Args:
            date: 基准日期
            
        Returns:
            股票代码列表
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        all_stocks = set()
        
        if DATA_ADAPTERS_AVAILABLE:
            try:
                for industry_code, industry_name in self.config['consumer_industries'].items():
                    # 获取行业成分股
                    industry_stocks = self.industry_adapter.get_industry_stocks(
                        industry_code=industry_code,
                        date=date
                    )
                    all_stocks.update(industry_stocks)
                    logger.info(f"获取{industry_name}股票{len(industry_stocks)}只")
                
                # 过滤ST股票
                if self.config['exclude_st_stocks']:
                    all_stocks = self._filter_st_stocks(list(all_stocks), date)
                
                self.universe_stocks = list(all_stocks)
                logger.info(f"消费行业股票池构建完成，共{len(self.universe_stocks)}只股票")
                
                return self.universe_stocks
                
            except Exception as e:
                logger.error(f"获取消费行业股票失败: {e}")
                return []
        else:
            # 模拟数据
            return self._get_simulated_consumer_stocks()
    
    def _filter_st_stocks(self, stocks: List[str], date: str) -> List[str]:
        """过滤ST股票"""
        try:
            # 这里应该调用实际的ST股票筛选逻辑
            # 暂时返回原始列表
            return stocks
        except Exception as e:
            logger.warning(f"ST股票过滤失败: {e}")
            return stocks
    
    def _get_simulated_consumer_stocks(self) -> List[str]:
        """模拟消费行业股票数据"""
        # 模拟一些消费行业股票代码
        simulated_stocks = [
            '000001.SZ', '000002.SZ', '000858.SZ', '000895.SZ', '000977.SZ',
            '002304.SZ', '002415.SZ', '002572.SZ', '600036.SH', '600519.SH',
            '600887.SH', '000568.SZ', '002027.SZ', '600298.SH', '600809.SH'
        ]
        return simulated_stocks[:self.config['min_stock_count']]
    
    def collect_factor_data(self, stocks: List[str], 
                          start_date: str, end_date: str) -> pd.DataFrame:
        """
        收集因子数据
        
        Args:
            stocks: 股票列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            因子数据DataFrame
        """
        if DATA_ADAPTERS_AVAILABLE:
            try:
                # 获取股票基础数据
                price_data = self.stock_adapter.get_stock_data(
                    stocks=stocks,
                    start_date=start_date,
                    end_date=end_date,
                    fields=['close', 'volume', 'market_cap', 'pe_ratio']
                )
                
                # 获取财务数据
                fundamental_data = self.stock_adapter.get_fundamental_data(
                    stocks=stocks,
                    start_date=start_date,
                    end_date=end_date,
                    fields=self.config['factor_list']
                )
                
                # 合并数据
                self.factor_data = self._merge_factor_data(price_data, fundamental_data)
                
                logger.info(f"因子数据收集完成: {self.factor_data.shape}")
                return self.factor_data
                
            except Exception as e:
                logger.error(f"因子数据收集失败: {e}")
                return pd.DataFrame()
        else:
            # 生成模拟数据
            return self._generate_simulated_factor_data(stocks, start_date, end_date)
    
    def _merge_factor_data(self, price_data: pd.DataFrame, 
                          fundamental_data: pd.DataFrame) -> pd.DataFrame:
        """合并因子数据"""
        try:
            # 简单的合并逻辑，实际应该更复杂
            merged_data = pd.concat([price_data, fundamental_data], axis=1)
            
            # 数据清洗和预处理
            merged_data = self._preprocess_factor_data(merged_data)
            
            return merged_data
            
        except Exception as e:
            logger.error(f"因子数据合并失败: {e}")
            return pd.DataFrame()
    
    def _preprocess_factor_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        因子数据预处理
        
        包括：去极值、标准化、缺失值处理
        """
        processed_data = data.copy()
        
        # MAD去极值
        processed_data = self._mad_outlier_treatment(processed_data)
        
        # 标准化
        processed_data = self._standardize_factors(processed_data)
        
        # 缺失值处理
        processed_data = processed_data.fillna(processed_data.mean())
        
        return processed_data
    
    def _mad_outlier_treatment(self, data: pd.DataFrame) -> pd.DataFrame:
        """MAD方法去极值"""
        for col in data.select_dtypes(include=[np.number]).columns:
            median = data[col].median()
            mad = (data[col] - median).abs().median()
            
            # 3倍MAD原则
            upper_bound = median + 3 * 1.4826 * mad
            lower_bound = median - 3 * 1.4826 * mad
            
            data[col] = np.clip(data[col], lower_bound, upper_bound)
        
        return data
    
    def _standardize_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """因子标准化"""
        for col in data.select_dtypes(include=[np.number]).columns:
            if col in self.config['factor_list']:
                data[col] = (data[col] - data[col].mean()) / data[col].std()
        
        return data
    
    def _generate_simulated_factor_data(self, stocks: List[str], 
                                      start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟因子数据"""
        np.random.seed(42)
        
        date_range = pd.date_range(start=start_date, end=end_date, freq='M')
        n_periods = len(date_range)
        n_stocks = len(stocks)
        n_factors = len(self.config['factor_list'])
        
        # 生成随机因子数据
        data_list = []
        for date in date_range:
            for stock in stocks:
                row = {'date': date, 'stock_code': stock}
                
                # 生成因子值
                for factor in self.config['factor_list']:
                    if 'rate' in factor or 'ratio' in factor:
                        # 比率类因子，正态分布
                        row[factor] = np.random.normal(0.1, 0.05)
                    elif 'growth' in factor:
                        # 增长率因子，可能为负
                        row[factor] = np.random.normal(0.05, 0.15)
                    else:
                        # 其他因子
                        row[factor] = np.random.normal(0, 1)
                
                # 生成收益率（目标变量）
                factor_signal = sum(row[f] for f in self.config['factor_list'][:5])
                row['forward_return'] = factor_signal * 0.001 + np.random.normal(0, 0.02)
                
                data_list.append(row)
        
        return pd.DataFrame(data_list)
    
    def analyze_factor_effectiveness(self) -> Dict[str, Any]:
        """
        分析因子有效性
        
        Returns:
            因子分析结果
        """
        if self.factor_data.empty:
            logger.warning("因子数据为空，无法进行分析")
            return {}
        
        factor_results = {}
        
        for factor_name in self.config['factor_list']:
            if factor_name not in self.factor_data.columns:
                continue
            
            try:
                if FACTOR_MODULES_AVAILABLE:
                    # 使用项目内置因子评估器
                    factor_series = self.factor_data[factor_name]
                    return_series = self.factor_data.get('forward_return', 
                                   self.factor_data.get('next_return', pd.Series()))
                    
                    if not return_series.empty:
                        evaluation = self.factor_evaluator.evaluate_factor(
                            factor_series, return_series, factor_name
                        )
                        factor_results[factor_name] = evaluation
                else:
                    # 简化的因子分析
                    factor_results[factor_name] = self._simple_factor_analysis(
                        self.factor_data, factor_name
                    )
                
                logger.info(f"因子{factor_name}分析完成")
                
            except Exception as e:
                logger.error(f"因子{factor_name}分析失败: {e}")
                continue
        
        self.factor_scores = factor_results
        return factor_results
    
    def _simple_factor_analysis(self, data: pd.DataFrame, 
                               factor_name: str) -> Dict[str, float]:
        """简化的因子分析"""
        try:
            factor_values = data[factor_name].dropna()
            
            if 'forward_return' in data.columns:
                returns = data['forward_return'].dropna()
            else:
                # 如果没有未来收益，计算简单收益率
                returns = data.groupby('stock_code')['close'].pct_change().shift(-1)
                returns = returns.dropna()
            
            # 对齐数据
            common_index = factor_values.index.intersection(returns.index)
            if len(common_index) < 10:
                return {'ic': 0, 'rank_ic': 0, 'sample_size': len(common_index)}
            
            aligned_factor = factor_values[common_index]
            aligned_returns = returns[common_index]
            
            # 计算IC
            ic = aligned_factor.corr(aligned_returns)
            rank_ic = stats.spearmanr(aligned_factor, aligned_returns)[0]
            
            return {
                'ic': ic if not np.isnan(ic) else 0,
                'rank_ic': rank_ic if not np.isnan(rank_ic) else 0,
                'sample_size': len(common_index)
            }
            
        except Exception as e:
            logger.error(f"简化因子分析失败: {e}")
            return {'ic': 0, 'rank_ic': 0, 'sample_size': 0}
    
    def select_top_factors(self, factor_results: Dict[str, Any]) -> List[str]:
        """
        选择最优因子
        
        Args:
            factor_results: 因子分析结果
            
        Returns:
            最优因子列表
        """
        if not factor_results:
            return []
        
        # 计算因子综合得分
        factor_scores = []
        
        for factor_name, results in factor_results.items():
            if FACTOR_MODULES_AVAILABLE and 'overall_score' in results:
                score = results['overall_score']
            else:
                # 简化评分
                ic = abs(results.get('ic', 0))
                rank_ic = abs(results.get('rank_ic', 0))
                sample_size = results.get('sample_size', 0)
                
                # 综合评分
                score = (ic * 0.5 + rank_ic * 0.5) * min(sample_size / 100, 1)
            
            factor_scores.append((factor_name, score))
        
        # 按得分排序
        factor_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 选择前N个因子
        top_factors = [name for name, score in factor_scores[:self.config['top_n_factors']]]
        
        logger.info(f"选择最优因子: {top_factors}")
        return top_factors
    
    def build_portfolio(self, top_factors: List[str], 
                       current_date: str = None) -> Dict[str, float]:
        """
        构建投资组合
        
        Args:
            top_factors: 最优因子列表
            current_date: 当前日期
            
        Returns:
            投资组合权重字典
        """
        if not top_factors or self.factor_data.empty:
            return {}
        
        if current_date is None:
            current_date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            # 获取最新因子数据
            latest_data = self._get_latest_factor_data(current_date)
            
            if latest_data.empty:
                return {}
            
            # 计算复合因子得分
            composite_score = self._calculate_composite_score(latest_data, top_factors)
            
            # 按得分排序选股
            sorted_stocks = composite_score.sort_values(ascending=False)
            
            # 选择前N只股票
            n_stocks = min(len(sorted_stocks), 20)  # 最多选择20只股票
            selected_stocks = sorted_stocks.head(n_stocks)
            
            # 等权重配置
            equal_weight = 1.0 / len(selected_stocks)
            portfolio_weights = {stock: equal_weight for stock in selected_stocks.index}
            
            self.portfolio_weights = portfolio_weights
            
            logger.info(f"投资组合构建完成，选择{len(portfolio_weights)}只股票")
            return portfolio_weights
            
        except Exception as e:
            logger.error(f"投资组合构建失败: {e}")
            return {}
    
    def _get_latest_factor_data(self, date: str) -> pd.DataFrame:
        """获取最新因子数据"""
        try:
            # 从完整因子数据中筛选最新数据
            if 'date' in self.factor_data.columns:
                latest_data = self.factor_data[self.factor_data['date'] == date]
                if latest_data.empty:
                    # 如果没有当日数据，使用最近的数据
                    latest_date = self.factor_data['date'].max()
                    latest_data = self.factor_data[self.factor_data['date'] == latest_date]
            else:
                # 如果没有日期列，使用全部数据
                latest_data = self.factor_data
            
            return latest_data
            
        except Exception as e:
            logger.error(f"获取最新因子数据失败: {e}")
            return pd.DataFrame()
    
    def _calculate_composite_score(self, data: pd.DataFrame, 
                                 factors: List[str]) -> pd.Series:
        """计算复合因子得分"""
        try:
            # 等权重合成
            composite_scores = pd.Series(index=data.index, dtype=float)
            
            for stock_idx in data.index:
                score = 0
                valid_factors = 0
                
                for factor in factors:
                    if factor in data.columns:
                        factor_value = data.loc[stock_idx, factor]
                        if not pd.isna(factor_value):
                            score += factor_value
                            valid_factors += 1
                
                if valid_factors > 0:
                    composite_scores[stock_idx] = score / valid_factors
                else:
                    composite_scores[stock_idx] = 0
            
            # 使用stock_code作为index
            if 'stock_code' in data.columns:
                composite_scores.index = data['stock_code']
            
            return composite_scores.dropna()
            
        except Exception as e:
            logger.error(f"复合因子得分计算失败: {e}")
            return pd.Series()
    
    def run_backtest(self, start_date: str = None, 
                    end_date: str = None) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            start_date: 回测开始日期
            end_date: 回测结束日期
            
        Returns:
            回测结果
        """
        if start_date is None:
            start_date = self.config['start_date']
        if end_date is None:
            end_date = self.config['end_date']
        
        try:
            logger.info(f"开始运行回测: {start_date} 到 {end_date}")
            
            # 1. 获取股票池
            stocks = self.get_consumer_industry_stocks()
            if not stocks:
                logger.error("股票池为空，无法运行回测")
                return {}
            
            # 2. 收集因子数据
            factor_data = self.collect_factor_data(stocks, start_date, end_date)
            if factor_data.empty:
                logger.error("因子数据为空，无法运行回测")
                return {}
            
            # 3. 分析因子有效性
            factor_results = self.analyze_factor_effectiveness()
            if not factor_results:
                logger.error("因子分析失败，无法运行回测")
                return {}
            
            # 4. 选择最优因子
            top_factors = self.select_top_factors(factor_results)
            if not top_factors:
                logger.error("未找到有效因子，无法运行回测")
                return {}
            
            # 5. 构建投资组合
            portfolio = self.build_portfolio(top_factors)
            if not portfolio:
                logger.error("投资组合构建失败")
                return {}
            
            # 6. 计算回测结果
            backtest_results = {
                'strategy_name': '消费行业多因子策略',
                'backtest_period': f"{start_date} 到 {end_date}",
                'universe_size': len(stocks),
                'factor_analysis': factor_results,
                'selected_factors': top_factors,
                'portfolio_weights': portfolio,
                'portfolio_size': len(portfolio),
                'performance_metrics': self._calculate_performance_metrics(factor_data, portfolio)
            }
            
            logger.info("回测完成")
            return backtest_results
            
        except Exception as e:
            logger.error(f"回测运行失败: {e}")
            return {}
    
    def _calculate_performance_metrics(self, data: pd.DataFrame, 
                                     portfolio: Dict[str, float]) -> Dict[str, float]:
        """计算性能指标"""
        try:
            if 'forward_return' not in data.columns:
                return {'annual_return': 0, 'volatility': 0, 'sharpe_ratio': 0}
            
            # 计算组合收益
            portfolio_returns = []
            
            for date in data['date'].unique() if 'date' in data.columns else [data.index[0]]:
                date_data = data[data['date'] == date] if 'date' in data.columns else data
                
                portfolio_return = 0
                total_weight = 0
                
                for stock, weight in portfolio.items():
                    stock_data = date_data[date_data['stock_code'] == stock] if 'stock_code' in date_data.columns else date_data
                    
                    if not stock_data.empty and 'forward_return' in stock_data.columns:
                        stock_return = stock_data['forward_return'].iloc[0]
                        if not pd.isna(stock_return):
                            portfolio_return += weight * stock_return
                            total_weight += weight
                
                if total_weight > 0:
                    portfolio_returns.append(portfolio_return / total_weight)
            
            if not portfolio_returns:
                return {'annual_return': 0, 'volatility': 0, 'sharpe_ratio': 0}
            
            # 计算年化收益率
            total_return = np.prod([1 + r for r in portfolio_returns]) - 1
            periods = len(portfolio_returns)
            annual_return = (1 + total_return) ** (12 / periods) - 1 if periods > 0 else 0
            
            # 计算波动率
            volatility = np.std(portfolio_returns) * np.sqrt(12)
            
            # 计算夏普比率
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            return {
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'total_return': total_return,
                'periods': periods
            }
            
        except Exception as e:
            logger.error(f"性能指标计算失败: {e}")
            return {'annual_return': 0, 'volatility': 0, 'sharpe_ratio': 0}
    
    def print_results(self, results: Dict[str, Any]):
        """打印策略结果"""
        if not results:
            print("❌ 策略运行失败，无结果可显示")
            return
        
        print("=" * 60)
        print(f"🎯 {results['strategy_name']} - 回测结果")
        print("=" * 60)
        
        print(f"📅 回测期间: {results['backtest_period']}")
        print(f"🏢 股票池规模: {results['universe_size']}只")
        print(f"📊 投资组合规模: {results['portfolio_size']}只")
        
        # 因子分析结果
        print("\n📈 因子分析结果:")
        print("-" * 40)
        if results['factor_analysis']:
            for factor_name, analysis in results['factor_analysis'].items():
                if FACTOR_MODULES_AVAILABLE and 'overall_score' in analysis:
                    score = analysis['overall_score']
                    ic = analysis.get('ic_analysis', {}).get('ic_spearman', 0)
                    print(f"  • {factor_name}: 综合得分={score:.4f}, IC={ic:.4f}")
                else:
                    ic = analysis.get('ic', 0)
                    rank_ic = analysis.get('rank_ic', 0)
                    print(f"  • {factor_name}: IC={ic:.4f}, Rank_IC={rank_ic:.4f}")
        
        # 最优因子
        print(f"\n🏆 最优因子: {', '.join(results['selected_factors'])}")
        
        # 投资组合
        print("\n💼 投资组合权重 (前10只):")
        print("-" * 40)
        sorted_portfolio = sorted(results['portfolio_weights'].items(), 
                                key=lambda x: x[1], reverse=True)
        for stock, weight in sorted_portfolio[:10]:
            print(f"  • {stock}: {weight:.4f}")
        
        # 性能指标
        metrics = results['performance_metrics']
        print(f"\n📊 策略表现:")
        print("-" * 40)
        print(f"  • 年化收益率: {metrics['annual_return']:.2%}")
        print(f"  • 年化波动率: {metrics['volatility']:.2%}")
        print(f"  • 夏普比率: {metrics['sharpe_ratio']:.4f}")
        
        print("=" * 60)


def demo_consumer_industry_strategy():
    """演示消费行业因子策略"""
    print("🚀 启动消费行业多因子投资策略演示")
    
    # 创建策略实例
    config = {
        'start_date': '2023-01-01',
        'end_date': '2024-01-01',
        'min_stock_count': 15,
        'top_n_factors': 3
    }
    
    strategy = ConsumerIndustryFactorStrategy(config)
    
    # 运行回测
    results = strategy.run_backtest()
    
    # 显示结果
    strategy.print_results(results)
    
    return strategy, results


if __name__ == "__main__":
    demo_consumer_industry_strategy()
