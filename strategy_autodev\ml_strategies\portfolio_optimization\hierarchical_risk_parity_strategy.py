# -*- coding: utf-8 -*-
"""
分层风险平价策略 (Hierarchical Risk Parity Strategy)

基于Chapter12中的HRP算法实现，用于构建风险平价投资组合。
HRP通过层次聚类来识别相似资产，并基于风险贡献进行权重分配。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from scipy.cluster.hierarchy import linkage
from scipy.spatial.distance import pdist, squareform
import logging

# 尝试导入基础策略类，如果失败则使用简化实现
try:
    from ..base_ml_strategy import BaseMLStrategy, MLSignal
except ImportError:
    try:
        # 尝试从核心模块导入
        from strategy_autodev.core.base_strategy import BaseStrategy as BaseMLStrategy
        from dataclasses import dataclass, field

        @dataclass
        class MLSignal:
            """ML策略信号"""
            stock_code: str
            date: pd.Timestamp
            signal: str  # 'BUY', 'SELL', 'HOLD'
            score: float
            confidence: float
            metadata: Dict[str, Any] = field(default_factory=dict)
    except ImportError:
        logging.warning("基础策略类不可用，使用简化实现")
    class BaseMLStrategy:
        def __init__(self, config=None):
            self.config = config or {}
    
    from dataclasses import dataclass, field
    
    @dataclass
    class MLSignal:
        """ML策略信号"""
        stock_code: str
        date: pd.Timestamp
        signal: str  # 'BUY', 'SELL', 'HOLD'
        score: float
        confidence: float
        metadata: Dict[str, Any] = field(default_factory=dict)

# 尝试导入策略分类，如果失败则定义本地版本
try:
    from strategy_autodev.core.interfaces import StrategyCategory
except ImportError:
    try:
        # 尝试其他可能的导入路径
        from strategy_autodev.core.strategy_categories import StrategyCategory
    except ImportError:
        logging.warning("策略分类接口不可用，使用本地定义")
    class StrategyCategory:
        PORTFOLIO_OPTIMIZATION = "portfolio_optimization"
        MACHINE_LEARNING = "machine_learning"

logger = logging.getLogger(__name__)


class HierarchicalRiskParityStrategy(BaseMLStrategy):
    """
    分层风险平价策略
    
    该策略使用层次聚类对资产进行分组，然后基于风险贡献
    进行权重分配，实现风险平价的投资组合构建。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化HRP策略
        
        Args:
            config: 策略配置参数
        """
        super().__init__(config)
        
        # 策略参数
        self.lookback_period = config.get('lookback_period', 252)  # 回望期
        self.rebalance_freq = config.get('rebalance_freq', 'M')    # 调仓频率
        self.linkage_method = config.get('linkage_method', 'single')  # 聚类方法
        self.min_weight = config.get('min_weight', 0.01)           # 最小权重
        self.max_weight = config.get('max_weight', 0.3)            # 最大权重
        
        # 内部状态
        self.current_weights = None
        self.correlation_matrix = None
        self.distance_matrix = None
        self.linkage_matrix = None
        
        logger.info(f"HRP策略初始化完成，回望期: {self.lookback_period}天")
    
    def get_distance_matrix(self, corr: pd.DataFrame) -> np.ndarray:
        """
        从相关性矩阵计算距离矩阵
        
        Args:
            corr: 相关性矩阵
            
        Returns:
            距离矩阵，0 <= d[i,j] <= 1
        """
        return np.sqrt((1 - corr) / 2)
    
    def quasi_diagonalize(self, link: np.ndarray) -> List[int]:
        """
        根据聚类结果对资产进行排序
        
        Args:
            link: 层次聚类的链接矩阵
            
        Returns:
            排序后的资产索引列表
        """
        link = link.astype(int)
        sort_idx = pd.Series([link[-1, 0], link[-1, 1]])
        num_items = link[-1, 3]  # 原始项目数量
        
        while sort_idx.max() >= num_items:
            sort_idx.index = list(range(0, sort_idx.shape[0] * 2, 2))
            df0 = sort_idx[sort_idx >= num_items]
            i = df0.index
            j = df0.values - num_items
            sort_idx[i] = link[j, 0]
            df0 = pd.Series(link[j, 1], index=i + 1)
            sort_idx = pd.concat([sort_idx, df0])
            sort_idx = sort_idx.sort_index()
            sort_idx.index = list(range(sort_idx.shape[0]))
        
        return sort_idx.tolist()
    
    def get_inverse_var_portfolio(self, cov: pd.DataFrame) -> np.ndarray:
        """
        计算逆方差投资组合权重
        
        Args:
            cov: 协方差矩阵
            
        Returns:
            逆方差权重
        """
        ivp = 1 / np.diag(cov)
        return ivp / ivp.sum()
    
    def get_cluster_variance(self, cov: pd.DataFrame, cluster_items: List) -> float:
        """
        计算聚类的方差
        
        Args:
            cov: 协方差矩阵
            cluster_items: 聚类中的资产列表
            
        Returns:
            聚类方差
        """
        cov_slice = cov.loc[cluster_items, cluster_items]
        weights = self.get_inverse_var_portfolio(cov_slice)
        return (weights @ cov_slice @ weights).item()
    
    def get_hrp_allocation(self, cov: pd.DataFrame, sorted_tickers: List[str]) -> pd.Series:
        """
        计算HRP权重分配
        
        Args:
            cov: 协方差矩阵
            sorted_tickers: 排序后的资产列表
            
        Returns:
            HRP权重
        """
        weights = pd.Series(1.0, index=sorted_tickers)
        clusters = [sorted_tickers]  # 初始化为一个包含所有资产的聚类
        
        while len(clusters) > 0:
            # 二分搜索
            clusters = [
                c[start:stop] for c in clusters
                for start, stop in ((0, int(len(c) / 2)), (int(len(c) / 2), len(c)))
                if len(c) > 1
            ]
            
            # 成对处理聚类
            for i in range(0, len(clusters), 2):
                if i + 1 < len(clusters):
                    cluster0 = clusters[i]
                    cluster1 = clusters[i + 1]
                    
                    cluster0_var = self.get_cluster_variance(cov, cluster0)
                    cluster1_var = self.get_cluster_variance(cov, cluster1)
                    
                    # 计算权重缩放因子
                    weight_scaler = 1 - cluster0_var / (cluster0_var + cluster1_var)
                    
                    weights[cluster0] *= weight_scaler
                    weights[cluster1] *= (1 - weight_scaler)
        
        return weights
    
    def apply_weight_constraints(self, weights: pd.Series) -> pd.Series:
        """
        应用权重约束
        
        Args:
            weights: 原始权重
            
        Returns:
            约束后的权重
        """
        # 应用最小和最大权重约束
        weights = weights.clip(lower=self.min_weight, upper=self.max_weight)
        
        # 重新标准化
        weights = weights / weights.sum()
        
        return weights
    
    def calculate_portfolio_weights(self, returns: pd.DataFrame) -> pd.Series:
        """
        计算投资组合权重
        
        Args:
            returns: 资产收益率数据
            
        Returns:
            投资组合权重
        """
        try:
            # 计算协方差和相关性矩阵
            cov_matrix = returns.cov()
            corr_matrix = returns.corr()
            
            # 存储用于分析
            self.correlation_matrix = corr_matrix
            
            # 计算距离矩阵
            self.distance_matrix = self.get_distance_matrix(corr_matrix)
            
            # 层次聚类
            condensed_distance = pdist(self.distance_matrix)
            self.linkage_matrix = linkage(condensed_distance, method=self.linkage_method)
            
            # 获取排序后的资产
            sorted_indices = self.quasi_diagonalize(self.linkage_matrix)
            sorted_tickers = corr_matrix.index[sorted_indices].tolist()
            
            # 计算HRP权重
            hrp_weights = self.get_hrp_allocation(cov_matrix, sorted_tickers)
            
            # 应用权重约束
            final_weights = self.apply_weight_constraints(hrp_weights)
            
            logger.info(f"HRP权重计算完成，资产数量: {len(final_weights)}")
            
            return final_weights
            
        except Exception as e:
            logger.error(f"HRP权重计算失败: {e}")
            # 返回等权重作为备选
            n_assets = len(returns.columns)
            return pd.Series(1.0 / n_assets, index=returns.columns)
    
    def generate_signals(self, data: pd.DataFrame) -> List[MLSignal]:
        """
        生成交易信号
        
        Args:
            data: 市场数据
            
        Returns:
            交易信号列表
        """
        try:
            # 计算收益率
            returns = data.pct_change().dropna()
            
            # 确保有足够的历史数据
            if len(returns) < self.lookback_period:
                logger.warning(f"历史数据不足，需要{self.lookback_period}天，实际{len(returns)}天")
                return []
            
            # 使用滚动窗口计算权重
            signals = []
            
            for i in range(self.lookback_period, len(returns)):
                # 获取历史数据窗口
                window_returns = returns.iloc[i-self.lookback_period:i]
                
                # 计算权重
                weights = self.calculate_portfolio_weights(window_returns)
                
                # 为每个资产创建信号
                current_date = returns.index[i]
                for asset, weight in weights.items():
                    if pd.notna(weight) and weight > 0:
                        signal = MLSignal(
                            date=current_date,
                            stock_code=asset,
                            signal='BUY' if weight > 0.1 else 'HOLD',
                            score=float(weight),
                            confidence=min(1.0, weight * 2),  # 权重越高置信度越高
                            metadata={'hrp_weight': float(weight)}
                        )
                        signals.append(signal)
            
            # 存储当前权重
            if signals:
                latest_signals = [s for s in signals if s.date == max(s.date for s in signals)]
                self.current_weights = pd.Series(
                    {s.stock_code: s.score for s in latest_signals}
                )
            
            logger.info(f"生成HRP信号完成，信号数量: {len(signals)}")
            
            return signals
            
        except Exception as e:
            logger.error(f"生成HRP信号失败: {e}")
            return []
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': 'HierarchicalRiskParityStrategy',
            'category': StrategyCategory.ML,
            'description': '分层风险平价策略，基于层次聚类的风险平价投资组合构建',
            'parameters': {
                'lookback_period': self.lookback_period,
                'rebalance_freq': self.rebalance_freq,
                'linkage_method': self.linkage_method,
                'min_weight': self.min_weight,
                'max_weight': self.max_weight
            },
            'features': [
                '层次聚类资产分组',
                '风险平价权重分配',
                '权重约束管理',
                '滚动窗口更新'
            ],
            'risk_level': 'Medium',
            'suitable_markets': ['股票', 'ETF', '债券', '商品'],
            'data_requirements': {
                'min_history': f'{self.lookback_period}天',
                'frequency': '日频',
                'fields': ['收盘价']
            }
        }
    
    def get_current_portfolio(self) -> Optional[pd.Series]:
        """
        获取当前投资组合权重
        
        Returns:
            当前权重，如果没有则返回None
        """
        return self.current_weights
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """
        获取风险指标
        
        Returns:
            风险指标字典
        """
        if self.current_weights is None:
            return {}
        
        metrics = {
            'portfolio_concentration': self.calculate_concentration(),
            'effective_assets': self.calculate_effective_assets(),
            'max_weight': self.current_weights.max(),
            'min_weight': self.current_weights.min(),
            'weight_std': self.current_weights.std()
        }
        
        return metrics
    
    def calculate_concentration(self) -> float:
        """
        计算投资组合集中度 (HHI)
        
        Returns:
            集中度指标
        """
        if self.current_weights is None:
            return 0.0
        
        return (self.current_weights ** 2).sum()
    
    def calculate_effective_assets(self) -> float:
        """
        计算有效资产数量
        
        Returns:
            有效资产数量
        """
        if self.current_weights is None:
            return 0.0
        
        return 1.0 / self.calculate_concentration()
    
    # -----------------------------------------------------------------------
    # 实现BaseMLStrategy的抽象方法
    # -----------------------------------------------------------------------
    def _build_model(self) -> Any:
        """
        构建模型 - HRP不需要传统的机器学习模型
        
        Returns:
            模型实例（这里返回策略本身）
        """
        logger.info("HRP策略不需要构建传统ML模型")
        return self
    
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备特征数据 - 计算收益率
        
        Args:
            data: 原始价格数据
            
        Returns:
            收益率数据
        """
        try:
            # 计算收益率
            returns = data.pct_change().dropna()
            logger.info(f"准备特征数据完成，收益率数据形状: {returns.shape}")
            return returns
        except Exception as e:
            logger.error(f"准备特征数据失败: {e}")
            return pd.DataFrame()
    
    def _train_model(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型 - HRP不需要传统的训练过程
        
        Args:
            X: 特征数据（这里是收益率）
            y: 目标变量（HRP中不使用）
            
        Returns:
            训练结果
        """
        logger.info("HRP策略不需要传统的模型训练")
        return {
            'status': 'success',
            'message': 'HRP策略基于协方差矩阵，无需训练',
            'model_type': 'hierarchical_risk_parity'
        }
    
    def _predict_model(self, X: np.ndarray) -> np.ndarray:
        """
        模型预测 - 计算HRP权重
        
        Args:
            X: 特征数据（收益率矩阵）
            
        Returns:
            预测的权重
        """
        try:
            # 将numpy数组转换为DataFrame
            if isinstance(X, np.ndarray):
                # 假设列名为资产编号
                columns = [f'asset_{i}' for i in range(X.shape[1])]
                returns_df = pd.DataFrame(X, columns=columns)
            else:
                returns_df = X
            
            # 计算HRP权重
            weights = self.calculate_portfolio_weights(returns_df)
            
            # 转换为numpy数组
            if isinstance(weights, pd.Series):
                return weights.values
            else:
                return np.array(weights)
                
        except Exception as e:
            logger.error(f"HRP权重预测失败: {e}")
            # 返回等权重
            n_assets = X.shape[1] if hasattr(X, 'shape') else len(X.columns)
            return np.ones(n_assets) / n_assets