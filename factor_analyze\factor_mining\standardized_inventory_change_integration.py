# -*- coding: utf-8 -*-\n#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准化存货变动率因子与factor_mining模块集成
Standardized Inventory Change Factor Integration with factor_mining

将标准化存货变动率因子集成到factor_mining模块中，
使其能够被factor_mining的标准接口调用和管理。
"""
# 确保文件以 UTF-8 格式保存

import sys
import os
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'factor_analyze'))
sys.path.append(os.path.join(project_root, 'factor_mining'))

# 导入标准化存货变动率因子
try:
    from factor_analyze.fundamental_factors.standardized_inventory_change_factor import (
        StandardizedInventoryChangeFactor,
        StandardizedInventoryChangeFactorForIndex,
        create_standardized_inventory_change_factor
    )
    FACTOR_AVAILABLE = True
except ImportError as e:
    logging.error(f"无法导入标准化存货变动率因子: {e}")
    FACTOR_AVAILABLE = False

# 导入factor_mining集成模块
try:
    from factor_analyze.factor_mining.factor_analyze_integration import FactorAnalyzeRegistry
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    logging.warning(f"无法导入factor_mining集成模块: {e}")
    INTEGRATION_AVAILABLE = False


def register_standardized_inventory_change_factor():
    """
    注册标准化存货变动率因子到factor_mining模块
    
    Returns:
        bool: 注册是否成功
    """
    if not FACTOR_AVAILABLE:
        logging.error("标准化存货变动率因子不可用，无法注册")
        return False
        
    if not INTEGRATION_AVAILABLE:
        logging.error("factor_mining集成模块不可用，无法注册")
        return False
    
    try:
        # 创建注册表实例
        registry = FactorAnalyzeRegistry()
        
        # 注册股票因子
        registry.register_stock_factor(
            'standardized_inventory_change',
            StandardizedInventoryChangeFactor,
            {
                'name': '标准化存货变动率因子(股票)',
                'category': 'fundamental',
                'description': '衡量公司存货变动相对于总资产的标准化程度，反映存货管理效率',
                'data_requirements': ['balance_sheet', 'inventories', 'total_assets'],
                'calculation_period': 'quarterly',
                'formula': '(存货_t - 存货_t-1) / ((总资产_t + 总资产_t-1) / 2)',
                'applicable_industries': ['制造业', '零售业', '批发业'],
                'economic_meaning': {
                    'positive': '存货相对增加，可能反映业务扩张或管理效率下降',
                    'negative': '存货相对减少，可能反映管理效率提升或销售强劲'
                },
                'reference': 'Thomas, Jacob K., and Huai Zhang (2002)',
                'data_lag': '1-2个月（基于财务报告发布时间）',
                'update_frequency': 'quarterly'
            }
        )
        
        # 注册指数因子
        registry.register_index_factor(
            'standardized_inventory_change',
            StandardizedInventoryChangeFactorForIndex,
            {
                'name': '标准化存货变动率因子(指数)',
                'category': 'fundamental',
                'description': '基于成分股加权计算的指数标准化存货变动率',
                'data_requirements': ['balance_sheet', 'inventories', 'total_assets', 'index_weights'],
                'calculation_period': 'quarterly',
                'formula': 'Σ(权重i × 成分股标准化存货变动率i)',
                'weighting_method': '市值权重或等权重',
                'applicable_indices': ['制造业指数', '消费指数', '工业指数'],
                'economic_meaning': {
                    'positive': '指数成分股整体存货相对增加',
                    'negative': '指数成分股整体存货相对减少'
                },
                'reference': 'Thomas, Jacob K., and Huai Zhang (2002)',
                'data_lag': '1-2个月（基于财务报告发布时间）',
                'update_frequency': 'quarterly'
            }
        )
        
        logging.info("标准化存货变动率因子已成功注册到factor_mining模块")
        return True
        
    except Exception as e:
        logging.error(f"注册标准化存货变动率因子失败: {e}")
        return False


def calculate_standardized_inventory_change_factor(
    symbols: List[str],
    start_date: str,
    end_date: str,
    target_type: str = 'stock',
    **kwargs
) -> Dict[str, Any]:
    """
    通过factor_mining接口计算标准化存货变动率因子
    
    Args:
        symbols: 股票代码或指数代码列表
        start_date: 开始日期
        end_date: 结束日期
        target_type: 目标类型（'stock' 或 'index'）
        **kwargs: 其他参数
        
    Returns:
        Dict[str, Any]: 计算结果
    """
    if not FACTOR_AVAILABLE:
        raise ImportError("标准化存货变动率因子不可用")
    
    try:
        # 创建因子实例
        factor = create_standardized_inventory_change_factor(target_type)
        
        if target_type == 'stock':
            # 批量计算股票因子
            result = factor.calculate_batch_factors(
                symbols=symbols,
                start_date=start_date,
                end_date=end_date
            )
        else:
            # 计算指数因子（需要成分股和权重信息）
            constituent_stocks = kwargs.get('constituent_stocks', symbols)
            weights = kwargs.get('weights', {stock: 1.0/len(constituent_stocks) for stock in constituent_stocks})
            
            results = []
            for symbol in symbols:
                index_result = factor.calculate_index_factor(
                    index_code=symbol,
                    constituent_stocks=constituent_stocks,
                    weights=weights,
                    start_date=start_date,
                    end_date=end_date
                )
                results.append(index_result)
            
            # 合并结果
            import pandas as pd
            result = pd.concat(results, ignore_index=True) if results else pd.DataFrame()
        
        return {
            'factor_name': 'standardized_inventory_change',
            'target_type': target_type,
            'symbols': symbols,
            'start_date': start_date,
            'end_date': end_date,
            'data': result,
            'metadata': {
                'calculation_time': datetime.now().isoformat(),
                'data_points': len(result) if not result.empty else 0,
                'factor_description': factor.get_factor_description()
            }
        }
        
    except Exception as e:
        logging.error(f"计算标准化存货变动率因子失败: {e}")
        raise


def get_factor_info() -> Dict[str, Any]:
    """
    获取标准化存货变动率因子的详细信息
    
    Returns:
        Dict[str, Any]: 因子信息
    """
    return {
        'factor_name': 'standardized_inventory_change',
        'factor_class': {
            'stock': 'StandardizedInventoryChangeFactor',
            'index': 'StandardizedInventoryChangeFactorForIndex'
        },
        'category': 'fundamental',
        'subcategory': 'inventory_management',
        'description': '标准化存货变动率因子，衡量公司存货变动相对于总资产的标准化程度',
        'formula': '(存货_t - 存货_t-1) / ((总资产_t + 总资产_t-1) / 2)',
        'data_requirements': {
            'primary': ['balance_sheet'],
            'fields': ['inventories', 'total_assets'],
            'frequency': 'quarterly',
            'lag': '1-2个月'
        },
        'applicable_targets': {
            'stocks': {
                'industries': ['制造业', '零售业', '批发业', '消费业'],
                'exclusions': ['金融业', '纯服务业']
            },
            'indices': {
                'suitable': ['制造业指数', '消费指数', '工业指数'],
                'weighting': ['市值权重', '等权重']
            }
        },
        'economic_interpretation': {
            'positive_values': '存货相对于总资产增加，可能反映业务扩张或存货管理效率下降',
            'negative_values': '存货相对于总资产减少，可能反映存货管理效率提升或销售强劲',
            'magnitude': '绝对值越大，存货变动相对于总资产的影响越显著'
        },
        'usage_notes': {
            'seasonality': '某些行业存在明显季节性，需要考虑季节性调整',
            'outliers': '建议进行异常值检测和处理',
            'industry_specificity': '不同行业的正常存货变动范围差异较大',
            'data_quality': '依赖财务报告质量，需要注意会计准则变更'
        },
        'reference': {
            'paper': 'Thomas, Jacob K., and Huai Zhang. "Inventory changes and future returns." Review of Accounting Studies 7.2-3 (2002): 163-187.',
            'implementation': 'factor_analyze/standardized_inventory_change_factor.py'
        },
        'version': '1.0.0',
        'last_updated': '2024-12-24'
    }


def test_integration():
    """
    测试factor_mining集成功能
    """
    print("=" * 60)
    print("标准化存货变动率因子 factor_mining 集成测试")
    print("=" * 60)
    
    # 1. 测试因子注册
    print("\n1. 测试因子注册...")
    success = register_standardized_inventory_change_factor()
    if success:
        print("✅ 因子注册成功")
    else:
        print("❌ 因子注册失败")
        return
    
    # 2. 测试因子信息获取
    print("\n2. 测试因子信息获取...")
    try:
        factor_info = get_factor_info()
        print(f"✅ 因子信息获取成功")
        print(f"  因子名称: {factor_info['factor_name']}")
        print(f"  因子类别: {factor_info['category']}")
        print(f"  适用目标: {list(factor_info['applicable_targets'].keys())}")
    except Exception as e:
        print(f"❌ 因子信息获取失败: {e}")
    
    # 3. 测试因子计算（使用模拟数据）
    print("\n3. 测试因子计算...")
    try:
        # 测试股票因子计算
        test_symbols = ['000858.SZ', '002415.SZ']  # 五粮液、海康威视
        result = calculate_standardized_inventory_change_factor(
            symbols=test_symbols,
            start_date='2022-01-01',
            end_date='2023-12-31',
            target_type='stock'
        )
        
        if not result['data'].empty:
            print(f"✅ 股票因子计算成功，获得 {result['metadata']['data_points']} 个数据点")
            print(f"  计算时间: {result['metadata']['calculation_time']}")
        else:
            print("⚠️ 股票因子计算成功但无数据")
            
    except Exception as e:
        print(f"❌ 因子计算失败: {e}")
    
    print("\n" + "=" * 60)
    print("集成测试完成")
    print("=" * 60)


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行集成测试
    test_integration()
