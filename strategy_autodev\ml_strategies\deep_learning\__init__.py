# -*- coding: utf-8 -*-
"""
深度学习策略模块
Deep Learning Strategies Module

基于Chapter 17和Chapter 20的深度学习算法交易策略实现
包含前馈神经网络、PyTorch深度学习、架构优化等策略
以及无监督学习：自动编码器、GAN、VAE等策略
"""

# 基础深度学习策略
try:
    from .deep_learning_base import DeepLearningBaseStrategy
    DEEP_LEARNING_BASE_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_BASE_AVAILABLE = False

try:
    from .feedforward_nn_strategy import FeedforwardNNStrategy
    FEEDFORWARD_NN_AVAILABLE = True
except ImportError:
    FEEDFORWARD_NN_AVAILABLE = False

try:
    from .keras_dl_strategy import KerasDLStrategy
    KERAS_DL_AVAILABLE = True
except ImportError:
    KERAS_DL_AVAILABLE = False

try:
    from .nn_optimization_strategy import NNOptimizationStrategy
    NN_OPTIMIZATION_AVAILABLE = True
except ImportError:
    NN_OPTIMIZATION_AVAILABLE = False

# Chapter 20 无监督深度学习策略
try:
    from .autoencoder_strategy import AutoencoderStrategy, register_autoencoder_strategy
    AUTOENCODER_AVAILABLE = True
except ImportError as e:
    import logging
    logging.warning(f"AutoencoderStrategy导入失败: {e}")
    AUTOENCODER_AVAILABLE = False

try:
    from .gan_strategy import GANStrategy, register_gan_strategy
    GAN_AVAILABLE = True
except ImportError as e:
    import logging
    logging.warning(f"GANStrategy导入失败: {e}")
    GAN_AVAILABLE = False

try:
    from .vae_strategy import VAEStrategy, register_vae_strategy
    VAE_AVAILABLE = True
except ImportError as e:
    import logging
    logging.warning(f"VAEStrategy导入失败: {e}")
    VAE_AVAILABLE = False

# 构建__all__列表
__all__ = []

if DEEP_LEARNING_BASE_AVAILABLE:
    __all__.append('DeepLearningBaseStrategy')
if FEEDFORWARD_NN_AVAILABLE:
    __all__.append('FeedforwardNNStrategy')
if KERAS_DL_AVAILABLE:
    __all__.append('KerasDLStrategy')
if NN_OPTIMIZATION_AVAILABLE:
    __all__.append('NNOptimizationStrategy')
if AUTOENCODER_AVAILABLE:
    __all__.extend(['AutoencoderStrategy', 'register_autoencoder_strategy'])
if GAN_AVAILABLE:
    __all__.extend(['GANStrategy', 'register_gan_strategy'])
if VAE_AVAILABLE:
    __all__.extend(['VAEStrategy', 'register_vae_strategy'])

__version__ = '1.1.0'
__author__ = 'RDAgent Strategy AutoDev System'
__description__ = 'Deep Learning strategies for algorithmic trading based on Chapter 17 and Chapter 20 implementations'


def register_all_chapter20_strategies():
    """
    注册所有Chapter 20策略
    
    Returns:
        注册结果字典
    """
    results = {}
    try:
        results['AutoencoderStrategy'] = register_autoencoder_strategy()
        results['GANStrategy'] = register_gan_strategy()
        results['VAEStrategy'] = register_vae_strategy()
    except Exception as e:
        import logging
        logging.error(f"注册Chapter 20策略失败: {e}")
    return results


def get_unsupervised_strategies():
    """
    获取无监督学习策略列表
    
    Returns:
        无监督策略字典
    """
    return {
        'AutoencoderStrategy': {
            'description': '基于自动编码器的特征提取和异常检测策略',
            'capabilities': ['feature_extraction', 'dimensionality_reduction', 'anomaly_detection']
        },
        'GANStrategy': {
            'description': '基于生成对抗网络的数据生成和增强策略',
            'capabilities': ['data_generation', 'data_augmentation', 'synthetic_data_creation']
        },
        'VAEStrategy': {
            'description': '基于变分自动编码器的概率生成建模策略',
            'capabilities': ['probabilistic_modeling', 'uncertainty_quantification', 'latent_space_interpolation']
        }
    }